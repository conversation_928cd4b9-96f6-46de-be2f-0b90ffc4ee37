package com.gclife.policy.controller.ciq;

import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPo;
import com.gclife.policy.model.bo.ciq.CiqRevokePolicyBo;
import com.gclife.policy.model.request.CiqRevokeListRequest;
import com.gclife.policy.model.request.ciq.CiqPolicyListRequest;
import com.gclife.policy.model.request.ciq.CiqPolicyReportRequest;
import com.gclife.policy.model.response.CiqRevokeListResponse;
import com.gclife.policy.model.response.ciq.*;
import com.gclife.policy.service.business.ciq.CiqPolicyBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 海关投保单
 * @date 18-1-4
 */
@Api(tags = "Ciq_policy", description = "海关投保单")
@RestController
@RequestMapping(value = "/v1/ciq/")
public class CiqPolicyController extends BaseController {
    @Autowired
    private CiqPolicyBusinessService ciqPolicyBusinessService;

    @ApiOperation(value = "海关保单查询", notes = "分页查询海关投保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value="policys")
    public ResultObject<BasePageResponse<CiqPolicyListResponse>> getCiqPolicyList(CiqPolicyListRequest request) {
        return ciqPolicyBusinessService.getCiqPolicyList(getCurrentLoginUsers(), request);
    }

    @ApiOperation(value = "海关撤单查询", notes = "分页查询海关撤单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value="revoke/policys")
    public ResultObject<BasePageResponse<CiqRevokeListResponse>> getCiqRevokeList(CiqRevokeListRequest request) {
        return ciqPolicyBusinessService.getCiqRevokeList(request);
    }

    @ApiOperation(value = "海关投保单报表", notes = "获取海关投保单报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value="policy/report")
    public ResultObject<BasePageResponse<CiqPolicyReportResponse>> getCiqPolicyReport(CiqPolicyReportRequest request) {
        return ciqPolicyBusinessService.getCiqPolicyReport(request);
    }

    @ApiOperation(value = "保单验真", notes = "根据验真码和证件号查询保单ID")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value="verify")
    public ResultObject<CiqPolicyVerifyResponse> getPolicyVerify(String idNo, String verifyNo) {
        return ciqPolicyBusinessService.getPolicyVerify(idNo, verifyNo);
    }

    @ApiOperation(value = "查看保单详情", notes = "根据保单ID查询保单详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value="policy")
    public ResultObject<CiqPolicyDetailResponse> getCiqPolicyList(String policyId) {
        return ciqPolicyBusinessService.getCiqPolicyDetail(policyId);
    }

//    @ApiOperation(value = "发送投保书", notes = "发送投保书至电子邮箱")
//    @ApiResponses({
//            @ApiResponse(code = 200, message = "请求成功")
//    })
//    @GetMapping(value="policy/email")
//    public ResultObject<BaseResponse> sendPolicy(String policyNo, String receiverEmail) {
//        return ciqPolicyBusinessService.sendPolicy(policyNo, receiverEmail);
//    }

    @ApiOperation(value = "保单撤单", notes = "保单撤单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @PostMapping(value="policy/revoke")
    public ResultObject revokePolicy(String policyId) {
        return ciqPolicyBusinessService.revocationPolicy(getCurrentLoginUsers().getUserId(),policyId);
    }


    @ApiOperation(value = "保单撤单获取保单信息", notes = "保单撤单获取保单信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idNo", value = "护照号码", example = "护照号码", paramType = "query", required = true)
    })
    @GetMapping(value="policy/revoke")
    public ResultObject<CiqRevokePolicyResponse> getRevocationPolicy(String idNo) {
        return ciqPolicyBusinessService.getRevocationPolicy(idNo);
    }

    @ApiOperation(value = "查询海关保单是否重复投保", notes = "查询海关保单是否重复投保")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idNo", value = "护照号码", example = "护照号码", paramType = "query", required = true)
    })
    @GetMapping(value="policy/duplicate")
    public ResultObject getDuplicatePolicy(String idNo) {
        return ciqPolicyBusinessService.getSafeguardingPolicy(idNo);
    }

    @ApiOperation(value = "获取保单信息", notes = "获取保单信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idNo", value = "护照号码", example = "护照号码", paramType = "query", required = true)
    })
    @GetMapping(value="policy/origin")
    public ResultObject<List<CiqRevokePolicyBo>> getRevocationPolicyOrigin(String idNo) {
        return ciqPolicyBusinessService.getRevocationPolicyOrigin(idNo);
    }

    @ApiOperation(value = "终止保单", notes = "终止保单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @PutMapping(value="policy/terminate")
    public ResultObject terminatePolicy(String policyId) {
        return ciqPolicyBusinessService.terminatePolicy(policyId);
    }

    @ApiOperation(value = "批量获取保单列表", notes = "批量获取保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageSize", value = "每一页的大小", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentPage", value = "当前页", paramType = "query", required = true)
    })
    @GetMapping(value="policy/batch")
    public ResultObject<List<PolicyPo>> retrivePolicyBatch(Integer pageSize, Integer currentPage) {
        return ciqPolicyBusinessService.retrivePolicyBatch(pageSize, currentPage);
    }

    @ApiOperation(value = "批量终止保单", notes = "批量终止保单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyIds", value = "保单ID集合", paramType = "query", required = true)
    })
    @PutMapping(value="policy/terminate/batch")
    public ResultObject terminatePolicyBatch(@RequestParam(name = "policyId") List<String> policyIds) {
        return ciqPolicyBusinessService.terminatePolicyBatch(policyIds);
    }
}
