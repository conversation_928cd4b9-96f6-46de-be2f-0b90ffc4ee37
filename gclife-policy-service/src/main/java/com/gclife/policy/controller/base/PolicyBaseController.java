package com.gclife.policy.controller.base;

import com.gclife.common.annotation.ErrorTip;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.request.ClientServiceAgentChangeRequest;
import com.gclife.policy.model.request.CoverageUpdateRequest;
import com.gclife.policy.model.request.PolicyPaymentUpdateRequest;
import com.gclife.policy.model.request.PolicyUpdateRequest;
import com.gclife.policy.model.response.PolicyAgentHistoryResponse;
import com.gclife.policy.model.response.PolicyPartInfoResponse;
import com.gclife.policy.model.response.PolicyResponse;
import com.gclife.policy.model.response.group.GroupPolicyResponse;
import com.gclife.policy.service.business.base.PolicyBaseBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-6-27
 * description:
 */
@Api(tags = "保单基础接口(2018)", description = "保单基础接口(2018)")
@Controller
@RequestMapping(value = "/v1/base/")
public class PolicyBaseController extends BaseController {

    @Autowired
    private PolicyBaseBusinessService policyBaseBusinessService;

    @ApiOperation(value = "根据代理人ID集合查询保单", notes = "根据代理人ID集合查询保单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "agent/policies")
    public ResultObject<List<PolicyResponse>> queryPolicyBaseList(@RequestBody List<String> agentIds) {
        return policyBaseBusinessService.queryPolicyBaseList(agentIds);
    }

    @ApiOperation(value = "根据保单id查询历史代理人", notes = "根据保单id查询历史代理人")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "agent/history")
    public ResultObject<PolicyAgentHistoryResponse> queryPolicyAgentHistory(String policyId) {
        return policyBaseBusinessService.queryPolicyAgentHistory(getAppRequestHandler(), policyId);
    }

    @ApiOperation(value = "根据保单ID查询保单基本数据", notes = "根据保单ID查询保单基本数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy")
    public ResultObject<PolicyPo> queryOnePolicy(String policyId) {
        return policyBaseBusinessService.queryOnePolicy(policyId);
    }

    @ApiOperation(value = "根据保单ID查询初始保单", notes = "根据保单ID查询初始保单(新契约保单)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "initial/policy")
    public ResultObject<PolicyPo> queryInitialPolicy(String policyId) {
        return policyBaseBusinessService.queryInitialPolicy(policyId);
    }

    @ApiOperation(value = "根据保单ID查询部分保单信息", notes = "根据保单ID查询部分保单信息(包含投保人、被保人及被保人险种、缴费信息、保费信息)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy/part/info")
    public ResultObject<PolicyPartInfoResponse> queryPolicyPartInfo(String policyId) {
        return policyBaseBusinessService.queryPolicyPartInfo(policyId);
    }

    @ApiOperation(value = "查询保单代理人数据", notes = "查询保单代理人数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy/agent")
    public ResultObject<PolicyAgentPo> queryOnePolicyAgent(String policyId) {
        return policyBaseBusinessService.queryOnePolicyAgent(policyId);
    }

    @ApiOperation(value = "查询投保人信息", notes = "查询投保人信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy/applicant")
    public ResultObject<PolicyApplicantBo> queryOnePolicyApplicant(String policyId) {
        return policyBaseBusinessService.queryOnePolicyApplicant(policyId);
    }

    @ApiOperation(value = "根据投保单ID查询被保人清单", notes = "根据投保单ID查询被保人清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy/insured")
    public ResultObject<List<PolicyInsuredBo>> queryPolicyInsured(String policyId) {
        return policyBaseBusinessService.queryPolicyInsured(policyId);
    }

    @ApiOperation(value = "查询最新一期缴费信息", notes = "查询最新一期缴费信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy/payment")
    public ResultObject<PolicyPaymentBo> queryOneNewPolicyPayment(String policyId) {
        return policyBaseBusinessService.queryOneNewPolicyPayment(policyId);
    }


    @ApiOperation(value = "更新保单代理人", notes = "更新保单代理人")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "agentId", value = "需要更新的代理人ID", example = "需要更新的代理人ID", paramType = "query", required = true),
    })
    @PutMapping(value = "policy/agent/update")
    public ResultObject updatePolicyAgent(String policyId, String agentId) {
        return policyBaseBusinessService.updatePolicyAgent(policyId, agentId, false);
    }

    @ApiOperation(value = "查询保单下所有的缴费信息", notes = "查询保单下所有的缴费信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    @GetMapping(value = "policy/payments")
    public ResultObject<List<PolicyPaymentBo>> queryPolicyPayments(String policyId) {
        return policyBaseBusinessService.queryPolicyPayments(policyId);
    }

    @ApiOperation(value = "查询保单下所有的缴费信息", notes = "查询保单下所有的缴费信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    @GetMapping(value = "payments")
    public ResultObject<List<PolicyPaymentBo>> listPolicyPayment(String policyId) {
        return policyBaseBusinessService.listPolicyPayment(policyId);
    }

    @ApiOperation(value = "查询客户下所有的缴费信息", notes = "查询客户下所有的缴费信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "customer/payments")
    public ResultObject<List<PolicyPaymentBo>> listCustomerPolicyPayment(@RequestBody List<String> customerIds) {
        return policyBaseBusinessService.listCustomerPolicyPayment(customerIds);
    }


    @ApiOperation(value = "更新保单缴费信息", notes = "更新保单缴费信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "policy/payment")
    public ResultObject updatePolicyPayment(@RequestBody PolicyPaymentUpdateRequest policyPaymentUpdateRequest) {
        return policyBaseBusinessService.updatePolicyPayment(policyPaymentUpdateRequest);
    }

    @ApiOperation(value = "回滚保单缴费信息", notes = "回滚保单缴费信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "policy/payment/rollback")
    public ResultObject rollbackPolicyPayment(@RequestBody List<PolicyPaymentPo> policyPaymentPos) {
        return policyBaseBusinessService.rollbackPolicyPayment(policyPaymentPos);
    }

    @ApiOperation(value = "更新险种状态", notes = "更新险种状态")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "coverage")
    public ResultObject updateCoverage(@RequestBody CoverageUpdateRequest coverageUpdateRequest) {
        return policyBaseBusinessService.updateCoverage(coverageUpdateRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "更新保单状态", notes = "更新保单状态")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "policy")
    public ResultObject updatePolicy(@RequestBody PolicyUpdateRequest policyUpdateRequest) {
        return policyBaseBusinessService.updatePolicy(policyUpdateRequest);
    }


    @ApiOperation(value = "查询保单主险产品信息", notes = "查询保单主险产品信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy/coverage")
    public ResultObject<PolicyCoveragePo> queryOneMainPolicyCoverage(String policyId) {
        return policyBaseBusinessService.queryOneMainPolicyCoverage(policyId);
    }

    @ApiOperation(value = "查询保单保费缴费信息", notes = "查询保单保费缴费信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy/premium")
    public ResultObject<PolicyPremiumBo> queryPolicyPremium(String policyId) {
        return policyBaseBusinessService.queryPolicyPremium(policyId);
    }

    @ApiOperation(value = "查询保单险种信息", notes = "查询保单险种信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy/coverage/list")
    public ResultObject<List<PolicyCoveragePo>> queryPolicyCoverage(String policyId) {
        return policyBaseBusinessService.queryPolicyCoverage(policyId);
    }

    @ApiOperation(value = "更新保单操作", notes = "更新保单操作")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "policy/operation/update")
    public ResultObject updatePolicyOperation(String policyId, String operationCode) {
        return policyBaseBusinessService.updatePolicyOperation(policyId, operationCode);
    }

    @ApiOperation(value = "失效保单操作", notes = "失效保单操作")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "policy/operation/invalid")
    public ResultObject invalidPolicyOperation(String policyId) {
        return policyBaseBusinessService.invalidPolicyOperation(policyId);
    }

    @ApiOperation(value = "查询保单操作", notes = "查询保单操作")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "policy/operation")
    public ResultObject<BaseOperationPo> queryPolicyOperation(String policyId) {
        return policyBaseBusinessService.queryPolicyOperation(policyId);
    }

    @ApiOperation(value = "查询待生效险种扩展信息", notes = "查询待生效险种扩展信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy/pending/coverage/extend")
    public ResultObject<List<PolicyCoverageExtendPo>> listPendingCoverageExtend(String policyId) {
        return policyBaseBusinessService.listPendingCoverageExtend(policyId);
    }

    @ApiOperation(value = "查询险种扩展信息", notes = "根据险种ID查询险种扩展信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ErrorTip(classType = PolicyErrorConfigEnum.class, key = "POLICY_QUERY_RENEWAL_INSURANCE_COVERAGE_EXTEND_ERROR")
    @PostMapping(value = "renewal/insurance/coverage/extend")
    public ResultObject listCoverageExtendByCoverageId(@RequestBody List<String> coverageIds) {
        return policyBaseBusinessService.listCoverageExtendByCoverageId(coverageIds);
    }

    @ApiOperation(value = "根据保单ID查询支付业务数据", notes = "根据保单ID查询支付业务数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true)
    })
    @GetMapping(value = "policy/payment/business/data")
    public ResultObject queryOnePolicyPaymentBusinessData(String policyId) {
        return policyBaseBusinessService.queryOnePolicyPaymentBusinessData(policyId);
    }


    @ApiOperation(value = "查询团险保单信息", notes = "查询团险保单信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "group/policy")
    public ResultObject<GroupPolicyResponse> queryGroupPolicyInfo(@RequestParam(name = "policyId") String policyId) {
        return policyBaseBusinessService.queryGroupPolicyInfo(policyId);
    }

    @ApiOperation(value = "查询保单详细信息", notes = "根据保单ID和数据生效日期查询保单详细信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "保单ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "dataEffectiveDate", value = "数据生效日期", example = "数据生效日期", paramType = "query")
    })
    @GetMapping(value = "policy/detail/info")
    public ResultObject queryPolicyDetailInfo(@RequestParam(name = "policyId") String policyId,
                                              @RequestParam(name = "dataEffectiveDate") Long dataEffectiveDate) {
        return policyBaseBusinessService.queryPolicyDetailInfo(policyId, dataEffectiveDate);
    }


    @ApiOperation(value = "证件号码查询被保人保单保额", notes = "证件号码查询被保人保单保额")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idNo", value = "证件号吗", example = "证件号吗", paramType = "query")
    })
    @GetMapping(value = "policy/idNo/amount")
    public ResultObject<List<PolicyCoverageBo>> queryIdNoByAmount(@RequestParam(name = "idNo") String idNo) {
        return policyBaseBusinessService.queryIdNoByAmount(idNo);
    }

    @ApiOperation(value = "客户ID查询被保人保单保额", notes = "客户ID查询被保人保单保额")
    @GetMapping(value = "policy/customer/amount")
    public ResultObject<List<PolicyCoverageBo>> queryAmountByCustomerId(@RequestParam(name = "customerAgentId") String customerAgentId) {
        return policyBaseBusinessService.queryAmountByCustomerId(customerAgentId);
    }

    @ApiOperation(value = "模糊查询投保人", notes = "模糊查询投保人")
    @ErrorTip(classType = PolicyErrorConfigEnum.class, key = "POLICY_QUERY_POLICY_APPLICANT_ERROR")
    @GetMapping(value = "fuzzy/applicant")
    public ResultObject<List<PolicyApplicantBo>> listFuzzyPolicyApplicant(@RequestParam(name = "keyword") String keyword,
                                                                          @RequestParam(name = "applicantType") String applicantType) {
        return policyBaseBusinessService.listFuzzyPolicyApplicant(keyword, applicantType);
    }


    @ApiOperation(value = "查询保单险种信息", notes = "查询保单险种信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "coverageIdList", value = "险种ID", example = "险种ID", paramType = "query", required = true)
    })
    @PostMapping(value = "query/policy/coverage")
    public ResultObject<List<PolicyCoverageBo>> queryPolicyCoverageByIdList(@RequestBody List<String> coverageIdList) {
        return policyBaseBusinessService.queryPolicyCoverageByIdList(coverageIdList);
    }

    @ApiOperation(value = "查询保单号集合", notes = "查询保单号集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyWord", value = "保单号", example = "保单号", paramType = "query", required = true)
    })
    @PostMapping(value = "query/policy/nos")
    public ResultObject<List<String>> queryPolicyNos(String keyWord) {
        return policyBaseBusinessService.queryPolicyNos(keyWord);
    }

    @ApiOperation(value = "17号产品暂予承保修改保单状态", notes = "17号产品暂予承保修改保单状态")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "applyId", value = "投保单ID", example = "投保单ID"),
            @ApiImplicitParam(name = "flag", value = "标识符", example = "标识符")
    })
    @PostMapping(value = "pre/policy/status/update")
    public ResultObject updatePrePolicyStatus(@RequestParam(name = "applyId") String applyId,@RequestParam(name = "flag") String flag) {
        return policyBaseBusinessService.updatePrePolicyStatus(applyId,flag);
    }

    @ApiOperation(value = "变更该客户作为投保人下的所有保单的服务业务人员", notes = "变更该客户作为投保人下的所有保单的服务业务人员")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "client/service/agent/change")
    public ResultObject<Void> postClientServiceAgentChange(@RequestBody ClientServiceAgentChangeRequest clientServiceAgentChangeRequest) {
        return policyBaseBusinessService.postClientServiceAgentChange(clientServiceAgentChangeRequest, this.getCurrentLoginUsers());
    }

}
