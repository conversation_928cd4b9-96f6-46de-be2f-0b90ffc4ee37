package com.gclife.policy.controller;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.platform.model.response.BranchTreeResponse;
import com.gclife.policy.model.bo.PolicyAttachmentBo;
import com.gclife.policy.model.bo.PolicyQueryListBo;
import com.gclife.policy.model.request.*;
import com.gclife.policy.model.request.group.PolicyListRequest;
import com.gclife.policy.model.response.*;
import com.gclife.policy.model.response.PolicyStatusResponse;
import com.gclife.policy.service.business.PolicyBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-16
 * description:
 */
@Api(tags = "保单基础", description = "保单基础")
@RestController
@RequestMapping(value = "/v1/policy/")
public class PolicyController extends BaseController {
    @Autowired
    private PolicyBusinessService policyBusinessService;

    @ApiOperation(value = "保单查询", notes = "查询保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "list")
    public ResultObject<BasePageResponse<PolicyQueryListResponse>> queryPolicyList(PolicyQueryListRequest request) {
        return policyBusinessService.queryPolicyList(request);
    }


    @ApiOperation(value = "人管代理人查询保单列表", notes = "人管代理人查询保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "agent/policy/list")
    public ResultObject<BasePageResponse<PolicyListResponse>> queryPolicyListByAgentId(PolicyListRequest policyListRequest) {
        return policyBusinessService.queryPolicyListByAgentId(policyListRequest, this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "查询保单详情", notes = "查询保单详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "1", paramType = "query", required = true)
    })
    @AutoCheckPermissions
    @GetMapping(value = "detail")
    public ResultObject<PolicyDetailResponse> queryPolicyDetailGet(@RequestParam("policyId") String policyId) {
        return policyBusinessService.loadPolicyDetailByPolicyId(this.getCurrentLoginUsers(),policyId);
    }

    @ApiOperation(value = "根据保单号查询保单是否存在", notes = "根据保单号查询保单是否存在")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyNo", value = "保单号", example = "保单号", paramType = "query", required = true)
    })
    @ApiParam(value = "保单号", name = "保单号", example = "保单号")
    @GetMapping(value = "query/policy/exist")
    public ResultObject<PolicyOnlineResponse> getPolicyExistByPolicyNo(@RequestParam(value = "policyNo") String policyNo) {
        return policyBusinessService.getPolicyExistByPolicyNo(policyNo);
    }

    @ApiOperation(value = "根据保单号查询保单是否存在", notes = "根据保单号查询保单是否存在")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyNo", value = "保单号", example = "保单号", paramType = "query", required = true)
    })
    @ApiParam(value = "保单号", name = "保单号", example = "保单号")
    @GetMapping(value = "query/policy/exist/endorse")
    public ResultObject<PolicyOnlineResponse> getPolicyExistByPolicyNoEndorse(@RequestParam(value = "policyNo") String policyNo) {
        return policyBusinessService.getPolicyExistByPolicyNoEndorse(policyNo);
    }

    @ApiOperation(value = "根据保单号查询保单是否存在", notes = "根据保单号查询保单是否存在")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyNo", value = "保单号", example = "保单号", paramType = "query", required = true)
    })
    @ApiParam(value = "保单号", name = "保单号", example = "保单号")
    @PostMapping(value = "query/policy/claim/exist")
    public ResultObject<PolicyOnlineResponse> getPolicyExist(@RequestBody PolicyOnlineRequest policyOnlineRequest) {
        return policyBusinessService.getPolicyExist(policyOnlineRequest);
    }

    @ApiOperation(value = "销售机构树", notes = "销售机构树")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "branch/tree")
    public ResultObject<List<BranchTreeResponse>> policyBranchTree() {
        return policyBusinessService.getPolicyBranchTree();
    }

    @ApiOperation(value = "attachment/save", notes = "附件保存(供其它服务调用)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "attachment/save")
    public ResultObject saveAttachment(@RequestBody AttachmentRequest request) {
        return policyBusinessService.saveAttachment(this.getCurrentLoginUsers(), request);
    }

    @ApiOperation(value = "更新单证生成状态", notes = "更新单证生成状态")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "update/attachment/generate/status")
    public ResultObject updateAttachmentGenerateStatus(String policyId, String attachmentTypeCode, String language, String status) {
        return policyBusinessService.updateAttachmentGenerateStatus(policyId, attachmentTypeCode, language, status);
    }

    @ApiOperation(value = "获取单证生成状态", notes = "获取单证生成状态")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "attachment/generate/status")
    public ResultObject<PolicyAttachmentBo> getAttachmentGenerateStatus(String policyId, String attachmentTypeCode, String language) {
        return policyBusinessService.getAttachmentGenerateStatus(policyId, attachmentTypeCode, language);
    }

    @ApiOperation(value = "电子保单下载", notes = "电子保单下载")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "download")
    public ResultObject<BaseResponse> downloadPolicy(HttpServletResponse httpServletResponse, PolicyDownLoadRequest policyDownLoadRequest) {
        return policyBusinessService.downloadPolicy(httpServletResponse, policyDownLoadRequest);
    }

    @ApiOperation(value = "发送电子保单", notes = "发送电子保单至电子邮箱")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "email")
    public ResultObject<BaseResponse> sendPolicy(String policyId, String receiverEmail) {

        return policyBusinessService.sendPolicy(policyId, receiverEmail);
    }

    @ApiOperation(value = "发送过犹豫期消息", notes = "发送过犹豫期消息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "push/hesitation/message")
    public String pushPolicyHesitation(@RequestParam(name = "pageSize") Integer pageSize, @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return policyBusinessService.pushPolicyHesitation(basePageRequest);
    }

    @ApiOperation(value = "attachment/{id}", notes = "获取附件信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "附件类型", example = "附件类型", paramType = "query", required = true),
            @ApiImplicitParam(name = "language", value = "附件语言", example = "附件语言", paramType = "query", required = true)
    })
    @ApiParam(value = "id", name = "主键ID", example = "主键ID")
    @AutoCheckPermissions
    @GetMapping(value = "attachment/{id}")
    public ResultObject<PolicyAttachmentResponse> getAttachmentByType(@PathVariable(value = "id") String id, String type, String language) {
        return policyBusinessService.getAttachmentByType(this.getCurrentLoginUsers(), id, type, language);
    }


    @ApiOperation(value = "查询客户保单在保状态", notes = "查询客户保单在保状态")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "policy/status")
    public ResultObject<List<PolicyStatusResponse>> queryPolicyStatus(@RequestParam(value = "idList") List<String> idList) {
        return policyBusinessService.queryPolicyStatus(idList);
    }

    @ApiOperation(value = "回滚保单信息", notes = "回滚保单信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @DeleteMapping(value = "rollback")
    public ResultObject rollbackPolicy(@RequestParam("policyId") String policyId,
                                       @RequestParam("oldVersionNo") String oldVersionNo) {
        return policyBusinessService.rollbackPolicy(policyId, oldVersionNo);
    }

    @ApiOperation(value = "多倍保查询疑似客户", notes = "多倍保查询疑似客户")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "suspected/customer/get")
    public ResultObject<SuspectedCustomerResponse> suspectedCustomerGet(@RequestBody SuspectedCustomerRequest suspectedCustomerRequest) {
        return policyBusinessService.suspectedCustomerGet(suspectedCustomerRequest);
    }

    @ApiOperation(value = "系统预警查询已承保的保单列表", notes = "系统预警查询已承保的保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "system/warning/query/policies")
    public ResultObject<List<PolicyQueryListBo>> querySystemWarningPolicyList(@RequestParam("applyId") String applyId) {
        return policyBusinessService.querySystemWarningPolicyList(applyId);
    }

    @ApiOperation(value = "根据customerAgentIds修改保单投保人手机号", notes = "根据customerAgentIds修改保单投保人手机号")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "client/mobile/update")
    public ResultObject updateClientMobile(@RequestBody ClientMobileUpdateRequest clientMobileUpdateRequest) {
        return policyBusinessService.updateClientMobile(clientMobileUpdateRequest);
    }

    @ApiOperation(value = "根据业务员id查询指定月份保单详情", notes = "根据业务员id查询指定月份保单详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "policyId", value = "保单ID", example = "1", paramType = "query", required = true)
    })
    @AutoCheckPermissions
    @GetMapping(value = "detail/month")
    public ResultObject<PolicyDetailMonthResponse> queryPolicyDetailByMonth(@RequestParam("agentId")String agentId, @RequestParam("approveDate")String approveDate) {
        return policyBusinessService.queryPolicyDetailByMonth(this.getCurrentLoginUsers(),agentId,approveDate);
    }

    @ApiOperation(value = "获取存在客户保单列表", notes = "获取存在客户保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "real/client/list")
    public ResultObject<List<PolicyRealClientListResponse>> getPolicyRealClient(@RequestBody List<String> allCustomerIds) {
        return policyBusinessService.getPolicyRealClient(allCustomerIds);
    }

    @ApiOperation(value = "批量查询投保单投保人", notes = "批量查询投保单投保人")
    @PostMapping(value = "applicant/list")
    public ResultObject<List<PolicyApplicantListResponse>> policyApplicantList(@RequestBody List<String> policyNos){
        return policyBusinessService.policyApplicantList(policyNos);

    }
}