package com.gclife.policy.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.policy.core.jooq.tables.PolicyReturnVisit;
import com.gclife.policy.core.jooq.tables.pojos.PolicyRelationPo;
import com.gclife.policy.dao.PolicySeeDao;
import com.gclife.policy.model.bo.PolicyConditionSeeBo;
import com.gclife.policy.model.bo.PolicySeeBo;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.request.PolicyConditionSeeRequest;
import com.gclife.policy.model.request.PolicySeeRequest;
import com.gclife.policy.model.request.group.PolicyListRequest;
import org.jooq.*;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.policy.core.jooq.Tables.*;
import static com.gclife.policy.core.jooq.tables.Policy.POLICY;
import static com.gclife.policy.core.jooq.tables.PolicyAgent.POLICY_AGENT;
import static com.gclife.policy.core.jooq.tables.PolicyApplicant.POLICY_APPLICANT;
import static com.gclife.policy.core.jooq.tables.PolicyCoveragePremium.POLICY_COVERAGE_PREMIUM;
import static com.gclife.policy.core.jooq.tables.PolicyInsured.POLICY_INSURED;
import static com.gclife.policy.core.jooq.tables.PolicyPremium.POLICY_PREMIUM;
import static com.gclife.policy.core.jooq.tables.PolicyCoverage.POLICY_COVERAGE;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL;

/**
 * <AUTHOR>
 * create 17-10-17
 * description:
 */
@Repository
public class PolicySeeDaoImpl extends BaseDaoImpl implements PolicySeeDao {

    /**
     * 根据工作流  请求查询条件    查询保单
     *
     * @param policySeeRequest
     * @return 返回保单
     */
    @Override
    public List<PolicySeeBo> getPolicyList(PolicySeeRequest policySeeRequest, List<String> branchIdList) {
        List<PolicySeeBo> policySeeBoList;

        SelectJoinStep selectOnStep = this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY_APPLICANT.NAME.as("name"))
                .select(POLICY_APPLICANT.ID_NO.as("idNo"))
                .select(POLICY_APPLICANT.ID_TYPE.as("idType"))
                .select(POLICY_APPLICANT.MOBILE.as("phone"))
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                .select(POLICY_AGENT.AGENT_ID)
                .select(POLICY_AGENT.AGENT_CODE)
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_COVERAGE.PRODUCT_NAME.as("productName"))
                .select(POLICY_COVERAGE.PREMIUM_FREQUENCY.as("premiumFrequency"))
                .select(POLICY_COVERAGE.COVERAGE_PERIOD.as("coveragePeriod"))
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT.as("coveragePeriodUnit"))
                //保单打印表
                .from(POLICY)
                //        投保人信息表                                                投保信息表ID
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())))
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.APPLICANT_ID.eq(POLICY.APPLICANT_ID))
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID));

        List<Condition> conditions = new ArrayList<Condition>();
        // 只查个险
        conditions.add(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()));
        //保单号 投保单号 投保人证件号码  投保人姓名 投保人手机号码
        if (AssertUtils.isNotEmpty(policySeeRequest.getPolicyNoOrCustomer())) {
            String policyNoOrCustomer = policySeeRequest.getPolicyNoOrCustomer();
            Condition condition = POLICY.POLICY_NO.like("%" + policyNoOrCustomer + "%").or(POLICY.APPLY_NO.like("%" + policyNoOrCustomer + "%"))
                    .or(POLICY_APPLICANT.ID_NO.like("%" + policyNoOrCustomer + "%")).or(POLICY_APPLICANT.NAME.like("%" + policyNoOrCustomer + "%"))
                    .or(POLICY_APPLICANT.MOBILE.like("%" + policyNoOrCustomer + "%"));
            if (AssertUtils.isNotEmpty(policySeeRequest.getPolicyIds())) {
                condition = condition.or(POLICY.POLICY_ID.in(policySeeRequest.getPolicyIds()));
            }
            conditions.add(condition);
            /*conditions.add(POLICY.POLICY_NO.like("%" + policyNoOrCustomer + "%").or(POLICY.APPLY_NO.like("%" + policyNoOrCustomer + "%"))
                    .or(POLICY_APPLICANT.ID_NO.like("%" + policyNoOrCustomer + "%")).or(POLICY_APPLICANT.NAME.like("%" + policyNoOrCustomer + "%"))
                    .or(POLICY_APPLICANT.MOBILE.like("%" + policyNoOrCustomer + "%")).or(POLICY.POLICY_ID.in(policySeeRequest.getPolicyIds())));*/
        }
        //投保日期
        if (AssertUtils.isNotNull(policySeeRequest.getApplyStartDate())) {
            conditions.add(POLICY.APPLY_DATE.ge(DateUtils.timeToTimeLow(policySeeRequest.getApplyStartDate())));
        }
        if (AssertUtils.isNotNull(policySeeRequest.getApplyEndDate())) {
            conditions.add(POLICY.APPLY_DATE.le(DateUtils.timeToTimeTop(policySeeRequest.getApplyEndDate())));
        }

        //保单生效日期
        if (AssertUtils.isNotNull(policySeeRequest.getEffectiveStartDate())) {
            conditions.add(POLICY.EFFECTIVE_DATE.ge(DateUtils.timeToTimeLow(policySeeRequest.getEffectiveStartDate())));
        }
        if (AssertUtils.isNotNull(policySeeRequest.getEffectiveEndDate())) {
            conditions.add(POLICY.EFFECTIVE_DATE.le(DateUtils.timeToTimeTop(policySeeRequest.getEffectiveEndDate())));
        }

        //保单生效日期
        if (AssertUtils.isNotNull(policySeeRequest.getApproveStartDate())) {
            conditions.add(POLICY.APPROVE_DATE.ge(DateUtils.timeToTimeLow(policySeeRequest.getApproveStartDate())));
        }
        if (AssertUtils.isNotNull(policySeeRequest.getApproveEndDate())) {
            conditions.add(POLICY.APPROVE_DATE.le(DateUtils.timeToTimeTop(policySeeRequest.getApproveEndDate())));
        }
        //保单生效日期
        if (AssertUtils.isNotEmpty(policySeeRequest.getApproveStartDateFormat())) {
            conditions.add(POLICY.APPROVE_DATE.ge(DateUtils.timeToTimeLow(
                    DateUtils.stringToTime(policySeeRequest.getApproveStartDateFormat(), DateUtils.FORMATE3)
            )));
        }
        if (AssertUtils.isNotEmpty(policySeeRequest.getApproveEndDateFormat())) {
            conditions.add(POLICY.APPROVE_DATE.le(DateUtils.timeToTimeTop(
                    DateUtils.stringToTime(policySeeRequest.getApproveEndDateFormat(), DateUtils.FORMATE3)
            )));
        }

        //投保单号
        if (AssertUtils.isNotEmpty(policySeeRequest.getApplyNo())) {
            conditions.add(POLICY.APPLY_NO.like(policySeeRequest.getApplyNo()));
        }
        //销售机构
        if (AssertUtils.isNotEmpty(policySeeRequest.getSalesBranchId())) {
            List<String> filterBranchList = branchIdList.stream().filter(branchId -> branchId.equals(policySeeRequest.getSalesBranchId())).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(filterBranchList)) {
                conditions.add(POLICY.SALES_BRANCH_ID.eq(policySeeRequest.getSalesBranchId()));
            }
        } else {
            conditions.add(POLICY.SALES_BRANCH_ID.in(branchIdList));
        }
        //销售渠道
        if (AssertUtils.isNotEmpty(policySeeRequest.getChannelTypeCode())) {
            conditions.add(POLICY.CHANNEL_TYPE_CODE.eq(policySeeRequest.getChannelTypeCode()));
        }
        //管理机构
        if (AssertUtils.isNotEmpty(policySeeRequest.getManagerBranchId())) {
            conditions.add(POLICY.MANAGER_BRANCH_ID.eq(policySeeRequest.getManagerBranchId()));
        }
        //代理人
        if (AssertUtils.isNotEmpty(policySeeRequest.getAgentId())) {
            conditions.add(POLICY_AGENT.AGENT_ID.eq(policySeeRequest.getAgentId()));
        }
        //保单状态
        if (AssertUtils.isNotEmpty(policySeeRequest.getPolicyStatus())) {
            conditions.add(POLICY.POLICY_STATUS.eq(policySeeRequest.getPolicyStatus()));
        }
        //新旧保单查询
        if (AssertUtils.isNotEmpty(policySeeRequest.getPolicyIds())) {
            conditions.add(POLICY.POLICY_ID.in(policySeeRequest.getPolicyIds()));
        }
        //条件
        selectOnStep.where(conditions);


        //分页查询
        selectOnStep.offset(policySeeRequest.getOffset()).limit(policySeeRequest.getPageSize());

        selectOnStep.orderBy(POLICY.RISK_COMMENCEMENT_DATE.nvl(POLICY.APPROVE_DATE).desc(), POLICY_COVERAGE.COVERAGE_ID.desc());
        //打印sql
        System.err.println(selectOnStep.toString());

        policySeeBoList = selectOnStep.fetchInto(PolicySeeBo.class);
        return policySeeBoList;
    }

    /**
     * 请求多查询条件    查询保单
     *
     * @param policyConditionSeeRequest
     * @return 返回保单
     */
    @Override
    public List<PolicyConditionSeeBo> getPolicyConditionSeeList(PolicyConditionSeeRequest policyConditionSeeRequest, List<String> branchIdList) {
        List<PolicyConditionSeeBo> policyConditionSeeBoList = new LinkedList<PolicyConditionSeeBo>();

        SelectJoinStep selectOnStep = this.getDslContext()
                .selectDistinct(POLICY.POLICY_NO.as("policyNo"))
                .select(POLICY.POLICY_ID.as("policyId"))
                .select(POLICY.APPLY_NO.as("applyNo"))
                .select(POLICY.SALES_BRANCH_ID.as("salesBranchId"))
                .select(POLICY.CHANNEL_TYPE_CODE.as("channelTypeCode"))
                .select(POLICY.MANAGER_BRANCH_ID.as("managerBranchId"))
                .select(POLICY.APPROVE_DATE.as("approveDate"))
                .select(POLICY.POLICY_STATUS.as("policyStatus"))
                .select(POLICY_APPLICANT.ID_NO.as("applicantIdNo"))
                .select(POLICY_APPLICANT.NAME.as("name"))
                .select(POLICY_APPLICANT.ID_TYPE.as("applicantIdType"))
                .select(POLICY_APPLICANT.MOBILE.as("phone"))
                .select(POLICY_AGENT.AGENT_CODE.as("agentCode"))
                .select(POLICY_AGENT.AGENT_ID.as("agentId"))
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_COVERAGE.PRODUCT_CODE.as("productCode"))
                .select(POLICY_COVERAGE.PRODUCT_NAME.as("productName"))
                .select(POLICY_COVERAGE.PREMIUM_FREQUENCY.as("premiumFrequency"))
                .select(POLICY_COVERAGE.COVERAGE_PERIOD.as("coveragePeriod"))
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT.as("coveragePeriodUnit"))
                .select(POLICY.EFFECTIVE_DATE.as("effectiveDate"))
                .select(POLICY.APPROVE_DATE)
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                //保单打印表
                .from(POLICY)
                //        投保人信息表                         投保信息表ID
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.APPLICANT_ID.eq(POLICY.APPLICANT_ID))
                //        代理人信息                           保单ID
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                //        保单险种表                           保单ID
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())));
        List<Condition> conditions = new ArrayList<Condition>();
        //保险合同号   就是  保单号码
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getPolicyNo())) {
            conditions.add(POLICY.POLICY_NO.eq(policyConditionSeeRequest.getPolicyNo()));
        }
        //投保单号
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getApplyNo())) {
            conditions.add(POLICY.APPLY_NO.eq(policyConditionSeeRequest.getApplyNo()));
        }
        //投保人姓名
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getApplyName())) {
            conditions.add(POLICY_APPLICANT.NAME.eq(policyConditionSeeRequest.getApplyName()));
        }
        //续期缴费方式
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getPremiumPeriodType())) {
            //        险种信息表                           保单ID
            selectOnStep.leftJoin(POLICY_COVERAGE_PREMIUM).on(POLICY_COVERAGE_PREMIUM.POLICY_ID.eq(POLICY.POLICY_ID));
            conditions.add(POLICY_COVERAGE_PREMIUM.PREMIUM_PERIOD_TYPE.eq(policyConditionSeeRequest.getPremiumPeriodType()));
        }
        //被保人姓名
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getInsuredName())) {
            //        被保人信息表                         保单ID
            selectOnStep.leftJoin(POLICY_INSURED).on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID));
            conditions.add(POLICY_INSURED.NAME.eq(policyConditionSeeRequest.getInsuredName()));
        }
        //缴费数据号
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getPaymentNo())) {
            //        保单缴费信息表                        保单ID
            selectOnStep.leftJoin(POLICY_PREMIUM).on(POLICY_PREMIUM.POLICY_ID.eq(POLICY.POLICY_ID));
            conditions.add(POLICY_PREMIUM.PAYMENT_NO.eq(policyConditionSeeRequest.getPaymentNo()));
        }
        //代理人编码
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getAgentCode())) {
            conditions.add(POLICY_AGENT.AGENT_CODE.eq(policyConditionSeeRequest.getAgentCode()));
        }

        //保单生效日期
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getEffectiveStartDateFormat())) {
            conditions.add(POLICY.EFFECTIVE_DATE.ge(DateUtils.timeToTimeLow(
                    DateUtils.stringToTime(policyConditionSeeRequest.getEffectiveStartDateFormat(), DateUtils.FORMATE3)
            )));
        }
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getEffectiveEndDateFormat())) {
            conditions.add(POLICY.EFFECTIVE_DATE.le(DateUtils.timeToTimeTop(
                    DateUtils.stringToTime(policyConditionSeeRequest.getEffectiveEndDateFormat(), DateUtils.FORMATE3)
            )));
        }

        //客户证件号码 投保人
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getInsuredIdNo())) {
            conditions.add(POLICY_APPLICANT.ID_NO.eq(policyConditionSeeRequest.getInsuredIdNo()));
        }
        //险种 编码
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getProductCode())) {
            conditions.add(POLICY_COVERAGE.PRODUCT_CODE.eq(policyConditionSeeRequest.getProductCode()));
        }
        //过滤机构
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getManagerBranchId())) {
            conditions.add(POLICY.MANAGER_BRANCH_ID.eq(policyConditionSeeRequest.getManagerBranchId()));
        }

        //投保单号
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getApplyNo())) {
            conditions.add(POLICY.APPLY_NO.eq(policyConditionSeeRequest.getApplyNo()));
        }
        //销售机构
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getSalesBranchId())) {
            List<String> filterBranchList = branchIdList.stream().filter(branchId -> branchId.equals(policyConditionSeeRequest.getSalesBranchId())).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(filterBranchList)) {
                conditions.add(POLICY.SALES_BRANCH_ID.eq(policyConditionSeeRequest.getSalesBranchId()));
            }
        } else {
            conditions.add(POLICY.SALES_BRANCH_ID.in(branchIdList));
        }
        //销售渠道
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getChannelTypeCode())) {
            conditions.add(POLICY.CHANNEL_TYPE_CODE.eq(policyConditionSeeRequest.getChannelTypeCode()));
        }
        //管理机构
        if (AssertUtils.isNotEmpty(policyConditionSeeRequest.getManagerBranchId())) {
            conditions.add(POLICY.MANAGER_BRANCH_ID.eq(policyConditionSeeRequest.getManagerBranchId()));
        }
        // 不查团险
        conditions.add(POLICY.POLICY_TYPE.ne(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name()));
        //条件
        selectOnStep.where(conditions);
        selectOnStep.orderBy(POLICY.APPROVE_DATE.desc());
        //分页查询
        selectOnStep.offset(policyConditionSeeRequest.getOffset()).limit(policyConditionSeeRequest.getPageSize());
        //打印sql
//        System.err.println(selectOnStep.toString());

        policyConditionSeeBoList = selectOnStep.fetchInto(PolicyConditionSeeBo.class);

        return policyConditionSeeBoList;
    }

    @Override
    public List<PolicySeeBo> getPolicyReturnVisitList(PolicySeeRequest policySeeRequest, List<String> branchIdList) {


        SelectConditionStep<Record> noPolicyReturnVisitList = this.getDslContext()
                .select(POLICY.POLICY_ID)
                .select(POLICY_RETURN_VISIT.POLICY_RETURN_VISIT_ID)
                .select(POLICY_RETURN_VISIT.RETURN_VISIT_TYPE)
                .select(POLICY_RETURN_VISIT.UPDATE_DATE.nvl(POLICY_RETURN_VISIT.CREATE_DATE).as("returnVisitDate"))
                .select(POLICY_RETURN_VISIT.RETURN_VISIT_USER_ID)
                .select(POLICY_RETURN_VISIT_CHANGE.RETURN_VISIT_CHANGE_ID)
                .select(POLICY_RETURN_VISIT_CHANGE.OPERATION_USER_ID)
                .select(POLICY_RETURN_VISIT_CHANGE.OPERATION_DATE)
                .select(POLICY_RETURN_VISIT_CHANGE.AUDIT_RESULT)
                //保单打印表
                .from(POLICY)
                .leftJoin(POLICY_RETURN_VISIT).on(POLICY_RETURN_VISIT.BUSINESS_ID.eq(POLICY.POLICY_ID).and(POLICY_RETURN_VISIT.RETURN_VISIT_TYPE.eq(PolicyTermEnum.RETURN_VISIT_TYPE.NEW_POLICY.name())))
                .leftJoin(POLICY_RETURN_VISIT_CHANGE).on(POLICY_RETURN_VISIT_CHANGE.POLICY_RETURN_VISIT_ID.eq(POLICY_RETURN_VISIT.POLICY_RETURN_VISIT_ID))
                .where(POLICY_RETURN_VISIT.POLICY_RETURN_VISIT_ID.isNull());

        SelectOrderByStep<Record> selectOrderByStep = this.getDslContext()
                .select(POLICY.POLICY_ID)
                .select(POLICY_RETURN_VISIT.POLICY_RETURN_VISIT_ID)
                .select(POLICY_RETURN_VISIT.RETURN_VISIT_TYPE)
                .select(POLICY_RETURN_VISIT.UPDATE_DATE.nvl(POLICY_RETURN_VISIT.CREATE_DATE).as("returnVisitDate"))
                .select(POLICY_RETURN_VISIT.RETURN_VISIT_USER_ID)
                .select(POLICY_RETURN_VISIT_CHANGE.RETURN_VISIT_CHANGE_ID)
                .select(POLICY_RETURN_VISIT_CHANGE.OPERATION_USER_ID)
                .select(POLICY_RETURN_VISIT_CHANGE.OPERATION_DATE)
                .select(POLICY_RETURN_VISIT_CHANGE.AUDIT_RESULT)
                //保单打印表
                .from(POLICY)
                .leftJoin(POLICY_RETURN_VISIT).on(POLICY_RETURN_VISIT.BUSINESS_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_RETURN_VISIT_CHANGE).on(POLICY_RETURN_VISIT_CHANGE.POLICY_RETURN_VISIT_ID.eq(POLICY_RETURN_VISIT.POLICY_RETURN_VISIT_ID))
                .where(POLICY_RETURN_VISIT.POLICY_RETURN_VISIT_ID.isNotNull())
                .unionAll(noPolicyReturnVisitList);
        Table<Record> recordTable = selectOrderByStep.asTable();

        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY.POLICY_TYPE.decode(LIFE_INSURANCE_PERSONAL.name(), POLICY_APPLICANT.NAME, LIFE_INSURANCE_GROUP.name(), POLICY_APPLICANT.DELEGATE_NAME).as("name"))
                .select(POLICY.POLICY_TYPE.decode(LIFE_INSURANCE_PERSONAL.name(), POLICY_APPLICANT.ID_NO, LIFE_INSURANCE_GROUP.name(), POLICY_APPLICANT.DELEGATE_ID_NO).as("idNo"))
                .select(POLICY.POLICY_TYPE.decode(LIFE_INSURANCE_PERSONAL.name(), POLICY_APPLICANT.ID_TYPE, LIFE_INSURANCE_GROUP.name(), POLICY_APPLICANT.DELEGATE_ID_TYPE).as("idType"))
                .select(POLICY.POLICY_TYPE.decode(LIFE_INSURANCE_PERSONAL.name(), POLICY_APPLICANT.MOBILE, LIFE_INSURANCE_GROUP.name(), POLICY_APPLICANT.DELEGATE_MOBILE).as("phone"))
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                .select(POLICY_AGENT.AGENT_ID)
                .select(POLICY_AGENT.AGENT_CODE)
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_COVERAGE.PRODUCT_NAME.as("productName"))
                .select(POLICY_COVERAGE.PREMIUM_FREQUENCY.as("premiumFrequency"))
                .select(POLICY_COVERAGE.COVERAGE_PERIOD.as("coveragePeriod"))
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT.as("coveragePeriodUnit"))
                .select(recordTable.fields())
                //保单打印表
                .from(POLICY)
                //        投保人信息表                                                投保信息表ID
                .leftJoin(POLICY_COVERAGE).on(
                        POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                                .and(
                                        (POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()).and(POLICY_COVERAGE.INSURED_ID.isNotNull()))
                                                .or(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_GROUP.name()).and(POLICY_COVERAGE.INSURED_ID.isNull())))
                )
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.APPLICANT_ID.eq(POLICY.APPLICANT_ID))
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(recordTable).on(recordTable.field(POLICY.POLICY_ID).eq(POLICY.POLICY_ID));

        List<Condition> conditions = new ArrayList<Condition>();
        //保单号 投保单号 投保人证件号码  投保人姓名 投保人手机号码
        if (AssertUtils.isNotEmpty(policySeeRequest.getPolicyNoOrCustomer())) {
            String policyNoOrCustomer = policySeeRequest.getPolicyNoOrCustomer();
            conditions.add(POLICY.POLICY_NO.like("%" + policyNoOrCustomer + "%").or(POLICY.APPLY_NO.like("%" + policyNoOrCustomer + "%"))
                    .or(POLICY_APPLICANT.ID_NO.like("%" + policyNoOrCustomer + "%")).or(POLICY_APPLICANT.NAME.like("%" + policyNoOrCustomer + "%"))
                    .or(POLICY_APPLICANT.MOBILE.like("%" + policyNoOrCustomer + "%")));
        }
        //投保日期
        if (AssertUtils.isNotNull(policySeeRequest.getApplyStartDate())) {
            conditions.add(POLICY.APPLY_DATE.ge(DateUtils.timeToTimeLow(policySeeRequest.getApplyStartDate())));
        }
        if (AssertUtils.isNotNull(policySeeRequest.getApplyEndDate())) {
            conditions.add(POLICY.APPLY_DATE.le(DateUtils.timeToTimeTop(policySeeRequest.getApplyEndDate())));
        }

        //保单生效日期
        if (AssertUtils.isNotNull(policySeeRequest.getEffectiveStartDate())) {
            conditions.add(POLICY.EFFECTIVE_DATE.ge(DateUtils.timeToTimeLow(policySeeRequest.getEffectiveStartDate())));
        }
        if (AssertUtils.isNotNull(policySeeRequest.getEffectiveEndDate())) {
            conditions.add(POLICY.EFFECTIVE_DATE.le(DateUtils.timeToTimeTop(policySeeRequest.getEffectiveEndDate())));
        }

        //保单生效日期
        if (AssertUtils.isNotNull(policySeeRequest.getApproveStartDate())) {
            conditions.add(POLICY.APPROVE_DATE.ge(DateUtils.timeToTimeLow(policySeeRequest.getApproveStartDate())));
        }
        if (AssertUtils.isNotNull(policySeeRequest.getApproveEndDate())) {
            conditions.add(POLICY.APPROVE_DATE.le(DateUtils.timeToTimeTop(policySeeRequest.getApproveEndDate())));
        }
        //保单生效日期
        if (AssertUtils.isNotEmpty(policySeeRequest.getApproveStartDateFormat())) {
            conditions.add(POLICY.APPROVE_DATE.ge(DateUtils.timeToTimeLow(
                    DateUtils.stringToTime(policySeeRequest.getApproveStartDateFormat(), DateUtils.FORMATE3)
            )));
        }
        if (AssertUtils.isNotEmpty(policySeeRequest.getApproveEndDateFormat())) {
            conditions.add(POLICY.APPROVE_DATE.le(DateUtils.timeToTimeTop(
                    DateUtils.stringToTime(policySeeRequest.getApproveEndDateFormat(), DateUtils.FORMATE3)
            )));
        }

        //投保单号
        if (AssertUtils.isNotEmpty(policySeeRequest.getApplyNo())) {
            conditions.add(POLICY.APPLY_NO.like(policySeeRequest.getApplyNo()));
        }
        //销售机构
        if (AssertUtils.isNotEmpty(policySeeRequest.getSalesBranchId())) {
            List<String> filterBranchList = branchIdList.stream().filter(branchId -> branchId.equals(policySeeRequest.getSalesBranchId())).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(filterBranchList)) {
                conditions.add(POLICY.SALES_BRANCH_ID.eq(policySeeRequest.getSalesBranchId()));
            }
        } else {
            conditions.add(POLICY.SALES_BRANCH_ID.in(branchIdList));
        }
        //销售渠道
        if (AssertUtils.isNotEmpty(policySeeRequest.getChannelTypeCode())) {
            conditions.add(POLICY.CHANNEL_TYPE_CODE.eq(policySeeRequest.getChannelTypeCode()));
        }
        //管理机构
        if (AssertUtils.isNotEmpty(policySeeRequest.getManagerBranchId())) {
            conditions.add(POLICY.MANAGER_BRANCH_ID.eq(policySeeRequest.getManagerBranchId()));
        }
        //代理人
        if (AssertUtils.isNotEmpty(policySeeRequest.getAgentId())) {
            conditions.add(POLICY_AGENT.AGENT_ID.eq(policySeeRequest.getAgentId()));
        }
        //保单状态
        if (AssertUtils.isNotEmpty(policySeeRequest.getPolicyStatus())) {
            conditions.add(POLICY.POLICY_STATUS.eq(policySeeRequest.getPolicyStatus()));
        }
        //回访查询
        if (AssertUtils.isNotNull(policySeeRequest.getPolicyReturnVisitStatus())) {
            if (policySeeRequest.getPolicyReturnVisitStatus().equals("NO_RETURN_VISIT")) {
                conditions.add(recordTable.field(POLICY_RETURN_VISIT.POLICY_RETURN_VISIT_ID).isNull());
            }
            if (policySeeRequest.getPolicyReturnVisitStatus().equals("YES_RETURN_VISIT")) {
                conditions.add(recordTable.field(POLICY_RETURN_VISIT.POLICY_RETURN_VISIT_ID).isNotNull());
            }
        }
        //条件
        selectOnConditionStep.where(conditions);


        //分页查询
        selectOnConditionStep.offset(policySeeRequest.getOffset()).limit(policySeeRequest.getPageSize());
        if (policySeeRequest.isPolicyReturnVisit()) {
            selectOnConditionStep.orderBy(POLICY.APPROVE_DATE.asc());
        } else {
            selectOnConditionStep.orderBy(POLICY.APPROVE_DATE.desc());
        }

        //打印sql
        System.err.println(selectOnConditionStep.toString());

        return selectOnConditionStep.fetchInto(PolicySeeBo.class);
    }

    @Override
    public List<PolicySeeBo> getPolicyReturnVisitAudit(PolicySeeRequest policySeeRequest, List<String> branchIdList) {
        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY.POLICY_TYPE.decode(LIFE_INSURANCE_PERSONAL.name(), POLICY_APPLICANT.NAME, LIFE_INSURANCE_GROUP.name(), POLICY_APPLICANT.DELEGATE_NAME).as("name"))
                .select(POLICY.POLICY_TYPE.decode(LIFE_INSURANCE_PERSONAL.name(), POLICY_APPLICANT.ID_NO, LIFE_INSURANCE_GROUP.name(), POLICY_APPLICANT.DELEGATE_ID_NO).as("idNo"))
                .select(POLICY.POLICY_TYPE.decode(LIFE_INSURANCE_PERSONAL.name(), POLICY_APPLICANT.ID_TYPE, LIFE_INSURANCE_GROUP.name(), POLICY_APPLICANT.DELEGATE_ID_TYPE).as("idType"))
                .select(POLICY.POLICY_TYPE.decode(LIFE_INSURANCE_PERSONAL.name(), POLICY_APPLICANT.MOBILE, LIFE_INSURANCE_GROUP.name(), POLICY_APPLICANT.DELEGATE_MOBILE).as("phone"))
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                .select(POLICY_AGENT.AGENT_ID)
                .select(POLICY_AGENT.AGENT_CODE)
                .select(POLICY_RETURN_VISIT.POLICY_RETURN_VISIT_ID)
                .select(POLICY_RETURN_VISIT.RETURN_VISIT_TYPE)
                .select(POLICY_RETURN_VISIT.UPDATE_DATE.nvl(POLICY_RETURN_VISIT.CREATE_DATE).as("returnVisitDate"))
                .select(POLICY_RETURN_VISIT.RETURN_VISIT_USER_ID)
                .select(POLICY_RETURN_VISIT_CHANGE.RETURN_VISIT_CHANGE_ID)
                .select(POLICY_RETURN_VISIT_CHANGE.OPERATION_USER_ID)
                .select(POLICY_RETURN_VISIT_CHANGE.OPERATION_DATE)
                .select(POLICY_RETURN_VISIT_CHANGE.AUDIT_RESULT)
                .select(POLICY_COVERAGE.PRODUCT_NAME.as("productName"))
                .select(POLICY_COVERAGE.PREMIUM_FREQUENCY.as("premiumFrequency"))
                .select(POLICY_COVERAGE.COVERAGE_PERIOD.as("coveragePeriod"))
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT.as("coveragePeriodUnit"))
                //保单打印表
                .from(POLICY)
                //        投保人信息表                                                投保信息表ID
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                        .and(
                                (POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()).and(POLICY_COVERAGE.INSURED_ID.isNotNull()))
                                        .or(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_GROUP.name()).and(POLICY_COVERAGE.INSURED_ID.isNull())))
                )
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.APPLICANT_ID.eq(POLICY.APPLICANT_ID))
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_RETURN_VISIT).on(POLICY_RETURN_VISIT.BUSINESS_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_RETURN_VISIT_CHANGE).on(POLICY_RETURN_VISIT_CHANGE.POLICY_RETURN_VISIT_ID.eq(POLICY_RETURN_VISIT.POLICY_RETURN_VISIT_ID));


        List<Condition> conditions = new ArrayList<Condition>();
        //保单号 投保单号 投保人证件号码  投保人姓名 投保人手机号码
        if (AssertUtils.isNotEmpty(policySeeRequest.getPolicyNoOrCustomer())) {
            String policyNoOrCustomer = policySeeRequest.getPolicyNoOrCustomer();
            conditions.add(POLICY.POLICY_NO.like("%" + policyNoOrCustomer + "%").or(POLICY.APPLY_NO.like("%" + policyNoOrCustomer + "%"))
                    .or(POLICY_APPLICANT.ID_NO.like("%" + policyNoOrCustomer + "%")).or(POLICY_APPLICANT.NAME.like("%" + policyNoOrCustomer + "%"))
                    .or(POLICY_APPLICANT.MOBILE.like("%" + policyNoOrCustomer + "%")));
        }
        //投保日期
        if (AssertUtils.isNotNull(policySeeRequest.getApplyStartDate())) {
            conditions.add(POLICY.APPLY_DATE.ge(DateUtils.timeToTimeLow(policySeeRequest.getApplyStartDate())));
        }
        if (AssertUtils.isNotNull(policySeeRequest.getApplyEndDate())) {
            conditions.add(POLICY.APPLY_DATE.le(DateUtils.timeToTimeTop(policySeeRequest.getApplyEndDate())));
        }

        //保单生效日期
        if (AssertUtils.isNotNull(policySeeRequest.getEffectiveStartDate())) {
            conditions.add(POLICY.EFFECTIVE_DATE.ge(DateUtils.timeToTimeLow(policySeeRequest.getEffectiveStartDate())));
        }
        if (AssertUtils.isNotNull(policySeeRequest.getEffectiveEndDate())) {
            conditions.add(POLICY.EFFECTIVE_DATE.le(DateUtils.timeToTimeTop(policySeeRequest.getEffectiveEndDate())));
        }

        //保单生效日期
        if (AssertUtils.isNotNull(policySeeRequest.getApproveStartDate())) {
            conditions.add(POLICY.APPROVE_DATE.ge(DateUtils.timeToTimeLow(policySeeRequest.getApproveStartDate())));
        }
        if (AssertUtils.isNotNull(policySeeRequest.getApproveEndDate())) {
            conditions.add(POLICY.APPROVE_DATE.le(DateUtils.timeToTimeTop(policySeeRequest.getApproveEndDate())));
        }
        //保单生效日期
        if (AssertUtils.isNotEmpty(policySeeRequest.getApproveStartDateFormat())) {
            conditions.add(POLICY.APPROVE_DATE.ge(DateUtils.timeToTimeLow(
                    DateUtils.stringToTime(policySeeRequest.getApproveStartDateFormat(), DateUtils.FORMATE3)
            )));
        }
        if (AssertUtils.isNotEmpty(policySeeRequest.getApproveEndDateFormat())) {
            conditions.add(POLICY.APPROVE_DATE.le(DateUtils.timeToTimeTop(
                    DateUtils.stringToTime(policySeeRequest.getApproveEndDateFormat(), DateUtils.FORMATE3)
            )));
        }

        //投保单号
        if (AssertUtils.isNotEmpty(policySeeRequest.getApplyNo())) {
            conditions.add(POLICY.APPLY_NO.like(policySeeRequest.getApplyNo()));
        }
        //销售机构
        if (AssertUtils.isNotEmpty(policySeeRequest.getSalesBranchId())) {
            List<String> filterBranchList = branchIdList.stream().filter(branchId -> branchId.equals(policySeeRequest.getSalesBranchId())).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(filterBranchList)) {
                conditions.add(POLICY.SALES_BRANCH_ID.eq(policySeeRequest.getSalesBranchId()));
            }
        } else {
            conditions.add(POLICY.SALES_BRANCH_ID.in(branchIdList));
        }
        //销售渠道
        if (AssertUtils.isNotEmpty(policySeeRequest.getChannelTypeCode())) {
            conditions.add(POLICY.CHANNEL_TYPE_CODE.eq(policySeeRequest.getChannelTypeCode()));
        }
        //管理机构
        if (AssertUtils.isNotEmpty(policySeeRequest.getManagerBranchId())) {
            conditions.add(POLICY.MANAGER_BRANCH_ID.eq(policySeeRequest.getManagerBranchId()));
        }
        //代理人
        if (AssertUtils.isNotEmpty(policySeeRequest.getAgentId())) {
            conditions.add(POLICY_AGENT.AGENT_ID.eq(policySeeRequest.getAgentId()));
        }
        //保单状态
        if (AssertUtils.isNotEmpty(policySeeRequest.getPolicyStatus())) {
            conditions.add(POLICY.POLICY_STATUS.eq(policySeeRequest.getPolicyStatus()));
        }
        conditions.add(POLICY_RETURN_VISIT_CHANGE.RETURN_VISIT_CHANGE_ID.isNotNull());
        conditions.add(POLICY_RETURN_VISIT_CHANGE.AUDIT_RESULT.isNull());

        //条件
        selectOnConditionStep.where(conditions);


        //分页查询
        selectOnConditionStep.offset(policySeeRequest.getOffset()).limit(policySeeRequest.getPageSize());
        selectOnConditionStep.orderBy(POLICY_RETURN_VISIT_CHANGE.OPERATION_DATE.asc());

        //打印sql
        System.err.println(selectOnConditionStep.toString());

        return selectOnConditionStep.fetchInto(PolicySeeBo.class);
    }

    @Override
    public PolicyRelationPo getPolicyRelation(String keyWord) {
        PolicyRelationPo policyRelationPo = null;
        SelectJoinStep<Record> recordSelectJoinStep = this.getDslContext().select(POLICY_RELATION.fields()).from(POLICY_RELATION);
        List<Condition> conditions = new ArrayList<Condition>();
        //保单号
        if (AssertUtils.isNotEmpty(keyWord)) {
            conditions.add(POLICY_RELATION.POLICY_NO.eq(keyWord));
            conditions.add(POLICY_RELATION.VALID_FLAG.eq(PolicyTermEnum.VALID_FLAG.effective.name()));
        }
        SelectOffsetStep<Record> selectOffsetStep = recordSelectJoinStep.where(conditions).limit(1);
        policyRelationPo = selectOffsetStep.fetchOneInto(PolicyRelationPo.class);
        return policyRelationPo;
    }

    @Override
    public List<PolicyRelationPo> getPolicyRelationPolicyNo(PolicyRelationPo policyRelationPo) {
        List<PolicyRelationPo> policyRelationPos = null;
        if (!AssertUtils.isNotNull(policyRelationPo)) {
            return null;
        }
        if (AssertUtils.isNotNull(policyRelationPo)) {
            String relationPolicyNo = policyRelationPo.getRelationPolicyNo();
            if (AssertUtils.isNotNull(relationPolicyNo)) {
                policyRelationPos =
                        this.getDslContext().select(POLICY_RELATION.fields()).from(POLICY_RELATION).where(POLICY_RELATION.RELATION_POLICY_NO.eq(relationPolicyNo).and(POLICY_RELATION.VALID_FLAG.eq(PolicyTermEnum.VALID_FLAG.effective.name()))).fetchInto(PolicyRelationPo.class);
            }
        }
        return policyRelationPos;
    }
}




















