package com.gclife.policy.dao.impl;

import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.BasePojo;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.policy.core.jooq.Tables;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.core.jooq.tables.records.PolicyBeneficiaryInfoRecord;
import com.gclife.policy.core.jooq.tables.records.PolicyBeneficiaryRecord;
import com.gclife.policy.core.jooq.tables.records.PolicyCoverageRecord;
import com.gclife.policy.dao.PolicyBaseDao;
import com.gclife.policy.dao.PolicyExtDao;
import com.gclife.policy.dao.PolicyLoanBaseDao;
import com.gclife.policy.dao.PolicyReferralInfoBaseDao;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.request.PolicyQueryListRequest;
import com.gclife.policy.model.request.PolicyReceiptListRequest;
import com.gclife.policy.model.request.SuspectedCustomerRequest;
import com.gclife.policy.model.response.PolicyPaymentBusinessCoverageDataResponse;
import com.gclife.policy.model.response.PolicyPaymentBusinessDataResponse;
import com.gclife.policy.model.response.PolicyRealClientListResponse;
import com.gclife.policy.service.base.PolicyOtherInfoService;
import com.gclife.product.model.config.ProductTermEnum;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.jooq.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.policy.core.jooq.Routines.generatePolicyNumber;
import static com.gclife.policy.core.jooq.Tables.*;
import static com.gclife.policy.core.jooq.tables.PolicyCoverage.POLICY_COVERAGE;

/**
 * <AUTHOR>
 * create 17-11-21
 * description:
 */
@Component
public class PolicyExtDaoImpl extends BaseDaoImpl implements PolicyExtDao {

    @Autowired
    PolicyBaseDao policyBaseDao;
    @Autowired
    PolicyLoanBaseDao policyLoanBaseDao;
    @Autowired
    PolicyReferralInfoBaseDao policyReferralInfoBaseDao;
    @Autowired
    PolicyOtherInfoService policyOtherInfoService;

    /**
     * 查询保单对象
     *
     * @param policyId 　保单ID
     * @return PolicyBo
     */
    @Override
    public PolicyBo loadPolicyByPolicyId(String policyId) {
        PolicyBo policyBo;
        try {
            /*查询保单信息*/
            policyBo = this.getDslContext()
                    .select(POLICY.fields())
                    .from(POLICY)
                    .where(POLICY.POLICY_ID.eq(policyId))
                    .fetchOneInto(PolicyBo.class);
            //数据验证
            AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            /*查询保单所属代理人*/
            PolicyAgentBo agent = this.loadPolicyAgentByPolicyId(policyId);
            policyBo.setPolicyAgent(agent);
            //数据验证
            AssertUtils.isNotNull(this.getLogger(), agent, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_AGENT_ERROR);

            /*查询保单投保人*/
            PolicyApplicantBo applicant = this.loadPolicyApplicantByPolicyId(policyId);
            policyBo.setPolicyApplicant(applicant);
            AssertUtils.isNotNull(this.getLogger(), applicant, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_APPLICANT_ERROR);


            /*查询保单联系*/
            PolicyContactInfoBo contactInfoBo = this.loadPolicyContactByPolicyId(policyId);
            policyBo.setPolicyContactInfo(contactInfoBo);

            /*查询保单账户*/
            List<PolicyAccountBo> policyAccountBos = this.loadPolicyAccountListByPolicyId(policyId);
            policyBo.setListPolicyAccount(policyAccountBos);

            /*查询保单附件*/
            List<PolicyAttachmentBo> listAttachment = this.loadPolicyAttachmentListByPolicyId(policyId);
            policyBo.setListPolicyAttachment(listAttachment);


            /*查询保单被保人*/
            List<PolicyInsuredBo> listInsured = this.loadPolicyInsuredListByPolicyId(policyId);
            policyBo.setListPolicyInsured(listInsured);
            AssertUtils.isNotEmpty(this.getLogger(), listInsured, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_INSURED_ERROR);

            /*查询保单险种*/
            List<PolicyCoverageBo> listCoverage = this.loadPolicyCoverageListByPolicyId(policyId);
            AssertUtils.isNotEmpty(this.getLogger(), listCoverage, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_INSURED_ERROR);
            policyBo.setListInsuredCoverage(listCoverage.stream()
                    .filter(coverageBo -> AssertUtils.isNotEmpty(coverageBo.getInsuredId()))
                    .collect(Collectors.toList())
            );
            //设置险种
            listInsured.forEach(insured -> {
                List<PolicyCoverageBo> list = listCoverage.stream().filter(coverage -> insured.getInsuredId().equals(coverage.getInsuredId())).collect(Collectors.toList());
                insured.setListPolicyCoverage(list);
            });

            /*查询保单受益人*/
            List<PolicyBeneficiaryInfoBo> listBeneficiary = this.loadPolicyBeneficiaryListByPolicyId(policyId);
            //设置受益人
            if (AssertUtils.isNotEmpty(listBeneficiary)) {
                listInsured.forEach(insured -> {
                    List<PolicyBeneficiaryInfoBo> list = listBeneficiary.stream().filter(beneficiaryInfoBo -> insured.getInsuredId().equals(beneficiaryInfoBo.getInsuredId())).collect(Collectors.toList());
                    insured.setListPolicyBeneficiary(list);
                });
            }

            /*查询保单保费信息*/
            PolicyPremiumBo policyPremium = this.loadPolicyPremiumByPolicyId(policyId);
            policyBo.setPolicyPremium(policyPremium);

            /*查询保单缴费信息*/
            List<PolicyPaymentBo> listPolicyPaymentBo = policyBaseDao.getListPayPolicyPayment(policyId);
            policyBo.setListPolicyPayment(listPolicyPaymentBo);

            /*查询保单被保人扩展信息*/
            List<PolicyInsuredExtendPo> policyInsuredExtendPoList = policyBaseDao.queryPolicyInsuredExtend(policyId);
            policyBo.setListPolicyInsuredExtend(policyInsuredExtendPoList);

            /*查询保单付费人信息*/
            PolicyPayorInfoBo policyPayorInfoBo = policyBaseDao.queryOnePolicyPayorInfo(policyId);
            policyBo.setPolicyPayorInfo(policyPayorInfoBo);

            /*查询保单加费信息*/
            List<PolicyAddPremiumPo> listPolicyAddPremiumPo = policyBaseDao.getPolicyAddPremium(policyId);
            policyBo.setListPolicyAddPremium(listPolicyAddPremiumPo);

            /*查询保单被保人统计信息*/
            PolicyInsuredCollectPo policyInsuredCollectPo = policyBaseDao.queryOnePolicyInsuredCollect(policyId);
            policyBo.setPolicyInsuredCollect(policyInsuredCollectPo);

            /*查询回执信息*/
            PolicyReceiptInfoBo policyReceiptInfoBo = this.loadPolicyReceipt(policyId);
            if (!AssertUtils.isNotNull(policyReceiptInfoBo)) {
                policyReceiptInfoBo = new PolicyReceiptInfoBo();
            }
            policyBo.setPolicyReceiptInfo(policyReceiptInfoBo);

            /*查询保单打印信息*/
            PolicyPrintInfoBo policyPrintInfoBo = this.loadPolicyPrintInfoBo(policyId);
            policyBo.setPolicyPrintInfo(policyPrintInfoBo);

            /*查询保单特别约定信息*/
            List<PolicySpecialContractPo> listPolicySpecialContract = policyBaseDao.getPolicySpecialContract(policyId);
            policyBo.setListPolicySpecialContract(listPolicySpecialContract);

            /*查询保单合同贷款信息*/
            PolicyLoanPo policyLoanPo = policyLoanBaseDao.queryPolicyLoanPo(policyId);
            if (AssertUtils.isNotNull(policyLoanPo)) {
                policyBo.setLoanContract(policyLoanPo);
            }

            /*查询保单推荐信息*/
            PolicyReferralInfoPo policyReferralInfoPo = policyReferralInfoBaseDao.queryPolicyReferralInfoPo(policyId);
            if (AssertUtils.isNotNull(policyReferralInfoPo)) {
                policyBo.setReferralInfo(policyReferralInfoPo);
            }
            /*查询保单持有人信息*/
            PolicyHolderPo policyHolderPo = policyOtherInfoService.queryPolicyHolderPo(policyId);
            if (AssertUtils.isNotNull(policyHolderPo)) {
                policyBo.setHolder(policyHolderPo);
            }
            return policyBo;
        } catch (Exception e) {
            this.getLogger().error(ExceptionUtils.getFullStackTrace(e));
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        }
    }

    @Override
    public PolicyBo loadSimplePolicyByPolicyId(String policyId) {
        PolicyBo policyBo = null;
        try {
            /*查询保单信息*/
            policyBo = this.getDslContext()
                    .select(POLICY.fields())
                    .from(POLICY)
                    .where(POLICY.POLICY_ID.eq(policyId))
                    .fetchOneInto(PolicyBo.class);
            //数据验证
            AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            /*查询保单投保人*/
            PolicyApplicantBo applicant = this.loadPolicyApplicantByPolicyId(policyId);
            policyBo.setPolicyApplicant(applicant);
            AssertUtils.isNotNull(this.getLogger(), applicant, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_APPLICANT_ERROR);
            /*查询保单被保人*/
            List<PolicyInsuredBo> listInsured = this.loadPolicyInsuredListByPolicyId(policyId);
            policyBo.setListPolicyInsured(listInsured);
            AssertUtils.isNotEmpty(this.getLogger(), listInsured, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_INSURED_ERROR);
            /*查询保单险种*/
            List<PolicyCoverageBo> listCoverage = this.loadPolicyCoverageListByPolicyId(policyId);
            AssertUtils.isNotEmpty(this.getLogger(), listCoverage, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_INSURED_ERROR);
            //设置险种
            listInsured.forEach(insured -> {
                List<PolicyCoverageBo> list = listCoverage.stream().filter(coverage -> insured.getInsuredId().equals(coverage.getInsuredId())).collect(Collectors.toList());
                insured.setListPolicyCoverage(list);
            });
            /*查询回执日期*/
            PolicyReceiptInfoBo policyReceiptInfoBo = this.loadPolicyReceipt(policyId);
            if (!AssertUtils.isNotNull(policyReceiptInfoBo)) {
                policyReceiptInfoBo = new PolicyReceiptInfoBo();
            }
            policyBo.setPolicyReceiptInfo(policyReceiptInfoBo);
            return policyBo;
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        }
    }

    /**
     * 查询保单所属代理人
     *
     * @param policyId 保单ID
     * @return PolicyAgentBo
     */
    @Override
    public PolicyAgentBo loadPolicyAgentByPolicyId(String policyId) {
        PolicyAgentBo policyAgentBo;
        try {
            policyAgentBo = this.getDslContext()
                    .select(POLICY_AGENT.fields())
                    .from(POLICY_AGENT)
                    .where(POLICY_AGENT.POLICY_ID.eq(policyId))
                    .fetchOneInto(PolicyAgentBo.class);
            return policyAgentBo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_AGENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_AGENT_ERROR);
        }
    }


    /**
     * 查询保单所属代理人
     *
     * @param policyNo 保单号
     * @return PolicyPo
     */
    @Override
    public PolicyPo loadPolicyPoByPolicyNo(String policyNo) {
        PolicyPo policyPo = null;
        try {
            policyPo = this.getDslContext()
                    .select(POLICY.fields())
                    .from(POLICY)
                    .where(POLICY.POLICY_NO.eq(policyNo))
                    .fetchOneInto(PolicyPo.class);
            return policyPo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_AGENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_AGENT_ERROR);
        }
    }


    /**
     * 查询保单缴费信息
     *
     * @param policyId 保单ID
     * @return PolicyPremiumBo
     */
    @Override
    public PolicyPremiumBo loadPolicyPremiumByPolicyId(String policyId) {
        PolicyPremiumBo policyPremiumBo;
        try {
            policyPremiumBo = this.getDslContext()
                    .select(POLICY_PREMIUM.fields())
                    .from(POLICY_PREMIUM)
                    .where(POLICY_PREMIUM.POLICY_ID.eq(policyId))
                    .fetchOneInto(PolicyPremiumBo.class);
            /*查询保单付费信息*/
            PolicyPaymentBo policyPaymentBo = this.loadPolicyPaymentByPolicyId(policyId);
            policyPremiumBo.setPolicyPayment(policyPaymentBo);
            return policyPremiumBo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_PREMIUM_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_PREMIUM_ERROR);
        }
    }


    /**
     * 查询保单投保人
     *
     * @param policyId 保单ID
     * @return PolicyApplicantBo
     */
    @Override
    public PolicyApplicantBo loadPolicyApplicantByPolicyId(String policyId) {
        PolicyApplicantBo policyApplicantBo = null;
        try {
            policyApplicantBo = this.getDslContext()
                    .select(POLICY_APPLICANT.fields())
                    .from(POLICY_APPLICANT)
                    .where(POLICY_APPLICANT.POLICY_ID.eq(policyId))
                    .fetchOneInto(PolicyApplicantBo.class);
            return policyApplicantBo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_APPLICANT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_APPLICANT_ERROR);
        }
    }

    @Override
    public List<PolicyApplicantPo> loadPolicyApplicantByCustomerIds(List<String> customerIds) {
        List<PolicyApplicantPo> policyApplicantPos = new ArrayList<>();
        try {
            policyApplicantPos = this.getDslContext()
                    .select(POLICY_APPLICANT.fields())
                    .from(POLICY_APPLICANT)
                    .where(POLICY_APPLICANT.CUSTOMER_ID.in(customerIds))
                    .fetchInto(PolicyApplicantPo.class);
            return policyApplicantPos;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_APPLICANT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_APPLICANT_ERROR);
        }
    }

    /**
     * 查询保单投保人
     *
     * @param policyId 保单ID
     * @return PolicyApplicantBo
     */
    @Override
    public PolicyContactInfoBo loadPolicyContactByPolicyId(String policyId) {
        PolicyContactInfoBo policyContactInfoBo = null;
        try {
            policyContactInfoBo = this.getDslContext()
                    .select(POLICY_CONTACT_INFO.fields())
                    .from(POLICY_CONTACT_INFO)
                    .where(POLICY_CONTACT_INFO.POLICY_ID.eq(policyId))
                    .fetchOneInto(PolicyContactInfoBo.class);
            return policyContactInfoBo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_CONTACT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_CONTACT_ERROR);
        }
    }


    /**
     * 查询保单账户信息
     *
     * @param policyId 保单ID
     * @return List<PolicyInsuredBo>
     */
    @Override
    public List<PolicyAccountBo> loadPolicyAccountListByPolicyId(String policyId) {
        List<PolicyAccountBo> policyAccountBos = null;
        try {
            policyAccountBos = this.getDslContext()
                    .select(POLICY_ACCOUNT.fields())
                    .from(POLICY_ACCOUNT)
                    .where(POLICY_ACCOUNT.POLICY_ID.eq(policyId))
                    .fetchInto(PolicyAccountBo.class);
            return policyAccountBos;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ACCOUNT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ACCOUNT_ERROR);
        }
    }

    /**
     * 查询保单被保人
     *
     * @param policyId 保单ID
     * @return List<PolicyInsuredBo>
     */
    @Override
    public List<PolicyInsuredBo> loadPolicyInsuredListByPolicyId(String policyId) {
        List<PolicyInsuredBo> policyInsuredBos = null;
        try {
            policyInsuredBos = this.getDslContext()
                    .select(POLICY_INSURED.fields())
                    .from(POLICY_INSURED)
                    .where(POLICY_INSURED.POLICY_ID.eq(policyId))
                    .fetchInto(PolicyInsuredBo.class);
            return policyInsuredBos;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_INSURED_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_INSURED_ERROR);
        }
    }

    /**
     * 查询保单附件
     *
     * @param policyId 保单ID
     * @return List<PolicyInsuredBo>
     */
    @Override
    public List<PolicyAttachmentBo> loadPolicyAttachmentListByPolicyId(String policyId) {
        List<PolicyAttachmentBo> policyAttachmentBos = null;
        try {
            policyAttachmentBos = this.getDslContext()
                    .select(POLICY_ATTACHMENT.fields())
                    .from(POLICY_ATTACHMENT)
                    .where(POLICY_ATTACHMENT.POLICY_ID.eq(policyId))
                    .orderBy(POLICY_ATTACHMENT.ATTACHMENT_SEQ.asc())
                    .fetchInto(PolicyAttachmentBo.class);
            return policyAttachmentBos;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ATTACHMENT_ERROR);
        }
    }

    @Override
    public List<PolicyAttachmentBo> getPolicyReceiptImages(String policyId) {
        List<PolicyAttachmentBo> attachmentBoList = new ArrayList<>();
        try {
            attachmentBoList = this.getDslContext()
                    .select(POLICY_ATTACHMENT.fields())
                    .from(POLICY_ATTACHMENT)
                    .where(POLICY_ATTACHMENT.POLICY_ID.eq(policyId))
                    .and(POLICY_ATTACHMENT.ATTACHMENT_TYPE_CODE.eq(PolicyTermEnum.CERTIFY_ATTACHMENT_TYPE.RECEIPT_IMAGE.name()))
                    .orderBy(POLICY_ATTACHMENT.ATTACHMENT_SEQ.asc())
                    .fetchInto(PolicyAttachmentBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ATTACHMENT_ERROR);
        }
        return attachmentBoList;
    }

    /**
     * 查询保单支付信息
     *
     * @param policyId 保单ID
     * @return List<PolicyInsuredBo>
     */
    public List<PolicyCoveragePaymentBo> loadPolicyCoveragePaymentListByPolicyId(String policyId) {
        List<PolicyCoveragePaymentBo> policyCoverageBos = null;
        try {
            policyCoverageBos = this.getDslContext()
                    .select(POLICY_COVERAGE_PAYMENT.fields())
                    .select(POLICY_COVERAGE.INSURED_ID)
                    .from(POLICY_COVERAGE_PAYMENT)
                    .innerJoin(POLICY_COVERAGE).on(POLICY_COVERAGE_PAYMENT.COVERAGE_ID.eq(POLICY_COVERAGE.COVERAGE_ID))
                    .where(POLICY_COVERAGE_PAYMENT.POLICY_ID.eq(policyId))
                    .fetchInto(PolicyCoveragePaymentBo.class);
            return policyCoverageBos;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_COVERAGE_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_COVERAGE_ERROR);
        }
    }

    /**
     * 查询保单险种
     *
     * @param policyId 保单ID
     * @return List<PolicyInsuredBo>
     */
    @Override
    public List<PolicyCoverageBo> loadPolicyCoverageListByPolicyId(String policyId) {
        List<PolicyCoverageBo> policyCoverageBos;
        try {
            policyCoverageBos = this.getDslContext()
                    .select(POLICY_COVERAGE.fields())
                    .from(POLICY_COVERAGE)
                    .where(POLICY_COVERAGE.POLICY_ID.eq(policyId))
                    .and(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())).fetch().map(record -> {
                        PolicyCoverageBo policyCoverageBo = BasePojo.getInstance(PolicyCoverageBo.class, record.into(PolicyCoverageRecord.class));
                        //险种保费
                        PolicyCoveragePremiumBo policyCoveragePremiumBo = policyBaseDao.queryOnePolicyCoveragePremium(policyCoverageBo.getPolicyId(), policyCoverageBo.getCoverageId());
                        policyCoverageBo.setPolicyCoveragePremium(policyCoveragePremiumBo);
                        //险种缴费
                        List<PolicyCoveragePaymentBo> listPolicyCoveragePayment = policyBaseDao.queryPolicyCoveragePayment(policyCoverageBo.getPolicyId(), policyCoverageBo.getCoverageId());
                        policyCoverageBo.setListPolicyCoveragePayment(listPolicyCoveragePayment);
                        //险种责任
                        List<PolicyCoverageDutyBo> listPolicyCoverageDutyBo = policyBaseDao.queryPolicyCoverageDuty(policyCoverageBo.getPolicyId(), policyCoverageBo.getCoverageId());
                        policyCoverageBo.setListCoverageDuty(listPolicyCoverageDutyBo);
                        //险种红利
                        PolicyCoverageBonusBo policyCoverageBonusBo = policyBaseDao.queryOnePolicyCoverageBonus(policyCoverageBo.getPolicyId(), policyCoverageBo.getCoverageId());
                        policyCoverageBo.setPolicyCoverageBonus(policyCoverageBonusBo);
                        return policyCoverageBo;
                    });
            return policyCoverageBos;
        } catch (Exception e) {
            this.getLogger().info(ExceptionUtils.getFullStackTrace(e));
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_COVERAGE_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_COVERAGE_ERROR);
        }
    }

    /**
     * 查询保单受益人
     *
     * @param policyId 保单ID
     * @return List<PolicyInsuredBo>
     */
    @Override
    public List<PolicyBeneficiaryInfoBo> loadPolicyBeneficiaryListByPolicyId(String policyId) {
        List<PolicyBeneficiaryInfoBo> policyBeneficiaryBos = new ArrayList<>();
        try {
            policyBeneficiaryBos = this.getDslContext()
                    .select(POLICY_BENEFICIARY.fields())
                    .select(POLICY_BENEFICIARY_INFO.fields())
                    .from(POLICY_BENEFICIARY)
                    .innerJoin(POLICY_BENEFICIARY_INFO).on(POLICY_BENEFICIARY.BENEFICIARY_ID.eq(POLICY_BENEFICIARY_INFO.BENEFICIARY_ID))
                    .where(POLICY_BENEFICIARY.POLICY_ID.eq(policyId))
                    .orderBy(POLICY_BENEFICIARY_INFO.BENEFICIARY_NO_ORDER.sortAsc(Arrays.stream(PolicyTermEnum.BENEFICIARY_NO.values()).map(PolicyTermEnum.BENEFICIARY_NO::name).collect(Collectors.toList())))
                    .fetch().stream().map(record -> {
                        //受益人
                        PolicyBeneficiaryBo policyBeneficiaryBo = BasePojo.getInstance(PolicyBeneficiaryBo.class, record.into(PolicyBeneficiaryRecord.class));
                        PolicyBeneficiaryInfoBo policyBeneficiaryInfoBo = BasePojo.getInstance(PolicyBeneficiaryInfoBo.class, record.into(PolicyBeneficiaryInfoRecord.class));
                        policyBeneficiaryInfoBo.setPolicyBeneficiary(policyBeneficiaryBo);
                        return policyBeneficiaryInfoBo;
                    }).collect(Collectors.toList());
            return policyBeneficiaryBos;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_COVERAGE_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_COVERAGE_ERROR);
        }
    }


    @Override
    public PolicyExcludeBo loadPolicyExcludeById(String policyId, String policyPaymentId) {
        PolicyExcludeBo policyExcludeBo = new PolicyExcludeBo();
        policyExcludeBo = this.getDslContext().select(POLICY_EXCLUDE.fields())
                .from(POLICY_EXCLUDE)
                .where(POLICY_EXCLUDE.POLICY_ID.eq(policyId))
                .and(POLICY_EXCLUDE.POLICY_PAYMENT_ID.eq(policyPaymentId))
                .fetchOneInto(PolicyExcludeBo.class);
        return policyExcludeBo;
    }

    @Override
    public PolicyPaymentBo loadPolicyPayment(String policyPaymentId) {
        PolicyPaymentBo policyPaymentBo;
        policyPaymentBo = this.getDslContext().select(POLICY_PAYMENT.fields())
                .from(POLICY_PAYMENT)
                .where(POLICY_PAYMENT.POLICY_PAYMENT_ID.eq(policyPaymentId))
                .and(POLICY_PAYMENT.PAY_STATUS_CODE.eq(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name()))
                .fetchOneInto(PolicyPaymentBo.class);
        return policyPaymentBo;
    }

    @Override
    public PolicyPaymentBo loadPolicyPaymentByPolicyId(String policyId) {
        PolicyPaymentBo policyPaymentBo;
        policyPaymentBo = this.getDslContext().select(POLICY_PAYMENT.fields())
                .from(POLICY_PAYMENT)
                .where(POLICY_PAYMENT.POLICY_ID.eq(policyId))
                .orderBy(POLICY_PAYMENT.CREATED_DATE.asc())
                .limit(1)
                .fetchOneInto(PolicyPaymentBo.class);
        return policyPaymentBo;
    }

    @Override
    public PolicyBo loadPolicyBoByApplyId(String applyId) {
        PolicyBo policyBo;
        try {
            policyBo = this.getDslContext()
                    .select(POLICY.fields())
                    .from(POLICY)
                    .where(POLICY.APPLY_ID.eq(applyId))
                    .fetchOneInto(PolicyBo.class);
            return policyBo;
        } catch (Exception e) {
            this.getLogger().error(ExceptionUtils.getFullStackTrace(e));
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        }
    }

    /**
     * 查询保单列表
     *
     * @param request   请求对象
     * @param branchIds 机构IDs
     * @return list
     */
    @Override
    public List<PolicyQueryListBo> getPolicyList(PolicyQueryListRequest request, List<String> branchIds) {
        List<PolicyQueryListBo> policyQueryListBos;
        try {
            List<Condition> conditions = new ArrayList<>();
            if (AssertUtils.isNotEmpty(request.getPolicyStatus())) {
                conditions.add(POLICY.POLICY_STATUS.eq(request.getPolicyStatus()));
            }
            if (AssertUtils.isNotEmpty(request.getSalesBranchId())) {
                conditions.add(POLICY.SALES_BRANCH_ID.eq(request.getSalesBranchId()));
            } else if (AssertUtils.isNotEmpty(branchIds)) {
                conditions.add(POLICY.SALES_BRANCH_ID.in(branchIds));
            }
            if (AssertUtils.isNotEmpty(request.getKeyword())) {
                conditions.add(POLICY.POLICY_NO.like("%" + request.getKeyword() + "%"));
            }
            policyQueryListBos = this.getDslContext()
                    .select(POLICY.fields())
                    .select(POLICY_APPLICANT.NAME)
                    .select(POLICY_AGENT.AGENT_ID)
                    .select(POLICY_PREMIUM.PREM_DUE_DATE)
                    .select(POLICY_PREMIUM.PERIOD_TOTAL_PREMIUM)
                    .select(POLICY_RECEIPT_INFO.RETURN_DATE)
                    .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                    .from(POLICY)
                    .innerJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                    .innerJoin(POLICY_AGENT).on(POLICY.POLICY_ID.eq(POLICY_AGENT.POLICY_ID))
                    .innerJoin(POLICY_PREMIUM).on(POLICY.POLICY_ID.eq(POLICY_PREMIUM.POLICY_ID))
                    .leftJoin(POLICY_RECEIPT_INFO).on(POLICY.POLICY_ID.eq(POLICY_RECEIPT_INFO.POLICY_ID))
                    .where(conditions)
                    .orderBy(POLICY.APPROVE_DATE.asc())
                    .offset(request.getOffset())
                    .limit(request.getPageSize())
                    .fetchInto(PolicyQueryListBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        }
        return policyQueryListBos;
    }

    @Override
    public List<PolicyQueryListBo> getSystemWarningPolicyList(String applyId) {
        return this.getDslContext()
                .select(POLICY.POLICY_ID, POLICY.POLICY_ID.countOver().as("totalLine"), POLICY.POLICY_NO, POLICY.APPLY_ID, POLICY.APPLY_NO, POLICY.POLICY_STATUS)
                .from(POLICY)
                .where(POLICY.APPLY_ID.eq(applyId))
                .fetchInto(PolicyQueryListBo.class);
    }

    @Override
    public List<PolicyReceiptBo> loadPolicyReceiptList(List<String> applyIds, PolicyReceiptListRequest policyReceiptListRequest) {
        List<PolicyReceiptBo> policyReceiptBos = new ArrayList<>();
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(POLICY.APPLY_ID)
                    .select(POLICY.POLICY_ID)
                    .select(POLICY.APPLY_NO)
                    .select(POLICY.POLICY_NO)
                    .select(POLICY.EFFECTIVE_DATE)
                    .select(POLICY.APPROVE_DATE)
                    .select(POLICY.SALES_BRANCH_ID)
                    .select(POLICY.MANAGER_BRANCH_ID)
                    .select(POLICY.CHANNEL_TYPE_CODE)
                    .select(POLICY_AGENT.AGENT_ID)
                    .select(POLICY_AGENT.AGENT_CODE)
                    .select(POLICY_APPLICANT.NAME.as("applicantName"))
                    .select(POLICY_APPLICANT.ID_NO.as("applicantIdNo"))
                    .select(POLICY_APPLICANT.ID_TYPE.as("applicantIdType"))
                    .select(POLICY_APPLICANT.MOBILE.as("applicantMobile"))
                    .select(POLICY_RECEIPT_INFO.RECEIPT_STATUS)
                    .select(POLICY.APPLY_DATE)
                    .select(POLICY_APPLICANT.HOME_AREA_CODE.as("sendAddrAreaCode"))
                    .select(POLICY_APPLICANT.HOME_ADDRESS.as("contactAddress"))
                    .select(POLICY_COVERAGE.PRODUCT_ID)
                    .select(POLICY_COVERAGE.PRODUCT_NAME)
                    .select(POLICY_RECEIPT_INFO.RECEIPT_DATE)
                    .select(POLICY_RECEIPT_INFO.POLICY_ID.countOver().as("totalLine"))
                    .from(POLICY_RECEIPT_INFO)
                    .leftJoin(POLICY).on(POLICY.POLICY_ID.eq(POLICY_RECEIPT_INFO.POLICY_ID))
                    .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY_RECEIPT_INFO.POLICY_ID))
                    .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY_RECEIPT_INFO.POLICY_ID))
                    .leftJoin(POLICY_CONTACT_INFO).on(POLICY_CONTACT_INFO.POLICY_ID.eq(POLICY_RECEIPT_INFO.POLICY_ID))
                    .leftJoin(POLICY_COVERAGE)
                    .on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).and(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
            List<Condition> conditions = new ArrayList<>();
            conditions.add(POLICY.APPLY_ID.in(applyIds));

            conditions.add(POLICY.POLICY_STATUS.notIn(Arrays.asList(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_HESITATION_REVOKE.name()
                    , PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_SURRENDER.name())));
            if (AssertUtils.isNotEmpty(policyReceiptListRequest.getSalesBranchId())) {
                conditions.add(POLICY.SALES_BRANCH_ID.eq(policyReceiptListRequest.getSalesBranchId()));
            }
            //关键字：投保人姓名、投保单号或者保单号
            if (AssertUtils.isNotEmpty(policyReceiptListRequest.getKeyword())) {
                String keyword = policyReceiptListRequest.getKeyword();
                conditions.add(POLICY_APPLICANT.NAME.like("%" + keyword.trim() + "%")
                        .or(POLICY.APPLY_NO.like("%" + keyword.trim() + "%"))
                        .or(POLICY.POLICY_NO.like("%" + keyword.trim() + "%"))
                        .or(POLICY_AGENT.AGENT_CODE.like("%" + keyword.trim() + "%")));
            }
            selectJoinStep.where(conditions);
            //按照IDs排序
            selectJoinStep.orderBy(POLICY_RECEIPT_INFO.RECEIPT_STATUS.sortAsc("SALE_APP_UPLOADED"),POLICY.APPLY_ID.sortAsc(applyIds));
            Table table = selectJoinStep.asTable();
            //查询产品名称
            SelectJoinStep applyCoverage = this.getDslContext()
//                    .select(APPLY_COVERAGE.APPLY_ID)
                    .select(POLICY_COVERAGE.PRODUCT_NAME)
                    .from(POLICY_COVERAGE);

            List<Condition> coverageConditions = new ArrayList<Condition>();
            coverageConditions.add(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
            coverageConditions.add(POLICY_COVERAGE.POLICY_ID.eq(table.field(1)));

            applyCoverage.where(coverageConditions).limit(1);

            SelectJoinStep select = this.getDslContext()
                    .select(table.fields())
                    .select(applyCoverage.asField().as("productName"))
                    .from(table);

            select.offset(policyReceiptListRequest.getOffset()).limit(policyReceiptListRequest.getPageSize());
            System.out.println(select.toString());
            policyReceiptBos = select.fetchInto(PolicyReceiptBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        }
        return policyReceiptBos;
    }

    @Override
    public PolicyReceiptBo loadPolicyReceiptBo(String policyId) {
        return this.getDslContext()
                .select(POLICY.APPLY_ID, POLICY.POLICY_ID, POLICY.APPLY_NO, POLICY.POLICY_NO, POLICY.EFFECTIVE_DATE, POLICY.SALES_BRANCH_ID, POLICY.MANAGER_BRANCH_ID, POLICY.CHANNEL_TYPE_CODE, POLICY.APPLY_DATE)
                .select(POLICY_AGENT.AGENT_CODE, POLICY_AGENT.AGENT_ID)
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_APPLICANT.ID_NO.as("applicantIdNo"))
                .select(POLICY_APPLICANT.ID_TYPE.as("applicantIdType"))
                .select(POLICY_APPLICANT.MOBILE.as("applicantMobile"))
                .select(POLICY_RECEIPT_INFO.RECEIPT_STATUS, POLICY_RECEIPT_INFO.RECEIPT_RETURN_DATE, POLICY_RECEIPT_INFO.RECEIPT_DATE, POLICY_RECEIPT_INFO.RECEIPT_SUBMIT_DATE, POLICY_RECEIPT_INFO.RECEIPT_REVIEW_DECISION_REMARK)
                .select(POLICY_APPLICANT.HOME_AREA_CODE.as("sendAddrAreaCode"))
                .select(POLICY_APPLICANT.HOME_ADDRESS.as("contactAddress"))
                .select(POLICY_RECEIPT_INFO.POLICY_ID.countOver().as("totalLine"))
                .from(POLICY_RECEIPT_INFO)
                .leftJoin(POLICY).on(POLICY.POLICY_ID.eq(POLICY_RECEIPT_INFO.POLICY_ID))
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY_RECEIPT_INFO.POLICY_ID))
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY_RECEIPT_INFO.POLICY_ID))
                .leftJoin(POLICY_CONTACT_INFO).on(POLICY_CONTACT_INFO.POLICY_ID.eq(POLICY_RECEIPT_INFO.POLICY_ID)).where(POLICY.POLICY_ID.eq(policyId)).fetchOneInto(PolicyReceiptBo.class);
    }

    @Override
    public PolicyReceiptInfoBo loadPolicyReceipt(String policyId) {
        PolicyReceiptInfoBo policyReceiptInfoBo = new PolicyReceiptInfoBo();
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(POLICY_RECEIPT_INFO.fields())
                .from(POLICY_RECEIPT_INFO);
        selectJoinStep.where(POLICY_RECEIPT_INFO.POLICY_ID.eq(policyId)).and(POLICY_RECEIPT_INFO.RECEIPT_STATUS.eq(PolicyTermEnum.RECEIPT_STATUS.REVIEW_COMPLETE.name()));
        policyReceiptInfoBo = (PolicyReceiptInfoBo) selectJoinStep.fetchOneInto(PolicyReceiptInfoBo.class);
        return policyReceiptInfoBo;
    }

    @Override
    public PolicyReceiptInfoBo loadPolicyReceiptPo(String policyId) {
        PolicyReceiptInfoBo policyReceiptInfoBo = new PolicyReceiptInfoBo();
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(POLICY_RECEIPT_INFO.fields())
                .from(POLICY_RECEIPT_INFO);
        selectJoinStep.where(POLICY_RECEIPT_INFO.POLICY_ID.eq(policyId));
        policyReceiptInfoBo = (PolicyReceiptInfoBo) selectJoinStep.fetchOneInto(PolicyReceiptInfoBo.class);
        return policyReceiptInfoBo;
    }

    @Override
    public PolicyPrintInfoBo loadPolicyPrintInfoBo(String policyId) {
        PolicyPrintInfoBo policyPrintInfoBo = new PolicyPrintInfoBo();
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(POLICY_PRINT_INFO.fields())
                .from(POLICY_PRINT_INFO);
        selectJoinStep.where(POLICY_PRINT_INFO.POLICY_ID.eq(policyId)).limit(1);
        policyPrintInfoBo = (PolicyPrintInfoBo) selectJoinStep.fetchOneInto(PolicyPrintInfoBo.class);
        return policyPrintInfoBo;
    }

    @Override
    public String loadGeneratePolicyNumber() {
        String policyNumber = "";
        List<String> policyNumberList = this.getDslContext()
                .select(generatePolicyNumber())
                .fetch().map(record -> {
                    return (String) record.get("generate_policy_number");
                });
        if (AssertUtils.isNotEmpty(policyNumberList)) {
            policyNumber = policyNumberList.get(0);
        }
        return policyNumber;
    }

    /**
     * 查询保单附件
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public PolicyAttachmentPo getPolicyAttachment(String policyId, String language) {
        PolicyAttachmentPo policyAttachmentPo;
        try {
            SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                    .select(POLICY_ATTACHMENT.fields())
                    .from(POLICY_ATTACHMENT)
                    .where(POLICY_ATTACHMENT.POLICY_ID.eq(policyId))
                    .and(POLICY_ATTACHMENT.ATTACHMENT_TYPE_CODE.eq(PolicyTermEnum.ATTACHMENT_TYPE_FLAG.POLICY_BOOK.name()))
                    .and(POLICY_ATTACHMENT.LANGUAGE.eq(language));

            System.out.println(selectConditionStep.toString());
            policyAttachmentPo = selectConditionStep.fetchOneInto(PolicyAttachmentPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ATTACHMENT_ERROR);
        }
        return policyAttachmentPo;
    }

    /**
     * 获取保单附件
     *
     * @param policyAttachmentPo 附件对象
     * @return 附件对象
     */
    @Override
    public PolicyAttachmentPo getPolicyAttachment(PolicyAttachmentPo policyAttachmentPo) {
        PolicyAttachmentPo policyAttachment;
        try {
            policyAttachment = this.getDslContext()
                    .select(POLICY_ATTACHMENT.fields())
                    .from(POLICY_ATTACHMENT)
                    .where(POLICY_ATTACHMENT.POLICY_ID.eq(policyAttachmentPo.getPolicyId()))
                    .and(POLICY_ATTACHMENT.ATTACHMENT_TYPE_CODE.eq(policyAttachmentPo.getAttachmentTypeCode()))
                    .and(POLICY_ATTACHMENT.LANGUAGE.eq(policyAttachmentPo.getLanguage()))
                    .fetchOneInto(PolicyAttachmentPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_POLICY_PAYMENT_SAVE_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_POLICY_PAYMENT_SAVE_ERROR);
        }
        return policyAttachment;
    }

   /* @Override
    public PolicyCoiPrintInfoPo getPolicyCoiPrintInfo(PolicyCoiPrintInfoVo policyCoiPrintInfoVo) {
        PolicyCoiPrintInfoPo policyCoiPrintInfoPo;
        try {
            policyCoiPrintInfoPo = this.getDslContext()
                    .select(POLICY_COI_PRINT_INFO.fields())
                    .from(POLICY_COI_PRINT_INFO)
                    .where(POLICY_COI_PRINT_INFO.POLICY_ID.eq(policyCoiPrintInfoVo.getPolicyId()))
                    .and(POLICY_COI_PRINT_INFO.ATTACHMENT_SEQ.eq(policyCoiPrintInfoVo.getAttachmentSeq()))
                    .fetchOneInto(PolicyCoiPrintInfoPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_POLICY_PAYMENT_SAVE_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_POLICY_PAYMENT_SAVE_ERROR);
        }
        return policyCoiPrintInfoPo;
    }*/

    /**
     * 查询附件
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public PolicyAttachmentPo getPolicyAttachmentByType(String policyId, String type, String language) {
        PolicyAttachmentPo policyAttachmentPo;
        try {
            SelectJoinStep selectOnStep = this.getDslContext()
                    .select(POLICY_ATTACHMENT.fields())
                    .from(POLICY_ATTACHMENT);
            List<Condition> conditions = new ArrayList<>();
            conditions.add(POLICY_ATTACHMENT.POLICY_ID.eq(policyId));
            if (AssertUtils.isNotEmpty(type)) {
                conditions.add(POLICY_ATTACHMENT.ATTACHMENT_TYPE_CODE.eq(type));
            }
            if (AssertUtils.isNotEmpty(language)) {
                conditions.add(POLICY_ATTACHMENT.LANGUAGE.eq(language));
            }
            policyAttachmentPo = (PolicyAttachmentPo) selectOnStep.where(conditions).fetchOneInto(PolicyAttachmentPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ATTACHMENT_ERROR);
        }
        return policyAttachmentPo;
    }

    @Override
    public PolicyPaymentBusinessDataResponse queryOnePolicyPaymentBusinessData(String policyId) {

        Table<Record> groupMainCoverage = this.getDslContext()
                .selectDistinct(Tables.POLICY_COVERAGE.POLICY_ID)
                .select(Tables.POLICY_COVERAGE.POLICY_NO)
                .select(Tables.POLICY_COVERAGE.PRODUCT_ID)
                .select(Tables.POLICY_COVERAGE.PRIMARY_FLAG)
                .select(Tables.POLICY_COVERAGE.COVERAGE_STATUS)
                .select(Tables.POLICY_COVERAGE.EFFECTIVE_DATE)
                .select(Tables.POLICY_COVERAGE.COVERAGE_PERIOD_UNIT)
                .select(Tables.POLICY_COVERAGE.COVERAGE_PERIOD)
                .select(Tables.POLICY_COVERAGE.MATURITY_DATE)
                .select(Tables.POLICY_COVERAGE.PRODUCT_CODE)
                .select(Tables.POLICY_COVERAGE.PRODUCT_NAME)
                .select(Tables.POLICY_COVERAGE.PREMIUM_FREQUENCY)
                .select(Tables.POLICY_COVERAGE.PREMIUM_PERIOD_UNIT)
                .select(Tables.POLICY_COVERAGE.PREMIUM_PERIOD)
                .select(Tables.POLICY_COVERAGE.COVERAGE_PERIOD_START_DATE)
                .select(Tables.POLICY_COVERAGE.COVERAGE_PERIOD_END_DATE)
                .from(Tables.POLICY_COVERAGE)
                .where(Tables.POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()),
                        Tables.POLICY_COVERAGE.INSURED_ID.isNotNull(),
                        Tables.POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())).asTable();

        PolicyPaymentBusinessDataResponse policyPaymentBusinessDataResponse = this.getDslContext()
                .select(POLICY.POLICY_ID, POLICY.POLICY_NO, POLICY.POLICY_TYPE, POLICY.REFERRAL_CODE, POLICY.ONLINE_LANGUAGE)
                .select(POLICY_APPLICANT.MOBILE.as("applicantMobile"))
                .select(POLICY.POLICY_TYPE.decode(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name(), POLICY_APPLICANT.NAME, POLICY_APPLICANT.COMPANY_NAME).as("applicantName"))
                .select(POLICY_APPLICANT.DELEGATE_MOBILE)
                .select(POLICY_AGENT.AGENT_ID, POLICY_AGENT.AGENT_CODE)
                //.select(POLICY_AGENT.AGENT_ID.as("agentId"),POLICY_AGENT.AGENT_CODE.as("agentCode"))
                .select(groupMainCoverage.field(POLICY_COVERAGE.PRODUCT_ID), groupMainCoverage.field(POLICY_COVERAGE.PRODUCT_NAME), groupMainCoverage.field(POLICY_COVERAGE.PRODUCT_CODE),
                        groupMainCoverage.field(POLICY_COVERAGE.PREMIUM_FREQUENCY),
                        groupMainCoverage.field(POLICY_COVERAGE.PREMIUM_PERIOD),
                        groupMainCoverage.field(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT))
                .from(POLICY)
                .innerJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .innerJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .innerJoin(groupMainCoverage).on(POLICY.POLICY_ID.eq(groupMainCoverage.field(POLICY_COVERAGE.POLICY_ID)))
                .where(POLICY.POLICY_ID.eq(policyId)).fetchOneInto(PolicyPaymentBusinessDataResponse.class);
        //查询险种信息
        policyPaymentBusinessDataResponse.setCoverages(this.getDslContext().selectFrom(POLICY_COVERAGE).where(POLICY_COVERAGE.POLICY_ID.eq(policyId), POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())).orderBy(POLICY_COVERAGE.PRIMARY_FLAG.desc()).fetchInto(PolicyPaymentBusinessCoverageDataResponse.class));
        return policyPaymentBusinessDataResponse;
    }

    /**
     * 保单复核列表
     *
     * @param applyIds
     * @param policyQueryListRequest
     * @return
     */
    @Override
    public List<PolicyReviewBo> loadPolicyReviewList(List<String> applyIds, PolicyQueryListRequest policyQueryListRequest) {
        List<PolicyReviewBo> policyReviewBos = new ArrayList<>();
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(POLICY.APPLY_ID)
                .select(POLICY.POLICY_ID)
                .select(POLICY.APPLY_NO)
                .select(POLICY.POLICY_NO)
                .select(POLICY.EFFECTIVE_DATE)
                .select(POLICY.APPROVE_DATE)
                .select(POLICY.SALES_BRANCH_ID)
                .select(POLICY.MANAGER_BRANCH_ID)
                .select(POLICY.CHANNEL_TYPE_CODE)
                .select(POLICY_AGENT.AGENT_ID)
                .select(POLICY_AGENT.AGENT_CODE)
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_APPLICANT.ID_NO)
                .select(POLICY_APPLICANT.ID_TYPE)
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_PREMIUM.ACTUAL_PREMIUM.as("totalPremium"))
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                .from(POLICY)
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_PREMIUM).on(POLICY_PREMIUM.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_CONTACT_INFO).on(POLICY_CONTACT_INFO.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE)
                .on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).and(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));

        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.APPLY_ID.in(applyIds));
        conditions.add(POLICY.POLICY_STATUS.notIn(Arrays.asList(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_HESITATION_REVOKE.name()
                , PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_SURRENDER.name())));
        if (AssertUtils.isNotEmpty(policyQueryListRequest.getChannelTypeCode())) {
            conditions.add(POLICY.CHANNEL_TYPE_CODE.eq(policyQueryListRequest.getChannelTypeCode()));
        }
        //关键字：投保人姓名、投保单号或者保单号
        if (AssertUtils.isNotEmpty(policyQueryListRequest.getKeyword())) {
            String keyword = policyQueryListRequest.getKeyword();
            conditions.add(POLICY_APPLICANT.NAME.like("%" + keyword.trim() + "%")
                    .or(POLICY.APPLY_NO.like("%" + keyword.trim() + "%"))
                    .or(POLICY.POLICY_NO.like("%" + keyword.trim() + "%"))
                    .or(POLICY_AGENT.AGENT_CODE.like("%" + keyword.trim() + "%")));
        }
        selectJoinStep.where(conditions);
        //按照IDs排序
        selectJoinStep.orderBy(POLICY.APPLY_ID.sortAsc(applyIds));
        Table table = selectJoinStep.asTable();
        //查询产品名称
        SelectJoinStep applyCoverage = this.getDslContext()
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .from(POLICY_COVERAGE);

        List<Condition> coverageConditions = new ArrayList<Condition>();
        coverageConditions.add(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        coverageConditions.add(POLICY_COVERAGE.POLICY_ID.eq(table.field(1)));

        applyCoverage.where(coverageConditions).limit(1);

        SelectJoinStep select = this.getDslContext()
                .select(table.fields())
                .select(applyCoverage.asField().as("productName"))
                .from(table);

        select.offset(policyQueryListRequest.getOffset()).limit(policyQueryListRequest.getPageSize());
        System.out.println(select.toString());
        policyReviewBos = select.fetchInto(PolicyReviewBo.class);
        return policyReviewBos;
    }

    @Override
    public List<PolicyAttachmentPo> listPolicyAttachmentPo(PolicyAttachmentPo policyAttachmentPo) {
        SelectConditionStep<Record> conditionStep = getDslContext()
                .select(POLICY_ATTACHMENT.fields())
                .from(POLICY_ATTACHMENT)
                .where(POLICY_ATTACHMENT.POLICY_ID.eq(policyAttachmentPo.getPolicyId()))
                .and(POLICY_ATTACHMENT.ATTACHMENT_TYPE_CODE.eq(policyAttachmentPo.getAttachmentTypeCode()));
        if (AssertUtils.isNotEmpty(policyAttachmentPo.getLanguage())) {
            conditionStep.and(POLICY_ATTACHMENT.LANGUAGE.eq(policyAttachmentPo.getLanguage()));
        }
        return conditionStep.fetchInto(PolicyAttachmentPo.class);
    }

    @Override
    public PolicyApplicantBo querySuspectedCustomer(SuspectedCustomerRequest suspectedCustomerRequest) {
        Condition a1 = POLICY_APPLICANT.BIRTHDAY.eq(suspectedCustomerRequest.getBirthday()).and(POLICY_APPLICANT.SEX.ne(suspectedCustomerRequest.getSex()));
        Condition a2 = POLICY_APPLICANT.BIRTHDAY.ne(suspectedCustomerRequest.getBirthday()).and(POLICY_APPLICANT.SEX.eq(suspectedCustomerRequest.getSex()));
        Condition a3 = POLICY_APPLICANT.BIRTHDAY.ne(suspectedCustomerRequest.getBirthday()).and(POLICY_APPLICANT.SEX.ne(suspectedCustomerRequest.getSex()));
        Condition a4 = POLICY_APPLICANT.BIRTHDAY.eq(suspectedCustomerRequest.getBirthday()).and(POLICY_APPLICANT.SEX.eq(suspectedCustomerRequest.getSex()));
        Condition a = POLICY_APPLICANT.NAME.equalIgnoreCase(suspectedCustomerRequest.getName().trim()).and(POLICY_APPLICANT.ID_NO.eq(suspectedCustomerRequest.getIdNo()))
                .and(a1.or(a2).or(a3));
        Condition b = POLICY_APPLICANT.NAME.notEqualIgnoreCase(suspectedCustomerRequest.getName().trim()).and(POLICY_APPLICANT.ID_NO.eq(suspectedCustomerRequest.getIdNo()))
                .and(a1.or(a2).or(a3).or(a4));
        Condition c = POLICY_APPLICANT.NAME.equalIgnoreCase(suspectedCustomerRequest.getName().trim()).and(POLICY_APPLICANT.ID_NO.ne(suspectedCustomerRequest.getIdNo()))
                .and(a4);
        Condition d = POLICY_APPLICANT.NAME.notEqualIgnoreCase(suspectedCustomerRequest.getName().trim()).and(POLICY_APPLICANT.ID_NO.eq(suspectedCustomerRequest.getIdNo()))
                .and(a4);

        SelectOnConditionStep<Record> form = this.getDslContext()
                .select(POLICY_APPLICANT.fields())
                .from(POLICY_APPLICANT)
                .leftJoin(POLICY_COVERAGE).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID));

        form.where(a.or(b).or(c).or(d)).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()).and(POLICY_COVERAGE.PRODUCT_ID.eq(ProductTermEnum.PRODUCT.PRODUCT_20A.id()))).limit(1);
        return form.fetchOneInto(PolicyApplicantBo.class);
    }

    @Override
    public PolicyApplicantBo queryApprovedAgainCustomer(SuspectedCustomerRequest suspectedCustomerRequest) {
        PolicyApplicantBo policyApplicantBo = null;
        policyApplicantBo = this.getDslContext()
                .select(POLICY_APPLICANT.fields())
                .from(POLICY_APPLICANT)
                .leftJoin(POLICY_COVERAGE).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID))
                .where(POLICY_APPLICANT.NAME.eq(suspectedCustomerRequest.getName())
                        .and(POLICY_APPLICANT.SEX.eq(suspectedCustomerRequest.getSex()))
                        .and(POLICY_APPLICANT.BIRTHDAY.eq(suspectedCustomerRequest.getBirthday()))
                        .and(POLICY_APPLICANT.ID_NO.eq(suspectedCustomerRequest.getIdNo()))
                        .and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                        .and(POLICY_COVERAGE.PRODUCT_ID.eq(ProductTermEnum.PRODUCT.PRODUCT_20A.id()))).fetchOneInto(PolicyApplicantBo.class);
        return policyApplicantBo;
    }

    @Override
    public List<PolicyPremiumPo> loadPolicyPremiumByAgentId(Users users, String agentId, String approveDate) {

        SelectOnConditionStep<Record2<String, BigDecimal>> on = this.getDslContext()
                .select(POLICY_PREMIUM.POLICY_ID, POLICY_PREMIUM.ACTUAL_PREMIUM).from(POLICY)
                .innerJoin(POLICY_AGENT)
                .on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .innerJoin(POLICY_PREMIUM)
                .on(POLICY_PREMIUM.POLICY_ID.eq(POLICY.POLICY_ID));

        List<Condition> conditions = new ArrayList<Condition>();

        if (AssertUtils.isNotEmpty(approveDate)) {
            long minTime = DateUtils.getThisMonthFirstDay(approveDate, "MM/yyyy");
            long maxTime = DateUtils.getThisMonthLastDay(approveDate, "MM/yyyy");
            conditions.add(POLICY.APPROVE_DATE.between(minTime,maxTime));
        }

        conditions.add(POLICY_AGENT.AGENT_ID.eq(agentId));
        SelectConditionStep<Record2<String, BigDecimal>> where = on.where(conditions);
        this.getLogger().info("sql" + where.toString());

        return where.fetchInto(PolicyPremiumPo.class);
    }

    /**
     * 获取保单存在客户列表
     *
     * @param allCustomerIds 客户ID
     * @return PolicyRealClientListResponses
     */
    @Override
    public List<PolicyRealClientListResponse> getPolicyRealClient(List<String> allCustomerIds) {
        SelectOnConditionStep on = this.getDslContext()
                .selectDistinct(POLICY.APPLY_ID, POLICY.APPLY_NO, POLICY.POLICY_ID, POLICY.POLICY_NO, POLICY.POLICY_STATUS, POLICY.POLICY_TYPE, POLICY.CREATED_DATE)
                .select(POLICY_APPLICANT.NAME, POLICY_APPLICANT.ID_TYPE, POLICY_APPLICANT.ID_NO, POLICY_APPLICANT.BIRTHDAY, POLICY_APPLICANT.SEX)
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .from(POLICY)
                .leftJoin(POLICY_COVERAGE).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID), POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                .leftJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .leftJoin(POLICY_INSURED).on(POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID))
//                .leftJoin(POLICY_BENEFICIARY).on(POLICY.POLICY_ID.eq(POLICY_BENEFICIARY.POLICY_ID))
                ;
        on.where(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        on.where(POLICY_APPLICANT.CUSTOMER_ID.in(allCustomerIds)
                .or(POLICY_INSURED.CUSTOMER_ID.in(allCustomerIds))
//                .or(POLICY_BENEFICIARY.CUSTOMER_ID.in(allCustomerIds))
        );
        on.orderBy(POLICY.CREATED_DATE, POLICY.POLICY_ID);
        System.out.println(on.toString());
        return on.fetchInto(PolicyRealClientListResponse.class);
    }


    /**
     * 查询保单对象
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public PolicyPo getPolicyPoByPolicyId(String policyId) {
        PolicyPo policyPo;
        try {
            policyPo = this.getDslContext()
                    .select(POLICY.fields())
                    .from(POLICY)
                    .where(POLICY.POLICY_ID.eq(policyId))
                    .fetchOneInto(PolicyPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        }
        return policyPo;
    }
}