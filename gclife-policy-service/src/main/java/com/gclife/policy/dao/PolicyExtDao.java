package com.gclife.policy.dao;

import com.gclife.common.dao.base.BaseDao;
import com.gclife.common.model.base.Users;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.request.PolicyQueryListRequest;
import com.gclife.policy.model.request.PolicyReceiptListRequest;
import com.gclife.policy.model.request.SuspectedCustomerRequest;
import com.gclife.policy.model.response.PolicyPaymentBusinessDataResponse;
import com.gclife.policy.model.response.PolicyRealClientListResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-21
 * description:
 */
public interface PolicyExtDao extends BaseDao {

    /**
     * 查询保单对象
     *
     * @param policyId 　保单ID
     * @return PolicyBo
     */
    public PolicyBo loadPolicyByPolicyId(String policyId);

    /**
     * 查询保单对象
     *
     * @param policyNo 　保单ID
     * @return PolicyBo
     */
    public PolicyPo loadPolicyPoByPolicyNo(String policyNo);

    /**
     * 保单对象
     *
     * @param policyId 　保单ID
     * @return PolicyBo
     */
    public PolicyBo loadSimplePolicyByPolicyId(String policyId);


    /**
     * 查询保单所属代理人
     *
     * @param policyId 保单ID
     * @return PolicyAgentBo
     */
    public PolicyAgentBo loadPolicyAgentByPolicyId(String policyId);


    /**
     * 查询保单投保人
     *
     * @param policyId 保单ID
     * @return PolicyApplicantBo
     */
    public PolicyApplicantBo loadPolicyApplicantByPolicyId(String policyId);

    /**
     * 根据customerAgentIds查询保单投保人
     *
     * @param customerIds 客户ids
     * @return List<PolicyApplicantPo>
     */
    List<PolicyApplicantPo> loadPolicyApplicantByCustomerIds(List<String> customerIds);

    /**
     * 查询保单联系信息
     *
     * @param policyId 　保单ID
     * @return PolicyContactInfoBo
     */
    public PolicyContactInfoBo loadPolicyContactByPolicyId(String policyId);

    /**
     * 查询保单账户信息
     *
     * @param policyId 　保单ID
     * @return List<PolicyAccountBo>
     */
    public List<PolicyAccountBo> loadPolicyAccountListByPolicyId(String policyId);

    /**
     * 查询保单被保人
     *
     * @param policyId 保单ID
     * @return List<PolicyInsuredBo>
     */
    public List<PolicyInsuredBo> loadPolicyInsuredListByPolicyId(String policyId);


    /**
     * 查询保单附件
     *
     * @param policyId 保单ID
     * @return List<PolicyInsuredBo>
     */
    public List<PolicyAttachmentBo> loadPolicyAttachmentListByPolicyId(String policyId);

    /**
     * 查询回执影像附件
     *
     * @param policyId
     * @return
     */
    List<PolicyAttachmentBo> getPolicyReceiptImages(String policyId);

    /**
     * 查询保单险种责任
     *
     * @param policyId 保单ID
     * @return List<PolicyInsuredBo>
     */
    public List<PolicyCoverageBo> loadPolicyCoverageListByPolicyId(String policyId);


    /**
     * 查询保单受益人
     *
     * @param policyId 保单ID
     * @return List<PolicyBeneficiaryInfoBo>
     */
    public List<PolicyBeneficiaryInfoBo> loadPolicyBeneficiaryListByPolicyId(String policyId);


    /**
     * 查询排除表信息
     *
     * @param policyId
     * @param policyPaymentId
     * @return
     */
    public PolicyExcludeBo loadPolicyExcludeById(String policyId, String policyPaymentId);

    /**
     * 查询保单支付信息
     *
     * @param policyPaymentId
     * @return
     */
    public PolicyPaymentBo loadPolicyPayment(String policyPaymentId);

    /**
     * 通过applyId查询保单
     *
     * @param applyId 投保单ID
     * @return PolicyBo
     */
    public PolicyBo loadPolicyBoByApplyId(String applyId);

    /**
     * 查询保单列表
     *
     * @param request   请求对象
     * @param branchIds 机构IDs
     * @return list
     */
    public List<PolicyQueryListBo> getPolicyList(PolicyQueryListRequest request, List<String> branchIds);

    /**
     * 系统预警查询已承保的保单列表
     * @return list
     */
    public List<PolicyQueryListBo> getSystemWarningPolicyList(String applyId);

    /**
     * 查询保单回执列表
     *
     * @param applyIds                 投保单ID
     * @param policyReceiptListRequest 　请求参数
     * @return list
     */
    public List<PolicyReceiptBo> loadPolicyReceiptList(List<String> applyIds, PolicyReceiptListRequest policyReceiptListRequest);

    /**
     * 回执详情
     *
     * @param policyId
     * @return
     */
    PolicyReceiptBo loadPolicyReceiptBo(String policyId);

    /**
     * 查询保单回执信息
     *
     * @param policyId 保单ID
     * @return
     */
    public PolicyReceiptInfoBo loadPolicyReceipt(String policyId);


    /**
     * 查询保单回执信息
     *
     * @param policyId 保单ID
     * @return
     */
    public PolicyReceiptInfoBo loadPolicyReceiptPo(String policyId);


    /**
     * 保单打印信息
     * @param policyId 保单ID
     * @return
     */
    PolicyPrintInfoBo loadPolicyPrintInfoBo(String policyId);

    /**
     * 保单号
     *
     * @return String
     */
    public String loadGeneratePolicyNumber();

    /**
     * 获取支付
     *
     * @param policyId 保单ID
     * @return PolicyPaymentBo
     */
    PolicyPaymentBo loadPolicyPaymentByPolicyId(String policyId);

    /**
     * 查询保单附件
     *
     * @param policyId 保单ID
     * @return
     */
    PolicyAttachmentPo getPolicyAttachment(String policyId, String language);

    /**
     * 获取保单附件
     *
     * @param policyAttachmentPo 附件对象
     * @return 附件对象
     */
    PolicyAttachmentPo getPolicyAttachment(PolicyAttachmentPo policyAttachmentPo);

    /**
     * 获取保单coi打印
     *
     * @param policyCoiPrintInfoVo 附件对象
     * @return 附件对象
     *//*
    PolicyCoiPrintInfoPo getPolicyCoiPrintInfo(PolicyCoiPrintInfoVo policyCoiPrintInfoVo);*/


    /**
     * 查询保单缴费信息
     *
     * @param policyId 保单ID
     * @return PolicyPremiumBo
     */
    PolicyPremiumBo loadPolicyPremiumByPolicyId(String policyId);

    /**
     * 查询保单对象
     *
     * @param policyId 保单ID
     * @return
     */
    PolicyPo getPolicyPoByPolicyId(String policyId);

    /**
     * 查询保单附件
     *
     * @param policyId 保单ID
     * @param type     附件类型
     * @param language 附件语言
     * @return PolicyAttachmentPo
     */
    PolicyAttachmentPo getPolicyAttachmentByType(String policyId, String type, String language);


    /**
     * 查询保单支付冗余业务数据
     * @param policyId 保单ID
     * @return ResultObject
     */
    PolicyPaymentBusinessDataResponse queryOnePolicyPaymentBusinessData(String policyId);

    /**
     * 保单复核列表
     *
     * @param applyIds
     * @param policyQueryListRequest
     * @return
     */
    List<PolicyReviewBo> loadPolicyReviewList(List<String> applyIds, PolicyQueryListRequest policyQueryListRequest);

    /**
     * 获取保单附件集合
     *
     * @param policyAttachmentPo
     * @return
     */
    List<PolicyAttachmentPo> listPolicyAttachmentPo(PolicyAttachmentPo policyAttachmentPo);

    /**
     * 查询疑似客户
     */
    PolicyApplicantBo querySuspectedCustomer(SuspectedCustomerRequest suspectedCustomerRequest);

    /**
     * 查询20号产品再次投保客户
     */
    PolicyApplicantBo queryApprovedAgainCustomer(SuspectedCustomerRequest suspectedCustomerRequest);
    /**
     * 查询保单缴费信息
     *
     * @param agentId 业务员ID
     * @return PolicyPremiumPo
     */
    List<PolicyPremiumPo> loadPolicyPremiumByAgentId(Users users, String agentId, String approveDate);

    /**
     * 获取保单存在客户列表
     *
     * @param allCustomerIds 客户ID
     * @return PolicyRealClientListResponses
     */
    List<PolicyRealClientListResponse> getPolicyRealClient(List<String> allCustomerIds);
}