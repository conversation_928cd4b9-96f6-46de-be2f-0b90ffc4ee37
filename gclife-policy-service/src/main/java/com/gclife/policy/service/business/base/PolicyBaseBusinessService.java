package com.gclife.policy.service.business.base;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.request.*;
import com.gclife.policy.model.request.ClientServiceAgentChangeRequest;
import com.gclife.policy.model.request.CoverageUpdateRequest;
import com.gclife.policy.model.request.PolicyPaymentUpdateRequest;
import com.gclife.policy.model.request.PolicyPremiumUpdateRequest;
import com.gclife.policy.model.request.PolicyUpdateRequest;
import com.gclife.policy.model.response.PolicyAgentHistoryResponse;
import com.gclife.policy.model.response.PolicyPartInfoResponse;
import com.gclife.policy.model.response.PolicyPaymentBusinessDataResponse;
import com.gclife.policy.model.response.PolicyResponse;
import com.gclife.policy.model.response.group.GroupPolicyResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-6-27
 * description:
 */
public interface PolicyBaseBusinessService extends BaseBusinessService {

    /**
     * 根据代理人ID集合查询其名下的所有保单数
     *
     * @param agentIds 代理人ID集合
     * @return ResultObject<List<PolicyResponse>>
     */
    ResultObject<List<PolicyResponse>> queryPolicyBaseList(List<String> agentIds);

    /**
     * 根据保单ID查询历史代理人最近一位
     *
     * @param policyId 保单ID
     * @return ResultObject<PolicyAgentHistoryResponse>
     */
    ResultObject<PolicyAgentHistoryResponse> queryPolicyAgentHistory(AppRequestHeads appRequestHeads, String policyId);

    /**
     * 根据保单ID查询保单基本数据
     *
     * @param policyId 保单ID
     * @return ResultObject<PolicyPo>
     */
    ResultObject<PolicyPo> queryOnePolicy(String policyId);

    /**
     * 根据保单ID查询初始保单
     * @param policyId 保单ID
     * @return
     */
    ResultObject<PolicyPo> queryInitialPolicy(String policyId);

    /**
     * 查询保单代理人数据
     *
     * @param policyId 保单ID
     * @return
     */
    ResultObject<PolicyAgentPo> queryOnePolicyAgent(String policyId);

    /**
     * 查询投保人信息
     *
     * @param policyId 保单ID
     * @return PolicyApplicantBo
     */
    ResultObject<PolicyApplicantBo> queryOnePolicyApplicant(String policyId);


    /**
     * 根据投保单ID查询被保人清单
     *
     * @param policyId 保单ID
     * @return list
     */
    ResultObject<List<PolicyInsuredBo>> queryPolicyInsured(String policyId);


    /**
     * 查询最新一期缴费信息
     *
     * @param policyId 保单ID
     * @return PolicyPaymentPo
     */
    ResultObject<PolicyPaymentBo> queryOneNewPolicyPayment(String policyId);

    /**
     * 更新保单代理人
     *
     * @param policyId  保单ID
     * @param agentId   代理人ID
     * @param branchFlag
     * @return
     */
    ResultObject updatePolicyAgent(String policyId, String agentId, boolean branchFlag);
    /**
     *查询保单下所有的缴费信息
     *
     * @param policyId 保单ID
     * @return PolicyPaymentBos
     */
    ResultObject<List<PolicyPaymentBo>> queryPolicyPayments(String policyId);

    /**
     * 查询保单下所有的缴费信息
     * @param policyId 保单ID
     * @return PolicyPaymentBos
     */
    ResultObject<List<PolicyPaymentBo>> listPolicyPayment(String policyId);

    /**
     * 查询客户下所有的缴费信息
     * @param customerIds 客户
     * @return PolicyPaymentBos
     */
    ResultObject<List<PolicyPaymentBo>> listCustomerPolicyPayment(List<String> customerIds);

    /**
     * 更新保单缴费信息
     * @param policyPaymentUpdateRequest
     * @return ResultObject
     */
    ResultObject updatePolicyPayment(PolicyPaymentUpdateRequest policyPaymentUpdateRequest);

    /**
     * 回滚保单缴费信息
     * @param policyPaymentPos 保单缴费信息
     * @return
     */
    ResultObject rollbackPolicyPayment(List<PolicyPaymentPo> policyPaymentPos);

    /**
     * 更新险种状态
     * @param coverageUpdateRequest 待更新险种信息
     * @param users 用户
     * @return
     */
    ResultObject updateCoverage(CoverageUpdateRequest coverageUpdateRequest, Users users);

    /**
     * 更新保单状态
     * @param policyUpdateRequest 待更新保单信息
     * @return
     */
    ResultObject updatePolicy(PolicyUpdateRequest policyUpdateRequest);

    /**
     * 查询主险产品信息
     * @param policyId 保单ID
     * @return ResultObject<PolicyCoverageBo>
     */
    ResultObject<PolicyCoveragePo> queryOneMainPolicyCoverage(String policyId);

    /**
     * 查询保单保费缴费信息
     * @param policyId 保单ID
     * @return ResultObject<PolicyPremiumBo>
     */
    ResultObject<PolicyPremiumBo> queryPolicyPremium(String policyId);

    /**
     * 修改保单保费状态
     * @param policyPremiumUpdateRequest 保费信息请求
     * @return ResultObject
     */
    ResultObject updatePolicyPremium(PolicyPremiumUpdateRequest policyPremiumUpdateRequest);

    /**
     * 查询待生效险种扩展信息
     * @param policyId 保单ID
     * @return
     */
    ResultObject<List<PolicyCoverageExtendPo>> listPendingCoverageExtend(String policyId);

    /**
     * 更新保单操作
     * @param policyId 保单ID
     * @param operationCode 操作编码
     * @return
     */
    ResultObject updatePolicyOperation(String policyId, String operationCode);

    /**
     * 失效保单操作
     * @param policyId 保单ID
     * @return
     */
    ResultObject invalidPolicyOperation(String policyId);

    /**
     * 查询保单险种信息
     * @param policyId
     * @return
     */
    ResultObject<List<PolicyCoveragePo>> queryPolicyCoverage(String policyId);

    /**
     * 根据险种ID查询险种扩展信息
     * @param coverageIds 险种id
     * @return
     */
    ResultObject<List<PolicyCoverageExtendPo>> listCoverageExtendByCoverageId(List<String> coverageIds);
    /*
     * 查询保单支付冗余业务数据
     * @param policyId 保单ID
     * @return ResultObject
     */
    ResultObject<PolicyPaymentBusinessDataResponse> queryOnePolicyPaymentBusinessData(String policyId);

    /**
     * 查询保单操作
     * @param policyId 保单ID
     * @return BaseOperationPo
     */
    ResultObject<BaseOperationPo> queryPolicyOperation(String policyId);

    /**
     * 根据保单ID查询部分保单信息(包含投保人、被保人及被保人险种、缴费信息、保费信息)
     * @param policyId 保单ID
     * @return
     */
    ResultObject<PolicyPartInfoResponse> queryPolicyPartInfo(String policyId);

    /**
     * 查询团险保单信息
     * @param policyId 保单ID
     * @return
     */
    ResultObject<GroupPolicyResponse> queryGroupPolicyInfo(String policyId);

    /**
     * 根据保单ID和数据生效日期查询保单详细信息
     * @param policyId 保单ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    ResultObject queryPolicyDetailInfo(String policyId, Long dataEffectiveDate);


    /**
     * 证件号码查询被保人保单保额
     * @param idNo
     * @return
     */
    ResultObject<List<PolicyCoverageBo>> queryIdNoByAmount(String idNo);

    /**
     * 模糊查询投保人
     * @param keyword 关键字
     * @param applicantType 投保人类型
     * @return
     */
    ResultObject<List<PolicyApplicantBo>> listFuzzyPolicyApplicant(String keyword, String applicantType);

    /**
     * 根据险种ID查询险种信息
     * @param coverageIdList
     * @return
     */
    ResultObject<List<PolicyCoverageBo>> queryPolicyCoverageByIdList(List<String> coverageIdList);

    /**
     * 客户ID查询被保人保单保额
     * @param customerAgentId
     * @return
     */
    ResultObject<List<PolicyCoverageBo>> queryAmountByCustomerId(String customerAgentId);

    /**
     * 根据关键字查询保单号集合
     * @param keyWord
     * @return
     */
    ResultObject<List<String>> queryPolicyNos(String keyWord);

    /**
     * 17号产品暂予承保修改保单状态
     *
     * @param applyId 投保单ID
     * @return
     */
    ResultObject updatePrePolicyStatus(String applyId,String flag);

    /**
     * 变更该客户作为投保人下的所有保单的服务业务人员
     * @param clientServiceAgentChangeRequest
     * @param users
     * @return
     */
    ResultObject<Void> postClientServiceAgentChange(ClientServiceAgentChangeRequest clientServiceAgentChangeRequest, Users users);
}