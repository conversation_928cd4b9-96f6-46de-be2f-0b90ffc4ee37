package com.gclife.policy.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.api.BaseApplyApi;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.message.api.MessageMailApi;
import com.gclife.common.util.*;
import com.gclife.party.api.CustomerBaseApi;
import com.gclife.party.api.CustomerManageApi;
import com.gclife.party.model.request.UserCustomerBusinessRequest;
import com.gclife.party.model.response.CustomerMessageResponse;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformEmployeeApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.EmployeResponse;
import com.gclife.policy.common.AsyncUtil;
import com.gclife.policy.core.jooq.tables.daos.PolicyReviewDecisionDao;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCustomerMergerItemPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyReviewDecisionPo;
import com.gclife.policy.dao.PolicyExtDao;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.config.PolicyWorkflowTermEnum;
import com.gclife.policy.model.request.PolicyQueryListRequest;
import com.gclife.policy.model.request.review.PolicyCustomerMergerDetailRequest;
import com.gclife.policy.model.request.review.PolicyCustomerMergerRequest;
import com.gclife.policy.model.request.review.PolicyReviewSaveRequest;
import com.gclife.policy.model.response.review.PolicyCustomerMergerResponse;
import com.gclife.policy.model.response.review.PolicyReviewDecisionResponse;
import com.gclife.policy.model.response.review.PolicyReviewListResponse;
import com.gclife.policy.model.vo.PolicyVo;
import com.gclife.policy.service.base.PolicyBaseService;
import com.gclife.policy.service.base.PolicyReviewBaseService;
import com.gclife.policy.service.business.MessageBusinessService;
import com.gclife.policy.service.business.PolicyReviewBusinessService;
import com.gclife.policy.validate.parameter.PolicyReviewParameterValidate;
import com.gclife.policy.validate.transfer.PolicyReviewTransData;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.config.ProcessParam;
import com.gclife.workflow.model.request.CompleteTaskParam;
import com.gclife.workflow.model.request.TerminationTaskRequest;
import com.gclife.workflow.model.request.WaitingTaskRequest;
import com.gclife.workflow.model.response.WorkItemResponse;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 2021/6/11 11:40
 * description:
 */
@Slf4j
@Service
public class PolicyReviewBusinessServiceImpl extends BaseBusinessServiceImpl implements PolicyReviewBusinessService {

    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PolicyExtDao policyExtDao;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private PlatformEmployeeApi platformEmployeeApi;
    @Autowired
    private PolicyBaseService policyBaseService;
    @Autowired
    private PolicyReviewBaseService policyReviewBaseService;
    @Autowired
    private BaseApplyApi baseApplyApi;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private CustomerManageApi customerManageApi;
    @Autowired
    private CustomerBaseApi customerBaseApi;
    @Autowired
    private PolicyReviewTransData policyReviewTransData;
    @Autowired
    private PolicyReviewParameterValidate policyReviewParameterValidate;
    @Autowired
    private AsyncUtil asyncUtil;


    /**
     * 保单复核列表
     *
     * @param policyQueryListRequest 列表参数
     * @param users                  用户
     * @return 列表
     */
    @Override
    public ResultObject<BasePageResponse<PolicyReviewListResponse>> queryPolicyReviewList(PolicyQueryListRequest policyQueryListRequest, Users users) {
        ResultObject<BasePageResponse<PolicyReviewListResponse>> resultObject = new ResultObject<>();
        //工作流请求参数
        WaitingTaskRequest tasksReqFc = new WaitingTaskRequest();
        tasksReqFc.setUserId(users.getUserId());
        tasksReqFc.setWorkflowItemType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.POLICY_REVIEW_TASK.name());
        tasksReqFc.setWorkflowType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.NEW_CONTRACT.name());
        ResultObject<List<WorkItemResponse>> resultObject1 = workFlowApi.queryWaitingTasks(tasksReqFc);
        AssertUtils.isResultObjectError(log, resultObject1);
        //获取流程中的数据
        List<WorkItemResponse> listWorkflowTaskResponse = resultObject1.getData();
        List<String> applyIds = new ArrayList<>();
        if (AssertUtils.isNotEmpty(listWorkflowTaskResponse)) {
            applyIds = listWorkflowTaskResponse.stream().map(WorkItemResponse::getBusinessId).distinct().collect(Collectors.toList());
        }
        //筛选的ID集合为空时,return
        if (!AssertUtils.isNotEmpty(applyIds)) {
            return resultObject;
        }
        //查询数据
        List<PolicyReviewBo> policyReviewBos = policyExtDao.loadPolicyReviewList(applyIds, policyQueryListRequest);
        if (!AssertUtils.isNotEmpty(policyReviewBos)) {
            return resultObject;
        }

        List<String> allBranchIds = new ArrayList<>();
        policyReviewBos.forEach(policyReviewBo -> {
            allBranchIds.add(policyReviewBo.getManagerBranchId());
            allBranchIds.add(policyReviewBo.getSalesBranchId());
        });

        List<BranchResponse> branchResponses = platformBranchApi.branchsPost(allBranchIds).getData();

        List<String> agentIds = policyReviewBos.stream().map(PolicyReviewBo::getAgentId).distinct().collect(Collectors.toList());
        AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
        applyAgentRequest.setListAgentId(agentIds);

        List<AgentResponse> agentResponses = agentApi.agentsGet(applyAgentRequest).getData();
        AssertUtils.isNotEmpty(log, agentResponses, PolicyErrorConfigEnum.POLICY_AGENT_IS_NOT_FOUND_OBJECT);

        List<PolicyReviewListResponse> policyReviewListResponses = (List<PolicyReviewListResponse>) this.converterList(policyReviewBos, new TypeToken<List<PolicyReviewListResponse>>() {
        }.getType());

        for (PolicyReviewListResponse policyReviewListResponse : policyReviewListResponses) {
            //设置机构名称
            if (AssertUtils.isNotEmpty(branchResponses)) {
                branchResponses.forEach(branchResponse -> {
                    if (branchResponse.getBranchId().equals(policyReviewListResponse.getManagerBranchId())) {
                        policyReviewListResponse.setManagerBranchName(branchResponse.getBranchName());
                    }
                    if (branchResponse.getBranchId().equals(policyReviewListResponse.getSalesBranchId())) {
                        policyReviewListResponse.setSalesBranchName(branchResponse.getBranchName());
                    }
                });
            }

            agentResponses.stream().filter(applyAgentRespFc -> policyReviewListResponse.getAgentId().equals(applyAgentRespFc.getAgentId())).findFirst().ifPresent((agent) -> {
                policyReviewListResponse.setAgentName(agent.getAgentName());
                policyReviewListResponse.setAgentIdNo(agent.getIdNo());
                policyReviewListResponse.setAgentMobile(agent.getMobile());
            });
            //国际化回执状态
            listWorkflowTaskResponse.stream().filter(workItemResponse -> policyReviewListResponse.getApplyId().equals(workItemResponse.getBusinessId())).findFirst().ifPresent(workItemResponse -> {
                policyReviewListResponse.setReviewStatus(workItemResponse.getWorkItemStatus());
            });
            listWorkflowTaskResponse.stream().filter(workflowTaskRespFc -> workflowTaskRespFc.getBusinessId().equals(policyReviewListResponse.getApplyId())).findFirst().ifPresent((value2) -> {
                if (AssertUtils.isNotEmpty(value2.getCreateUserId())) {
                    EmployeResponse employeResponse = platformEmployeeApi.employeGet(value2.getCreateUserId()).getData();
                    if (AssertUtils.isNotNull(employeResponse)) {
                        policyReviewListResponse.setReviewer(employeResponse.getEmployeName());
                    }
                }
            });
        }

        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(policyReviewBos) ? policyReviewBos.get(0).getTotalLine() : null;
        /*
         * 数据返回
         */
        BasePageResponse<PolicyReviewListResponse> basePageResponse = BasePageResponse.getData(policyQueryListRequest.getCurrentPage(), policyQueryListRequest.getPageSize(), totalLine, policyReviewListResponses);

        resultObject.setData(basePageResponse);
        return resultObject;
    }

    /**
     * 保单复核签收
     *
     * @param users   用户
     * @param applyId 投保单ID
     * @return ResultObject
     */
    @Override
    public ResultObject reviewInfoSign(Users users, String applyId) {
        AssertUtils.isNotNull(log, applyId, PolicyErrorConfigEnum.POLICY_QUERY_APPLY_ID_IS_NOT_NULL);
        ResultObject resultObject = workFlowApi.claimTask(users.getUserId(), applyId, PolicyWorkflowTermEnum.WORKFLOW_STATUS.POLICY_REVIEW_TASK.name(), null);
        AssertUtils.isResultObjectError(log, resultObject);
        return resultObject;
    }

    /**
     * 保单复核保存
     *
     * @param users                   用户
     * @param policyReviewSaveRequest 保单复核
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject savePolicyReview(Users users, PolicyReviewSaveRequest policyReviewSaveRequest) {
        ResultObject resultObject = new ResultObject();
        log.info("保单复核保存 -> policyReviewSaveRequest:{}", JackSonUtils.toJson(policyReviewSaveRequest));
        AssertUtils.isNotNull(log, policyReviewSaveRequest.getApplyId(), PolicyErrorConfigEnum.POLICY_QUERY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(log, policyReviewSaveRequest.getPolicyId(), PolicyErrorConfigEnum.POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);
        policyReviewTransData.savePolicyReviewData(users, policyReviewSaveRequest, "SAVE");

        //将更改的信息更新至子系统
        PolicyBo policyBo = policyBaseService.queryPolicyBo(policyReviewSaveRequest.getPolicyId());
        PolicyVo policyVo = (PolicyVo) this.converterObject(policyBo, PolicyVo.class);
        ResultObject policyReviewUpdateData = baseApplyApi.policyReviewUpdateData(policyVo);
        AssertUtils.isResultObjectError(log, policyReviewUpdateData);
        return resultObject;
    }

    /**
     * 保单复核提交结论
     *
     * @param users                   用户
     * @param policyReviewSaveRequest 保单复核
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject submitPolicyReview(Users users, PolicyReviewSaveRequest policyReviewSaveRequest) {
        ResultObject resultObject = new ResultObject();
        log.info("保单复核提交结论 -> policyReviewSaveRequest:{}", JackSonUtils.toJson(policyReviewSaveRequest));
        AssertUtils.isNotNull(log, policyReviewSaveRequest.getApplyId(), PolicyErrorConfigEnum.POLICY_QUERY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(log, policyReviewSaveRequest.getPolicyId(), PolicyErrorConfigEnum.POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(log, policyReviewSaveRequest.getPolicyReviewResult(), PolicyErrorConfigEnum.POLICY_REVIEW_RESULT_IS_NOT_NULL);

        //结论不通过  终结工作流
        if ("HESITATION_REVOKE".equals(policyReviewSaveRequest.getPolicyReviewResult())) {
            //将更改的信息更新至子系统
            PolicyBo policyBo = policyBaseService.queryPolicyBo(policyReviewSaveRequest.getPolicyId());
            PolicyVo policyVo = (PolicyVo) this.converterObject(policyBo, PolicyVo.class);
            policyVo.setSubmitPolicyReviewFlag(TerminologyConfigEnum.WHETHER.YES.name());
            ResultObject policyReviewUpdateData = baseApplyApi.policyReviewUpdateData(policyVo);
            AssertUtils.isResultObjectError(log, policyReviewUpdateData);

            //保存保单复核结论记录
            PolicyReviewDecisionPo policyReviewDecisionPo = new PolicyReviewDecisionPo();
            policyReviewDecisionPo.setPolicyId(policyReviewSaveRequest.getPolicyId());
            policyReviewDecisionPo.setApplyId(policyReviewSaveRequest.getApplyId());
            policyReviewDecisionPo.setPolicyReviewResultCode(policyReviewSaveRequest.getPolicyReviewResult());
            policyReviewDecisionPo.setRemarks(policyReviewSaveRequest.getRemark());
            policyReviewDecisionPo.setPolicyReviewDate(DateUtils.getCurrentTime());
            policyReviewBaseService.savePolicyReviewDecision(users.getUserId(),policyReviewDecisionPo);

            TerminationTaskRequest terminationTaskRequest = new TerminationTaskRequest();
            terminationTaskRequest.setBusinessNo(policyReviewSaveRequest.getApplyId());
            terminationTaskRequest.setTerminationMessage("保单复核结论不通过");
            this.getLogger().info("保单复核结论不通过,正在结束工作流");
            resultObject = workFlowApi.terminationTask(terminationTaskRequest);
            return resultObject;
        }
        //结论通过
        if ("REVIEW_PASS".equals(policyReviewSaveRequest.getPolicyReviewResult())) {
            //保存数据
            policyReviewTransData.savePolicyReviewData(users, policyReviewSaveRequest, "SUBMIT");

            //判断是否有未处理的客户合并  其中包括 需要合并但没有保存合并数据 或者 存在合并结论为NOT_MERGED的数据
            if (policyReviewTransData.isUnprocessedSuspectedCustomers(policyReviewSaveRequest.getPolicyId())) {
                throwsException(log, PolicyErrorConfigEnum.POLICY_PLEASE_COMPLETE_THE_MERGER_SUSPECTED_CUSTOMERS_ERROR);
            }

            //删除已生成的单证附件，打印的时候会重新生成最新的
            //DELETE FROM policy_attachment WHERE "language" NOTNULL AND policy_id IN('投保单ID','保单ID','计划书ID');
            policyReviewTransData.deleteGenerateAttachment(policyReviewSaveRequest);

            //将更改的信息更新至子系统
            PolicyBo policyBo = policyBaseService.queryPolicyBo(policyReviewSaveRequest.getPolicyId());
            PolicyVo policyVo = (PolicyVo) this.converterObject(policyBo, PolicyVo.class);
            policyVo.setSubmitPolicyReviewFlag(TerminologyConfigEnum.WHETHER.YES.name());
            ResultObject policyReviewUpdateData = baseApplyApi.policyReviewUpdateData(policyVo);
            AssertUtils.isResultObjectError(log, policyReviewUpdateData);

            //保存保单复核结论记录
            PolicyReviewDecisionPo policyReviewDecisionPo = new PolicyReviewDecisionPo();
            policyReviewDecisionPo.setPolicyId(policyReviewSaveRequest.getPolicyId());
            policyReviewDecisionPo.setApplyId(policyReviewSaveRequest.getApplyId());
            policyReviewDecisionPo.setPolicyReviewResultCode(policyReviewSaveRequest.getPolicyReviewResult());
            policyReviewDecisionPo.setRemarks(policyReviewSaveRequest.getRemark());
            policyReviewDecisionPo.setPolicyReviewDate(DateUtils.getCurrentTime());
            policyReviewBaseService.savePolicyReviewDecision(users.getUserId(),policyReviewDecisionPo);

            //完成工作流
            CompleteTaskParam completeTaskReqFc = new CompleteTaskParam();
            completeTaskReqFc.setBusinessId(policyReviewSaveRequest.getApplyId());
            completeTaskReqFc.setWorkflowItemType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.POLICY_REVIEW_TASK.name());
            completeTaskReqFc.setNeedUnderwritingFlag("true");
            ProcessParam processParam = new ProcessParam();
            //网销渠道的单，保单复核完成后就会结束流程
            if (PolicyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(policyBo.getChannelTypeCode())) {
                processParam.setOnLineFlag("true");
//                //保单复核通过后发送一条短信给投保人
                String businessCode = PolicyTermEnum.MSG_BUSINESS_TYPE.POLICY_UNDERWRITTEN_TO_CUSTOMER.name();
                messageBusinessService.pushPolicyMessageSingleToApplicant(businessCode, policyBo);
            } else {
                processParam.setOnLineFlag("false");
            }
            completeTaskReqFc.setProcessParam(processParam);
            resultObject = workFlowApi.completeTask(completeTaskReqFc);
            log.info(JSON.toJSONString(resultObject));
            AssertUtils.isResultObjectError(log, resultObject);
        }

        // 确保重新生成附件的方法在最后，删除之后异步生成保单打印
        asyncUtil.asyncGeneratePolicyAllBook(policyReviewSaveRequest.getPolicyId());
        return resultObject;
    }

    @Override
    public ResultObject<PolicyReviewDecisionResponse> queryPolicyReviewDecision(String applyId, Users users) {
        ResultObject<PolicyReviewDecisionResponse> resultObject = new ResultObject<>();
        PolicyReviewDecisionResponse policyReviewDecisionResponse = new PolicyReviewDecisionResponse();
        if (!AssertUtils.isNotEmpty(applyId)) {
            return null;
        }
        PolicyReviewDecisionPo policyReviewDecisionPo = policyReviewBaseService.queryPolicyReviewDecision(applyId, users.getUserId());
        if (AssertUtils.isNotNull(policyReviewDecisionPo)) {
            ClazzUtils.copyPropertiesIgnoreNull(policyReviewDecisionPo,policyReviewDecisionResponse);
            resultObject.setData(policyReviewDecisionResponse);
        }else {
            resultObject.setData(null);
        }
        return resultObject;
    }

    /**
     * 查询客户合并详情
     *
     * @param policyId 保单ID
     * @param users    用户
     * @return PolicyCustomerMergerResponse
     */
    @Override
    public ResultObject<PolicyCustomerMergerResponse> queryPolicyCustomerMergerData(String policyId, Users users) {
        ResultObject<PolicyCustomerMergerResponse> resultObject = new ResultObject<>();
        PolicyCustomerMergerResponse policyCustomerMergerResponse = new PolicyCustomerMergerResponse();

        PolicyBo policyBo = policyBaseService.queryPolicyBo(policyId);
        AssertUtils.isNotNull(getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);

        PolicyApplicantBo applicant = policyBo.getApplicant();
        AssertUtils.isNotNull(this.getLogger(), applicant, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_APPLICANT_ERROR);

        PolicyInsuredBo policyInsuredBo = policyBo.getListInsured().get(0);
        AssertUtils.isNotNull(getLogger(), policyInsuredBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_INSURED_ERROR);

        if (!PolicyTermEnum.RELATIONSHIP_WITH_THE_INSURED.ONESELF.name().equals(policyInsuredBo.getRelationship())) {
            policyCustomerMergerResponse.setMergerApplicant(policyReviewTransData.transPolicyCustomerMergerDetail(policyId, applicant.getCustomerId(), PolicyTermEnum.CUSTOMER_TYPE.APPLICANT.name()));
        }
        policyCustomerMergerResponse.setMergerInsured(policyReviewTransData.transPolicyCustomerMergerDetail(policyId, policyInsuredBo.getCustomerId(), PolicyTermEnum.CUSTOMER_TYPE.INSURED.name()));

        List<PolicyCustomerMergerBo> policyCustomerMergerBos = policyReviewBaseService.queryPolicyCustomerMergers(policyId);
        if (AssertUtils.isNotEmpty(policyCustomerMergerBos)) {
            List<PolicyCustomerMergerBo> collect = policyCustomerMergerBos.stream().filter(policyCustomerMergerBo -> !"MERGED".equals(policyCustomerMergerBo.getMergerStatus())).collect(Collectors.toList());
            if (!AssertUtils.isNotEmpty(collect)) {
                policyCustomerMergerResponse.setEditFlag(TerminologyConfigEnum.WHETHER.NO.name());
            }
        }

        resultObject.setData(policyCustomerMergerResponse);
        return resultObject;
    }

    /**
     * 保存客户合并结论
     *
     * @param users                       用户
     * @param policyCustomerMergerRequest 客户合并结论
     * @return
     */
    @Override
    @Transactional
    public ResultObject saveCustomerMerger(Users users, PolicyCustomerMergerRequest policyCustomerMergerRequest) {
        //数据校验
        policyReviewParameterValidate.validPolicyCustomerMerger(policyCustomerMergerRequest, "SAVE");

        String policyId = policyCustomerMergerRequest.getPolicyId();
        PolicyBo policyBo = policyBaseService.queryPolicyBo(policyId);
        AssertUtils.isNotNull(getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);

        PolicyApplicantBo applicant = policyBo.getApplicant();
        AssertUtils.isNotNull(this.getLogger(), applicant, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_APPLICANT_ERROR);

        PolicyInsuredBo policyInsuredBo = policyBo.getListInsured().get(0);
        AssertUtils.isNotNull(getLogger(), policyInsuredBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_INSURED_ERROR);

        //保存结论
        //投保人
        PolicyCustomerMergerDetailRequest mergerApplicant = policyCustomerMergerRequest.getMergerApplicant();
        if (AssertUtils.isNotNull(mergerApplicant)) {
            PolicyCustomerMergerBo policyCustomerMergerApplicant = policyReviewBaseService.queryPolicyCustomerMerger(policyId, PolicyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
            if (!AssertUtils.isNotNull(policyCustomerMergerApplicant)) {
                policyCustomerMergerApplicant = new PolicyCustomerMergerBo();
            }
            policyCustomerMergerApplicant.setCustomerMergerType(PolicyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
            policyCustomerMergerApplicant.setMergerConclusion(mergerApplicant.getMergerConclusion());
            if ("SAME_CUSTOMER".equals(mergerApplicant.getMergerConclusion())) {
                policyCustomerMergerApplicant.setCurrentPolicyFlag(mergerApplicant.getCurrentPolicyFlag());
                policyCustomerMergerApplicant.setDataCalibrationCustomerId(mergerApplicant.getDataCalibrationCustomerId());
            }
            policyCustomerMergerApplicant.setMergerStatus("NOT_MERGED");
            policyCustomerMergerApplicant.setPolicyId(policyId);
            policyReviewBaseService.savePolicyCustomerMerger(policyCustomerMergerApplicant, users.getUserId());

            //同一客户
            if ("SAME_CUSTOMER".equals(mergerApplicant.getMergerConclusion())) {
                ResultObject<List<CustomerMessageResponse>> baseCustomerList = customerBaseApi.getBaseCustomerList(mergerApplicant.getDataCalibrationCustomerId());
                if (!AssertUtils.isResultObjectListDataNull(baseCustomerList)) {
                    List<CustomerMessageResponse> customerMessageResponses = baseCustomerList.getData();
                    //保存CustomerId数据至PolicyCustomerMergerItem
                    policyReviewBaseService.deletePolicyCustomerMergerItem(policyCustomerMergerApplicant.getPolicyCustomerMergerId());
                    List<PolicyCustomerMergerItemPo> policyCustomerMergerItemPos = (List<PolicyCustomerMergerItemPo>) this.converterList(customerMessageResponses, new TypeToken<List<PolicyCustomerMergerItemPo>>() {
                    }.getType());
                    for (PolicyCustomerMergerItemPo policyCustomerMergerItemPo : policyCustomerMergerItemPos) {
                        policyCustomerMergerItemPo.setPolicyId(policyId);
                        policyCustomerMergerItemPo.setPolicyCustomerMergerId(policyCustomerMergerApplicant.getPolicyCustomerMergerId());
                    }
                    policyReviewBaseService.savePolicyCustomerMergerItem(policyCustomerMergerItemPos, users.getUserId());
                }
            }
        }
        //被保人
        PolicyCustomerMergerDetailRequest mergerInsured = policyCustomerMergerRequest.getMergerInsured();
        if (AssertUtils.isNotNull(mergerInsured)) {
            PolicyCustomerMergerBo policyCustomerMergerInsured = policyReviewBaseService.queryPolicyCustomerMerger(policyId, PolicyTermEnum.CUSTOMER_TYPE.INSURED.name());
            if (!AssertUtils.isNotNull(policyCustomerMergerInsured)) {
                policyCustomerMergerInsured = new PolicyCustomerMergerBo();
            }
            policyCustomerMergerInsured.setCustomerMergerType(PolicyTermEnum.CUSTOMER_TYPE.INSURED.name());
            policyCustomerMergerInsured.setMergerConclusion(mergerInsured.getMergerConclusion());
            if ("SAME_CUSTOMER".equals(mergerInsured.getMergerConclusion())) {
                policyCustomerMergerInsured.setCurrentPolicyFlag(mergerInsured.getCurrentPolicyFlag());
                policyCustomerMergerInsured.setDataCalibrationCustomerId(mergerInsured.getDataCalibrationCustomerId());
            }
            policyCustomerMergerInsured.setMergerStatus("NOT_MERGED");
            policyCustomerMergerInsured.setPolicyId(policyId);
            policyReviewBaseService.savePolicyCustomerMerger(policyCustomerMergerInsured, users.getUserId());
            //同一客户
            if ("SAME_CUSTOMER".equals(mergerInsured.getMergerConclusion())) {
                ResultObject<List<CustomerMessageResponse>> baseCustomerList = customerBaseApi.getBaseCustomerList(mergerInsured.getDataCalibrationCustomerId());
                if (!AssertUtils.isResultObjectListDataNull(baseCustomerList)) {
                    List<CustomerMessageResponse> customerMessageResponses = baseCustomerList.getData();
                    //保存CustomerId数据至PolicyCustomerMergerItem
                    policyReviewBaseService.deletePolicyCustomerMergerItem(policyCustomerMergerInsured.getPolicyCustomerMergerId());
                    List<PolicyCustomerMergerItemPo> policyCustomerMergerItemPos = (List<PolicyCustomerMergerItemPo>) this.converterList(customerMessageResponses, new TypeToken<List<PolicyCustomerMergerItemPo>>() {
                    }.getType());
                    for (PolicyCustomerMergerItemPo policyCustomerMergerItemPo : policyCustomerMergerItemPos) {
                        policyCustomerMergerItemPo.setPolicyId(policyId);
                        policyCustomerMergerItemPo.setPolicyCustomerMergerId(policyCustomerMergerInsured.getPolicyCustomerMergerId());
                    }
                    policyReviewBaseService.savePolicyCustomerMergerItem(policyCustomerMergerItemPos, users.getUserId());
                }
            }
        }
        return ResultObject.success();
    }

    /**
     * 提交客户合并结论
     *
     * @param users                       用户
     * @param policyCustomerMergerRequest 客户合并结论
     * @return
     */
    @Override
    @Transactional
    public ResultObject submitCustomerMerger(Users users, PolicyCustomerMergerRequest policyCustomerMergerRequest) {
        //数据校验
        policyReviewParameterValidate.validPolicyCustomerMerger(policyCustomerMergerRequest, "SUBMIT");
        String policyId = policyCustomerMergerRequest.getPolicyId();
        PolicyBo policyBo = policyBaseService.queryPolicyBo(policyId);
        AssertUtils.isNotNull(getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);

        PolicyApplicantBo applicant = policyBo.getApplicant();
        AssertUtils.isNotNull(this.getLogger(), applicant, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_APPLICANT_ERROR);

        PolicyInsuredBo policyInsuredBo = policyBo.getListInsured().get(0);
        AssertUtils.isNotNull(getLogger(), policyInsuredBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_INSURED_ERROR);
        //保存结论
        //投保人
        PolicyCustomerMergerDetailRequest mergerApplicant = policyCustomerMergerRequest.getMergerApplicant();
        if (AssertUtils.isNotNull(mergerApplicant)) {
            PolicyCustomerMergerBo policyCustomerMergerApplicant = policyReviewBaseService.queryPolicyCustomerMerger(policyId, PolicyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
            if (!AssertUtils.isNotNull(policyCustomerMergerApplicant)) {
                policyCustomerMergerApplicant = new PolicyCustomerMergerBo();
            }
            policyCustomerMergerApplicant.setCustomerMergerType(PolicyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
            policyCustomerMergerApplicant.setMergerConclusion(mergerApplicant.getMergerConclusion());
            policyCustomerMergerApplicant.setPolicyId(policyId);
            if ("SAME_CUSTOMER".equals(mergerApplicant.getMergerConclusion())) {
                policyCustomerMergerApplicant.setCurrentPolicyFlag(mergerApplicant.getCurrentPolicyFlag());
                policyCustomerMergerApplicant.setDataCalibrationCustomerId(mergerApplicant.getDataCalibrationCustomerId());
            }
            policyCustomerMergerApplicant.setMergerStatus("MERGED");
            policyReviewBaseService.savePolicyCustomerMerger(policyCustomerMergerApplicant, users.getUserId());

            //同一客户
            if ("SAME_CUSTOMER".equals(mergerApplicant.getMergerConclusion())) {
                ResultObject<List<CustomerMessageResponse>> baseCustomerList = customerBaseApi.getBaseCustomerList(mergerApplicant.getDataCalibrationCustomerId());
                if (!AssertUtils.isResultObjectListDataNull(baseCustomerList)) {
                    List<CustomerMessageResponse> customerMessageResponses = baseCustomerList.getData();
                    //保存CustomerId数据至PolicyCustomerMergerItem
                    policyReviewBaseService.deletePolicyCustomerMergerItem(policyCustomerMergerApplicant.getPolicyCustomerMergerId());
                    List<PolicyCustomerMergerItemPo> policyCustomerMergerItemPos = (List<PolicyCustomerMergerItemPo>) this.converterList(customerMessageResponses, new TypeToken<List<PolicyCustomerMergerItemPo>>() {
                    }.getType());
                    for (PolicyCustomerMergerItemPo policyCustomerMergerItemPo : policyCustomerMergerItemPos) {
                        policyCustomerMergerItemPo.setPolicyId(policyId);
                        policyCustomerMergerItemPo.setPolicyCustomerMergerId(policyCustomerMergerApplicant.getPolicyCustomerMergerId());
                    }
                    policyReviewBaseService.savePolicyCustomerMergerItem(policyCustomerMergerItemPos, users.getUserId());

                    CustomerMessageResponse customerMessageResponse = customerMessageResponses.get(0);
                    String originApplicantCustomerId = applicant.getCustomerId();
                    String originRealCustomerId = customerMessageResponse.getCustomerId();

                    //用所选择的CustomerId数据覆盖当前保单数据
                    if ("NO".equals(mergerApplicant.getCurrentPolicyFlag()) && AssertUtils.isNotEmpty(mergerApplicant.getDataCalibrationCustomerId())) {
                        if (!customerMessageResponse.getSex().equals(applicant.getSex())
                                || DateUtils.timeToTimeLow(customerMessageResponse.getBirthday()) != DateUtils.timeToTimeLow(applicant.getBirthday())
                                || !customerMessageResponse.getStature().equals(applicant.getStature())
                                || !customerMessageResponse.getAvoirdupois().equals(applicant.getAvoirdupois())
                        ) {
                            throwsException(PolicyErrorConfigEnum.POLICY_THE_MERGER_SUSPECTED_CUSTOMERS_ERROR);
                        }
                        ClazzUtils.copyPropertiesIgnoreNull(customerMessageResponse, applicant);
                        applicant.setCustomerId(originApplicantCustomerId);
                        policyBaseService.savePolicyApplicant(applicant);
                        customerManageApi.updateCustomerAgentDataByCustomer(mergerApplicant.getDataCalibrationCustomerId(), originApplicantCustomerId);
                    }
                    //用当前保单数据覆盖所选择的CustomerId数据
                    if ("YES".equals(mergerApplicant.getCurrentPolicyFlag()) && AssertUtils.isNotEmpty(mergerApplicant.getDataCalibrationCustomerId())) {
                        UserCustomerBusinessRequest userCustomerBusinessRequest = new UserCustomerBusinessRequest();
                        ClazzUtils.copyPropertiesIgnoreNull(applicant, userCustomerBusinessRequest);
                        userCustomerBusinessRequest.setCustomerId(originRealCustomerId);
                        userCustomerBusinessRequest.setOriginCustomerAgentId(applicant.getCustomerId());
                        customerManageApi.updateRealCustomerBusinessSingle(userCustomerBusinessRequest);
                    }
                }
            }
        }
        //被保人
        PolicyCustomerMergerDetailRequest mergerInsured = policyCustomerMergerRequest.getMergerInsured();
        if (AssertUtils.isNotNull(mergerInsured)) {
            PolicyCustomerMergerBo policyCustomerMergerInsured = policyReviewBaseService.queryPolicyCustomerMerger(policyId, PolicyTermEnum.CUSTOMER_TYPE.INSURED.name());
            if (!AssertUtils.isNotNull(policyCustomerMergerInsured)) {
                policyCustomerMergerInsured = new PolicyCustomerMergerBo();
            }
            policyCustomerMergerInsured.setCustomerMergerType(PolicyTermEnum.CUSTOMER_TYPE.INSURED.name());
            policyCustomerMergerInsured.setMergerConclusion(mergerInsured.getMergerConclusion());
            if ("SAME_CUSTOMER".equals(mergerInsured.getMergerConclusion())) {
                policyCustomerMergerInsured.setCurrentPolicyFlag(mergerInsured.getCurrentPolicyFlag());
                policyCustomerMergerInsured.setDataCalibrationCustomerId(mergerInsured.getDataCalibrationCustomerId());
            }
            policyCustomerMergerInsured.setMergerStatus("MERGED");
            policyCustomerMergerInsured.setPolicyId(policyId);
            policyReviewBaseService.savePolicyCustomerMerger(policyCustomerMergerInsured, users.getUserId());
            //同一客户
            if ("SAME_CUSTOMER".equals(mergerInsured.getMergerConclusion())) {
                ResultObject<List<CustomerMessageResponse>> baseCustomerList = customerBaseApi.getBaseCustomerList(mergerInsured.getDataCalibrationCustomerId());
                if (!AssertUtils.isResultObjectListDataNull(baseCustomerList)) {
                    List<CustomerMessageResponse> customerMessageResponses = baseCustomerList.getData();
                    //保存CustomerId数据至PolicyCustomerMergerItem
                    policyReviewBaseService.deletePolicyCustomerMergerItem(policyCustomerMergerInsured.getPolicyCustomerMergerId());
                    List<PolicyCustomerMergerItemPo> policyCustomerMergerItemPos = (List<PolicyCustomerMergerItemPo>) this.converterList(customerMessageResponses, new TypeToken<List<PolicyCustomerMergerItemPo>>() {
                    }.getType());
                    for (PolicyCustomerMergerItemPo policyCustomerMergerItemPo : policyCustomerMergerItemPos) {
                        policyCustomerMergerItemPo.setPolicyId(policyId);
                        policyCustomerMergerItemPo.setPolicyCustomerMergerId(policyCustomerMergerInsured.getPolicyCustomerMergerId());
                    }
                    policyReviewBaseService.savePolicyCustomerMergerItem(policyCustomerMergerItemPos, users.getUserId());

                    CustomerMessageResponse customerMessageResponse = customerMessageResponses.get(0);
                    String originInsuredCustomerId = policyInsuredBo.getCustomerId();
                    String originRealCustomerId = customerMessageResponse.getCustomerId();

                    //用所选择的CustomerId数据覆盖当前保单数据
                    if ("NO".equals(mergerInsured.getCurrentPolicyFlag()) && AssertUtils.isNotEmpty(mergerInsured.getDataCalibrationCustomerId())) {
                        if (!customerMessageResponse.getSex().equals(policyInsuredBo.getSex())
                                || DateUtils.timeToTimeLow(customerMessageResponse.getBirthday()) != DateUtils.timeToTimeLow(policyInsuredBo.getBirthday())
                                || !customerMessageResponse.getStature().equals(policyInsuredBo.getStature())
                                || !customerMessageResponse.getAvoirdupois().equals(policyInsuredBo.getAvoirdupois())
                        ) {
                            throwsException(PolicyErrorConfigEnum.POLICY_THE_MERGER_SUSPECTED_CUSTOMERS_ERROR);
                        }
                        ClazzUtils.copyPropertiesIgnoreNull(customerMessageResponse, policyInsuredBo);
                        policyInsuredBo.setCustomerId(originInsuredCustomerId);
                        policyBaseService.savePolicyInsured(policyInsuredBo);
                        if (PolicyTermEnum.RELATIONSHIP_WITH_THE_INSURED.ONESELF.name().equals(policyInsuredBo.getRelationship())) {
                            String originApplicantCustomerId = applicant.getCustomerId();
                            ClazzUtils.copyPropertiesIgnoreNull(customerMessageResponse, applicant);
                            applicant.setCustomerId(originApplicantCustomerId);
                            policyBaseService.savePolicyApplicant(applicant);
                        }
                        customerManageApi.updateCustomerAgentDataByCustomer(mergerInsured.getDataCalibrationCustomerId(), originInsuredCustomerId);
                    }
                    //用当前保单数据覆盖所选择的CustomerId数据
                    if ("YES".equals(mergerInsured.getCurrentPolicyFlag()) && AssertUtils.isNotEmpty(mergerInsured.getDataCalibrationCustomerId())) {
                        UserCustomerBusinessRequest userCustomerBusinessRequest = new UserCustomerBusinessRequest();
                        ClazzUtils.copyPropertiesIgnoreNull(policyInsuredBo, userCustomerBusinessRequest);
                        userCustomerBusinessRequest.setCustomerId(originRealCustomerId);
                        userCustomerBusinessRequest.setOriginCustomerAgentId(policyInsuredBo.getCustomerId());
                        customerManageApi.updateRealCustomerBusinessSingle(userCustomerBusinessRequest);
                    }
                }
            }
        }
        //将更改的信息更新至子系统
        PolicyVo policyVo = (PolicyVo) this.converterObject(policyBo, PolicyVo.class);
        ResultObject policyReviewUpdateData = baseApplyApi.policyReviewUpdateData(policyVo);
        AssertUtils.isResultObjectError(log, policyReviewUpdateData);
        return ResultObject.success();
    }

}
