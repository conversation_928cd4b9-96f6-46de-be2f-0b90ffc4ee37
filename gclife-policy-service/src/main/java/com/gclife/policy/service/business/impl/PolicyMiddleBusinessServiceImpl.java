package com.gclife.policy.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.policy.core.jooq.tables.pojos.PolicyInsuredPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPaymentPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyReceiptInfoPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyReturnVisitPo;
import com.gclife.policy.model.bo.PolicyAndInsuredBo;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.request.PolicyReceiptInfoUpdateRequest;
import com.gclife.policy.model.request.PolicyReturnVisitRequest;
import com.gclife.policy.model.response.PolicyAndInsuredResponse;
import com.gclife.policy.model.response.PolicyInsuredResponse;
import com.gclife.policy.service.base.PolicyBaseService;
import com.gclife.policy.service.base.PolicyPaymentBaseService;
import com.gclife.policy.service.business.PolicyMiddleBusinessService;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 20-8-28
 */
@Service
public class PolicyMiddleBusinessServiceImpl extends BaseBusinessServiceImpl implements PolicyMiddleBusinessService {
    @Autowired
    private PolicyBaseService policyBaseService;
    @Autowired
    private PolicyPaymentBaseService policyPaymentBaseService;

    /**
     * 保存保单回访记录
     * @param returnVisitRequest 回访信息
     * @param users 用户
     * @return
     */
    @Override
    public ResultObject savePolicyReturnVisit(PolicyReturnVisitRequest returnVisitRequest, Users users) {
        AssertUtils.isNotEmpty(this.getLogger(), returnVisitRequest.getBusinessId(), PolicyErrorConfigEnum.POLICY_PARAMETER_BUSINESS_ID_IS_NOT_NULL);
        // 查询保单回访
        PolicyReturnVisitPo policyReturnVisitPo = policyBaseService.queryReturnVisitByBusinessId(returnVisitRequest.getBusinessId());
        if (!AssertUtils.isNotNull(policyReturnVisitPo)) {
            policyReturnVisitPo = new PolicyReturnVisitPo();
        }
        ClazzUtils.copyPropertiesIgnoreNull(returnVisitRequest, policyReturnVisitPo);
        policyBaseService.savePolicyReturnVisit(policyReturnVisitPo, users.getUserId());
        return new ResultObject();
    }

    /**
     * 保单回执信息更新
     * @param receiptInfoUpdateRequest
     * @param users 用户
     * @return
     */
    @Override
    public ResultObject updatePolicyReceiptInfo(PolicyReceiptInfoUpdateRequest receiptInfoUpdateRequest, Users users) {
        if (!AssertUtils.isNotEmpty(receiptInfoUpdateRequest.getBusinessIds()) || !AssertUtils.isNotEmpty(receiptInfoUpdateRequest.getReceiptStatus())) {
            return ResultObject.success();
        }
        // 查询支付数据
        List<PolicyPaymentPo> policyPaymentPos = policyPaymentBaseService.listPolicyPaymentByBusinessId(receiptInfoUpdateRequest.getBusinessIds());
        if (AssertUtils.isNotEmpty(policyPaymentPos)) {
            List<String> policyIds = policyPaymentPos.stream().map(PolicyPaymentPo::getPolicyId).collect(Collectors.toList());
            // 查询回执信息
            List<PolicyReceiptInfoPo> policyReceiptInfoPos = policyBaseService.listPolicyReceiptInfo(policyIds);
            policyReceiptInfoPos.forEach(policyReceiptInfoPo -> policyReceiptInfoPo.setReceiptStatus(receiptInfoUpdateRequest.getReceiptStatus()));
            policyBaseService.updatePolicyReceiptInfo(policyReceiptInfoPos, users.getUserId());
        }
        return ResultObject.success();
    }

    /**
     * 查询保障中客户
     * @param agentId 业务员ID
     * @return
     */
    @Override
    public ResultObject<List<String>> listEffectiveCustomer(String agentId) {
        List<String> policyStatusList = new ArrayList<>();
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_PENDING_EFFECT.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_REINSTATEMENT.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_WAIVER_PREMIUM.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_EFFECTIVE_HC.name());

        List<String> customerIds = policyBaseService.listEffectiveCustomer(agentId, policyStatusList);

        ResultObject<List<String>> resultObject = new ResultObject<>();
        resultObject.setData(customerIds);
        return resultObject;
    }

    @Override
    public ResultObject<List<PolicyAndInsuredResponse>> getSokSanCustomer(String idNo) {
        ResultObject<List<PolicyAndInsuredResponse>> resultObject = new ResultObject<>();
        List<PolicyAndInsuredBo> policyAndInsuredBos = policyBaseService.querySokSanPolicyInsured(idNo);

        if (AssertUtils.isNotNull(policyAndInsuredBos)) {
            this.getLogger().info("查询保单信息:{}", JSON.toJSON(policyAndInsuredBos));

            List<PolicyAndInsuredResponse> policyInsuredResponseList = (List<PolicyAndInsuredResponse>)
                    this.converterList(policyAndInsuredBos, new TypeToken<List<PolicyAndInsuredResponse>>(){}.getType());

            resultObject.setData(policyInsuredResponseList);
        }

        this.getLogger().info("转换后的保单信息:{}", JSON.toJSON(resultObject));
        return resultObject;
    }
}
