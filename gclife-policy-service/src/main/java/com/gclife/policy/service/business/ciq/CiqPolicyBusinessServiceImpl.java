package com.gclife.policy.service.business.ciq;

import com.gclife.agent.api.AgentCommissionApi;
import com.gclife.agent.api.AgentExtApi;
import com.gclife.agent.api.AgentTeamApi;
import com.gclife.agent.model.request.commission.CommissionPolicyCancelRequest;
import com.gclife.agent.model.response.AgentKeyWordResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.api.CiqApplyApi;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.core.jooq.tables.daos.PolicyDao;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPo;
import com.gclife.policy.dao.PolicyExtDao;
import com.gclife.policy.dao.ciq.CiqPolicyDao;
import com.gclife.policy.model.bo.CiqRevokeListBo;
import com.gclife.policy.model.bo.PolicyApplicantBo;
import com.gclife.policy.model.bo.PolicyPaymentBo;
import com.gclife.policy.model.bo.ciq.CiqPolicyDetailBo;
import com.gclife.policy.model.bo.ciq.CiqPolicyListBo;
import com.gclife.policy.model.bo.ciq.CiqPolicyReportBo;
import com.gclife.policy.model.bo.ciq.CiqRevokePolicyBo;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.config.ciq.CiqPolicyErrorConfigEnum;
import com.gclife.policy.model.request.CiqRevokeListRequest;
import com.gclife.policy.model.request.ciq.CiqPolicyListRequest;
import com.gclife.policy.model.request.ciq.CiqPolicyReportRequest;
import com.gclife.policy.model.response.CiqRevokeListResponse;
import com.gclife.policy.model.response.ciq.*;
import com.gclife.policy.service.data.PolicyBoService;
import com.gclife.policy.validate.transfer.CiqPolicyDataTransfer;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 海关投保单
 * @date 18-1-4
 */
@Service
public class CiqPolicyBusinessServiceImpl extends BaseBusinessServiceImpl implements CiqPolicyBusinessService {
    @Autowired
    private CiqPolicyDao ciqPolicyDao;;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private CiqApplyApi ciqApplyApi;
    @Autowired
    private PolicyDao policyDao;
    @Autowired
    private PolicyExtDao policyExtDao;
    @Autowired
    private PolicyBoService policyBoService;
    @Autowired
    private CiqPolicyDataTransfer ciqPolicyDataTransfer;
    @Autowired
    private AgentExtApi agentExtApi;
    @Autowired
    private AgentTeamApi agentTeamApi;
    @Autowired
    private AgentCommissionApi agentCommissionApi;

    /**
     * 分页查询海关投保单列表
     *
     * @param request 请求对象
     * @return 海关投保单列表
     */
    @Override
    public ResultObject<BasePageResponse<CiqPolicyListResponse>> getCiqPolicyList(Users users, CiqPolicyListRequest request) {
        ResultObject<BasePageResponse<CiqPolicyListResponse>> resultObject = new ResultObject<>();
        try {
            // 查询我的团队下的人
            List<AgentResponse> agentResponses = agentTeamApi.getTeamList(users.getUserId(), "MANAGER").getData();
            BasePageResponse basePageResponse = new BasePageResponse();
            // 获取代理人agentId集
            if (AssertUtils.isNotEmpty(agentResponses)) {
                List<String> agentIds = agentResponses.stream().map(AgentResponse::getAgentId).collect(Collectors.toList());

                // 查询海关投保单记录
                List<CiqPolicyListBo> ciqPolicyListBos = this.ciqPolicyDao.getCiqPolicyList(request, agentIds);
                // 数据转换
                List<CiqPolicyListResponse> ciqPolicyListResponses = (List<CiqPolicyListResponse>) this.converterList(
                        ciqPolicyListBos, new TypeToken<List<CiqPolicyListResponse>>() {
                        }.getType()
                );

                if (AssertUtils.isNotEmpty(ciqPolicyListResponses)) {
                    List<String> types = new ArrayList<>();
                    types.add(TerminologyTypeEnum.BRANCH_NAME.name());
                    types.add(TerminologyTypeEnum.POLICY_STATUS.name());
                    Map<String, List<SyscodeResponse>> internationalList = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(users.getLanguage(), types).getData();
                    List<SyscodeResponse> branches = internationalList.get(TerminologyTypeEnum.BRANCH_NAME.name());
                    List<SyscodeResponse> policyStatuses = internationalList.get(TerminologyTypeEnum.POLICY_STATUS.name());

                    // 设置返回数据中代理人信息
                    ciqPolicyListResponses.forEach(response -> {
                        if (AssertUtils.isNotEmpty(agentResponses)) {
                            agentResponses.stream().filter(agentResponse -> agentResponse.getAgentId().equals(response.getAgentId()))
                                    .findFirst().ifPresent(agentResponse -> {
                                        response.setAgentCode(agentResponse.getAgentCode());
                                        response.setAgentName(agentResponse.getAgentName());
                                    });
                        }

                        if (AssertUtils.isNotEmpty(branches)) {
                            branches.stream()
                                    .filter(syscode -> syscode.getCodeKey().equals(response.getBranchId()))
                                    .findFirst().ifPresent(syscode -> response.setBranchName(syscode.getCodeName()));
                        }

                        if (AssertUtils.isNotEmpty(policyStatuses)) {
                            policyStatuses.stream()
                                    .filter(syscode -> syscode.getCodeKey().equals(response.getPolicyStatus()))
                                    .findFirst().ifPresent(syscode -> response.setPolicyStatus(syscode.getCodeName()));
                        }
                    });
                }

                //获取总页数
                Integer totalLine = AssertUtils.isNotNull(ciqPolicyListBos) ? ciqPolicyListBos.get(0).getTotalLine() : null;
                basePageResponse = BasePageResponse.getData(
                        request.getCurrentPage(), request.getPageSize(), totalLine, ciqPolicyListResponses);
            }
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 分页查询海关撤单列表
     *
     * @param request 请求对象
     * @return 海关撤单列表
     */
    @Override
    public ResultObject<BasePageResponse<CiqRevokeListResponse>> getCiqRevokeList(CiqRevokeListRequest request) {
        ResultObject<BasePageResponse<CiqRevokeListResponse>> resultObject = new ResultObject<>();
        try {
            if (!AssertUtils.isNotEmpty(request.getBranchId())) {
                request.setBranchId("GMC101201");
            }
            // 查询当前用户对应代理人列表
            List<AgentKeyWordResponse> agentRespFcs = agentExtApi.getUserAgents(request.getBranchId()).getData();
            BasePageResponse basePageResponse = new BasePageResponse();
            // 获取代理人agentId集
            if (AssertUtils.isNotEmpty(agentRespFcs)) {
                List<String> agentIds = agentRespFcs.stream().map(AgentKeyWordResponse::getAgentId).collect(Collectors.toList());

                // 查询海关投保单记录
                List<CiqRevokeListBo> ciqRevokeListBos = this.ciqPolicyDao.getCiqRevokeList(request, agentIds);
                // 数据转换
                List<CiqRevokeListResponse> ciqRevokeListResponses = (List<CiqRevokeListResponse>) this.converterList(
                        ciqRevokeListBos, new TypeToken<List<CiqRevokeListResponse>>() {
                        }.getType()
                );

                if (AssertUtils.isNotEmpty(ciqRevokeListResponses)) {
                    // 查询撤单员信息
                    List<String> revokeUserIds = ciqRevokeListResponses.stream().map(CiqRevokeListResponse::getRevokeUserId).collect(Collectors.toList());
                    List<AgentResponse> revokeAgentRespFcs = agentExtApi.agentsGetByUserId(revokeUserIds).getData();

                    ciqRevokeListResponses.forEach(response -> {
                        // 设置录单员信息
                        if (AssertUtils.isNotEmpty(agentRespFcs)) {
                            agentRespFcs.stream().filter(agentRespFc -> agentRespFc.getAgentId().equals(response.getAgentId()))
                                    .findFirst().ifPresent(agentRespFc -> {
                                response.setAgentCode(agentRespFc.getAgentCode());
                                response.setAgentName(agentRespFc.getAgentName());
                                response.setBranchName(agentRespFc.getBranchName());
                            });
                        }
                        // 设置撤单员信息
                        if (AssertUtils.isNotEmpty(revokeAgentRespFcs)) {
                            revokeAgentRespFcs.stream().filter(revokeAgentRespFc -> revokeAgentRespFc.getUserId().equals(response.getRevokeUserId()))
                                    .findFirst().ifPresent(revokeAgentRespFc -> {
                                response.setRevokeAgentCode(revokeAgentRespFc.getAgentCode());
                                response.setRevokeAgentName(revokeAgentRespFc.getAgentName());
                            });
                        }
                    });
                }

                //获取总页数
                Integer totalLine = AssertUtils.isNotNull(ciqRevokeListBos) ? ciqRevokeListBos.get(0).getTotalLine() : null;
                basePageResponse = BasePageResponse.getData(
                        request.getCurrentPage(), request.getPageSize(), totalLine, ciqRevokeListResponses);
            }
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 获取海关投保单报表
     *
     * @param request 请求对象
     * @return 海关投保单报表
     */
    @Override
    public ResultObject<BasePageResponse<CiqPolicyReportResponse>> getCiqPolicyReport(CiqPolicyReportRequest request) {
        ResultObject<BasePageResponse<CiqPolicyReportResponse>> resultObject = new ResultObject<>();
        try {
            if (!AssertUtils.isNotEmpty(request.getBranchId())) {
                request.setBranchId("GMC101201");
            }
            // 查询当前用户对应代理人列表
            List<AgentKeyWordResponse> agentRespFcs = agentExtApi.getUserAgents(request.getBranchId()).getData();
            // 获取代理人branchId集
            List<String> branchIds = new ArrayList<>();
            if (AssertUtils.isNotEmpty(agentRespFcs)) {
                branchIds = agentRespFcs.stream().map(agentRespFc -> agentRespFc.getBranchId()).distinct().collect(Collectors.toList());
            }

            // 获取海关投保单报表
            List<CiqPolicyReportBo> ciqPolicyReportBos = this.ciqPolicyDao.getCiqPolicyReport(request, branchIds);
            // 数据转换
            List<CiqPolicyReportResponse> ciqPolicyReportResponses =
                    (List<CiqPolicyReportResponse>) this.converterList(ciqPolicyReportBos, new TypeToken<List<CiqPolicyReportResponse>>() {
                    }.getType());

            // 设置投保网点
            if (AssertUtils.isNotEmpty(ciqPolicyReportResponses)) {
                ciqPolicyReportResponses.forEach(response -> {
                    if (AssertUtils.isNotEmpty(agentRespFcs)) {
                        agentRespFcs.stream().filter(agentRespFc -> agentRespFc.getBranchId().equals(response.getBranchId()))
                                .findFirst().ifPresent(agentRespFc -> {
                            response.setBranchName(agentRespFc.getBranchName());
                        });
                    }
                });
            }
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(ciqPolicyReportBos) ? ciqPolicyReportBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(
                    request.getCurrentPage(), request.getPageSize(), totalLine, ciqPolicyReportResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 保单验真
     *
     * @param idNo     证件号
     * @param verifyNo 验真码
     * @return 保单号
     */
    @Override
    public ResultObject<CiqPolicyVerifyResponse> getPolicyVerify(String idNo, String verifyNo) {
        ResultObject<CiqPolicyVerifyResponse> resultObject = new ResultObject<>();
        try {
            // 验证
            AssertUtils.isNotEmpty(this.getLogger(), idNo, CiqPolicyErrorConfigEnum.CIQ_POLICY_PARAMETER_ID_NO_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), verifyNo, CiqPolicyErrorConfigEnum.CIQ_POLICY_PARAMETER_VERIFY_NO_IS_NOT_NULL);
            // 查询
            PolicyPo policyPo = ciqPolicyDao.getPolicyIdByVerifyNo(idNo, verifyNo);
            CiqPolicyVerifyResponse ciqPolicyVerifyResponse = new CiqPolicyVerifyResponse();
            if (AssertUtils.isNotNull(policyPo)) {
                ciqPolicyVerifyResponse.setPolicyId(policyPo.getPolicyId());
            }
            resultObject.setData(ciqPolicyVerifyResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 保单详情
     *
     * @param policyId 保单ID
     * @return 保单详情
     */
    @Override
    public ResultObject<CiqPolicyDetailResponse> getCiqPolicyDetail(String policyId) {
        ResultObject<CiqPolicyDetailResponse> resultObject = new ResultObject<>();
        try {
            // 验证
            AssertUtils.isNotEmpty(this.getLogger(), policyId, CiqPolicyErrorConfigEnum.CIQ_POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);
            // 查询保单信息
            CiqPolicyDetailBo ciqPolicyDetailBo = ciqPolicyDao.getCiqPolicyDetail(policyId);
            AssertUtils.isNotEmpty(this.getLogger(), policyId, CiqPolicyErrorConfigEnum.CIQ_POLICY_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);

            // 数据转换
            CiqPolicyDetailResponse ciqPolicyDetailResponse = (CiqPolicyDetailResponse) this.converterObject(ciqPolicyDetailBo, CiqPolicyDetailResponse.class);

            // 币种
            List<SyscodeRespFc> currencySyscodes = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.CURRENCY.name()).getData();
            if (AssertUtils.isNotEmpty(currencySyscodes) && AssertUtils.isNotEmpty(ciqPolicyDetailResponse.getCurrencyCode())) {
                currencySyscodes.stream()
                        .filter(syscodeRespFc -> ciqPolicyDetailResponse.getCurrencyCode().equals(syscodeRespFc.getCodeKey()))
                        .findFirst().ifPresent(syscodeRespFc -> ciqPolicyDetailResponse.setCurrencyCode(syscodeRespFc.getSymbol()));
            }
            // 性别
            List<SyscodeRespFc> genderSyscodes = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.GENDER.name()).getData();
            if (AssertUtils.isNotEmpty(genderSyscodes) && AssertUtils.isNotEmpty(ciqPolicyDetailResponse.getSex())) {
                genderSyscodes.stream()
                        .filter(syscodeRespFc -> ciqPolicyDetailResponse.getSex().equals(syscodeRespFc.getCodeKey()))
                        .findFirst().ifPresent(syscodeRespFc -> ciqPolicyDetailResponse.setSex(syscodeRespFc.getCodeName()));
            }
            // 证件类型
            List<SyscodeRespFc> idTypeSyscodes = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.ID_TYPE.name()).getData();
            if (AssertUtils.isNotEmpty(idTypeSyscodes) && AssertUtils.isNotEmpty(ciqPolicyDetailResponse.getIdType())) {
                idTypeSyscodes.stream()
                        .filter(syscodeRespFc -> ciqPolicyDetailResponse.getIdType().equals(syscodeRespFc.getCodeKey()))
                        .findFirst().ifPresent(syscodeRespFc -> ciqPolicyDetailResponse.setIdType(syscodeRespFc.getCodeName()));
            }

            // 设置返回数据
            resultObject.setData(ciqPolicyDetailResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_FAIL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject revocationPolicy(String userId, String policyId) {
        ResultObject resultObject = new ResultObject<>();
        try {
            // 验证
            AssertUtils.isNotEmpty(this.getLogger(), userId, CiqPolicyErrorConfigEnum.CIQ_POLICY_PARAMETER_USER_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), policyId, CiqPolicyErrorConfigEnum.CIQ_POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);

            PolicyPo policyPo = policyDao.fetchOneByPolicyId(policyId);
            AssertUtils.isNotNull(getLogger(), policyPo, CiqPolicyErrorConfigEnum.CIQ_POLICY_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);


            PolicyApplicantBo policyApplicantBo = policyExtDao.loadPolicyApplicantByPolicyId(policyId);
            //查询是否是24小时之内的有效保单
            CiqRevokePolicyBo ciqRevokePolicyBo = ciqPolicyDao.getCiqPolicyRevoke(policyApplicantBo.getIdNo());
            AssertUtils.isNotNull(getLogger(), ciqRevokePolicyBo, CiqPolicyErrorConfigEnum.CIQ_POLICY_QUERY_POLICY_REVOKE_IS_NOT_FOUND_OBJECT);
            //海关投保请求撤单
            resultObject = ciqApplyApi.ciqApplyRevoke(ciqPolicyDataTransfer.transCiqRequestData(policyPo.getVerifyNo(), policyApplicantBo));
            //增加撤单记录
            policyBoService.savePolicyUndoPo(ciqPolicyDataTransfer.transRevokeData(userId, policyPo));
            //修改保单状态
            policyPo.setPolicyStatus(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_HESITATION_REVOKE.name());
            policyPo.setInvalidDate(DateUtils.getCurrentTime());
            policyPo.setThoroughInvalidDate(DateUtils.getCurrentTime());
            policyBoService.savePolicyPo(policyPo);

            //产生付费记录

            //修改佣金状态
            PolicyPaymentBo policyPaymentBo = policyExtDao.loadPolicyPaymentByPolicyId(policyId);
            CommissionPolicyCancelRequest commissionPolicyCancelRequest=new CommissionPolicyCancelRequest();
            commissionPolicyCancelRequest.setPolicyId(policyId);
            commissionPolicyCancelRequest.setPolicyPaymentId(policyPaymentBo.getPolicyPaymentId());
            resultObject = agentCommissionApi.revokeChannelCommission(commissionPolicyCancelRequest);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_FAIL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject getSafeguardingPolicy(String idNo) {
        ResultObject resultObject = new ResultObject<>();
        try {
            // 验证
            AssertUtils.isNotEmpty(this.getLogger(), idNo, CiqPolicyErrorConfigEnum.CIQ_POLICY_PARAMETER_ID_NO_IS_NOT_NULL);
            List<CiqRevokePolicyBo> ciqRevokePolicyBoList = ciqPolicyDao.getCiqPolicyRevokeOrigin(idNo);
            if (AssertUtils.isNotEmpty(ciqRevokePolicyBoList)) {
                ciqRevokePolicyBoList.forEach(ciqRevokePolicyRespFc -> {
                    if (AssertUtils.isNotNull(ciqRevokePolicyRespFc.getEffectiveDate())) {
                        if (DateUtils.getCurrentTime() - ciqRevokePolicyRespFc.getEffectiveDate() < 86400000) {
                            throw new RequestException(CiqPolicyErrorConfigEnum.CIQ_APPLY_BUSINESS_CIQ_POLICY_IS_EXIST);
                        }
                    }
                });
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_QUERY_POLICY_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<CiqRevokePolicyResponse> getRevocationPolicy(String idNo) {
        ResultObject<CiqRevokePolicyResponse> resultObject = new ResultObject<>();
        try {
            // 验证
            AssertUtils.isNotEmpty(this.getLogger(), idNo, CiqPolicyErrorConfigEnum.CIQ_POLICY_PARAMETER_ID_NO_IS_NOT_NULL);
            CiqRevokePolicyBo ciqRevokePolicyBo = ciqPolicyDao.getCiqPolicyRevoke(idNo);
            AssertUtils.isNotNull(getLogger(), ciqRevokePolicyBo, CiqPolicyErrorConfigEnum.CIQ_POLICY_QUERY_POLICY_REVOKE_IS_NOT_FOUND_OBJECT);
            CiqRevokePolicyResponse ciqRevokePolicyResponse = (CiqRevokePolicyResponse) this.converterObject(ciqRevokePolicyBo, CiqRevokePolicyResponse.class);
            ciqRevokePolicyResponse.setEffectiveDate(DateUtils.timeStrToString(ciqRevokePolicyBo.getEffectiveDate(), DateUtils.FORMATE25));
            resultObject.setData(ciqRevokePolicyResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_QUERY_POLICY_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<CiqRevokePolicyBo>> getRevocationPolicyOrigin(String idNo) {
        ResultObject<List<CiqRevokePolicyBo>> resultObject = new ResultObject<>();
        try {
            // 验证
            AssertUtils.isNotEmpty(this.getLogger(), idNo, CiqPolicyErrorConfigEnum.CIQ_POLICY_PARAMETER_ID_NO_IS_NOT_NULL);
            List<CiqRevokePolicyBo> ciqRevokePolicyBoList = ciqPolicyDao.getCiqPolicyRevokeOrigin(idNo);
            resultObject.setData(ciqRevokePolicyBoList);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_QUERY_POLICY_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<CiqRevokePolicyBo> terminatePolicy(String policyId) {
        ResultObject<CiqRevokePolicyBo> resultObject = new ResultObject<>();
        try {
            // 验证
            AssertUtils.isNotEmpty(this.getLogger(), policyId, CiqPolicyErrorConfigEnum.CIQ_POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);
            PolicyPo policyPo = policyDao.fetchOneByPolicyId(policyId);
            AssertUtils.isNotNull(getLogger(), policyPo, CiqPolicyErrorConfigEnum.CIQ_POLICY_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
            policyPo.setPolicyStatus(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID_THOROUGH.name());
            policyBoService.savePolicyPo(policyPo);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_TERMINATE_FAIL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<PolicyPo>> retrivePolicyBatch(Integer pageSize, Integer currentPage) {
        ResultObject<List<PolicyPo>> resultObject = new ResultObject<>();
        try {
            BasePageRequest basePageRequest = new BasePageRequest();
            basePageRequest.setCurrentPage(currentPage);
            basePageRequest.setPageSize(pageSize);
            List<PolicyPo> policyPoList = ciqPolicyDao.retrivePolicyBatch(basePageRequest);
            resultObject.setData(policyPoList);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_TERMINATE_FAIL);
            }
        }

        return resultObject;
    }

    /**
     * 批量终止保单
     *
     * @param policyIds 保单ID集
     * @return ResultObject
     */
    @Override
    public ResultObject terminatePolicyBatch(List<String> policyIds) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            // 验证
            AssertUtils.isNotEmpty(this.getLogger(), policyIds, CiqPolicyErrorConfigEnum.CIQ_POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);
            List<PolicyPo> policyPos = policyDao.fetchByPolicyId(policyIds.toArray(new String[0]));
            AssertUtils.isNotEmpty(getLogger(), policyPos, CiqPolicyErrorConfigEnum.CIQ_POLICY_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
            policyPos.forEach(policyPo -> policyPo.setPolicyStatus(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID_THOROUGH.name()));
            policyBoService.savePolicyPo(policyPos);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(CiqPolicyErrorConfigEnum.CIQ_POLICY_TERMINATE_FAIL);
            }
        }
        return resultObject;
    }
}
