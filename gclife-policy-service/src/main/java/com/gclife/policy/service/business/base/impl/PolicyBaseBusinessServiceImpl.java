package com.gclife.policy.service.business.base.impl;

import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.api.AgentCommissionApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.agent.model.response.AgentSimpleBaseResponse;
import com.gclife.agent.model.response.commission.CommissionSettlementConfigResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.party.api.CustomerBaseApi;
import com.gclife.party.api.CustomerManageApi;
import com.gclife.party.model.response.CustomerAgentResponse;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.dao.PolicyExtDao;
import com.gclife.policy.dao.PolicyQueryBaseDao;
import com.gclife.policy.dao.PolicySeeDao;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.request.*;
import com.gclife.policy.model.response.PolicyAgentHistoryResponse;
import com.gclife.policy.model.response.PolicyPartInfoResponse;
import com.gclife.policy.model.response.PolicyPaymentBusinessDataResponse;
import com.gclife.policy.model.response.PolicyResponse;
import com.gclife.policy.model.response.group.GroupPolicyResponse;
import com.gclife.policy.service.base.*;
import com.gclife.policy.service.business.base.PolicyBaseBusinessService;
import com.gclife.policy.transform.PolicyBaseCommisisonTransfer;
import com.gclife.policy.validate.transfer.PolicyPaymentTransData;
import com.gclife.policy.validate.transfer.PolicyTransData;
import com.gclife.product.api.ProductRateApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.insurance.policy.PolicyCoveragePaymentRequest;
import com.gclife.product.model.request.insurance.policy.PolicyPaymentRequest;
import com.gclife.product.model.response.insurnce.policy.PolicyPaymentResponse;
import com.gclife.renewal.api.RenewalBaseApi;
import com.gclife.renewal.model.response.RenewalResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.policy.model.config.PolicyErrorConfigEnum.*;
import static com.gclife.policy.model.config.PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS;

/**
 * <AUTHOR>
 * create 18-6-27
 * description:
 */
@Service
public class PolicyBaseBusinessServiceImpl extends BaseBusinessServiceImpl implements PolicyBaseBusinessService {
    @Autowired
    private PolicyBaseService policyBaseService;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PolicyQueryBaseDao policyQueryBaseDao;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private PolicyPaymentTransData policyPaymentTransData;
    @Autowired
    private PolicyExtDao policyExtDao;
    @Autowired
    private PolicyTransData policyTransData;
    @Autowired
    private AgentCommissionApi agentCommissionApi;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;
    @Autowired
    private ProductRateApi productRateApi;
    @Autowired
    private PolicyInsuredBaseService policyInsuredBaseService;
    @Autowired
    private PolicyCoverageBaseService policyCoverageBaseService;
    @Autowired
    private PolicyBeneficiaryBaseService policyBeneficiaryBaseService;
    @Autowired
    private PolicyPremiumBaseService policyPremiumBaseService;
    @Autowired
    private PolicyPaymentBaseService policyPaymentBaseService;
    @Autowired
    private RenewalBaseApi renewalBaseApi;
    @Autowired
    private PolicyApplicantBaseService policyApplicantBaseService;
    @Autowired
    private CustomerBaseApi customerBaseApi;
    @Autowired
    private CustomerManageApi customerManageApi;
    @Autowired
    private PolicySeeDao policySeeDao;
    @Autowired
    private PolicyBaseCommisisonTransfer policyBaseCommisisonTransfer;

    @Override
    public ResultObject<List<PolicyResponse>> queryPolicyBaseList(List<String> agentIds) {
        ResultObject<List<PolicyResponse>> resultObject = new ResultObject<>();
        List<PolicyResponse> policyResponses = new ArrayList<>();
        try {
            List<PolicyPo> policyPos = policyBaseService.listPolicyByAgentId(agentIds);
            if (AssertUtils.isNotEmpty(policyPos)) {
                policyResponses = (List<PolicyResponse>) this.converterList(policyPos, new TypeToken<List<PolicyResponse>>() {
                }.getType());
            }
            resultObject.setData(policyResponses);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyAgentHistoryResponse> queryPolicyAgentHistory(AppRequestHeads appRequestHeads, String policyId) {
        ResultObject<PolicyAgentHistoryResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            PolicyAgentHistoryPo policyAgentHistoryPo = policyBaseService.queryPolicyAgentHistoryByPolicyId(appRequestHeads, policyId);
            if (AssertUtils.isNotNull(policyAgentHistoryPo)) {
                resultObject.setData((PolicyAgentHistoryResponse) converterObject(policyAgentHistoryPo, PolicyAgentHistoryResponse.class));
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_AGENT_HISTORY_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyPo> queryOnePolicy(String policyId) {
        ResultObject<PolicyPo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);
            resultObject.setData(policyPo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 根据保单ID查询初始保单
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public ResultObject<PolicyPo> queryInitialPolicy(String policyId) {
        ResultObject<PolicyPo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);
            AssertUtils.isNotNull(getLogger(), policyPo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
            if (!policyPo.getPolicyNo().equals(policyPo.getFirstPolicyNo())) {
                // 查找初始保单
                PolicyPo firstPolicyPo = policyBaseService.queryPolicyByPolicyNo(policyPo.getFirstPolicyNo());
                AssertUtils.isNotNull(getLogger(), firstPolicyPo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
                resultObject.setData(firstPolicyPo);
            } else {
                resultObject.setData(policyPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(getLogger(), e, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyAgentPo> queryOnePolicyAgent(String policyId) {
        ResultObject<PolicyAgentPo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            PolicyAgentPo policyAgentPo = policyBaseService.queryPolicyAgent(policyId);
            resultObject.setData(policyAgentPo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_AGENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyApplicantBo> queryOnePolicyApplicant(String policyIdOrNo) {
        ResultObject<PolicyApplicantBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyIdOrNo, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            PolicyApplicantBo policyApplicantBo = policyBaseService.queryPolicyApplicant(policyIdOrNo);
            resultObject.setData(policyApplicantBo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_APPLICANT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<PolicyInsuredBo>> queryPolicyInsured(String policyId) {
        ResultObject<List<PolicyInsuredBo>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            List<PolicyInsuredBo> policyInsuredBoList = policyBaseService.listPolicyInsured(policyId);
            resultObject.setData(policyInsuredBoList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_INSURED_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyPaymentBo> queryOneNewPolicyPayment(String policyId) {
        ResultObject<PolicyPaymentBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            PolicyPaymentBo policyPaymentBo = policyBaseService.queryNewPolicyPayment(policyId);
            resultObject.setData(policyPaymentBo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_PREMIUM_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject updatePolicyAgent(String policyId, String agentId, boolean branchFlag) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), agentId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_AGENT_ID_IS_NOT_NULL);
            PolicyAgentPo policyAgentPo = policyBaseService.queryPolicyAgent(policyId);
            AssertUtils.isNotNull(getLogger(), policyAgentPo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_AGENT_IS_NOT_FOUND_OBJECT);
            ResultObject<AgentResponse> respFcResultObject = agentApi.agentByIdGet(agentId);
            AssertUtils.isResultObjectDataNull(getLogger(), respFcResultObject, PolicyErrorConfigEnum.POLICY_AGENT_IS_NOT_FOUND_OBJECT);
            if (policyAgentPo.getAgentCode().equals(respFcResultObject.getData().getAgentCode())) {
                return resultObject;
            }
            //存储代理人历史表
            PolicyAgentHistoryPo policyAgentHistoryPo = (PolicyAgentHistoryPo) converterObject(policyAgentPo, PolicyAgentHistoryPo.class);
            policyBaseService.savePolicyAgentHistory(policyAgentHistoryPo);

            //更新保单代理人
            policyAgentPo.setAgentId(respFcResultObject.getData().getAgentId());
            policyAgentPo.setAgentCode(respFcResultObject.getData().getAgentCode());
            policyBaseService.savePolicyAgent(policyAgentPo);

            //更新保单所属销售机构
            PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);
            policyPo.setSalesBranchId(respFcResultObject.getData().getBranchId());
            policyPo.setChannelTypeCode(respFcResultObject.getData().getChannelTypeCode());
            if (branchFlag) {
                policyPo.setFirstSalesBranchId(respFcResultObject.getData().getBranchId());
            }
            ResultObject<BranchResponse> branchResponseResultObject = platformBranchBaseApi.queryOneBranchById(respFcResultObject.getData().getBranchId());
            if (!AssertUtils.isResultObjectDataNull(branchResponseResultObject)) {
                policyPo.setManagerBranchId(branchResponseResultObject.getData().getManagerBranchId());
            }
            policyBaseService.savePolicyPo(policyPo);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_UPDATE_POLICY_AGENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<PolicyPaymentBo>> queryPolicyPayments(String policyId) {
        ResultObject<List<PolicyPaymentBo>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            List<PolicyPaymentBo> policyPaymentBos = policyBaseService.listPolicyPayment(policyId);
            PolicyPremiumBo policyPremiumBo = policyPremiumBaseService.queryPolicyPremiumBo(policyId);
            List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.queryPolicyCoverage(policyId);

            List<Long> frequency = new ArrayList<>();
            List<String> paymentBusinessType = new ArrayList<>();
            frequency.add(1L);
            paymentBusinessType.add(PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_NEW_CONTRACT.name());

            if (AssertUtils.isNotEmpty(policyCoveragePos)) {
                List<String> monthlyTripleProductId = Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_13.id());
                Optional<PolicyCoveragePo> first = policyCoveragePos.stream()
                        .filter(coverageBo -> monthlyTripleProductId.contains(coverageBo.getProductId()) &&
                                PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag()) &&
                                PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(coverageBo.getPremiumFrequency()))
                        .findFirst();
                if (first.isPresent()) {
                    frequency.add(2L);
                    frequency.add(3L);
                    paymentBusinessType.add(PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_RENEWAL.name());
                }
            }
            if (AssertUtils.isNotEmpty(policyPaymentBos)
                    && AssertUtils.isNotNull(policyPremiumBo)
                    && AssertUtils.isNotNull(policyPremiumBo.getPremiumBeforeDiscount())
                    && AssertUtils.isNotNull(policyPremiumBo.getDiscountType())
                    && AssertUtils.isNotNull(policyPremiumBo.getSpecialDiscount())
            ) {
                policyPaymentBos.forEach(policyPaymentBo -> {
                    if (frequency.contains(policyPaymentBo.getFrequency()) && paymentBusinessType.contains(policyPaymentBo.getPaymentBusinessType())) {
                        policyPaymentBo.setDiscountPremiumFlag(TerminologyConfigEnum.WHETHER.YES.name());
                        policyPaymentBo.setDiscountType(policyPremiumBo.getDiscountType());
                        policyPaymentBo.setPromotionType(policyPremiumBo.getPromotionType());
                        policyPaymentBo.setDiscountModel(policyPremiumBo.getDiscountModel());
                        policyPaymentBo.setPremiumBeforeDiscount(policyPaymentBo.getActualPremium());
                        if (!AssertUtils.isNotEmpty(policyPremiumBo.getDiscountModel()) || ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(policyPremiumBo.getDiscountModel())) {
                            BigDecimal premiumAfterDiscount = policyPaymentBo.getActualPremium()
                                    .multiply(new BigDecimal("1").subtract(policyPremiumBo.getSpecialDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            policyPaymentBo.setTotalPremium(premiumAfterDiscount);
                            policyPaymentBo.setActualPremium(premiumAfterDiscount);
                            policyPaymentBo.setSpecialDiscount(new BigDecimal(new BigDecimal("100").multiply(policyPremiumBo.getSpecialDiscount()).stripTrailingZeros().toPlainString()));
                        } else if (ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(policyPremiumBo.getDiscountModel())) {
                            policyPaymentBo.setTotalPremium(policyPremiumBo.getPremiumBeforeDiscount().subtract(policyPremiumBo.getSpecialDiscount()));
                            policyPaymentBo.setActualPremium(policyPremiumBo.getPremiumBeforeDiscount().subtract(policyPremiumBo.getSpecialDiscount()));
                            policyPaymentBo.setSpecialDiscount(policyPremiumBo.getSpecialDiscount());
                        }
                    }
                });
            }
            resultObject.setData(policyPaymentBos);
        } catch (Exception e) {
            e.printStackTrace();
            this.throwsException(this.getLogger(), e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询保单下所有的缴费信息
     *
     * @param policyId 保单ID
     * @return PolicyPaymentBos
     */
    @Override
    public ResultObject<List<PolicyPaymentBo>> listPolicyPayment(String policyId) {
        // 查询保单缴费信息
        List<PolicyPaymentBo> listPolicyPayment = policyPaymentBaseService.listPolicyPaymentBo(policyId);
        AssertUtils.isNotEmpty(getLogger(), listPolicyPayment, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_PAYMENT_IS_NOT_FOUND);

        ResultObject<List<PolicyPaymentBo>> resultObject = new ResultObject<>();
        resultObject.setData(listPolicyPayment);
        return resultObject;
    }

    /**
     * 查询客户下所有的缴费信息
     *
     * @param customerIds 客户
     * @return PolicyPaymentBos
     */
    @Override
    public ResultObject<List<PolicyPaymentBo>> listCustomerPolicyPayment(List<String> customerIds) {
        ResultObject<List<PolicyPaymentBo>> resultObject = new ResultObject<>();
        // 查询保单缴费信息
        List<PolicyPaymentBo> listPolicyPayment = policyPaymentBaseService.listCustomerPolicyPayment(customerIds);
        resultObject.setData(listPolicyPayment);
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject updatePolicyPayment(PolicyPaymentUpdateRequest policyPaymentUpdateRequest) {
        ResultObject<List<PolicyPaymentPo>> resultObject = new ResultObject<>();
        List<PolicyBo> policyBoList = new ArrayList<>();
        try {
            // 返回数据
            List<PolicyPaymentPo> policyPaymentPos = new ArrayList<>();

            AssertUtils.isNotEmpty(getLogger(), policyPaymentUpdateRequest.getPaymentIdList(), PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_PAYMENT_ID_LIST_IS_NOT_EMPTY);
            AssertUtils.isNotEmpty(getLogger(), policyPaymentUpdateRequest.getPaymentStatusCode(), PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_PAYMENT_STATUS_CODE_IS_NOT_NULL);
            List<PolicyPaymentBo> policyPaymentBoList = policyPaymentBaseService.listPolicyPaymentBo(policyPaymentUpdateRequest.getPaymentIdList());
            AssertUtils.isNotEmpty(getLogger(), policyPaymentBoList, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR);

            //续期附加险特殊处理
            boolean equalsPaymentSuccess = policyPaymentUpdateRequest.getPaymentStatusCode().equals(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name());
            if (PolicyTermEnum.RENEWAL_TYPE.RENEWAL.name().equals(policyPaymentUpdateRequest.getBusinessType())
                    && AssertUtils.isNotEmpty(policyPaymentUpdateRequest.getListAdditionCoverage())
                    && equalsPaymentSuccess) {
                this.resetRenewalAdditionCoverage(policyPaymentBoList, policyPaymentUpdateRequest);
            }

            if (equalsPaymentSuccess) {
                PolicyPo policyPo = policyBaseService.queryPolicyPo(policyPaymentBoList.get(0).getPolicyId());
                AssertUtils.isNotNull(getLogger(), policyPo, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_IS_NOT_NULL);
                PolicyBo policyBo = (PolicyBo) this.converterObject(policyPo, PolicyBo.class);
                // 查询保单代理人信息
                PolicyAgentPo applyAgentPo = policyBaseService.queryPolicyAgent(policyPaymentBoList.get(0).getPolicyId());
                if (AssertUtils.isNotNull(applyAgentPo)) {
                    PolicyAgentBo applyAgentBo = (PolicyAgentBo) this.converterObject(applyAgentPo, PolicyAgentBo.class);
                    policyBo.setPolicyAgent(applyAgentBo);
                }
                // 查询保单投保人信息
                PolicyApplicantBo applyApplicantBo = policyBaseService.queryPolicyApplicant(policyPaymentBoList.get(0).getPolicyId());
                if (AssertUtils.isNotNull(applyApplicantBo)) {
                    policyBo.setPolicyApplicant(applyApplicantBo);
                }
                policyBoList.add(policyBo);
            }

            if (PolicyTermEnum.BUSINESS_TYPE.REINSTATEMENT.name().equals(policyPaymentUpdateRequest.getBusinessType()) &&
                    PolicyTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name().equals(policyPaymentUpdateRequest.getPaymentStatusCode())) {
                // 复效流程结束
                List<PolicyPaymentBo> policyPaymentBos = policyBaseService.listPolicyPayment(policyPaymentBoList.get(0).getPolicyId());
                // 提前产生的缴费信息状态也要改为“待缴费”
                policyPaymentBos.forEach(policyPaymentBo -> {
                    if (DateUtils.timeToTimeLow(policyPaymentBo.getReceivableDate()) > DateUtils.getCurrentTime()) {
                        List<PolicyCoveragePaymentBo> policyCoveragePaymentBos = policyPaymentBaseService.listPolicyCoveragePayment(policyPaymentBo.getPolicyPaymentId());
                        policyPaymentBo.setListPolicyCoveragePayment(policyCoveragePaymentBos);
                        policyPaymentBoList.add(policyPaymentBo);

                        // 保存保单操作表
                        PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyPaymentBo.getPolicyId());
                        if (!AssertUtils.isNotNull(policyOperationPo)) {
                            policyOperationPo = new PolicyOperationPo();
                            policyOperationPo.setPolicyId(policyPaymentBo.getPolicyId());
                            policyOperationPo.setOperationCode(PolicyTermEnum.OPERATION_CODE.RENEWAL_PENDING_PAYMENT.name());
                            policyBaseService.savePolicyOperation(policyOperationPo);
                        } else if (policyOperationPo.getOperationCode().equals(PolicyTermEnum.OPERATION_CODE.RENEWAL_FINISHED.name())) {
                            // 续期完成
                            policyOperationPo.setPolicyId(policyPaymentBo.getPolicyId());
                            policyOperationPo.setOperationCode(PolicyTermEnum.OPERATION_CODE.RENEWAL_PENDING_PAYMENT.name());
                            policyBaseService.savePolicyOperation(policyOperationPo);
                        }
                    }
                });
            }

            // 查询保费及险种保费信息
            PolicyPremiumBo policyPremiumBo = policyPremiumBaseService.queryPolicyPremiumBo(policyPaymentBoList.get(0).getPolicyId());


            policyPaymentBoList.forEach(policyPaymentBo -> {
                // 设置返回数据
                PolicyPaymentPo policyPaymentPo = new PolicyPaymentPo();
                ClazzUtils.copyPropertiesIgnoreNull(policyPaymentBo, policyPaymentPo);
                policyPaymentPos.add(policyPaymentPo);

                policyPaymentBo.setPaymentStatusCode(policyPaymentUpdateRequest.getPaymentStatusCode());
                if (AssertUtils.isNotEmpty(policyPaymentUpdateRequest.getPaymentModeCode())) {
                    policyPaymentBo.setPaymentModeCode(policyPaymentUpdateRequest.getPaymentModeCode());
                }
                policyBaseService.savePolicyPayment(policyPaymentBo);
                //支付成功产生佣金
                if (equalsPaymentSuccess) {
                    //设置业务日期
                    if (DateUtils.getCurrentTime() > DateUtils.timeToTimeLow(policyPaymentBo.getReceivableDate())) {
                        policyPaymentBo.setBizDate(DateUtils.getCurrentTime());
                    } else {
                        policyPaymentBo.setBizDate(policyPaymentBo.getReceivableDate());
                    }
                    policyPaymentBo.setBizYearMonth(DateUtils.getTimeYearMonth(policyPaymentBo.getBizDate()));
                    policyPaymentBo.setGainedDate(DateUtils.getCurrentTime());
                    if (AssertUtils.isNotNull(policyPaymentUpdateRequest.getActualPayDate())) {
                        policyPaymentBo.setGainedDate(policyPaymentUpdateRequest.getActualPayDate());
                    }
                    if (!AssertUtils.isNotNull(policyPaymentBo.getActualPremium()) || policyPaymentBo.getActualPremium().compareTo(BigDecimal.ZERO) == 0) {
                        policyPaymentBo.setActualPremium(policyPaymentBo.getTotalPremium());
                    }
                    //险种缴费
                    List<PolicyCoveragePaymentBo> policyCoveragePaymentBoList = policyPaymentBo.getListPolicyCoveragePayment();
                    AssertUtils.isNotEmpty(getLogger(), policyCoveragePaymentBoList, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_PAYMENT_ERROR);
                    policyCoveragePaymentBoList.forEach(policyCoveragePaymentBo -> {
                        long months = DateUtils.intervalMonth(policyCoveragePaymentBo.getEffectiveDate(), policyPaymentBo.getReceivableDate());
                        policyCoveragePaymentBo.setCoverageYear(months / 12 + 1);

                        if (!AssertUtils.isNotNull(policyCoveragePaymentBo.getActualPremium()) || policyCoveragePaymentBo.getActualPremium().compareTo(BigDecimal.ZERO) == 0) {
                            policyCoveragePaymentBo.setActualPremium(policyCoveragePaymentBo.getTotalPremium());
                        }
                    });
                    //计算费率
                    PolicyPaymentRequest policyPaymentRequest = (PolicyPaymentRequest) this.converterObject(policyPaymentBo, PolicyPaymentRequest.class);
                    policyPaymentRequest.setListPolicyCoveragePayment((List<PolicyCoveragePaymentRequest>) this.converterList(policyCoveragePaymentBoList, new TypeToken<List<PolicyCoveragePaymentRequest>>() {
                    }.getType()));
                    //佣金机构只和出单时的机构有关 6.8.6->对于这个销售执行渠道，销售人员将来可能会变更渠道，因此佣金率将按照最新的渠道
                    PolicyPo policyPo = policyBaseService.queryPolicyPo(policyPaymentBo.getPolicyId());
                    if (AssertUtils.isNotNull(policyPo) && AssertUtils.isNotEmpty(policyPo.getFirstSalesBranchId()) && !PolicyTermEnum.CHANNEL_TYPE.GMSE.name().equals(policyPo.getChannelTypeCode())) {
                        policyPaymentRequest.setBizBranchId(policyPo.getFirstSalesBranchId());
                    }
                    ResultObject<PolicyPaymentResponse> reqFcResultObject = productRateApi.rateCalculationRate(policyPaymentRequest);
                    AssertUtils.isResultObjectDataNull(getLogger(), reqFcResultObject);

                    //保存缴费信息
                    policyBaseService.savePolicyPayment(policyPaymentTransData.transPolicyPaymentPo(policyPaymentBo, reqFcResultObject.getData()));
                    policyCoveragePaymentBoList.forEach(policyCoveragePaymentBo -> {
                        List<PolicyCoveragePaymentRequest> policyCoveragePaymentReqFcList = reqFcResultObject.getData().getListPolicyCoveragePayment();
                        policyCoveragePaymentReqFcList.stream().filter(policyCoveragePaymentReqFc ->
                                policyCoveragePaymentBo.getPolicyCoveragePaymentId().equals(policyCoveragePaymentReqFc.getPolicyCoveragePaymentId())).findFirst()
                                .ifPresent((a) ->
                                        policyCoverageBaseService.savePolicyCoveragePayment(policyPaymentTransData.transPolicyCoveragePaymentPo(policyCoveragePaymentBo, a))
                                );

                        policyPremiumBo.getListCoveragePremium().stream()
                                .filter(coveragePremiumPo -> coveragePremiumPo.getPolicyCoveragePremiumId().equals(policyCoveragePaymentBo.getPolicyCoveragePremiumId()))
                                .findFirst().ifPresent(coveragePremiumPo -> {
                            // 更新总实缴保费
                            coveragePremiumPo.setTotalActualPremium(coveragePremiumPo.getTotalActualPremium().add(policyCoveragePaymentBo.getActualPremium()));
                            policyPremiumBaseService.savePolicyCoveragePremium(coveragePremiumPo);
                        });
                    });

                    // 更新总实缴保费
                    policyPremiumBo.setTotalActualPremium(policyPremiumBo.getTotalActualPremium().add(policyPaymentBo.getActualPremium()));
                }
            });

            //设置保费备注
            if (AssertUtils.isNotEmpty(policyPaymentUpdateRequest.getRemark())) {
                policyPremiumBo.setPremiumStatus(policyPaymentUpdateRequest.getPaymentStatusCode());
                policyPremiumBo.setRemark(policyPaymentUpdateRequest.getRemark());
            }

            if (AssertUtils.isNotEmpty(policyPaymentUpdateRequest.getRemark()) || equalsPaymentSuccess) {
                policyPremiumBaseService.savePolicyPremium(policyPremiumBo);
            }

            //查询是否还有待支付的续期
            String paymentBusinessType = PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_RENEWAL.name();
            String operationCode = PolicyTermEnum.OPERATION_CODE.RENEWAL_FINISHED.name();
            if (PolicyTermEnum.RENEWAL_TYPE.GROUP_INSTALLMENT.name().equals(policyPaymentUpdateRequest.getBusinessType())) {
                paymentBusinessType = PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_GROUP_INSTALLMENT.name();
                operationCode = PolicyTermEnum.OPERATION_CODE.GROUP_INSTALLMENT_FINISHED.name();
            }
            List<PolicyPaymentPo> policyPaymentBos = policyBaseService.queryPolicyPaymentPo(policyPaymentBoList.get(0).getPolicyId());
            List<String> paymentStatus = Arrays.asList(PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name(), PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FAILED.name(),
                    PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT_PASS.name(), PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT_NOPASS.name());
            String finalPaymentBusinessType = paymentBusinessType;
            List<PolicyPaymentPo> waitingPolicyPaymentBos = policyPaymentBos.stream()
                    .filter(policyPaymentPo -> paymentStatus.contains(policyPaymentPo.getPaymentStatusCode())
                            && finalPaymentBusinessType.equals(policyPaymentPo.getPaymentBusinessType()))
                    .collect(Collectors.toList());
            if (equalsPaymentSuccess && !AssertUtils.isNotEmpty(waitingPolicyPaymentBos)) {
                updatePolicyOperation(policyPaymentBoList.get(0).getPolicyId(), operationCode);
            }
            //将本次以外的待缴费的续期支付方式置空
            if (AssertUtils.isNotEmpty(waitingPolicyPaymentBos)) {
                waitingPolicyPaymentBos.stream().filter(policyPaymentPo -> !policyPaymentUpdateRequest.getPaymentIdList().contains(policyPaymentPo.getPolicyPaymentId()))
                        .forEach(policyPaymentPo -> {
                            policyPaymentPo.setPaymentModeCode(null);
                            policyBaseService.savePolicyPayment(policyPaymentPo);
                        });
            }
            resultObject.setData(policyPaymentPos);
        } catch (Exception e) {
            e.printStackTrace();
            this.throwsTransactionalException(this.getLogger(), e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_PAYMENT_ERROR);
        }
        return resultObject;
    }

    private void resetRenewalAdditionCoverage(List<PolicyPaymentBo> policyPaymentBoList, PolicyPaymentUpdateRequest policyPaymentUpdateRequest) {
        String policyId = policyPaymentBoList.get(0).getPolicyId();
        List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.queryPolicyCoverage(policyId);
        // 查询保单数据,待备份
        PolicyBo policyBo = policyExtDao.loadPolicyByPolicyId(policyId);
        final Long[] dataEffectiveDate = {null};
        final boolean[] changeCoverageFlag = {false};

        List<RenewalAdditionCoverageRequest> listAdditionCoverage = policyPaymentUpdateRequest.getListAdditionCoverage();
        listAdditionCoverage.forEach(renewalAdditionCoverageRequest -> {
            policyCoveragePos.stream().filter(policyCoveragePo -> policyCoveragePo.getPolicyId().equals(renewalAdditionCoverageRequest.getPolicyId())
                    && policyCoveragePo.getProductId().equals(renewalAdditionCoverageRequest.getProductId())).findFirst().ifPresent(policyCoveragePo -> {
                //短期附加险才操作
                if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyCoveragePo.getPremiumFrequency())
                        || ("1".equals(policyCoveragePo.getCoveragePeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoveragePo.getCoveragePeriodUnit()))) {
                    //续保，更新险种满期日期
                    if (TerminologyConfigEnum.WHETHER.YES.name().equals(renewalAdditionCoverageRequest.getRenewalPermitFlag())) {
                        // 重置短期险保障日期
                        if (policyCoveragePo.getCoveragePeriodStartDate() < DateUtils.getCurrentTime()) {
                            // 保障开始日期
                            long coveragePeriodStartDate = policyCoveragePo.getCoveragePeriodEndDate() + 1;
                            // 保障结束日期
                            long coveragePeriodEndDate = DateUtils.addStringYearsRT(policyCoveragePo.getCoveragePeriodEndDate(), 1);
                            policyCoveragePo.setCoveragePeriodStartDate(coveragePeriodStartDate);
                            policyCoveragePo.setCoveragePeriodEndDate(coveragePeriodEndDate);
                            policyCoveragePo.setMaturityDate(coveragePeriodEndDate);
                            policyCoveragePo.setCoverageStatus(PolicyTermEnum.COVERAGE_STATUS.EFFECTIVE.name());
                        }
                    } else {
                        //不续保,重算保费
                        //1.已过满期日期 需要重新算费
                        if (DateUtils.getCurrentTime() > policyCoveragePo.getMaturityDate()) {
                            // 查询保单保费信息
                            PolicyPremiumBo policyPremiumBo = policyPremiumBaseService.queryPolicyPremiumBo(policyId);
                            List<PolicyCoveragePremiumPo> policyCoveragePremiumPos = new ArrayList<>();
                            final BigDecimal[] periodOriginalPremium = {BigDecimal.ZERO};
                            final BigDecimal[] periodTotalPremium = {BigDecimal.ZERO};
                            policyPremiumBo.getListCoveragePremium().forEach(policyCoveragePremiumPo -> {
                                if (policyCoveragePo.getCoverageId().equals(policyCoveragePremiumPo.getCoverageId())) {
                                    policyCoveragePremiumPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                                    getLogger().info("不续保的policyCoveragePremiumPo:{}", JackSonUtils.toJson(policyCoveragePremiumPo));
                                    policyCoveragePremiumPos.add(policyCoveragePremiumPo);
                                } else {
                                    periodOriginalPremium[0] = periodOriginalPremium[0].add(policyCoveragePremiumPo.getPeriodOriginalPremium());
                                    periodTotalPremium[0] = periodTotalPremium[0].add(policyCoveragePremiumPo.getPeriodTotalPremium());
                                }
                            });
                            if (AssertUtils.isNotEmpty(policyCoveragePremiumPos)) {
                                policyPremiumBo.setPeriodOriginalPremium(periodOriginalPremium[0].compareTo(BigDecimal.ZERO) == 0 ? policyPremiumBo.getPeriodOriginalPremium() : periodOriginalPremium[0]);
                                policyPremiumBo.setPeriodTotalPremium(periodTotalPremium[0].compareTo(BigDecimal.ZERO) == 0 ? policyPremiumBo.getPeriodTotalPremium() : periodTotalPremium[0]);
                                // 更新保单保费信息
                                policyPremiumBaseService.savePolicyPremium(policyPremiumBo, null);
                                // 更新险种保费信息
                                policyPremiumBaseService.updatePolicyCoveragePremium(policyCoveragePremiumPos, null);
                            }
                            policyCoveragePo.setCoverageStatus(PolicyTermEnum.COVERAGE_STATUS.INVALID.name());
                            policyCoveragePo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                            changeCoverageFlag[0] = true;
                            dataEffectiveDate[0] = policyCoveragePo.getMaturityDate() + 1;
                        }
                        policyPaymentBoList.forEach(policyPaymentBo -> {
                            final BigDecimal[] actualPremiumPayment = {BigDecimal.ZERO};
                            List<PolicyCoveragePaymentBo> listPolicyCoveragePayment = policyPaymentBo.getListPolicyCoveragePayment();
                            listPolicyCoveragePayment.forEach(policyCoveragePaymentBo -> {
                                if (policyCoveragePo.getCoverageId().equals(policyCoveragePaymentBo.getCoverageId())) {
                                    policyCoveragePaymentBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                                    getLogger().info("不续保的policyCoveragePaymentBo:{}", JackSonUtils.toJson(policyCoveragePaymentBo));
                                    policyPaymentBaseService.savePolicyCoveragePayment(policyCoveragePaymentBo, null);
                                } else {
                                    actualPremiumPayment[0] = actualPremiumPayment[0].add(policyCoveragePaymentBo.getPeriodOriginalPremium());
                                }
                            });
                            policyPaymentBo.setActualPremium(actualPremiumPayment[0].compareTo(BigDecimal.ZERO) == 0 ? policyPaymentBo.getTotalPremium() : actualPremiumPayment[0]);
                            policyPaymentBo.setPeriodActualPremium(actualPremiumPayment[0].compareTo(BigDecimal.ZERO) == 0 ? policyPaymentBo.getTotalPremium() : actualPremiumPayment[0]);
                            policyBaseService.savePolicyPayment(policyPaymentBo);

                            //移除掉失效的险种缴费信息,以便后续算佣金
                            policyPaymentBo.getListPolicyCoveragePayment().removeIf(policyCoveragePaymentBo -> policyCoveragePaymentBo.getValidFlag().equals(TerminologyConfigEnum.VALID_FLAG.invalid.name()));
                        });


                    }
                    policyCoveragePo.setRenewalPermitFlag(renewalAdditionCoverageRequest.getRenewalPermitFlag());
                    policyCoverageBaseService.savePolicyCoverage(policyCoveragePo);
                }
            });
        });
        getLogger().info("policyPaymentBoList:{}", JackSonUtils.toJson(policyPaymentBoList));
        if (changeCoverageFlag[0]) {
            getLogger().info("变更险种，开始备份保单");
            AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
            // 备份保单信息
            policyTransData.savePolicyBaseHistoryData(policyBo);

            //产生新保单版本
            String newVersionNo = DateUtils.getJobNumberByTime("", "", DateUtils.FORMATE53, false);
            policyBo.setVersionNo(newVersionNo);
            policyBo.setDataEffectiveDate(AssertUtils.isNotNull(dataEffectiveDate[0]) ? dataEffectiveDate[0] : DateUtils.getCurrentTime());

            policyBaseService.savePolicy(policyBo, null);
            getLogger().info("变更险种，备份保单结束");
        }
    }

    /**
     * 回滚保单缴费信息
     *
     * @param policyPaymentPos 保单缴费信息
     * @return
     */
    @Override
    @Transactional
    public ResultObject rollbackPolicyPayment(List<PolicyPaymentPo> policyPaymentPos) {
        ResultObject resultObject = new ResultObject();
        try {
            if (AssertUtils.isNotEmpty(policyPaymentPos)) {
                // 回滚缴费数据
                policyBaseService.updatePolicyPayment("", policyPaymentPos);
                // 回滚佣金
                policyPaymentPos.forEach(policyPaymentPo -> {
                    PolicyPo policyPo = policyBaseService.queryPolicyPo(policyPaymentPo.getPolicyId());
                    // 判断渠道佣金和个代佣金
                    CommissionSettlementConfigResponse commissionSettlementConfigRespFc = agentCommissionApi.loadCommissionSettlementConfig(policyPo.getSalesBranchId()).getData();
                    if (AssertUtils.isNotNull(commissionSettlementConfigRespFc)) {
                        if (PolicyTermEnum.COMMISSION_SETTLEMENT_TYPE.CHANNEL.name().equals(commissionSettlementConfigRespFc.getSettlementCode())) {
                            // 发佣保单删除(渠道)
                            agentCommissionApi.deleteChannelCommission(policyPaymentPo.getPolicyId(), policyPaymentPo.getPolicyPaymentId());
                        } else if (PolicyTermEnum.COMMISSION_SETTLEMENT_TYPE.AGENT.name().equals(commissionSettlementConfigRespFc.getSettlementCode())) {
                            // 发佣保单删除
                            agentCommissionApi.deleteCommission(policyPaymentPo.getPolicyId(), policyPaymentPo.getPolicyPaymentId());
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            setTransactionalResultObjectException(this.getLogger(), resultObject, e, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_ROOLBACK_PAYMENT_ERROR);
        }
        return resultObject;
    }

    /**
     * 更新险种状态
     * @param coverageUpdateRequest 待更新险种信息
     * @param users 用户
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject updateCoverage(CoverageUpdateRequest coverageUpdateRequest, Users users) {
        String policyId = coverageUpdateRequest.getPolicyId();
        AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
        // 查询保单数据
        PolicyBo policyBo = policyExtDao.loadPolicyByPolicyId(policyId);
        AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
        // 备份保单信息
        policyTransData.savePolicyBaseHistoryData(policyBo);
        // 更新险种状态
        List<PolicyCoveragePo> policyCoveragePos = new ArrayList<>();
        policyBo.getListInsuredCoverage().forEach(policyCoverageBo -> {
            coverageUpdateRequest.getCoverageIds().stream()
                    .filter(coverageId -> coverageId.equals(policyCoverageBo.getCoverageId()))
                    .findFirst().ifPresent(coverageId -> {
                policyCoverageBo.setCoverageStatus(coverageUpdateRequest.getCoverageStatus());
                policyCoveragePos.add(policyCoverageBo);
            });
        });
        policyCoverageBaseService.updatePolicyCoverage(policyCoveragePos, users.getUserId());

        return ResultObject.success();
    }

    /**
     * 更新保单状态
     *
     * @param policyUpdateRequest 待更新保单信息
     * @return
     */
    @Override
    @Transactional
    public ResultObject updatePolicy(PolicyUpdateRequest policyUpdateRequest) {
        ResultObject resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotNull(this.getLogger(), policyUpdateRequest, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), policyUpdateRequest.getPolicyId(), PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), policyUpdateRequest.getPolicyStatus(), PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_STATUS_IS_NOT_NULL);

            // 查询保单数据
            PolicyBo policyBo = policyExtDao.loadPolicyByPolicyId(policyUpdateRequest.getPolicyId());
            AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
            if (policyBo.getPolicyStatus().equals(policyUpdateRequest.getPolicyStatus())) {
                // 状态未变更，不作处理
                return resultObject;
            }
            // 备份保单信息
            policyTransData.savePolicyBaseHistoryData(policyBo);

            ClazzUtils.copyPropertiesIgnoreNull(policyUpdateRequest, policyBo);
            //产生新保单版本
            String newVersionNo = DateUtils.getJobNumberByTime("", "", DateUtils.FORMATE53, false);
            policyBo.setVersionNo(newVersionNo);
            policyBo.setDataEffectiveDate(DateUtils.getCurrentTime());


            if (PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name().equals(policyUpdateRequest.getPolicyStatus())) {
                //失效变成保障中 失效时间清空
                policyBo.setInvalidDate(null);
                policyBo.setThoroughInvalidDate(null);
                //还有待支付的续期，增加操作表记录
                boolean[] payFlag = new boolean[1];
                ResultObject<List<RenewalResponse>> resultObject1 = renewalBaseApi.queryRenewalByPolicyId(policyBo.getPolicyId());
                if (!AssertUtils.isResultObjectListDataNull(resultObject1)) {
                    resultObject1.getData().forEach(renewalResponse -> {
                        if (renewalResponse.getRenewalStatus().equals(PolicyTermEnum.RENEWAL_STATUS.PAYMENT.name())) {
                            //还有待缴费的续期
                            payFlag[0] = true;
                        }
                    });
                    if (payFlag[0]) {
                        PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyBo.getPolicyId());
                        if (!AssertUtils.isNotNull(policyOperationPo)) {
                            policyOperationPo = new PolicyOperationPo();
                            policyOperationPo.setPolicyId(policyBo.getPolicyId());
                        }
                        policyOperationPo.setOperationCode(PolicyTermEnum.OPERATION_CODE.RENEWAL_PENDING_PAYMENT.name());
                        policyBaseService.savePolicyOperation(policyOperationPo);
                    }
                }
            }
            policyBaseService.savePolicyPo(policyBo);
            //TODO 暂定两种失效状态作废操作表
            if (PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_IEXPIRE.name().equals(policyUpdateRequest.getPolicyStatus())
                    || PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID.name().equals(policyUpdateRequest.getPolicyStatus())) {
                // 保单效力终止，失效保单操作
                PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyBo.getPolicyId());
                if (AssertUtils.isNotNull(policyOperationPo)) {
                    policyOperationPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                    policyBaseService.savePolicyOperation(policyOperationPo);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.throwsTransactionalException(this.getLogger(), e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyCoveragePo> queryOneMainPolicyCoverage(String policyId) {
        ResultObject<PolicyCoveragePo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            List<PolicyCoveragePo> policyCoveragePoList = policyCoverageBaseService.listPolicyCoverageOfInsured(policyId);
            AssertUtils.isNotEmpty(getLogger(), policyCoveragePoList, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_COVERAGE_ERROR);
            policyCoveragePoList.forEach(policyCoveragePo -> {
                if (policyCoveragePo.getPrimaryFlag().equals(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                    resultObject.setData(policyCoveragePo);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_COVERAGE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyPremiumBo> queryPolicyPremium(String policyId) {
        ResultObject<PolicyPremiumBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            resultObject.setData(policyPremiumBaseService.queryPolicyPremium(policyId));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_PREMIUM_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject updatePolicyPremium(PolicyPremiumUpdateRequest policyPremiumUpdateRequest) {
        ResultObject resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(getLogger(), policyPremiumUpdateRequest.getPolicyId(), PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), policyPremiumUpdateRequest.getPolicyStatus(), PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_STATUS_IS_NOT_NULL);

            // 查询保单数据
            PolicyPremiumBo policyPremiumBo = policyPremiumBaseService.queryPolicyPremium(policyPremiumUpdateRequest.getPolicyId());
            // 业务校验
            AssertUtils.isNotNull(this.getLogger(), policyPremiumBo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_PREMIUM_IS_NOT_FOUND_OBJECT);
            policyPremiumBo.setPremiumStatus(policyPremiumUpdateRequest.getPolicyStatus());
            policyPremiumBaseService.savePolicyPremium(policyPremiumBo);

        } catch (Exception e) {
            e.printStackTrace();
            this.throwsTransactionalException(this.getLogger(), e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_PREMIUM_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询保单支付冗余业务数据
     *
     * @param policyId 保单ID
     * @return ResultObject
     */
    @Override
    public ResultObject<PolicyPaymentBusinessDataResponse> queryOnePolicyPaymentBusinessData(String policyId) {
        ResultObject<PolicyPaymentBusinessDataResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            PolicyPaymentBusinessDataResponse policyPaymentBusinessDataResponse = policyExtDao.queryOnePolicyPaymentBusinessData(policyId);
            resultObject.setData(policyPaymentBusinessDataResponse);
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(), resultObject, e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<BaseOperationPo> queryPolicyOperation(String policyId) {
        ResultObject<BaseOperationPo> resultObject = new ResultObject<>();
        AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
        PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyId);
        if (AssertUtils.isNotNull(policyOperationPo)) {
            resultObject.setData(policyBaseService.queryBaseOperationPoByOperationCode(policyOperationPo.getOperationCode()));
        }
        return resultObject;
    }

    /**
     * 根据保单ID查询部分保单信息(包含投保人、被保人及被保人险种、缴费信息、保费信息)
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public ResultObject<PolicyPartInfoResponse> queryPolicyPartInfo(String policyId) {
        ResultObject<PolicyPartInfoResponse> resultObject = new ResultObject<>();
        AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
        // 查询保单信息
        PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);
        AssertUtils.isNotNull(getLogger(), policyPo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);

        PolicyPartInfoResponse policyPartInfoResponse = (PolicyPartInfoResponse) this.converterObject(policyPo, PolicyPartInfoResponse.class);

        // 查询投保人
        PolicyApplicantBo policyApplicantBo = policyBaseService.queryPolicyApplicant(policyId);
        AssertUtils.isNotNull(getLogger(), policyApplicantBo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_APPLICANT_IS_NOT_FOUND_OBJECT);
        policyPartInfoResponse.setApplicant(policyApplicantBo);

        // 查询被保人（包含险种信息）
        List<PolicyInsuredBo> policyInsuredBos = policyBaseService.listPolicyInsured(policyId);
        AssertUtils.isNotEmpty(getLogger(), policyInsuredBos, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_INSURED_IS_NOT_FOUND_OBJECT);
        policyPartInfoResponse.setListInsured(policyInsuredBos);

        // 查询保单缴费信息
        List<PolicyPaymentBo> listPolicyPayment = policyBaseService.listPolicyPayment(policyId);
        AssertUtils.isNotEmpty(getLogger(), listPolicyPayment, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_PAYMENT_IS_NOT_FOUND);

        // 查询险种缴费信息
        listPolicyPayment.forEach(policyPaymentBo -> {
            List<PolicyCoveragePaymentBo> policyCoveragePaymentBos = policyCoverageBaseService.queryPolicyCoveragePaymentBo(null, policyPaymentBo.getPolicyPaymentId());
            policyPaymentBo.setListPolicyCoveragePayment(policyCoveragePaymentBos);
        });
        policyPartInfoResponse.setListPolicyPayment(listPolicyPayment);

        // 查询保单保费信息
        PolicyPremiumBo policyPremium = policyPremiumBaseService.queryPolicyPremiumBo(policyId);
        policyPartInfoResponse.setPolicyPremium(policyPremium);

        resultObject.setData(policyPartInfoResponse);

        return resultObject;
    }

    /**
     * 查询待生效险种扩展信息
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public ResultObject<List<PolicyCoverageExtendPo>> listPendingCoverageExtend(String policyId) {
        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), policyId, PolicyErrorConfigEnum.POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);

        ResultObject<List<PolicyCoverageExtendPo>> resultObject = new ResultObject<>();
        List<PolicyCoverageExtendPo> policyCoverageExtendPos = policyCoverageBaseService.listPendingCoverageExtend(policyId);
        AssertUtils.isNotEmpty(this.getLogger(), policyCoverageExtendPos, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_COVERAGE_IS_NOT_FOUND_OBJECT);

        resultObject.setData(policyCoverageExtendPos);

        return resultObject;
    }

    /**
     * 更新保单操作
     *
     * @param policyId      保单ID
     * @param operationCode 操作编码
     * @return
     */
    @Override
    @Transactional
    public ResultObject updatePolicyOperation(String policyId, String operationCode) {
        AssertUtils.isNotEmpty(this.getLogger(), policyId, PolicyErrorConfigEnum.POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), operationCode, PolicyErrorConfigEnum.POLICY_PARAMETER_OPERATION_CODE_IS_NOT_NULL);

        ResultObject resultObject = new ResultObject();
        // 查询数据
        PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyId);

        if (!AssertUtils.isNotNull(policyOperationPo)) {
            policyOperationPo = new PolicyOperationPo();
            policyOperationPo.setPolicyId(policyId);
        }
        policyOperationPo.setOperationCode(operationCode);
        policyBaseService.savePolicyOperation(policyOperationPo);
        return resultObject;
    }

    /**
     * 失效保单操作
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public ResultObject invalidPolicyOperation(String policyId) {
        PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyId);
        if (AssertUtils.isNotNull(policyOperationPo)) {
            policyOperationPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
            policyBaseService.savePolicyOperation(policyOperationPo);
        }
        return ResultObject.success();
    }

    /**
     * 查询保单险种信息
     *
     * @param policyId
     * @return
     */
    @Override
    public ResultObject<List<PolicyCoveragePo>> queryPolicyCoverage(String policyId) {
        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), policyId, PolicyErrorConfigEnum.POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);

        ResultObject<List<PolicyCoveragePo>> resultObject = new ResultObject<>();
        List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.queryPolicyCoverage(policyId);
        AssertUtils.isNotEmpty(this.getLogger(), policyCoveragePos, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_COVERAGE_IS_NOT_FOUND_OBJECT);

        resultObject.setData(policyCoveragePos);

        return resultObject;

    }

    /**
     * 根据险种ID查询险种扩展信息
     *
     * @param coverageIds 险种id
     * @return
     */
    @Override
    public ResultObject<List<PolicyCoverageExtendPo>> listCoverageExtendByCoverageId(List<String> coverageIds) {
        ResultObject<List<PolicyCoverageExtendPo>> resultObject = new ResultObject<>();
        // 查询扩展表中续保险种数据
        List<PolicyCoverageExtendPo> coverageExtendPoList = policyCoverageBaseService.listCoverageExtendByCoverageId(coverageIds, PolicyTermEnum.BUSINESS_TYPE.RENEWAL_INSURANCE.name());
        resultObject.setData(coverageExtendPoList);
        return resultObject;
    }


    /**
     * 查询团险保单信息
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public ResultObject<GroupPolicyResponse> queryGroupPolicyInfo(String policyId) {
        // 查询保单信息
        PolicyBo policyBo = policyBaseService.queryPolicyBo(policyId);
        AssertUtils.isNotNull(getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);

        GroupPolicyResponse groupPolicyResponse = (GroupPolicyResponse) this.converterObject(policyBo, GroupPolicyResponse.class);

        // 查询保单公共险种
        List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.listGroupPolicyCoverage(policyId);
        groupPolicyResponse.setListCoverage(policyCoveragePos);

        // 查询被保人扩展信息
        List<PolicyInsuredExtendPo> listPolicyInsuredExtend = policyInsuredBaseService.listPolicyInsuredExtend(Collections.singletonList(policyId));
        groupPolicyResponse.setListPolicyInsuredExtend(listPolicyInsuredExtend);

        // 查询被保人统计信息
        PolicyInsuredCollectPo policyInsuredCollect = policyInsuredBaseService.queryPolicyInsuredCollect(policyId);
        groupPolicyResponse.setPolicyInsuredCollect(policyInsuredCollect);

        // 查询受益人信息
        List<PolicyBeneficiaryInfoBo> policyBeneficiaryInfoBos = policyBeneficiaryBaseService.listBeneficiaryInfoBo(policyId);
        if (AssertUtils.isNotEmpty(policyBeneficiaryInfoBos)) {
            Map<String, List<PolicyBeneficiaryInfoBo>> beneficiaryMap = policyBeneficiaryInfoBos.parallelStream().collect(Collectors.groupingBy(PolicyBeneficiaryInfoBo::getInsuredId));
            groupPolicyResponse.getListPolicyInsured().forEach(insuredBo -> {
                insuredBo.setListPolicyBeneficiary(beneficiaryMap.get(insuredBo.getInsuredId()));
            });
        }

        // 查询险种档次信息
        List<PolicyCoverageLevelPo> policyCoverageLevelPos = policyCoverageBaseService.listPolicyCoverageLevel(policyId, null);
        groupPolicyResponse.getListPolicyInsured().forEach(insuredBo -> {
            insuredBo.getListPolicyCoverage().forEach(coverageBo -> {
                List<PolicyCoverageLevelPo> coverageLevelPos = new ArrayList<>();
                policyCoverageLevelPos.forEach(coverageLevelPo -> {
                    if (coverageLevelPo.getCoverageId().equals(coverageBo.getCoverageId())) {
                        coverageLevelPos.add(coverageLevelPo);
                    }
                });
                coverageBo.setListCoverageLevel(coverageLevelPos);
            });
        });

        ResultObject<GroupPolicyResponse> resultObject = new ResultObject<>();
        resultObject.setData(groupPolicyResponse);
        return resultObject;
    }

    /**
     * 根据保单ID和数据生效日期查询保单详细信息
     *
     * @param policyId          保单ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public ResultObject queryPolicyDetailInfo(String policyId, Long dataEffectiveDate) {
        return null;
    }

    @Override
    public ResultObject<List<PolicyCoverageBo>> queryIdNoByAmount(String idNo) {
        ResultObject<List<PolicyCoverageBo>> resultObject = new ResultObject<>();
        List<PolicyCoverageBo> policyCoverageBoList = policyCoverageBaseService.queryCoverageByInsuredNo(idNo);
        resultObject.setData(policyCoverageBoList);
        return resultObject;
    }


    /**
     * 模糊查询投保人
     *
     * @param keyword       关键字
     * @param applicantType 投保人类型
     * @return
     */
    @Override
    public ResultObject<List<PolicyApplicantBo>> listFuzzyPolicyApplicant(String keyword, String applicantType) {
        ResultObject<List<PolicyApplicantBo>> resultObject = new ResultObject<>();
        if (AssertUtils.isNotEmpty(keyword)) {
            List<PolicyApplicantBo> policyApplicantBos = policyApplicantBaseService.listFuzzyPolicyApplicant(keyword, applicantType);
            resultObject.setData(policyApplicantBos);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<PolicyCoverageBo>> queryPolicyCoverageByIdList(List<String> coverageIdList) {
        ResultObject<List<PolicyCoverageBo>> resultObject = new ResultObject<>();
        List<PolicyCoverageBo> policyCoverageBoList = policyCoverageBaseService.queryPolicyCoverageByIdList(coverageIdList);
        resultObject.setData(policyCoverageBoList);
        return resultObject;
    }

    /**
     * 客户ID查询被保人保单保额
     *
     * @param customerAgentId
     * @return
     */
    @Override
    public ResultObject<List<PolicyCoverageBo>> queryAmountByCustomerId(String customerAgentId) {
        ResultObject<List<PolicyCoverageBo>> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(customerAgentId)) {
            return resultObject;
        }
        ResultObject<List<CustomerAgentResponse>> listCustomerAgentObject = customerBaseApi.getCustomerRelationByCustomerAgentId(customerAgentId);
        AssertUtils.isResultObjectError(getLogger(), listCustomerAgentObject);
        List<CustomerAgentResponse> customerAgentResponses = new ArrayList<>(listCustomerAgentObject.getData());

//        //查询所有疑似客户所关联的代理人客户
//        ResultObject<List<CustomerMessageResponse>> listResultObject = customerManageApi.querySuspectedCustomer(customerAgentId);
//        if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
//            listResultObject.getData().forEach(customerMessageResponse -> {
//                ResultObject<List<CustomerAgentResponse>> relation = customerBaseApi.getCustomerRelationByCustomerAgentId(customerMessageResponse.getCustomerId());
//                if (!AssertUtils.isResultObjectListDataNull(relation)) {
//                    customerAgentResponses.addAll(relation.getData());
//                }
//            });
//        }

        if (!AssertUtils.isNotNull(customerAgentResponses)) {
            return resultObject;
        }
        List<String> customerAgentIds = customerAgentResponses.stream().map(CustomerAgentResponse::getCustomerAgentId).distinct().collect(Collectors.toList());
        List<PolicyCoverageBo> policyCoverageBoList = policyCoverageBaseService.queryAmountByCustomerId(customerAgentIds);
        resultObject.setData(policyCoverageBoList);
        return resultObject;
    }

    @Override
    public ResultObject<List<String>> queryPolicyNos(String keyWord) {
        ResultObject<List<String>> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(keyWord)) {
            return resultObject;
        }
        PolicyRelationPo policyRelation = policySeeDao.getPolicyRelation(keyWord);
        List<PolicyRelationPo> relationPos = policySeeDao.getPolicyRelationPolicyNo(policyRelation);
        List<String> policyIds = new ArrayList<>();

        if (AssertUtils.isNotNull(relationPos)) {
            relationPos.forEach(policyRelationPo -> {
                String policyId = policyRelationPo.getPolicyId();
                policyIds.add(policyId);
            });
        }
        resultObject.setData(policyIds);

        return resultObject;
    }

    /**
     * 变更该客户作为投保人下的所有保单的服务业务人员
     *
     * @param clientServiceAgentChangeRequest
     * @param users
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<Void> postClientServiceAgentChange(ClientServiceAgentChangeRequest clientServiceAgentChangeRequest, Users users) {
        if (!AssertUtils.isNotEmpty(clientServiceAgentChangeRequest.getCustomerAgentIds()) || !AssertUtils.isNotEmpty(clientServiceAgentChangeRequest.getServiceAgentId())) {
            return ResultObject.success();
        }
        AgentResponse agentResponse = agentApi.agentByIdGet(clientServiceAgentChangeRequest.getServiceAgentId()).getData();
        AssertUtils.isNotNull(this.getLogger(), agentResponse, POLICY_BUSINESS_POLICY_AGENT_ERROR);
        // 当前分配的业务员必须在职
        if (!PolicyTermEnum.AGENT_STATUS.INDUCTION.name().equals(agentResponse.getAgentStatus())) {
            throw new RequestException(POLICY_QUERY_ASSIGN_AGENT_STATUS_DRAG);
        }
        List<PolicyEndorseBo> policyEndorseBos = policyBaseService.queryPolicyByApplicantCustomerId(clientServiceAgentChangeRequest.getCustomerAgentIds());
        if (!AssertUtils.isNotEmpty(policyEndorseBos)) {
            return ResultObject.success();
        }
        policyEndorseBos.stream().map(PolicyEndorseBo::getPolicyId).distinct().forEach(policyId -> {
            // 新增服务员业务员分配申请记录
            PolicyServiceAgentPo policyServiceAgentPo = new PolicyServiceAgentPo();
            PolicyServiceAgentPo oldPolicyServiceAgentPo = policyQueryBaseDao.getPolicyServiceAgentPoByPolicyId(policyId);
            PolicyAgentPo policyAgentPo = policyBaseService.queryOnePolicyAgentPo(policyId);
            PolicyPo policyPo = policyBaseService.getPolicyPoByPk(policyId);
            // 设置历史业务员
            if (AssertUtils.isNotNull(oldPolicyServiceAgentPo)) {
                // 已经分配过服务业务人员 将当前的服务业务人员设置为历史服务业务人员
                policyServiceAgentPo.setBranchId(oldPolicyServiceAgentPo.getBranchId());
                policyServiceAgentPo.setHistoryServiceAgentId(oldPolicyServiceAgentPo.getServiceAgentId());
                policyServiceAgentPo.setHistoryServiceAgentCode(oldPolicyServiceAgentPo.getServiceAgentCode());
            } else {
                // 第一次分配服务业务人员 将初始业务人员设置为历史服务业务人员
                AgentResponse initialAgent = agentApi.agentByIdGet(policyAgentPo.getAgentId()).getData();
                AssertUtils.isNotNull(getLogger(), initialAgent, POLICY_BUSINESS_POLICY_AGENT_ERROR);

                policyServiceAgentPo.setBranchId(policyPo.getSalesBranchId());
                policyServiceAgentPo.setHistoryServiceAgentId(initialAgent.getAgentId());
                policyServiceAgentPo.setHistoryServiceAgentCode(initialAgent.getAgentCode());
            }
            policyServiceAgentPo.setPolicyId(policyPo.getPolicyId());
            policyServiceAgentPo.setPolicyNo(policyPo.getPolicyNo());
            policyServiceAgentPo.setEffectiveFlag(TerminologyConfigEnum.WHETHER.YES.name());
            // 更新服务业务员
            policyServiceAgentPo.setServiceAgentCode(agentResponse.getAgentCode());
            policyServiceAgentPo.setServiceAgentId(agentResponse.getAgentId());
            policyServiceAgentPo.setAssignStatus(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name());
            policyServiceAgentPo.setEffectiveDate(DateUtils.getCurrentTime());
            policyBaseService.savePolicyServiceAgentPo(users.getUserId(), policyServiceAgentPo);
        });
        return ResultObject.success();
    }

    @Override
    @Transactional
    public ResultObject updatePrePolicyStatus(String applyId, String flag) {
        ResultObject resultObject = new ResultObject<>();
        PolicyPo policyPo = policyBaseService.queryPolicyByApplyId(applyId);
        if (AssertUtils.isNotNull(policyPo)) {
            PolicyBo policyBo = policyBaseService.queryPolicyBo(policyPo.getPolicyId());
            if (PolicyTermEnum.YES_NO.YES.name().equals(flag)) {
                policyPo.setPolicyStatus(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name());
                policyBaseCommisisonTransfer.transferCommissionPolicy(policyBo);
            } else {
                policyPo.setPolicyStatus(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECT_TERMINATION.name());
            }

        }
        policyBaseService.savePolicyPo(policyPo);

        //更改policy_payment状态
        PolicyPaymentPo policyPaymentPo = policyBaseService.queryPolicyPayment(policyPo.getPolicyId());
        if (AssertUtils.isNotNull(policyPaymentPo)) {
            policyPaymentPo.setPaymentStatusCode(PAYMENT_SUCCESS.name());
        }
        policyBaseService.savePolicyPayment(policyPaymentPo);

        return resultObject;
    }
}
