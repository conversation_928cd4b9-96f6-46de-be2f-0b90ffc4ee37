package com.gclife.policy.service.business.impl;

import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.api.ApplyPlanApi;
import com.gclife.apply.model.respone.ApplyPlanResponse;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.message.api.MessageSmsApi;
import com.gclife.message.model.request.SmsVerifyCodeCheckRequest;
import com.gclife.message.model.request.SmsVerifyCodeGeneratorRequest;
import com.gclife.message.model.respone.SmsVerifyCodeCheckResponse;
import com.gclife.message.model.respone.SmsVerifyCodeGeratorResponse;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCoverageLevelPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCoveragePo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyInsuredPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPo;
import com.gclife.policy.dao.PolicyApplicantBaseDao;
import com.gclife.policy.dao.PolicyApplicantExtDao;
import com.gclife.policy.dao.PolicyBaseDao;
import com.gclife.policy.dao.PolicyExtDao;
import com.gclife.policy.dao.PolicyListDao;
import com.gclife.policy.model.bo.ClientPolicyBo;
import com.gclife.policy.model.bo.CustomerRenewalBo;
import com.gclife.policy.model.bo.OfficialPolicyBo;
import com.gclife.policy.model.bo.PolicyApplicantBo;
import com.gclife.policy.model.bo.PolicyBeneficiaryInfoBo;
import com.gclife.policy.model.bo.PolicyCoverageBo;
import com.gclife.policy.model.bo.PolicyExtBo;
import com.gclife.policy.model.bo.PolicyInfoExtBo;
import com.gclife.policy.model.bo.PolicyInsuredBo;
import com.gclife.policy.model.bo.PolicyPaymentBo;
import com.gclife.policy.model.bo.PolicyTodoListBo;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.feign.PolicyInsuredListReqFc;
import com.gclife.policy.model.feign.PolicyQueryReqFc;
import com.gclife.policy.model.request.ClientPolicyRequest;
import com.gclife.policy.model.request.OfficialPolicyRequest;
import com.gclife.policy.model.request.PolicyVerifyCodeRequest;
import com.gclife.policy.model.response.*;
import com.gclife.policy.model.response.official.OfficialCoverageResponse;
import com.gclife.policy.model.response.official.OfficialInsuredResponse;
import com.gclife.policy.model.response.official.OfficialPaymentResponse;
import com.gclife.policy.model.response.official.OfficialPolicyDetailResponse;
import com.gclife.policy.model.response.official.OfficialPolicyListResponse;
import com.gclife.policy.model.response.official.OfficialPolicyResponse;
import com.gclife.policy.service.base.PolicyBaseService;
import com.gclife.policy.service.base.PolicyCoverageBaseService;
import com.gclife.policy.service.business.PolicyBusinessService;
import com.gclife.policy.service.business.PolicyListService;
import com.gclife.policy.validate.transfer.GroupPolicyTransData;
import com.gclife.policy.validate.transfer.PolicyTransData;
import com.gclife.renewal.api.RenewalBaseApi;
import com.gclife.renewal.api.RenewalInsuranceApi;
import com.gclife.renewal.api.RenewalPaymentApi;
import com.gclife.renewal.model.response.RenewalAppDetailResponse;
import com.gclife.renewal.model.response.RenewalInsuranceAppDetailResponse;
import com.gclife.thirdparty.api.ThirdpartyVerifiCodeOpenBaseApi;
import com.gclife.thirdparty.model.request.VerifiyRequest;
import com.gclife.thirdparty.model.response.VerifiyResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 17-11-13
 * description:
 */
@Service
public class PolicyListServiceImpl extends BaseBusinessServiceImpl implements PolicyListService {

    @Autowired
    private PolicyListDao policyListDao;
    @Autowired
    private PolicyTransData policyTransData;
    @Autowired
    private PolicyApplicantExtDao policyApplicantExtDao;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private ApplyPlanApi applyPlanApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private MessageSmsApi messageSmsApi;
    @Autowired
    private ThirdpartyVerifiCodeOpenBaseApi thirdpartyVerifiCodeOpenBaseApi;
    @Autowired
    private PolicyBaseService policyBaseService;
    @Autowired
    private PolicyBaseDao policyBaseDao;
    @Autowired
    private PolicyCoverageBaseService policyCoverageBaseService;
    @Autowired
    private PolicyBusinessService policyBusinessService;
    @Autowired
    private AttachmentApi attachmentApi;
    @Autowired
    private PolicyExtDao policyExtDao;
    @Autowired
    private RenewalInsuranceApi renewalInsuranceApi;
    @Autowired
    private RenewalPaymentApi renewalPaymentApi;
    @Autowired
    private RenewalBaseApi renewalBaseApi;
    @Autowired
    private PolicyApplicantBaseDao policyApplicantBaseDao;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private GroupPolicyTransData groupPolicyTransData;
    @Override
    public ResultObject<BasePageResponse<PolicyListResponse>> getPolicyList(PolicyQueryReqFc policyQueryReqFc) {
        AssertUtils.isNotNull(getLogger(), policyQueryReqFc, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        AssertUtils.isNotEmpty(getLogger(), policyQueryReqFc.getAgentId(), PolicyErrorConfigEnum.POLICY_QUERY_AGENT_ID_IS_NOT_NULL);
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        BasePageResponse<PolicyListResponse> policyListResponseBasePageResponse = new BasePageResponse<>();
        try {
            //查询状态的分类列表
            List<String> listCodes = new ArrayList<>();
            if (AssertUtils.isNotEmpty(policyQueryReqFc.getPolicyStatus())) {
                ResultObject<List<SyscodeRespFc>> listResultObject = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.POLICY_STATUS.name(), policyQueryReqFc.getPolicyStatus());
                if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                    listCodes = listResultObject.getData().stream().map(SyscodeRespFc::getCodeKey).distinct().collect(Collectors.toList());
                }
            }
            //查询保单数据
            List<PolicyExtBo> listPolicyExtBo = policyListDao.getListPolicyExtBo(policyQueryReqFc, listCodes);
            if (AssertUtils.isNotNull(listPolicyExtBo)) {
                policyListResponseBasePageResponse.setData(policyTransData.transPolicyListData(listPolicyExtBo, null));
                resultObject.setData(policyListResponseBasePageResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            }
            e.printStackTrace();
        }

        return resultObject;
    }

    @Override
    public ResultObject<PolicyInfoResponse> getPolicyInfo(String policyId, Users currentLoginUsers) {
        AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
        ResultObject<PolicyInfoResponse> resultObject = new ResultObject<>();
        try {
            PolicyInfoResponse policyInfoResponse;
            PolicyInfoExtBo policyInfoExtBo = policyListDao.getPolicyInfoExtBoById(policyId);

            //查询保单数据
            if (!AssertUtils.isNotNull(policyInfoExtBo)) {
                return resultObject;
            }

            //查询被保人
            List<PolicyInsuredBo> policyInsuredBos = policyBaseDao.getPolicyInsuredList(policyId, null);
            policyInfoExtBo.setListPolicyInsured(policyInsuredBos);
            List<PolicyCoveragePo> listPolicyCoveragePo = policyBaseDao.getPolicyCoverageList(policyId, null);
            List<PolicyCoverageBo> listPolicyCoverageBo = new ArrayList<>();
            listPolicyCoveragePo.forEach(policyCoveragePo -> {
                PolicyCoverageBo policyCoverageBo = new PolicyCoverageBo();
                ClazzUtils.copyPropertiesIgnoreNull(policyCoveragePo, policyCoverageBo);
                listPolicyCoverageBo.add(policyCoverageBo);
            });

            //单独设置险种
            if (AssertUtils.isNotEmpty(listPolicyCoverageBo)) {
                Map<String, List<PolicyCoverageBo>> map = listPolicyCoverageBo.stream()
                        .filter(coverageBo -> AssertUtils.isNotEmpty(coverageBo.getInsuredId()))
                        .collect(Collectors.groupingBy(PolicyCoverageBo::getInsuredId));
                // 设置被保人险种信息
                policyInsuredBos.forEach(policyInsuredBo -> {
                    if (AssertUtils.isNotEmpty(map.get(policyInsuredBo.getInsuredId()))) {
                        policyInsuredBo.setListPolicyCoverage(map.get(policyInsuredBo.getInsuredId()));
                        policyInsuredBo.setMult(map.get(policyInsuredBo.getInsuredId()).get(0).getMult());
                    }
                });
            }

            //查询受益人
            List<PolicyBeneficiaryInfoBo> policyBeneficiaryInfoBos = policyBaseDao.queryPolicyBeneficiaryInfoBo(policyId);
            if (AssertUtils.isNotEmpty(policyBeneficiaryInfoBos)) {
                //移除不可更改的受益人
                policyBeneficiaryInfoBos.removeIf(policyBeneficiaryInfoBo -> TerminologyConfigEnum.WHETHER.NO.name().equals(policyBeneficiaryInfoBo.getModifyFlag()));

                Map<String, List<PolicyBeneficiaryInfoBo>> map = policyBeneficiaryInfoBos.stream()
                        .filter(policyBeneficiaryInfoBo -> AssertUtils.isNotEmpty(policyBeneficiaryInfoBo.getInsuredId()))
                        .collect(Collectors.groupingBy(PolicyBeneficiaryInfoBo::getInsuredId));
                // 设置被保人险种信息
                policyInsuredBos.forEach(policyInsuredBo -> {
                    if (AssertUtils.isNotEmpty(map.get(policyInsuredBo.getInsuredId()))) {
                        policyInsuredBo.setListPolicyBeneficiary(map.get(policyInsuredBo.getInsuredId()));
                    }
                });
            }

            policyInfoResponse = policyTransData.transformPolicyData(policyInfoExtBo, currentLoginUsers);
            if (AssertUtils.isNotEmpty(policyInfoExtBo.getApplyId())) {
                //查询计划书
                ResultObject<ApplyPlanResponse> respFcResultObject = applyPlanApi.getApplyPlan(policyInfoExtBo.getApplyId());
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject)) {
                    policyInfoResponse.setPlanNo(respFcResultObject.getData().getApplyPlanNo());
                }
            }
            resultObject.setData(policyInfoResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<CommissionPolicyResp> getPolicyInfoByPolicyNo(String userId, String policyNo) {
        ResultObject<CommissionPolicyResp> resultObject = new ResultObject<>();
        AssertUtils.isNotEmpty(getLogger(), policyNo, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
        try {
            //获取代理人信息
            CommissionPolicyResp commissionPolicyResp = policyListDao.getCommissionPolicyRespByPolicyNo(policyNo);
            if (AssertUtils.isNotEmpty(commissionPolicyResp.getAgentId())) {
                commissionPolicyResp.setAgentName(agentApi.agentByIdGet(commissionPolicyResp.getAgentId()).getData().getAgentName());
            }

            resultObject.setData(commissionPolicyResp);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<CustomerResponse>> getCustomerInfo(String policyId, String customerType) {

        return null;
    }

    @Override
    public ResultObject<List<PolicyAppApplicantResponse>> getPolicyApplicant(List<String> policyIds) {
        ResultObject<List<PolicyAppApplicantResponse>> resultObject = new ResultObject<>();
        AssertUtils.isNotEmpty(getLogger(), policyIds, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_APPLICANT_ERROR);
        try {
            List<PolicyApplicantBo> policyApplicantBoList = policyApplicantExtDao.fetchByApplicantId(policyIds);

            List<PolicyAppApplicantResponse> policyAppApplicantResponseList = (List<PolicyAppApplicantResponse>) this.converterList(policyApplicantBoList, new TypeToken<List<PolicyAppApplicantResponse>>() {
            }.getType());

            resultObject.setData(policyAppApplicantResponseList);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_APPLICANT_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<PolicyListResponse>> getNewPolicyList(PolicyQueryReqFc policyQueryReqFc, Users users) {
        AssertUtils.isNotNull(getLogger(), policyQueryReqFc, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        AssertUtils.isNotEmpty(getLogger(), policyQueryReqFc.getAgentId(), PolicyErrorConfigEnum.POLICY_QUERY_AGENT_ID_IS_NOT_NULL);
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        BasePageResponse<PolicyListResponse> policyListResponseBasePageResponse = new BasePageResponse<>();
        //查询状态的分类列表
        List<String> listCodes = new ArrayList<>();
        if (AssertUtils.isNotEmpty(policyQueryReqFc.getPolicyStatus())) {
            ResultObject<List<SyscodeRespFc>> listResultObject = platformBaseInternationServiceApi.getTerminologyList(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS.name(), policyQueryReqFc.getPolicyStatus());
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                listCodes = listResultObject.getData().stream().map(SyscodeRespFc::getCodeKey).distinct().collect(Collectors.toList());
            }
        }
        //查询保单数据
        List<PolicyExtBo> listPolicyExtBo = policyListDao.getNewListPolicyExtBo(policyQueryReqFc, listCodes);
        if (AssertUtils.isNotNull(listPolicyExtBo)) {
            policyListResponseBasePageResponse.setData(policyTransData.transPolicyListData(listPolicyExtBo, users.getLanguage()));
            policyListResponseBasePageResponse.setTotalLine(listPolicyExtBo.get(0).getTotalLine());
            resultObject.setData(policyListResponseBasePageResponse);
        }
        return resultObject;
    }

    /**
     * 批量查询我的保单详情
     *
     * @param users
     * @param policyNos
     * @return
     */
    @Override
    public ResultObject<List<CommissionPolicyResp>> postPolicyInfoByPolicyNo(Users users, List<String> policyNos) {
        ResultObject<List<CommissionPolicyResp>> resultObject = new ResultObject<>();
        AssertUtils.isNotEmpty(getLogger(), policyNos, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
        //获取代理人信息
        List<CommissionPolicyResp> commissionPolicyResps = policyListDao.postCommissionPolicyRespByPolicyNo(policyNos);
        if (AssertUtils.isNotEmpty(commissionPolicyResps)) {
            List<String> agentIds = commissionPolicyResps.stream().map(CommissionPolicyResp::getAgentId).distinct().collect(Collectors.toList());
            AgentApplyQueryRequest agentApplyQueryReqFc = new AgentApplyQueryRequest();
            agentApplyQueryReqFc.setListAgentId(agentIds);
            ResultObject<List<AgentResponse>> listResultObject = agentApi.agentsGet(agentApplyQueryReqFc);

            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                List<AgentResponse> applyAgentRespFcList = listResultObject.getData();
                commissionPolicyResps.forEach(commissionPolicyResp -> {
                    applyAgentRespFcList.stream().filter(applyAgentRespFc -> applyAgentRespFc.getAgentId().equals(commissionPolicyResp.getAgentId()))
                            .findFirst().ifPresent(applyAgentRespFc -> commissionPolicyResp.setAgentName(applyAgentRespFc.getAgentName()));
                });
            }

        }
        resultObject.setData(commissionPolicyResps);
        return resultObject;
    }

    /**
     * 官网保单查询获取手机验证码
     *
     * @param policyVerifyCodeRequest 请求参数
     * @return
     */
    @Override
    public ResultObject<SmsVerifyCodeGeratorResponse> queryPolicyVerifyCode(PolicyVerifyCodeRequest policyVerifyCodeRequest) {
        // 校验验证码
        VerifiyRequest verifiyRequest = new VerifiyRequest();
        verifiyRequest.setAppId(policyVerifyCodeRequest.getAppId());
        verifiyRequest.setTicket(policyVerifyCodeRequest.getTicket());
        verifiyRequest.setRandstr(policyVerifyCodeRequest.getRandstr());
        verifiyRequest.setUserIp(policyVerifyCodeRequest.getIp());
        ResultObject<VerifiyResponse> verifiyObject = thirdpartyVerifiCodeOpenBaseApi.optionVerifiyCode(verifiyRequest);
        if (!AssertUtils.isNotNull(verifiyObject) || !AssertUtils.isNotNull(verifiyObject.getData()) || !"SUCCESS".equals(verifiyObject.getData().getResult())) {
            throwsException(PolicyErrorConfigEnum.POLICY_BUSINESS_VERIFY_NOT_MATCH_ERROR);
        }

        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), policyVerifyCodeRequest.getCountryCode(), PolicyErrorConfigEnum.POLICY_PARAMETER_COUNTRY_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), policyVerifyCodeRequest.getMobile(), PolicyErrorConfigEnum.POLICY_PARAMETER_MOBILE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), policyVerifyCodeRequest.getTypeCode(), PolicyErrorConfigEnum.POLICY_PARAMETER_SMS_TYPE_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), policyVerifyCodeRequest.getChannelType(), PolicyErrorConfigEnum.POLICY_PARAMETER_CHANNEL_TYPE_IS_NOT_NULL);

        // 查询个险
        List<OfficialPolicyBo> personalPolicyBos = policyListDao.listPersonalPolicyByPhone(policyVerifyCodeRequest.getMobile());
        // 查询团险投保人代表
        List<OfficialPolicyBo> groupPolicyBos = policyListDao.listGroupPolicyByPhone(policyVerifyCodeRequest.getMobile());
        // 查询团险被保人
        List<OfficialPolicyBo> groupInsuredPolicyBos = policyListDao.listGroupInsuredPolicyByPhone(policyVerifyCodeRequest.getMobile());

        if (!AssertUtils.isNotEmpty(personalPolicyBos) && !AssertUtils.isNotEmpty(groupPolicyBos) && !AssertUtils.isNotEmpty(groupInsuredPolicyBos)) {
            throwsException(PolicyErrorConfigEnum.POLICY_BUSINESS_MOBILE_ERROR);
        }

        SmsVerifyCodeGeneratorRequest smsVerifyCodeGeneratorReqFc = new SmsVerifyCodeGeneratorRequest();
        smsVerifyCodeGeneratorReqFc.setCountryCode(policyVerifyCodeRequest.getCountryCode());
        smsVerifyCodeGeneratorReqFc.setMobile(policyVerifyCodeRequest.getMobile());
        smsVerifyCodeGeneratorReqFc.setTypeCode(policyVerifyCodeRequest.getTypeCode());
        smsVerifyCodeGeneratorReqFc.setChannelType(policyVerifyCodeRequest.getChannelType());
        smsVerifyCodeGeneratorReqFc.setLanguage(policyVerifyCodeRequest.getLanguage());

        this.getLogger().info("smsVerifyCodeGeneratorReqFc: {}", JackSonUtils.toJson(smsVerifyCodeGeneratorReqFc));
        // 调用获取验证码服务接口
        return messageSmsApi.smsVerifyCodeGenerator(smsVerifyCodeGeneratorReqFc);
    }

    /**
     * 官网保单查询
     *
     * @param officialPolicyRequest
     * @return
     */
    @Override
    public ResultObject<OfficialPolicyListResponse> queryOfficialPolicy(OfficialPolicyRequest officialPolicyRequest, String language) {
        // 参数校验
        String mobile = officialPolicyRequest.getMobile();
        AssertUtils.isNotEmpty(this.getLogger(), mobile, PolicyErrorConfigEnum.POLICY_PARAMETER_MOBILE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), officialPolicyRequest.getVerifyCode(), PolicyErrorConfigEnum.POLICY_PARAMETER_VERIFY_CODE_IS_NOT_NULL);

        // 校验短信验证码
        SmsVerifyCodeCheckRequest smsVerifyCodeCheckRequest = new SmsVerifyCodeCheckRequest();
        ClazzUtils.copyPropertiesIgnoreNull(officialPolicyRequest, smsVerifyCodeCheckRequest);
        ResultObject<SmsVerifyCodeCheckResponse> codeCheckObject = messageSmsApi.smsVerifyCodeCheck(smsVerifyCodeCheckRequest);
        if (AssertUtils.isResultObjectError(codeCheckObject) ||
                AssertUtils.isResultObjectDataNull(codeCheckObject) ||
                !"Y".equals(codeCheckObject.getData().getIsVerify()) || !"Y".equals(codeCheckObject.getData().getIsPass())) {
            throwsException(PolicyErrorConfigEnum.POLICY_BUSINESS_VERIFY_NOT_MATCH_ERROR);
        }

        return queryOfficialPolicy(mobile, language);
    }

    /**
     * 官网保单查询(不校验验证码)
     *
     * @param mobile 手机号
     * @return
     */
    @Override
    public ResultObject<OfficialPolicyListResponse> queryOfficialPolicy(String mobile, String language) {
        AssertUtils.isNotEmpty(this.getLogger(), mobile, PolicyErrorConfigEnum.POLICY_PARAMETER_MOBILE_IS_NOT_NULL);

        OfficialPolicyListResponse officialPolicyListResponse = new OfficialPolicyListResponse();
        // 查询个险
        List<OfficialPolicyBo> personalPolicyBos = policyListDao.listPersonalPolicyByPhone(mobile);
        if (AssertUtils.isNotEmpty(personalPolicyBos)) {
            // 设置手机号和姓名
            officialPolicyListResponse.setMobile(mobile);
            personalPolicyBos.stream()
                    .filter(personalPolicyBo -> mobile.equals(personalPolicyBo.getApplicantMobile()))
                    .findFirst().ifPresent(personalPolicyBo -> officialPolicyListResponse.setName(personalPolicyBo.getApplicantName()));
            personalPolicyBos.stream()
                    .filter(personalPolicyBo -> mobile.equals(personalPolicyBo.getInsuredMobile()))
                    .findFirst().ifPresent(personalPolicyBo -> officialPolicyListResponse.setName(personalPolicyBo.getInsuredName()));

            List<OfficialPolicyResponse> listPersonalPolicy = new ArrayList<>();
            personalPolicyBos.forEach(personalPolicyBo -> {
                OfficialPolicyResponse officialPolicyResponse = (OfficialPolicyResponse) this.converterObject(personalPolicyBo, OfficialPolicyResponse.class);
                OfficialPolicyDetailResponse policyDetail = new OfficialPolicyDetailResponse();
                // 查询险种
                List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.listPolicyCoverageOfInsured(personalPolicyBo.getPolicyId());
                if (AssertUtils.isNotEmpty(policyCoveragePos)) {
                    List<OfficialCoverageResponse> listCoverage = (List<OfficialCoverageResponse>) this.converterList(
                            policyCoveragePos, new TypeToken<List<OfficialCoverageResponse>>() {
                            }.getType()
                    );
                    policyDetail.setListCoverage(listCoverage);
                    policyDetail.setPremiumFrequency(personalPolicyBo.getPremiumFrequency());
                }
                // 查询缴费
                List<PolicyPaymentBo> policyPaymentBos = policyBaseService.listPolicyPayment(personalPolicyBo.getPolicyId());
                if (AssertUtils.isNotEmpty(policyPaymentBos)) {
                    List<OfficialPaymentResponse> listPayment = (List<OfficialPaymentResponse>) this.converterList(
                            policyPaymentBos, new TypeToken<List<OfficialPaymentResponse>>() {
                            }.getType()
                    );
                    policyDetail.setListPayment(listPayment);
                    BigDecimal totalPremium = policyPaymentBos.stream()
                            .filter(policyPaymentBo -> PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(policyPaymentBo.getPaymentStatusCode()))
                            .map(PolicyPaymentBo::getTotalPremium)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    policyDetail.setTotalPremium(totalPremium);
                }

                //查询保单详情pdf
                ResultObject<PolicyAttachmentResponse> policyAttachmentResponseResultObject = policyBusinessService.getAttachmentByType(null, personalPolicyBo.getPolicyId(), PolicyTermEnum.ATTACHMENT_TYPE_FLAG.POLICY_ALL_BOOK.name(), language);
                if (AssertUtils.isNotNull(policyAttachmentResponseResultObject)) {
                    if (AssertUtils.isNotNull(policyAttachmentResponseResultObject.getData())) {
                        String attachmentId = policyAttachmentResponseResultObject.getData().getAttachmentId();
                        ResultObject<AttachmentResponse> attachmentResponseResultObject = attachmentApi.attachmentAutoGet(attachmentId);
                        if (!AssertUtils.isResultObjectDataNull(attachmentResponseResultObject)) {
                            AttachmentResponse attachmentResponse = attachmentResponseResultObject.getData();
                            if (AssertUtils.isNotNull(attachmentResponse)) {
                                String url = attachmentResponse.getUrl();
                                String attachmentUrl = attachmentResponse.getAttachmentUrl();
                                officialPolicyResponse.setPolicyDownloadPdfUrl(url);
                                officialPolicyResponse.setPolicyDownloadAttachmentUrl(attachmentUrl);
                            }
                        }
                    }

                }


                officialPolicyResponse.setPolicyDetail(policyDetail);
                listPersonalPolicy.add(officialPolicyResponse);
            });
            officialPolicyListResponse.setListPersonalPolicy(listPersonalPolicy);
        }

        // 查询团险
        List<OfficialPolicyBo> groupPolicyBos = policyListDao.listGroupPolicyByPhone(mobile);
        if (AssertUtils.isNotEmpty(groupPolicyBos)) {
            // 设置手机号和姓名
            officialPolicyListResponse.setMobile(mobile);
            groupPolicyBos.stream()
                    .filter(groupPolicyBo -> mobile.equals(groupPolicyBo.getDelegateMobile()))
                    .findFirst().ifPresent(groupPolicyBo -> officialPolicyListResponse.setName(groupPolicyBo.getDelegateName()));

            List<OfficialPolicyResponse> listGroupPolicy = new ArrayList<>();
            groupPolicyBos.forEach(groupPolicyBo -> {
                OfficialPolicyResponse officialPolicyResponse = (OfficialPolicyResponse) this.converterObject(groupPolicyBo, OfficialPolicyResponse.class);
                OfficialPolicyDetailResponse policyDetail = new OfficialPolicyDetailResponse();
                // 查询公共险种
                List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.listGroupPolicyCoverage(groupPolicyBo.getPolicyId());

                List<PolicyCoverageBo> policyCoverageBos = policyCoverageBaseService.listPolicyCoverage(groupPolicyBo.getPolicyId());
                // 查询险种档次
                List<PolicyCoverageLevelPo> policyCoverageLevelPos = policyCoverageBaseService.listPolicyCoverageLevel(groupPolicyBo.getPolicyId(), null);
                if (AssertUtils.isNotEmpty(policyCoverageLevelPos)) {
                    // 按险种ID分组
                    Map<String, List<PolicyCoverageLevelPo>> coverageLevelPoMap =
                            policyCoverageLevelPos.parallelStream().collect(Collectors.groupingBy(PolicyCoverageLevelPo::getCoverageId));
                    // 设置险种档次数据
                    policyCoverageBos.forEach(policyCoverageBo -> {
                            if (com.gclife.common.model.config.TerminologyConfigEnum.WHETHER.NO.name().equals(policyCoverageBo.getDutyChooseFlag())) {
                                policyCoverageBo.setListCoverageLevel(coverageLevelPoMap.get(policyCoverageBo.getCoverageId()));
                            }
                        });
                    policyCoverageBos.forEach(policyCoverageBo -> {
                        if (TerminologyConfigEnum.WHETHER.NO.name().equals(policyCoverageBo.getDutyChooseFlag())) {
                            policyCoverageBo.setListCoverageLevel(coverageLevelPoMap.get(policyCoverageBo.getCoverageId()));
                        }
                    });
                }
                List<PolicyCoverageBo> groupPolicyCoverageBos = groupPolicyTransData.transSummaryCoverage(policyCoverageBos);
                // 将险种按产品分组
                Map<String, List<PolicyCoverageBo>> productMap = groupPolicyCoverageBos.stream().collect(Collectors.groupingBy(PolicyCoverageBo::getProductId));
                List<PolicyCoveragePo> policyCoveragePoList = policyCoverageBaseService.listPolicyCoveragePo(groupPolicyBo.getPolicyId());
                if (AssertUtils.isNotNull(policyCoveragePos) && AssertUtils.isNotNull(policyCoveragePoList)) {
                    for (PolicyCoveragePo policyCoveragePo : policyCoveragePos) {
                        List<PolicyCoverageBo> policyCoverageBos1 = productMap.get(policyCoveragePo.getProductId());
                        int mult = 0;
                        BigDecimal amount = BigDecimal.ZERO;
                        BigDecimal totalPremium = BigDecimal.ZERO;
                        for (PolicyCoverageBo policyCoverageBo : policyCoverageBos1) {
                            if (AssertUtils.isNotNull(policyCoverageBo.getTotalPremium())) {
                                totalPremium = totalPremium.add(policyCoverageBo.getTotalPremium());
                            }
                            if (AssertUtils.isNotEmpty(policyCoverageBo.getMult())) {
                                mult = mult + (Integer.parseInt(policyCoverageBo.getMult()));
                            }
                            if (AssertUtils.isNotNull(policyCoverageBo.getTotalAmount())) {
                                amount = amount.add(new BigDecimal(policyCoverageBo.getTotalAmount()));
                            }
                        }
                        policyCoveragePo.setMult(mult + "");
                        policyCoveragePo.setTotalPremium(totalPremium);
                        policyCoveragePo.setAmount(amount.compareTo(BigDecimal.ZERO) > 0 ? amount : null);
                    }
                }
                // 单位信息
                PolicyApplicantBo policyApplicantBo = policyApplicantBaseDao.queryPolicyApplicant(groupPolicyBo.getPolicyId());
                if (AssertUtils.isNotNull(policyApplicantBo)) {
                    if ("PRO880000000000029".equals(policyCoveragePos.get(0).getProductId())) {
                        policyApplicantBo.setTaxRegistrationNo(policyApplicantBo.getIdNo());
                        policyApplicantBo.setCompanyContractName(policyApplicantBo.getDelegateName());
                        policyApplicantBo.setCompanyContractPosition(policyApplicantBo.getDelegatePosition());
                        policyApplicantBo.setCompanyContractMobile(policyApplicantBo.getDelegateMobile());
                    }
                    PolicyApplicantResponse applicant =
                            (PolicyApplicantResponse) this.converterObject(policyApplicantBo, PolicyApplicantResponse.class);
                    officialPolicyResponse.setApplicant(applicant);
                }
                if (AssertUtils.isNotEmpty(policyCoveragePos)) {
                    List<OfficialCoverageResponse> listCoverage = (List<OfficialCoverageResponse>) this.converterList(
                            policyCoveragePos, new TypeToken<List<OfficialCoverageResponse>>() {
                            }.getType()
                    );
                    listCoverage.stream().forEach(coverage-> {
                        if (AssertUtils.isNotEmpty(coverage.getCoveragePeriodUnit())) {
                            //需要国际化
                            SyscodeResponse syscodeRespFc = platformInternationalBaseApi.queryOneInternational(com.gclife.common.TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), coverage.getCoveragePeriodUnit(), language).getData();
                            SyscodeResponse syscodeRespFc1 = platformInternationalBaseApi.queryOneInternational(com.gclife.common.TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name(), coverage.getPrimaryFlag(), language).getData();
                            if (AssertUtils.isNotNull(syscodeRespFc)) {
                                coverage.setCoveragePeriodUnitName(coverage.getCoveragePeriod() + syscodeRespFc.getCodeName());
                            }
                            if (AssertUtils.isNotNull(syscodeRespFc1)) {
                                coverage.setPrimaryFlagName(syscodeRespFc1.getCodeName());
                            }
                        }
                    });
                    policyDetail.setListCoverage(listCoverage);
                }
                // 查询被保人清单
                List<PolicyInsuredBo> policyInsuredBos = policyBaseService.listPolicyInsuredBo(groupPolicyBo.getPolicyId());
                if (AssertUtils.isNotEmpty(policyInsuredBos)) {
                    List<OfficialInsuredResponse> listInsured = (List<OfficialInsuredResponse>) this.converterList(
                            policyInsuredBos, new TypeToken<List<OfficialInsuredResponse>>() {
                            }.getType()
                    );
                    // 处理证件号码
                    listInsured.stream()
                            .filter(insured -> AssertUtils.isNotEmpty(insured.getIdNo()))
                            .forEach(insured -> {
                                String idNo = insured.getIdNo();
                                insured.setIdNo(idNo.substring(0, 1) + "********" + idNo.substring(idNo.length() - 1, idNo.length()));
                            });
                    policyDetail.setListInsured(listInsured);
                }

                //查询保单详情pdf
                ResultObject<PolicyAttachmentResponse> policyAttachmentResponseResultObject = policyBusinessService.getAttachmentByType(null, groupPolicyBo.getPolicyId(), PolicyTermEnum.ATTACHMENT_TYPE_FLAG.POLICY_ALL_BOOK.name(), language);
                if (AssertUtils.isNotNull(policyAttachmentResponseResultObject)) {
                    if (AssertUtils.isNotNull(policyAttachmentResponseResultObject.getData())) {
                        String attachmentId = policyAttachmentResponseResultObject.getData().getAttachmentId();
                        ResultObject<AttachmentResponse> attachmentResponseResultObject = attachmentApi.attachmentAutoGet(attachmentId);
                        if (!AssertUtils.isResultObjectDataNull(attachmentResponseResultObject)) {
                            AttachmentResponse attachmentResponse = attachmentResponseResultObject.getData();
                            if (AssertUtils.isNotNull(attachmentResponse)) {
                                String url = attachmentResponse.getUrl();
                                String attachmentUrl = attachmentResponse.getAttachmentUrl();
                                officialPolicyResponse.setPolicyDownloadPdfUrl(url);
                                officialPolicyResponse.setPolicyDownloadAttachmentUrl(attachmentUrl);
                            }
                        }
                    }

                }
                officialPolicyResponse.setPolicyDetail(policyDetail);
                listGroupPolicy.add(officialPolicyResponse);
            });
            officialPolicyListResponse.setListGroupPolicy(listGroupPolicy);
        }
        // 查询团险作为被保人
        List<OfficialPolicyBo> groupInsuredPolicyBos = policyListDao.listGroupInsuredPolicyByPhone(mobile);
        if (AssertUtils.isNotEmpty(groupInsuredPolicyBos)) {
            // 设置手机号和姓名
            officialPolicyListResponse.setMobile(mobile);
            groupInsuredPolicyBos.stream()
                    .filter(groupPolicyBo -> mobile.equals(groupPolicyBo.getInsuredMobile()))
                    .findFirst().ifPresent(groupPolicyBo -> officialPolicyListResponse.setName(groupPolicyBo.getInsuredName()));

            List<OfficialPolicyResponse> listGroupInsuredPolicy = new ArrayList<>();
            groupInsuredPolicyBos.forEach(groupPolicyBo -> {
                OfficialPolicyResponse officialPolicyResponse = (OfficialPolicyResponse) this.converterObject(groupPolicyBo, OfficialPolicyResponse.class);
                OfficialPolicyDetailResponse policyDetail = new OfficialPolicyDetailResponse();
                // 查询被保人险种
                List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.listPolicyCoverageOfInsured(groupPolicyBo.getPolicyId(), groupPolicyBo.getInsuredId());
                // 被保人查看的保费为null
                policyCoveragePos.forEach(policyCoveragePo -> {
                    policyCoveragePo.setTotalPremium(null);
                });
                if (AssertUtils.isNotEmpty(policyCoveragePos)) {
                    List<OfficialCoverageResponse> listCoverage = (List<OfficialCoverageResponse>) this.converterList(
                            policyCoveragePos, new TypeToken<List<OfficialCoverageResponse>>() {
                            }.getType()
                    );
                    listCoverage.forEach(coverage-> {
                        if (AssertUtils.isNotEmpty(coverage.getCoveragePeriodUnit())) {
                            //需要国际化
                            SyscodeResponse syscodeRespFc = platformInternationalBaseApi.queryOneInternational(com.gclife.common.TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), coverage.getCoveragePeriodUnit(), language).getData();
                            SyscodeResponse syscodeRespFc1 = platformInternationalBaseApi.queryOneInternational(com.gclife.common.TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name(), coverage.getPrimaryFlag(), language).getData();
                            if (AssertUtils.isNotNull(syscodeRespFc)) {
                                coverage.setCoveragePeriodUnitName(coverage.getCoveragePeriod() + syscodeRespFc.getCodeName());
                            }
                            if (AssertUtils.isNotNull(syscodeRespFc1)) {
                                coverage.setPrimaryFlagName(syscodeRespFc1.getCodeName());
                            }
                        }
                    });
                    policyDetail.setListCoverage(listCoverage);
                }

                //组装被保人信息
                List<OfficialInsuredResponse> officialInsuredResponses = new ArrayList<>();
                OfficialInsuredResponse insuredResponse = (OfficialInsuredResponse) this.converterObject(
                        groupPolicyBo, OfficialInsuredResponse.class);
                //查询被保人保额
                List<PolicyCoveragePo> policyInsuredCoveragePos =
                        policyCoverageBaseService.listPolicyCoverageOfInsured(groupPolicyBo.getPolicyId(), groupPolicyBo.getInsuredId());
                if (AssertUtils.isNotEmpty(policyInsuredCoveragePos)) {
                    policyInsuredCoveragePos.stream().filter(policyCoveragePo ->
                            PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoveragePo.getPrimaryFlag()))
                            .findFirst().ifPresent(policyCoveragePo -> {
                                insuredResponse.setSumInsured(policyCoveragePo.getAmount());
                            });
                }
                // 处理证件号码
                String idNo = insuredResponse.getIdNo();
                if (AssertUtils.isNotEmpty(idNo)){
                    insuredResponse.setIdNo(idNo.substring(0, 1) + "********" + idNo.substring(idNo.length() - 1, idNo.length()));
                }
                insuredResponse.setName(groupPolicyBo.getInsuredName());
                officialInsuredResponses.add(insuredResponse);
                policyDetail.setListInsured(officialInsuredResponses);

                PolicyApplicantBo policyApplicantBo = policyBaseService.queryPolicyApplicant(groupPolicyBo.getPolicyId());
                if (AssertUtils.isNotNull(policyApplicantBo)) {
                    officialPolicyResponse.setApplicantName(policyApplicantBo.getCompanyName());
                    if ("PRO880000000000029".equals(policyCoveragePos.get(0).getProductId())) {
                        policyApplicantBo.setTaxRegistrationNo(policyApplicantBo.getIdNo());
                        policyApplicantBo.setCompanyContractName(policyApplicantBo.getDelegateName());
                        policyApplicantBo.setCompanyContractPosition(policyApplicantBo.getDelegatePosition());
                        policyApplicantBo.setCompanyContractMobile(policyApplicantBo.getDelegateMobile());
                    }
                    PolicyApplicantResponse applicant =
                            (PolicyApplicantResponse) this.converterObject(policyApplicantBo, PolicyApplicantResponse.class);
                    officialPolicyResponse.setApplicant(applicant);
                }
                //增加标识区分是否作为被保人
                officialPolicyResponse.setIsInsuredFlag("YES");
                officialPolicyResponse.setPolicyDetail(policyDetail);
                listGroupInsuredPolicy.add(officialPolicyResponse);
            });
            officialPolicyListResponse.setListGroupPolicy(listGroupInsuredPolicy);
        }
        if (!AssertUtils.isNotEmpty(personalPolicyBos) && !AssertUtils.isNotEmpty(groupPolicyBos) && !AssertUtils.isNotEmpty(groupInsuredPolicyBos)) {
            throwsException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        }

        ResultObject<OfficialPolicyListResponse> resultObject = new ResultObject<>();
        resultObject.setData(officialPolicyListResponse);
        return resultObject;
    }

    /**
     * 查询客户APP的保单列表
     *
     * @param policyQueryReqFc
     * @param users
     * @return
     */
    @Override
    public ResultObject<BasePageResponse<PolicyListResponse>> getClientPolicyQueryList(PolicyQueryReqFc policyQueryReqFc, Users users) {
        AssertUtils.isNotNull(getLogger(), policyQueryReqFc, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        AssertUtils.isNotEmpty(getLogger(), policyQueryReqFc.getCustomerIds(), PolicyErrorConfigEnum.POLICY_CUSTOMER_ID_IS_NOT_NULL);
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        BasePageResponse<PolicyListResponse> policyListResponseBasePageResponse = new BasePageResponse<>();
        //查询状态的分类列表
        List<String> policyStatusList = new ArrayList<>();
        if (PolicyTermEnum.CLIENT_POLICY_STATUS.EFFECTIVE.name().equals(policyQueryReqFc.getPolicyStatusType())) {
//            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_PENDING_EFFECT.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_REINSTATEMENT.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_WAIVER_PREMIUM.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_EFFECTIVE_HC.name());
        }
        if (PolicyTermEnum.CLIENT_POLICY_STATUS.INVALID.name().equals(policyQueryReqFc.getPolicyStatusType())) {
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INDEMNITY_TERMINATION.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_HESITATION_REVOKE.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID_THOROUGH.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECT_TERMINATION.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_SURRENDER.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_IEXPIRE.name());
        }

        //查询保单数据
        List<PolicyExtBo> listPolicyExtBo = policyListDao.getClientPolicyQueryList(policyQueryReqFc, policyStatusList);
        if (AssertUtils.isNotNull(listPolicyExtBo)) {
            policyListResponseBasePageResponse.setData(policyTransData.transPolicyListData(listPolicyExtBo, users.getLanguage()));
            policyListResponseBasePageResponse.setTotalLine(listPolicyExtBo.get(0).getTotalLine());
            resultObject.setData(policyListResponseBasePageResponse);
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<PolicyListResponse>> getClientPolicyInsuredQueryList(PolicyQueryReqFc policyQueryReqFc, Users users) {
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        BasePageResponse<PolicyListResponse> policyListResponseBasePageResponse = new BasePageResponse<>();
        List<String> policyStatusList = new ArrayList<>();

        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_PENDING_EFFECT.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_REINSTATEMENT.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_WAIVER_PREMIUM.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_EFFECTIVE_HC.name());

        List<PolicyExtBo> insuredQueryList = policyListDao.getClientPolicyInsuredQueryList(policyQueryReqFc, policyStatusList);

        if (AssertUtils.isNotNull(insuredQueryList)) {
            policyListResponseBasePageResponse.setData(policyTransData.transPolicyListData(insuredQueryList, users.getLanguage()));
            policyListResponseBasePageResponse.setTotalLine(insuredQueryList.get(0).getTotalLine());
            resultObject.setData(policyListResponseBasePageResponse);
        }
        return resultObject;
    }

    /**
     * 查询客户APP的待办列表
     *
     * @param policyQueryReqFc
     * @param users
     * @return
     */
    @Override
    public ResultObject<List<PolicyTodoListResponse>> getClientPolicyQueryTodoList(PolicyQueryReqFc policyQueryReqFc, Users users) {
        ResultObject<List<PolicyTodoListResponse>> resultObject = new ResultObject<>();

        AssertUtils.isNotEmpty(getLogger(), policyQueryReqFc.getCustomerIds(), PolicyErrorConfigEnum.POLICY_CUSTOMER_ID_IS_NOT_NULL);

        //查询保单数据
        List<PolicyTodoListBo> policyTodoListBos = policyListDao.getClientPolicyQueryTodoList(policyQueryReqFc);
        if (AssertUtils.isNotNull(policyTodoListBos)) {
            List<PolicyTodoListResponse> policyTodoListResponses = new ArrayList<>();
            policyTodoListBos.forEach(policyTodoListBo -> {
                PolicyTodoListResponse policyTodoListResponse = new PolicyTodoListResponse();
                ClazzUtils.copyPropertiesIgnoreNull(policyTodoListBo, policyTodoListResponse);
                policyTodoListResponse.setClientPolicyStatus(PolicyTermEnum.POLICY_STATUS_FLAG.valueOf(policyTodoListBo.getPolicyStatus()).code());

                if (Arrays.asList(PolicyTermEnum.OPERATION_CODE.RENEWAL_INSURANCE_PENDING_PAYMENT.name(),
                        PolicyTermEnum.OPERATION_CODE.RENEWAL_INSURANCE_RE_APPLY.name(),
                        PolicyTermEnum.OPERATION_CODE.RENEWAL_INSURANCE_APPLY.name(),
                        PolicyTermEnum.OPERATION_CODE.RENEWAL_INSURANCE_PENDING_REVIEW.name()
                ).contains(policyTodoListBo.getButtonCode())) {
                    policyTodoListResponse.setButtonCode("RENEWAL_INSURANCE");
                    ResultObject<RenewalInsuranceAppDetailResponse> renewalInsuranceApplyRespFcResultObject = renewalInsuranceApi.queryRenewalInsuranceAppDetail(policyTodoListBo.getPolicyId());
                    AssertUtils.isResultObjectError(getLogger(), renewalInsuranceApplyRespFcResultObject, PolicyErrorConfigEnum.POLICY_QUERY_RENEWAL_INSURANCE_ERROR);
                    RenewalInsuranceAppDetailResponse renewalInsuranceAppDetailResponse = renewalInsuranceApplyRespFcResultObject.getData();
                    policyTodoListResponse.setTotalPremium(renewalInsuranceAppDetailResponse.getTotalPremium());
                    policyTodoListResponse.setBusinessId(renewalInsuranceAppDetailResponse.getRenewalId());
                } else {
                    ResultObject<RenewalAppDetailResponse> renewalAppDetailResponseResultObject = renewalPaymentApi.queryRenewalAppDetail(policyTodoListBo.getPolicyId());
                    AssertUtils.isResultObjectDataNull(getLogger(), renewalAppDetailResponseResultObject, PolicyErrorConfigEnum.POLICY_QUERY_RENEWAL_INSURANCE_ERROR);
                    policyTodoListResponse.setBusinessId(renewalAppDetailResponseResultObject.getData().getBusinessId());
                    policyTodoListResponse.setTotalPremium(renewalAppDetailResponseResultObject.getData().getTotalPremium());
                    policyTodoListResponse.setButtonCode("RENEWAL");
                }
                policyTodoListResponse.setColorValue("#00459700");
                policyTodoListResponses.add(policyTodoListResponse);
            });
            resultObject.setData(policyTodoListResponses);
        }
        return resultObject;
    }

    /**
     * 查询客户投保保单列表
     * @param clientPolicyRequest
     * @return
     */
    @Override
    public ResultObject<List<ClientPolicyResponse>> listClientPolicy(ClientPolicyRequest clientPolicyRequest) {
        AssertUtils.isNotNull(getLogger(), clientPolicyRequest, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        AssertUtils.isNotEmpty(getLogger(), clientPolicyRequest.getCustomerIds(), PolicyErrorConfigEnum.POLICY_CUSTOMER_ID_IS_NOT_NULL);
        // 查询状态的分类列表
        List<String> policyStatusList = new ArrayList<>();
        if (PolicyTermEnum.CLIENT_POLICY_STATUS.EFFECTIVE.name().equals(clientPolicyRequest.getPolicyStatusType())) {
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_REINSTATEMENT.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_WAIVER_PREMIUM.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_EFFECTIVE_HC.name());
        }
        if (PolicyTermEnum.CLIENT_POLICY_STATUS.INVALID.name().equals(clientPolicyRequest.getPolicyStatusType())) {
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INDEMNITY_TERMINATION.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_HESITATION_REVOKE.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID_THOROUGH.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECT_TERMINATION.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_SURRENDER.name());
            policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_IEXPIRE.name());
        }

        ResultObject<List<ClientPolicyResponse>> resultObject = ResultObject.success();
        // 查询保单数据
        List<ClientPolicyBo> clientPolicyBos = policyBaseService.listCustomerPolicy(clientPolicyRequest.getCustomerIds(), policyStatusList);
        if (AssertUtils.isNotNull(clientPolicyBos)) {
            List<String> policyIds = clientPolicyBos.stream().map(PolicyPo::getPolicyId).collect(Collectors.toList());
            // 查询险种数据
            List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.listCoverage(policyIds);
            List<ClientPolicyResponse> clientPolicyResponses = new ArrayList<>();
            clientPolicyBos.forEach(clientPolicyBo -> {
                ClientPolicyResponse clientPolicyResponse = (ClientPolicyResponse) this.converterObject(clientPolicyBo, ClientPolicyResponse.class);
                clientPolicyResponse.setTotalAmount(BigDecimal.ZERO);
                // 计算总保额
                policyCoveragePos.stream()
                        .filter(policyCoveragePo -> policyCoveragePo.getPolicyId().equals(clientPolicyBo.getPolicyId()))
                        .forEach(policyCoveragePo -> {
                    if (AssertUtils.isNotNull(policyCoveragePo.getAmount())) {
                        if (AssertUtils.isNotEmpty(policyCoveragePo.getMult())) {
                            clientPolicyResponse.setTotalAmount(clientPolicyResponse.getTotalAmount().add(policyCoveragePo.getAmount().multiply(new BigDecimal(policyCoveragePo.getMult()))));
                        } else {
                            clientPolicyResponse.setTotalAmount(clientPolicyResponse.getTotalAmount().add(policyCoveragePo.getAmount()));
                        }
                    }
                });
                clientPolicyResponses.add(clientPolicyResponse);
            });
            resultObject.setData(clientPolicyResponses);
        }
        return resultObject;
    }

    /**
     * 业务员客户续期续保列表
     * @param agentId 业务员ID
     * @return
     */
    @Override
    public ResultObject<List<CustomerRenewalResponse>> listCustomerRenewal(String agentId) {
        ResultObject<List<CustomerRenewalResponse>> resultObject = new ResultObject<>();
        // 查询续期续保保单数据
        List<CustomerRenewalBo> customerRenewalBos = policyListDao.listCustomerRenewal(agentId);
        if (AssertUtils.isNotNull(customerRenewalBos)) {
            List<CustomerRenewalResponse> customerRenewalResponses = new ArrayList<>();
            customerRenewalBos.forEach(customerRenewalBo -> {
                CustomerRenewalResponse customerRenewalResponse = new CustomerRenewalResponse();
                ClazzUtils.copyPropertiesIgnoreNull(customerRenewalBo, customerRenewalResponse);
                if (PolicyTermEnum.OPERATION_CODE.RENEWAL_PENDING_PAYMENT.name().equals(customerRenewalBo.getOperationCode())) {
                    // 续期，查询宽末期
                    ResultObject<RenewalAppDetailResponse> renewalAppDetailResponseResultObject = renewalPaymentApi.queryRenewalAppDetail(customerRenewalBo.getPolicyId());
                    AssertUtils.isResultObjectDataNull(getLogger(), renewalAppDetailResponseResultObject, PolicyErrorConfigEnum.POLICY_QUERY_RENEWAL_INSURANCE_ERROR);
                    customerRenewalResponse.setGracePeriodExpDate(renewalAppDetailResponseResultObject.getData().getGainedDate());
                    customerRenewalResponse.setRenewalFlag(PolicyTermEnum.RENEWAL_TYPE.RENEWAL.name());
                    customerRenewalResponse.setBusinessId(renewalAppDetailResponseResultObject.getData().getBusinessId());
                } else {
                    // 续保
                    ResultObject<RenewalInsuranceAppDetailResponse> renewalInsuranceApplyRespFcResultObject = renewalInsuranceApi.queryRenewalInsuranceAppDetail(customerRenewalBo.getPolicyId());
                    AssertUtils.isResultObjectError(getLogger(), renewalInsuranceApplyRespFcResultObject, PolicyErrorConfigEnum.POLICY_QUERY_RENEWAL_INSURANCE_ERROR);
                    customerRenewalResponse.setRenewalFlag(PolicyTermEnum.RENEWAL_TYPE.RENEWAL_INSURANCE.name());
                    customerRenewalResponse.setBusinessId(renewalInsuranceApplyRespFcResultObject.getData().getRenewalId());
                }
                customerRenewalResponses.add(customerRenewalResponse);
            });
            resultObject.setData(customerRenewalResponses);
        }
        return resultObject;
    }

    @Override
    public ResultObject<OnlineProductPaymentSuccessResponse> getOnlineProductPaymentSuccess(String applyId) {
        ResultObject<OnlineProductPaymentSuccessResponse> resultObject = new ResultObject<>();
        OnlineProductPaymentSuccessResponse onlineProductPaymentSuccessResponse = new OnlineProductPaymentSuccessResponse();
        // 投保单转保单需要时间 10秒 查询20次
        PolicyPo policyPo;
        int cycleCount = 0;
        do {
            policyPo = policyBaseDao.getPolicyByApplyId(applyId);
            try {
                Thread.sleep(500L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            ++cycleCount;
        } while (Objects.isNull(policyPo) && cycleCount < 20);

        AssertUtils.isNotNull(getLogger(), policyPo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        String policyId = policyPo.getPolicyId();
        // 投保人信息
        PolicyApplicantBo policyApplicantBo = policyExtDao.loadPolicyApplicantByPolicyId(policyId);
        // 险种信息
        List<PolicyCoverageBo> policyCoverageBos = policyExtDao.loadPolicyCoverageListByPolicyId(policyId);
        Optional<PolicyCoverageBo> coverageBoOptional = policyCoverageBos.stream()
                .filter(policyCoverageBo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverageBo.getPrimaryFlag()))
                .findFirst();
        String productName = null;
        if (coverageBoOptional.isPresent()) {
            PolicyCoverageBo policyCoverageBo = coverageBoOptional.get();
            productName = policyCoverageBo.getProductName();
        }

        onlineProductPaymentSuccessResponse.setPolicyId(policyId);
        onlineProductPaymentSuccessResponse.setPolicyNo(policyPo.getPolicyNo());
        onlineProductPaymentSuccessResponse.setEffectiveDate(policyPo.getEffectiveDate());
        onlineProductPaymentSuccessResponse.setApplicantName(policyApplicantBo.getName());
        onlineProductPaymentSuccessResponse.setProductName(productName);

        resultObject.setData(onlineProductPaymentSuccessResponse);
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<OfficialInsuredResponse>> queryInsuredList(PolicyInsuredListReqFc policyInsuredReqFc, String language) {
        ResultObject<BasePageResponse<OfficialInsuredResponse>> resultObject = new ResultObject<>();
        String policyId = policyInsuredReqFc.getPolicyId();
        // 校验保单id
        AssertUtils.isNotEmpty(this.getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
              List<PolicyInsuredBo> policyInsuredBos = policyBaseService.pagePolicyInsuredBo(policyId, policyInsuredReqFc);
        if (AssertUtils.isNotEmpty(policyInsuredBos)) {
                  List<OfficialInsuredResponse> listInsured = (List<OfficialInsuredResponse>) this.converterList(
                          policyInsuredBos, new TypeToken<List<OfficialInsuredResponse>>() {
                          }.getType()
                  );
                  // 处理证件号码
                  listInsured.stream()
                          .filter(insured -> AssertUtils.isNotEmpty(insured.getIdNo()))
                          .forEach(insured -> {
                              String idNo = insured.getIdNo();
                              insured.setIdNo(idNo.substring(0, 1) + "********" + idNo.substring(idNo.length() - 1, idNo.length()));
                          });
            Integer totalLine = AssertUtils.isNotNull(policyInsuredBos) ? policyInsuredBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(
                    policyInsuredReqFc.getCurrentPage(), policyInsuredReqFc.getPageSize(), totalLine, listInsured);
            resultObject.setData(basePageResponse);
        }
        return resultObject;
    }

}
