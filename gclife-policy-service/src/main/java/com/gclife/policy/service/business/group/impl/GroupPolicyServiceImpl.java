package com.gclife.policy.service.business.group.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.response.AgentListResponse;
import com.gclife.app.model.request.AppGroupPolicyQueryRequest;
import com.gclife.app.model.response.*;
import com.gclife.apply.api.group.GroupApplyApi;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.api.AttachmentPDFDocumentApi;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.model.response.CoiBatchResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.InternationalTypeEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.payment.api.PaymentApi;
import com.gclife.payment.api.PaymentBaseApi;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.payment.model.response.PaymentStatusResponse;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformCareerApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.CareerNameResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.core.jooq.tables.daos.PolicyPrintInfoDao;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.dao.*;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.config.PolicyWorkflowTermEnum;
import com.gclife.policy.model.request.group.PolicyListRequest;
import com.gclife.policy.model.response.PolicyListResponse;
import com.gclife.policy.model.response.group.CoiUrlDetailResponse;
import com.gclife.policy.model.response.group.CoiUrlResponse;
import com.gclife.policy.model.response.group.GroupPolicyDetailResponse;
import com.gclife.policy.service.base.PolicyBaseService;
import com.gclife.policy.service.base.PolicyCoverageBaseService;
import com.gclife.policy.service.base.PolicyQueryBaseService;
import com.gclife.policy.service.business.PolicyBusinessService;
import com.gclife.policy.service.business.PolicyPrintService;
import com.gclife.policy.service.business.base.PolicyBaseBusinessService;
import com.gclife.policy.service.business.group.GroupPolicyService;
import com.gclife.policy.service.data.PolicyBoService;
import com.gclife.policy.validate.transfer.GroupPolicyTransData;
import com.gclife.policy.validate.transfer.LanguageCodeTransData;
import com.gclife.policy.validate.transfer.PolicyTransData;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.renewal.api.GroupRenewalApi;
import com.gclife.renewal.model.response.RenewalAppDetailResponse;
import com.gclife.thirdparty.model.response.ShortUrlResponse;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.request.WaitingTaskRequest;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.*;
import static com.gclife.policy.model.config.PolicyTermEnum.ATTACHMENT_TYPE_FLAG.*;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP;

@Slf4j
@Service
public class GroupPolicyServiceImpl extends BaseBusinessServiceImpl implements GroupPolicyService {
    @Autowired
    private PolicyQueryBaseService policyQueryBaseService;
    @Autowired
    private PolicyPrintDao policyPrintDao;
    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private PolicyPrintInfoDao policyPrintInfoDao;
    @Autowired
    private PolicyBaseService policyBaseService;
    @Autowired
    private GroupPolicyTransData groupPolicyTransData;
    @Autowired
    private AttachmentPDFDocumentApi attachmentPDFDocumentApi;
    @Autowired
    private AttachmentApi attachmentApi;
    @Autowired
    private GroupApplyApi groupApplyApi;
    @Autowired
    private PolicyCoverageBaseService policyCoverageBaseService;
    @Autowired
    private PolicyBusinessService policyBusinessService;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private PolicyListDao policyListDao;
    @Autowired
    private PolicyTransData policyTransData;
    @Autowired
    private PlatformCareerApi platformCareerApi;
    @Autowired
    private LanguageCodeTransData languageCodeTransData;
    @Autowired
    private GroupRenewalApi groupRenewalApi;
    @Autowired
    private PaymentApi paymentApi;
    @Autowired
    private PolicySeeDao policySeeDao;
    @Autowired
    private PolicyBaseBusinessService policyBaseBusinessService;
    @Autowired
    private PolicyExtDao policyExtDao;
    @Autowired
    private PolicyPrintService policyPrintService;
    @Autowired
    private PaymentBaseApi paymentBaseApi;
    @Autowired
    private PolicyBoService policyBoService;
    @Autowired
    private PolicyBaseDao policyBaseDao;
    @Autowired
    private PolicyQueryBaseDao policyQueryBaseDao;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;

    @Override
    public ResultObject<BasePageResponse<PolicyListResponse>> queryPrintPolicyList(PolicyListRequest policyListRequest, Users users) {
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        try {
            WaitingTaskRequest tasksRequest = new WaitingTaskRequest();
            tasksRequest.setWorkflowItemType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.GROUP_POLICY_PRINT_TASK.name());
            tasksRequest.setWorkflowType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.NEW_CONTRACT_GROUP.name());
            tasksRequest.setUserId(users.getUserId());
            resultObject = this.getWorkflowList(users, tasksRequest, policyListRequest, null, LIFE_INSURANCE_GROUP.name());
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_PRINT_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }


    @Override
    public ResultObject<GroupPolicyDetailResponse> queryPolicyDetail(String policyId, Users currentLoginUsers) {
        ResultObject<GroupPolicyDetailResponse> resultObject = new ResultObject<>();
        try {
            getLogger().info("-------保单详情1------", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE54));
            PolicyBo policyBo = policyBaseService.queryPolicyBo(policyId);
            AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
            getLogger().info("-------保单详情2------", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE54));
            // 查询险种档次
            List<PolicyCoverageLevelPo> policyCoverageLevelPos = policyCoverageBaseService.listPolicyCoverageLevel(policyId, null);
            getLogger().info("-------保单详情3------", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE54));
            if (AssertUtils.isNotEmpty(policyCoverageLevelPos)) {
                // 按险种ID分组
                Map<String, List<PolicyCoverageLevelPo>> coverageLevelPoMap =
                        policyCoverageLevelPos.parallelStream().collect(Collectors.groupingBy(PolicyCoverageLevelPo::getCoverageId));
                // 设置险种档次数据
                policyBo.getListInsured().forEach(policyInsuredBo -> {
                    policyInsuredBo.getListCoverage().forEach(policyCoverageBo -> {
                        if (TerminologyConfigEnum.WHETHER.NO.name().equals(policyCoverageBo.getDutyChooseFlag())) {
                            policyCoverageBo.setListCoverageLevel(coverageLevelPoMap.get(policyCoverageBo.getCoverageId()));
                        }
                    });
                });
                policyBo.getListInsuredCoverage().forEach(policyCoverageBo -> {
                    if (TerminologyConfigEnum.WHETHER.NO.name().equals(policyCoverageBo.getDutyChooseFlag())) {
                        policyCoverageBo.setListCoverageLevel(coverageLevelPoMap.get(policyCoverageBo.getCoverageId()));
                    }
                });
            }
            getLogger().info("-------保单详情4------", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE54));
            GroupPolicyDetailResponse groupPolicyDetailResponse = groupPolicyTransData.transGroupPolicyDetail(currentLoginUsers, policyBo);
            getLogger().info("-------保单详情5------", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE54));
            resultObject.setData(groupPolicyDetailResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            }
        }
        return resultObject;
    }


    /**
     * 保单打印结束
     *
     * @param users       当前用户
     * @param printInfoId 保单打印ID
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject policyPrintFinish(Users users, String printInfoId) {
        ResultObject resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), printInfoId, PolicyErrorConfigEnum.POLICY_PARAMETER_PRINT_INFO_ID_IS_NOT_NULL);
            // 查询打印数据
            PolicyPrintInfoBo policyPrintInfoBo = policyPrintDao.getPolicyPrintData(printInfoId);
            // 数据校验
            AssertUtils.isNotNull(this.getLogger(), policyPrintInfoBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRINT_INFO_IS_NOT_FOUND_OBJECT);

            this.getLogger().error(JSON.toJSONString(policyPrintInfoBo));

            // 更新保单打印表
            PolicyPrintInfoPo policyPrintInfoPo = (PolicyPrintInfoPo) this.converterObject(policyPrintInfoBo, PolicyPrintInfoPo.class);
            policyPrintInfoPo.setPrintDate(DateUtils.getCurrentTime());
            policyPrintInfoPo.setPrintstatus(PolicyTermEnum.YES_NO.YES.name());
            policyPrintInfoDao.update(policyPrintInfoPo);

            // 更新回执信息
            PolicyReceiptInfoPo policyReceiptInfoPo = policyBaseService.queryPolicyReceiptInfo(policyPrintInfoPo.getPolicyId());
            if (AssertUtils.isNotNull(policyReceiptInfoPo)) {
                policyReceiptInfoPo.setReceiptStatus(PolicyTermEnum.RECEIPT_STATUS.WAIT_RECEIPT.name());
                policyBaseService.savePolicyReceiptInfo(users.getUserId(), policyReceiptInfoPo);
            }

            // 调用工作流
            resultObject = workFlowApi.claimTask(users.getUserId(), policyPrintInfoBo.getApplyId(), PolicyWorkflowTermEnum.WORKFLOW_STATUS.GROUP_POLICY_PRINT_TASK.name(), "true");
            this.getLogger().error(JSON.toJSONString(resultObject));
            AssertUtils.isResultObjectError(this.getLogger(), resultObject);

//            //打印结束发消息推送给代理人
//            PolicyBo policyBo = policyExtDao.loadPolicyByPolicyId(policyPrintInfoBo.getPolicyId());
//            AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
//            messageBusinessService.pushWorkFlowMessage(policyBo, PolicyTermEnum.MSG_BUSINESS_TYPE.POLICY_PRINT_CP.name());
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<PolicyListResponse>> queryPolicyReceipts(PolicyListRequest policyListRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        try {
            WaitingTaskRequest tasksRequest = new WaitingTaskRequest();
            tasksRequest.setWorkflowItemType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.GROUP_RETURN_RECEIPT_TASK.name());
            tasksRequest.setWorkflowType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.NEW_CONTRACT_GROUP.name());
            tasksRequest.setUserId(currentLoginUsers.getUserId());
            resultObject = this.getWorkflowList(currentLoginUsers, tasksRequest, policyListRequest, LIFE_INSURANCE_GROUP.name(), TerminologyTypeEnum.POLICY_RECEIPT_WORK_FLOW_STATUS.name());
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<PolicyListResponse>> queryPolicyReceiptReviews(PolicyListRequest policyListRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        try {
            WaitingTaskRequest tasksRequest = new WaitingTaskRequest();
            tasksRequest.setWorkflowItemType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.GROUP_RECEIPT_REVIEW_TASK.name());
            tasksRequest.setWorkflowType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.NEW_CONTRACT_GROUP.name());
            tasksRequest.setUserId(currentLoginUsers.getUserId());
            resultObject = this.getWorkflowList(currentLoginUsers, tasksRequest, policyListRequest, LIFE_INSURANCE_GROUP.name(), TerminologyTypeEnum.POLICY_RECEIPT_REVIEW_WORK_FLOW_STATUS.name());
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_RECEIPT_REVIEW_LIST_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<PolicyListResponse>> queryPolicyListByBranch(PolicyListRequest policyListRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        try {
             getLogger().info("团险保单查询1",DateUtils.timeStrToString(System.currentTimeMillis()));
            //查询新旧单号关联表，用于新生成规则单号查询
            if (AssertUtils.isNotNull(policyListRequest.getKeyword())) {
                ResultObject<List<String>> listResultObject = policyBaseBusinessService.queryPolicyNos(policyListRequest.getKeyword());
                if (AssertUtils.isNotNull(listResultObject)) {
                    List<String> policyIds = listResultObject.getData();
                    if (AssertUtils.isNotEmpty(policyIds)) {
                        policyListRequest.setPolicyIds(policyIds);
                    }
                }
            }
            getLogger().info("团险保单查询2",DateUtils.timeStrToString(System.currentTimeMillis()));

            List<PolicyListBo> policyListBos = policyQueryBaseService.loadPolicyListByBranch(currentLoginUsers.getUserId(), policyListRequest, LIFE_INSURANCE_GROUP.name());
            getLogger().info("团险保单查询4",DateUtils.timeStrToString(System.currentTimeMillis()));
            List<PolicyListResponse> policyListResponses = groupPolicyTransData.transPolicyList(currentLoginUsers, policyListBos, null);
            getLogger().info("团险保单查询5",DateUtils.timeStrToString(System.currentTimeMillis()));
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(policyListBos) ? policyListBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(policyListRequest.getCurrentPage(), policyListRequest.getPageSize(), totalLine, policyListResponses);
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<PolicyListResponse>> queryPolicyListByUserId(PolicyListRequest policyListRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        try {
            List<PolicyListBo> policyListBos = policyQueryBaseService.loadPolicyListByUserId(currentLoginUsers.getUserId(), policyListRequest, LIFE_INSURANCE_GROUP.name());
            List<PolicyListResponse> policyListResponses = groupPolicyTransData.transPolicyList(currentLoginUsers, policyListBos, null);
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(policyListBos) ? policyListBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(policyListRequest.getCurrentPage(), policyListRequest.getPageSize(), totalLine, policyListResponses);
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    /**
     * 团险保单打印
     *
     * @param printInfoId 保单打印信息ID
     * @param language    打印语言
     * @return
     */
    @Override
    public ResultObject getPolicyPrintFile(HttpServletResponse response, String printInfoId, String language) throws Exception {
        ResultObject<AttachmentByteResponse> resultObject = new ResultObject<>();
        // 参数校验
        AssertUtils.isNotEmpty(getLogger(), printInfoId, PolicyErrorConfigEnum.POLICY_PARAMETER_PRINT_INFO_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), language, PolicyErrorConfigEnum.POLICY_PARAMETER_LANGUAGE_IS_NOT_NULL);
        if (!(KM_KH.name().equals(language) || EN_US.name().equals(language) || ZH_CN.name().equals(language))) {
            throwsException(PolicyErrorConfigEnum.POLICY_PARAMETER_LANGUAGE_FORMAT_INVALID);
        }
        // 查询打印数据
        // 下载PDF保单文件
        PolicyPrintInfoBo policyPrintInfoBo = policyPrintDao.getPolicyPrintData(printInfoId, language);
        AssertUtils.isNotNull(getLogger(), policyPrintInfoBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRINT_INFO_IS_NOT_FOUND_OBJECT);
        AttachmentByteResponse data = this.getAttachmentByteResFc(language, policyPrintInfoBo);
        // 返回文件流
        if (!AssertUtils.isNotNull(data)) {
            return resultObject;
        }
        byte[] bytesFile = data.getFileByte();
        response.setHeader("Content-Type", "application/pdf");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(policyPrintInfoBo.getPolicyNo() + ".pdf", "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST,GET");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        OutputStream outputStream = response.getOutputStream();
        outputStream.write(bytesFile);
        outputStream.close();
        resultObject.setData(data);
        return resultObject;
    }

    @Override
    public ResultObject<CoiUrlResponse> queryCoiUrlList(String policyId, String language) {
        ResultObject<CoiUrlResponse> resultObject = new ResultObject<>();
        CoiUrlResponse coiUrlResponse = new CoiUrlResponse();
        List<CoiUrlDetailResponse> coiUrls = new ArrayList<>();
        AssertUtils.isNotEmpty(this.getLogger(),policyId,PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(),language,PolicyErrorConfigEnum.POLICY_PARAMETER_LANGUAGE_IS_NOT_NULL);
        List<PolicyAttachmentPo> policyAttachmentList = policyBaseDao.getPolicyAttachmentList(policyId, COI_BOOK.name());
        if (!AssertUtils.isNotEmpty(policyAttachmentList)) {
            return resultObject;
        }
        //查询列表基础数据
        PolicyListBo policyListBo = policyQueryBaseDao.queryPolicyListBo(policyId);
        List<BranchResponse> saleBranches = platformBranchApi.branchsPost(Arrays.asList(policyListBo.getSalesBranchId())).getData();
        List<AgentListResponse> agentListResponses = agentBaseAgentApi.queryOneAgentById(Arrays.asList(policyListBo.getAgentId())).getData();
        policyAttachmentList.stream().filter(policyAttachmentPo -> language.equals(policyAttachmentPo.getLanguage())).findFirst().ifPresent(policyAttachmentPo -> {
            String attachmentId = policyAttachmentPo.getAttachmentId();
            //查询coi批次记录,兼容分批次产生和不分批次产生
            ResultObject<List<CoiBatchResponse>> listResultObject = attachmentApi.attachmentCoiList(attachmentId);
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                List<CoiBatchResponse> coiBatchResponses = listResultObject.getData();
                final int[] i = {1};
                coiBatchResponses.forEach(coiBatchResponse -> {
                    if (AssertUtils.isNotEmpty(coiBatchResponse.getUrl())) {
                        CoiUrlDetailResponse coiUrlDetailResponse = new CoiUrlDetailResponse();
                        if (AssertUtils.isNotNull(policyListBo)) {
                            coiUrlDetailResponse = (CoiUrlDetailResponse) this.converterObject(policyListBo, CoiUrlDetailResponse.class);
                            coiUrlDetailResponse.setPolicyNo(policyListBo.getPolicyNo()+"-"+i[0]);
                        }
                        if (AssertUtils.isNotNull(saleBranches)) {
                            coiUrlDetailResponse.setSalesBranchName(saleBranches.get(0).getBranchName());
                        }
                        if (AssertUtils.isNotNull(agentListResponses)) {
                            coiUrlDetailResponse.setAgentCode(agentListResponses.get(0).getAgentCode());
                            coiUrlDetailResponse.setAgentName(agentListResponses.get(0).getAgentName());
                        }
                        coiUrlDetailResponse.setCoiUrl(coiBatchResponse.getUrl());
                        coiUrlDetailResponse.setName(i[0]);
                        coiUrlDetailResponse.setAttachmentId(coiBatchResponse.getAttachmentId());
                        coiUrls.add(coiUrlDetailResponse);
                        i[0]++;
                    }
                });
            }else {
                ResultObject<List<AttachmentResponse>> listResultObject1 = attachmentApi.attachmentList(Arrays.asList(attachmentId));
                if (!AssertUtils.isResultObjectListDataNull(listResultObject1)) {
                    List<AttachmentResponse> data = listResultObject1.getData();
                    CoiUrlDetailResponse coiUrlDetailResponse = new CoiUrlDetailResponse();
                    if (AssertUtils.isNotNull(policyListBo)) {
                        coiUrlDetailResponse = (CoiUrlDetailResponse) this.converterObject(policyListBo, CoiUrlDetailResponse.class);
                        coiUrlDetailResponse.setPolicyNo(policyListBo.getPolicyNo()+"-1");
                    }
                    if (AssertUtils.isNotNull(saleBranches)) {
                        coiUrlDetailResponse.setSalesBranchName(saleBranches.get(0).getBranchName());
                    }
                    if (AssertUtils.isNotNull(agentListResponses)) {
                        coiUrlDetailResponse.setAgentCode(agentListResponses.get(0).getAgentCode());
                        coiUrlDetailResponse.setAgentName(agentListResponses.get(0).getAgentName());
                    }
                    coiUrlDetailResponse.setCoiUrl(data.get(0).getUrl());
                    coiUrlDetailResponse.setName(1);
                    coiUrlDetailResponse.setAttachmentId(attachmentId);
                    coiUrls.add(coiUrlDetailResponse);
                }
            }
        });
        coiUrlResponse.setCoiUrls(coiUrls);
        resultObject.setData(coiUrlResponse);
        return resultObject;
    }

    /*@Override
    @Transactional
    public ResultObject saveCoiPrintInfos(Users users, List<PolicyCoiPrintInfoVo> policyCoiPrintInfoVos) {
        ResultObject resultObject = new ResultObject();
        try {
            if (!AssertUtils.isNotEmpty(policyCoiPrintInfoVos)) {
                return null;
            }
            List<PolicyCoiPrintInfoPo> policyCoiPrintInfoPos = new ArrayList<>();
            policyCoiPrintInfoVos.forEach(policyCoiPrintInfoVo -> {
                PolicyCoiPrintInfoPo policyCoiPrintInfo = policyExtDao.getPolicyCoiPrintInfo(policyCoiPrintInfoVo);
                if (AssertUtils.isNotNull(policyCoiPrintInfo)) {
                    ClazzUtils.copyPropertiesIgnoreNull(policyCoiPrintInfoVo, policyCoiPrintInfo);
                }else {
                    policyCoiPrintInfo = new PolicyCoiPrintInfoPo();
                    ClazzUtils.copyPropertiesIgnoreNull(policyCoiPrintInfoVo, policyCoiPrintInfo);
                }
                policyCoiPrintInfoPos.add(policyCoiPrintInfo);
            });
            policyBoService.savePolicyCoiPrintPos(policyCoiPrintInfoPos);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return null;
    }*/

    /**
     * 下载PDF附件
     *
     * @param language          附件语言
     * @param policyPrintInfoBo 打印ID
     * @return
     */
    private AttachmentByteResponse getAttachmentByteResFc(String language, PolicyPrintInfoBo policyPrintInfoBo) throws RequestException {
        // 生成团险保险合同并保存
        List<String> attachmentIdList = getGroupPolicyAttachmentIdList(language, policyPrintInfoBo);
        getLogger().info("getAttachmentByteResFc|attachmentIds:" + JSON.toJSONString(attachmentIdList));

        // 下载PDF附件
        ResultObject<AttachmentByteResponse> attachmentByteObject = attachmentPDFDocumentApi.electronicPolicyDownload(attachmentIdList.toArray(new String[attachmentIdList.size()]));
        AssertUtils.isResultObjectError(this.getLogger(), attachmentByteObject);
        return attachmentByteObject.getData();
    }

    /**
     * put 添加投保附件
     *
     * @param attachmentMap
     * @param policyAttachmentBoList
     * @param language
     * @param applyId
     */
    private void putApplyAttachmentId(Map<String, String> attachmentMap, List<PolicyAttachmentBo> policyAttachmentBoList, String language, String applyId) {
        Optional<PolicyAttachmentBo> applyAttachmentBoOptional = policyAttachmentBoList.stream().filter(bo -> applyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && APPLY_BOOK.name().equals(bo.getAttachmentTypeCode())).findFirst();
        if (applyAttachmentBoOptional.isPresent()) {
            attachmentMap.put(APPLY_BOOK.name(), applyAttachmentBoOptional.get().getAttachmentId());
        } else {
            // 附件为空, 调微服务生成附件
            ResultObject<List<AttachmentResponse>> listResultObject = groupApplyApi.applyPdfGenerate(applyId, language);
            AssertUtils.isResultObjectError(this.getLogger(), listResultObject);
            List<AttachmentResponse> attachmentResponses = listResultObject.getData();
            attachmentResponses.stream()
                    .filter(attachmentResponse -> APPLY_BOOK.name().equals(attachmentResponse.getTemplateType()))
                    .findFirst().ifPresent(attachmentResponse -> attachmentMap.put(APPLY_BOOK.name(), attachmentResponse.getMediaId()));
        }
    }

    /**
     * put 添加保单相关附件
     *
     * @param attachmentMap
     * @param policyAttachmentBoList
     * @param language
     * @param policyId
     */
    private void putPolicyAttachmentId(Map<String, String> attachmentMap, List<PolicyAttachmentBo> policyAttachmentBoList, String language, String policyId) {
        Optional<PolicyAttachmentBo> policyAttachmentBoOptional = policyAttachmentBoList.stream()
                .filter(bo -> policyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && POLICY_BOOK.name().equals(bo.getAttachmentTypeCode()))
                .findFirst();
        if (policyAttachmentBoOptional.isPresent()) {
            getLogger().info("========================已有保单附件========================");
            // 保单
            attachmentMap.put(POLICY_BOOK.name(), policyAttachmentBoOptional.get().getAttachmentId());
            // 首刊 封面
            policyAttachmentBoList.stream()
                    .filter(bo -> policyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && FIRST_ISSUE_BOOK.name().equals(bo.getAttachmentTypeCode()))
                    .findFirst().ifPresent(policyAttachmentBo -> attachmentMap.put(FIRST_ISSUE_BOOK.name(), policyAttachmentBo.getAttachmentId()));
            /***客户服务指南***/
            Optional<PolicyAttachmentBo> customerServiceInstructionBookOptional = policyAttachmentBoList.stream().filter(bo -> CUSTOMER_SERVICE_INSTRUCTION_BOOK.name().equals(bo.getAttachmentTypeCode()) && language.equals(bo.getLanguage())).findFirst();
            if (customerServiceInstructionBookOptional.isPresent()) {
                attachmentMap.put(CUSTOMER_SERVICE_INSTRUCTION_BOOK.name(), customerServiceInstructionBookOptional.get().getAttachmentId());
            }
            /***签收回执***/
            Optional<PolicyAttachmentBo> acknowledgmentLetterBookOptional = policyAttachmentBoList.stream().filter(bo -> ACKNOWLEDGMENT_LETTER_BOOK.name().equals(bo.getAttachmentTypeCode()) && language.equals(bo.getLanguage())).findFirst();
            if (acknowledgmentLetterBookOptional.isPresent()) {
                attachmentMap.put(ACKNOWLEDGMENT_LETTER_BOOK.name(), acknowledgmentLetterBookOptional.get().getAttachmentId());
            }
            /***被保险人清单***/
            Optional<PolicyAttachmentBo> policyInsuredListOptional = policyAttachmentBoList.stream().filter(bo -> POLICY_INSURED_LIST.name().equals(bo.getAttachmentTypeCode()) && language.equals(bo.getLanguage())).findFirst();
            if (policyInsuredListOptional.isPresent()) {
                attachmentMap.put(POLICY_INSURED_LIST.name(), policyInsuredListOptional.get().getAttachmentId());
            }
            /***保险条款***/
            List<PolicyAttachmentBo> policyTermsBookList = policyAttachmentBoList.stream().filter(bo -> (POLICY_TERMS_BOOK.name().equals(bo.getAttachmentTypeCode()) || PREMIUM_RATE_AND_CASH_VALUE.name().equals(bo.getAttachmentTypeCode())) && language.equals(bo.getLanguage())).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(policyTermsBookList)) {
                Comparator<PolicyAttachmentBo> comparator = (p1, p2) -> (int) (p1.getAttachmentSeq() - p2.getAttachmentSeq());
                policyTermsBookList.sort(comparator);
                List<String> attachmentIdList = policyTermsBookList.stream().map(PolicyAttachmentBo::getAttachmentId).collect(Collectors.toList());
                attachmentMap.put(POLICY_TERMS_BOOK.name(), JSON.toJSONString(attachmentIdList));
            }
           /* *//***COI***//*
            Optional<PolicyAttachmentBo> coiBookBoOptional = policyAttachmentBoList.stream().filter(bo -> policyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && COI_BOOK.name().equals(bo.getAttachmentTypeCode())).findFirst();
            if (coiBookBoOptional.isPresent()) {
                attachmentMap.put(COI_BOOK.name(), coiBookBoOptional.get().getAttachmentId());
            }*/
        } else {
            getLogger().info("========================没有有保单附件========================");
            // 生成附件并保存
            List<AttachmentResponse> attachmentResponses = groupPolicyTransData.groupAttachmentPdfGenerate(policyId, language);

            Optional<PolicyAttachmentBo> policyAttachmentBoCoiBookOptional = policyAttachmentBoList.stream()
                    .filter(bo -> policyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && COI_BOOK.name().equals(bo.getAttachmentTypeCode()))
                    .findFirst();
            // 保单
            attachmentResponses.stream()
                    .filter(bo -> POLICY_BOOK.name().equals(bo.getTemplateType()))
                    .findFirst().ifPresent(attachmentResponse -> attachmentMap.put(POLICY_BOOK.name(), attachmentResponse.getMediaId()));
            // 首刊 封面
            attachmentResponses.stream()
                    .filter(attachmentResponse -> FIRST_ISSUE_BOOK.name().equals(attachmentResponse.getTemplateType()))
                    .findFirst().ifPresent(attachmentResponse -> attachmentMap.put(FIRST_ISSUE_BOOK.name(), attachmentResponse.getMediaId()));

            /***客户服务指南***/
            Optional<AttachmentResponse> customerServiceInstructionBookOptional = attachmentResponses.stream().filter(bo -> CUSTOMER_SERVICE_INSTRUCTION_BOOK.name().equals(bo.getTemplateType())).findFirst();
            if (customerServiceInstructionBookOptional.isPresent()) {
                attachmentMap.put(CUSTOMER_SERVICE_INSTRUCTION_BOOK.name(), customerServiceInstructionBookOptional.get().getMediaId());
            }
            /***签收回执***/
            Optional<AttachmentResponse> acknowledgmentLetterBookOptional = attachmentResponses.stream().filter(bo -> ACKNOWLEDGMENT_LETTER_BOOK.name().equals(bo.getTemplateType())).findFirst();
            if (acknowledgmentLetterBookOptional.isPresent()) {
                attachmentMap.put(ACKNOWLEDGMENT_LETTER_BOOK.name(), acknowledgmentLetterBookOptional.get().getMediaId());
            }
            /***被保险人清单***/
            Optional<AttachmentResponse> policyInsuredListOptional = attachmentResponses.stream().filter(bo -> POLICY_INSURED_LIST.name().equals(bo.getTemplateType())).findFirst();
            if (policyInsuredListOptional.isPresent()) {
                attachmentMap.put(POLICY_INSURED_LIST.name(), policyInsuredListOptional.get().getMediaId());
            }
            /***保险条款***/
            List<AttachmentResponse> policyTermsBookList = attachmentResponses.stream().filter(bo -> POLICY_TERMS_BOOK.name().equals(bo.getTemplateType()) || PREMIUM_RATE_AND_CASH_VALUE.name().equals(bo.getTemplateType())).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(policyTermsBookList)) {
                Comparator<AttachmentResponse> comparator = (p1, p2) -> (int) (p1.getSeq() - p2.getSeq());
                policyTermsBookList.sort(comparator);
                List<String> attachmentIdList = policyTermsBookList.stream().map(AttachmentResponse::getMediaId).collect(Collectors.toList());
                attachmentMap.put(POLICY_TERMS_BOOK.name(), JSON.toJSONString(attachmentIdList));
            }
            /***COI***//*
            if (policyAttachmentBoCoiBookOptional.isPresent()) {
                attachmentMap.put(COI_BOOK.name(), policyAttachmentBoCoiBookOptional.get().getAttachmentId());
            }*/
        }
    }

    private ResultObject<BasePageResponse<PolicyListResponse>> getWorkflowList(Users users, WaitingTaskRequest tasksRequest, PolicyListRequest policyListRequest, String policyType, String workflowStatus) {
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        List<PolicyListBo> policyListBos = policyQueryBaseService.loadPolicyListByWorkFlow(tasksRequest, policyListRequest, policyType);
        if (!AssertUtils.isNotEmpty(policyListBos)) {
            return resultObject;
        }
        List<PolicyListResponse> policyListResponses = groupPolicyTransData.transPolicyList(users, policyListBos, workflowStatus);
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(policyListBos) ? policyListBos.get(0).getTotalLine() : null;
        BasePageResponse basePageResponse = BasePageResponse.getData(policyListRequest.getCurrentPage(), policyListRequest.getPageSize(), totalLine, policyListResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    @Override
    @Async
    public void produceAttachment(Users currentLoginUsers, PolicyBo policyBo) {
        log.info("产生附件1");
        String applyId = policyBo.getApplyId();
        String policyId = policyBo.getPolicyId();
        // 主险产品ID
        String productMainId = policyBo.getListCoverage().stream()
                .filter(coverageBo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag()))
                .findFirst().get().getProductId();
        //提前生成投保单单证
        groupApplyApi.applyPdfGenerate(applyId, KM_KH.name());
        groupApplyApi.applyPdfGenerate(applyId, ZH_CN.name());
        groupApplyApi.applyPdfGenerate(applyId, EN_US.name());
        //提前生成 保险证PDF
        groupPolicyTransData.groupAttachmentPdfGenerate(policyId, KM_KH.name());
        groupPolicyTransData.groupAttachmentPdfGenerate(policyId, ZH_CN.name());
        groupPolicyTransData.groupAttachmentPdfGenerate(policyId, EN_US.name());
        //提前生成 29号coi
        if ("PRO880000000000029".equals(productMainId)) {
            PolicyThreadLocalBo.asyncFlag.set(true);
            this.getLogger().info("29号产品生成coi生成柬文开始===========================");
            groupPolicyTransData.groupAttachmentPdf29CoiGenerate(policyId, KM_KH.name());
            this.getLogger().info("29号产品生成coi生成中文开始===========================");
            groupPolicyTransData.groupAttachmentPdf29CoiGenerate(policyId, ZH_CN.name());
            this.getLogger().info("29号产品生成coi生成英文开始===========================");
            groupPolicyTransData.groupAttachmentPdf29CoiGenerate(policyId, EN_US.name());
            PolicyThreadLocalBo.asyncFlag.set(false);
        }

        //产生单证发送邮箱
        PolicyBo policyPo = policyBaseService.queryPolicyBo(policyId);
        PolicyApplicantBo policyApplicantBo = policyBaseService.queryPolicyApplicant(policyId);
        policyPo.setApplicant(policyApplicantBo);
        if (AssertUtils.isNotNull(policyApplicantBo) && AssertUtils.isNotEmpty(policyApplicantBo.getCompanyContractEmail())) {
            String receiverApplicantEmail = policyApplicantBo.getCompanyContractEmail();
            log.info(" SEND EMAIL :" + receiverApplicantEmail + " POLICY_ID " + policyPo.getPolicyId());
            policyBusinessService.sendPolicy(policyPo, receiverApplicantEmail);
        }
        // 提前生成 保单打印PDF
        log.info("【团险投保单转保单】 生成 保单打印PDF ...开始 ->> policyId：{}", policyId);
        String printInfoId = null;
        PolicyPrintInfoBo policyPrintInfoBo = policyExtDao.loadPolicyPrintInfoBo(policyId);
        if (AssertUtils.isNotNull(policyPrintInfoBo)) {
            printInfoId = policyPrintInfoBo.getPrintInfoId();
        }
        // 生成 POLICY_ALL_BOOK
        policyPrintService.generatePolicyAllPdf(printInfoId, TerminologyConfigEnum.LANGUAGE.EN_US.name());
        policyPrintService.generatePolicyAllPdf(printInfoId, TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
        policyPrintService.generatePolicyAllPdf(printInfoId, TerminologyConfigEnum.LANGUAGE.KM_KH.name());
        log.info("【团险投保单转保单】 生成 保单打印PDF ...完成 ->> policyId：{}", policyId);
        log.info("产生附件2");
    }

    /**
     * 我的保单列表-团险保单
     *
     * @param appGroupPolicyQueryRequest 请求参数
     * @param users                      用户
     * @return PolicyListResponse
     */
    @Override
    public ResultObject<BasePageResponse<AppGroupPolicyListResponse>> getAppGroupPolicyList(AppGroupPolicyQueryRequest appGroupPolicyQueryRequest, Users users) {
        AssertUtils.isNotNull(getLogger(), appGroupPolicyQueryRequest, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
        AssertUtils.isNotEmpty(getLogger(), appGroupPolicyQueryRequest.getAgentId(), PolicyErrorConfigEnum.POLICY_QUERY_AGENT_ID_IS_NOT_NULL);
        ResultObject<BasePageResponse<AppGroupPolicyListResponse>> resultObject = new ResultObject<>();
        BasePageResponse<AppGroupPolicyListResponse> policyListResponseBasePageResponse = new BasePageResponse<>();
        //查询状态的分类列表
        List<String> listCodes = new ArrayList<>();
        if (AssertUtils.isNotEmpty(appGroupPolicyQueryRequest.getPolicyStatus())) {
            ResultObject<List<SyscodeRespFc>> listResultObject = platformBaseInternationServiceApi.getTerminologyList(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS.name(), appGroupPolicyQueryRequest.getPolicyStatus());
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                listCodes = listResultObject.getData().stream().map(SyscodeRespFc::getCodeKey).distinct().collect(Collectors.toList());
            }
        }
        //查询保单数据
        List<AppGroupPolicyListResponse> groupPolicyQueryBos = policyListDao.getGroupListPolicyExtBo(appGroupPolicyQueryRequest, listCodes);
        if (AssertUtils.isNotNull(groupPolicyQueryBos)) {
            groupPolicyQueryBos.forEach(appGroupPolicyListResponse -> {
                //回执凭证上传标识
                PolicyReceiptInfoPo policyReceiptInfoPo = policyBaseService.queryPolicyReceiptInfo(appGroupPolicyListResponse.getPolicyId());
                List<PolicyAttachmentPo> policyAttachmentPos = policyBaseService.listPolicyAttachment(appGroupPolicyListResponse.getPolicyId(), PolicyTermEnum.CERTIFY_ATTACHMENT_TYPE.RECEIPT_IMAGE.name());
                // 3.9.0调整 回执凭证上传按钮展示条件：回执新不为空且状态为待回执，回执影像为空，保单状态“有效”或“待生效”
                boolean pendingReceiptFlag = AssertUtils.isNotNull(policyReceiptInfoPo)
                        && PolicyTermEnum.RECEIPT_STATUS.WAIT_RECEIPT.name().equals(policyReceiptInfoPo.getReceiptStatus())
                        && !AssertUtils.isNotEmpty(policyAttachmentPos)
                        && (PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name().equals(appGroupPolicyListResponse.getPolicyStatus())
                        || PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_PENDING_EFFECT.name().equals(appGroupPolicyListResponse.getPolicyStatus()));
                if (pendingReceiptFlag) {
                    appGroupPolicyListResponse.setColorValue("#2656A6");
                    appGroupPolicyListResponse.setButtonCode("PENDING_RECEIPT");
                    appGroupPolicyListResponse.setButtonName(languageCodeTransData.queryOneInternational("BUTTON_CODE", "PENDING_RECEIPT", null));
                }

                //4.8.5团险续期增加按钮展示
                PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(appGroupPolicyListResponse.getPolicyId(), PolicyTermEnum.OPERATION_CODE.GROUP_INSTALLMENT_PENDING_PAYMENT.name());
                if (AssertUtils.isNotNull(policyOperationPo)) {
                    BaseOperationPo baseOperationPo = policyBaseService.queryBaseOperationPoByOperationCode(policyOperationPo.getOperationCode());
                    if (AssertUtils.isNotNull(baseOperationPo) && PolicyTermEnum.YES_NO.NO.name().equals(baseOperationPo.getFinishFlag())) {
                        //有操作，需要展示按钮
                        appGroupPolicyListResponse.setColorValue(baseOperationPo.getColorValue());
                        appGroupPolicyListResponse.setButtonCode(baseOperationPo.getOperationCode());
                        appGroupPolicyListResponse.setButtonName(languageCodeTransData.getCodeNameByInternationalKey(InternationalTypeEnum.OPERATION_CODE.name(), baseOperationPo.getOperationCode(), users.getLanguage()));
                    }
                }
                if (AssertUtils.isNotEmpty(appGroupPolicyListResponse.getButtonCode())) {
                    appGroupPolicyListResponse.setIndex(1);
                }
            });
            groupPolicyQueryBos.sort(Comparator.comparing(AppGroupPolicyListResponse::getIndex));
            policyListResponseBasePageResponse.setData(groupPolicyQueryBos);
            policyListResponseBasePageResponse.setTotalLine(groupPolicyQueryBos.get(0).getTotalLine());
            resultObject.setData(policyListResponseBasePageResponse);
        }
        return resultObject;
    }

    /**
     * 我的保单-团险保单详情
     *
     * @param policyId 保单ID
     * @param users    用户
     * @return GroupPolicyQueryDetailResponse
     */
    @Override
    public ResultObject<AppGroupPolicyDetailResponse> getAppGroupPolicyInfo(String policyId, Users users) {
        ResultObject<AppGroupPolicyDetailResponse> resultObject = new ResultObject<>();
        AppGroupPolicyDetailResponse groupPolicyQueryDetail = new AppGroupPolicyDetailResponse();
        PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);
        AssertUtils.isNotNull(log, policyPo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        ClazzUtils.copyPropertiesIgnoreNull(policyPo, groupPolicyQueryDetail);

        PolicyApplicantBo policyApplicantBo = policyBaseService.queryPolicyApplicant(policyId);
        AssertUtils.isNotNull(getLogger(), policyApplicantBo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_APPLICANT_IS_NOT_FOUND_OBJECT);
        AppGroupPolicyApplicantResponse groupApplicant = new AppGroupPolicyApplicantResponse();
        ClazzUtils.copyPropertiesIgnoreNull(policyApplicantBo, groupApplicant);

        if (AssertUtils.isNotEmpty(groupApplicant.getCompanyIndustry())) {
            ResultObject<CareerNameResponse> careerNameResponseResultObject = platformCareerApi.careerNameGet(groupApplicant.getCompanyIndustry());
            if (!AssertUtils.isResultObjectDataNull(careerNameResponseResultObject)) {
                groupApplicant.setCompanyIndustryName(careerNameResponseResultObject.getData().getCareerName());
            }
        }
        groupPolicyQueryDetail.setGroupApplicant(groupApplicant);
        groupPolicyQueryDetail.setCompanyName(policyApplicantBo.getCompanyName());

        //查询被保人
        List<PolicyInsuredBo> policyInsuredBos = policyBaseService.getPolicyAllInsuredList(policyId);
        AssertUtils.isNotEmpty(log, policyInsuredBos, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_INSURED_IS_NOT_FOUND_OBJECT);
        groupPolicyQueryDetail.setInsuredSum(policyInsuredBos.size() + "");
        List<AppGroupPolicyInsuredListResponse> groupInsuredList = new ArrayList<>();
        policyInsuredBos.forEach(policyInsuredBo -> {
            AppGroupPolicyInsuredListResponse appGroupPolicyInsuredListResponse = new AppGroupPolicyInsuredListResponse();
            appGroupPolicyInsuredListResponse.setPolicyId(policyInsuredBo.getPolicyId());
            appGroupPolicyInsuredListResponse.setInsuredId(policyInsuredBo.getInsuredId());
            appGroupPolicyInsuredListResponse.setName(policyInsuredBo.getName());
            appGroupPolicyInsuredListResponse.setSex(policyInsuredBo.getSex());
            appGroupPolicyInsuredListResponse.setOccupationCode(policyInsuredBo.getOccupationCode());
            appGroupPolicyInsuredListResponse.setInsuredStatus(policyInsuredBo.getInsuredStatus());
            groupInsuredList.add(appGroupPolicyInsuredListResponse);
        });
        groupPolicyQueryDetail.setGroupInsuredList(groupInsuredList);

        //查询保单公共险种
        ResultObject<List<SyscodeResponse>> queryInternational = platformInternationalBaseApi.queryInternational("PRODUCT_ID", null);
        List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.listAllGroupPolicyCoverage(policyId);
        AssertUtils.isNotEmpty(log, policyCoveragePos, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_COVERAGE_IS_NOT_FOUND);
        policyCoveragePos.stream().filter(policyCoveragePo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoveragePo.getPrimaryFlag()))
                .findFirst().ifPresent(policyCoveragePo -> {
                    groupPolicyQueryDetail.setProductId(policyCoveragePo.getProductId());
                    groupPolicyQueryDetail.setProductName(policyCoveragePo.getProductName());
                    if (!AssertUtils.isResultObjectListDataNull(queryInternational)) {
                        queryInternational.getData().stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals(policyCoveragePo.getProductId())).findFirst()
                                .ifPresent(syscodeResponse -> groupPolicyQueryDetail.setProductName(syscodeResponse.getCodeName()));
                    }
                });
        List<String> additionalProductId = policyCoveragePos.stream().filter(policyCoveragePo -> !PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoveragePo.getPrimaryFlag()))
                .map(PolicyCoveragePo::getProductId).collect(Collectors.toList());
        List<String> additionalProductName = new ArrayList<>();
        if (!AssertUtils.isResultObjectListDataNull(queryInternational) && AssertUtils.isNotEmpty(additionalProductId)) {
            additionalProductId.forEach(s -> queryInternational.getData().stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals(s)).findFirst()
                    .ifPresent(syscodeResponse -> additionalProductName.add(syscodeResponse.getCodeName())));
        }
        groupPolicyQueryDetail.setAdditionalProductName(additionalProductName);

        //回执凭证上传标识
        PolicyReceiptInfoPo policyReceiptInfoPo = policyBaseService.queryPolicyReceiptInfo(policyId);
        List<PolicyAttachmentPo> policyAttachmentPos = policyBaseService.listPolicyAttachment(policyId, PolicyTermEnum.CERTIFY_ATTACHMENT_TYPE.RECEIPT_IMAGE.name());
        // 3.9.0调整 回执凭证上传按钮展示条件：回执新不为空且状态为待回执，回执影像为空，保单状态“有效”或“待生效”
        boolean pendingReceiptFlag = AssertUtils.isNotNull(policyReceiptInfoPo)
                && PolicyTermEnum.RECEIPT_STATUS.WAIT_RECEIPT.name().equals(policyReceiptInfoPo.getReceiptStatus())
                && !AssertUtils.isNotEmpty(policyAttachmentPos)
                && (PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name().equals(policyPo.getPolicyStatus())
                || PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_PENDING_EFFECT.name().equals(policyPo.getPolicyStatus()));
        if (pendingReceiptFlag) {
            groupPolicyQueryDetail.setReceiptImageFlag(TerminologyConfigEnum.WHETHER.YES.name());
            groupPolicyQueryDetail.setColorValue("#2656A6");
            groupPolicyQueryDetail.setButtonCode("PENDING_RECEIPT");
            groupPolicyQueryDetail.setButtonName(languageCodeTransData.queryOneInternational("BUTTON_CODE", "PENDING_RECEIPT", null));
        }
        //4.8.5团险续期增加按钮展示
        PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyId, PolicyTermEnum.OPERATION_CODE.GROUP_INSTALLMENT_PENDING_PAYMENT.name());
        if (AssertUtils.isNotNull(policyOperationPo)) {
            BaseOperationPo baseOperationPo = policyBaseService.queryBaseOperationPoByOperationCode(policyOperationPo.getOperationCode());
            if (AssertUtils.isNotNull(baseOperationPo) && PolicyTermEnum.YES_NO.NO.name().equals(baseOperationPo.getFinishFlag())) {
                ResultObject<RenewalAppDetailResponse> renewalAppDetailResponseResultObject = groupRenewalApi.queryGroupInstallmentAppDetail(policyId);
                if (!AssertUtils.isResultObjectDataNull(renewalAppDetailResponseResultObject)) {
                    String businessId = renewalAppDetailResponseResultObject.getData().getBusinessId();
                    groupPolicyQueryDetail.setBusinessId(businessId);
                    ResultObject<ShortUrlResponse> shortUrlResponseResultObject = paymentApi.queryGuideUrl(businessId);
                    if (!AssertUtils.isResultObjectDataNull(shortUrlResponseResultObject)) {
                        groupPolicyQueryDetail.setButtonCode(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name());
                        groupPolicyQueryDetail.setButtonName(languageCodeTransData.getCodeNameByInternationalKey("BUTTON_CODE", "PAYMENT_INSTRUCTIONS", users.getLanguage()));
                        groupPolicyQueryDetail.setColorValue("#2656A6");
                        groupPolicyQueryDetail.setPayFlag(TerminologyConfigEnum.WHETHER.YES.name());
                    }
                    // 5.4.2-支付退回原因只会在APP上有支付指引按钮时展示 团险单目前只有续期才有支付指引按钮
                    ResultObject<PaymentStatusResponse> paymentStatusResponse = paymentBaseApi.queryOnePaymentDoByBusinessId(businessId);
                    if (!AssertUtils.isResultObjectDataNull(paymentStatusResponse)) {
                        PaymentStatusResponse paymentStatusResponseData = paymentStatusResponse.getData();
                        // 设置支付退回信息
                        if (PaymentTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name().equals(paymentStatusResponseData.getStatus())
                                && AssertUtils.isNotEmpty(paymentStatusResponseData.getErrorCode())) {
                            groupPolicyQueryDetail.setAdultTitleCode("PAYMENT_FAILED");// 支付退回标题 默认支付失败
                            groupPolicyQueryDetail.setAdultContent(paymentStatusResponseData.getRemark());
                        }
                    }
                }
            }
        }

        resultObject.setData(groupPolicyQueryDetail);
        return resultObject;
    }

    /**
     * 我的保单-团险保单被保人详情
     *
     * @param policyId  保单ID
     * @param insuredId 被保人ID
     * @param users     用户
     * @return AppGroupInsuredDetailResponse
     */
    @Override
    public ResultObject<AppGroupInsuredDetailResponse> getAppGroupPolicyInsuredDetail(String policyId, String insuredId, Users users) {
        ResultObject<AppGroupInsuredDetailResponse> resultObject = new ResultObject<>();
        AppGroupInsuredDetailResponse appGroupInsuredDetail = new AppGroupInsuredDetailResponse();
        List<PolicyInsuredBo> policyInsuredBos = policyBaseService.getPolicyAllInsuredList(policyId);
        AssertUtils.isNotEmpty(log, policyInsuredBos, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_INSURED_IS_NOT_FOUND_OBJECT);
        Optional<PolicyInsuredBo> first = policyInsuredBos.stream().filter(policyInsuredBo -> policyInsuredBo.getInsuredId().equals(insuredId)).findFirst();
        PolicyInsuredBo policyInsuredBo = new PolicyInsuredBo();
        if (first.isPresent()) {
            policyInsuredBo = first.get();
        } else {
            throwsException(PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_INSURED_IS_NOT_FOUND_OBJECT);
        }
        ClazzUtils.copyPropertiesIgnoreNull(policyInsuredBo, appGroupInsuredDetail);

        //产品信息包括已失效
        List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.listPolicyInsuredCoverageByValidFlag(policyId, insuredId);
        if (AssertUtils.isNotEmpty(policyCoveragePos)) {
            List<AppGroupInsuredProductResponse> listCoverage = (List<AppGroupInsuredProductResponse>) this.converterList(
                    policyCoveragePos, new TypeToken<List<AppGroupInsuredProductResponse>>() {
                    }.getType()
            );
            List<String> coverageIds = policyCoveragePos.stream().map(PolicyCoveragePo::getCoverageId).collect(Collectors.toList());
            // 查询险种档次信息包括已失效
            List<PolicyCoverageLevelPo> policyCoverageLevelPos = policyCoverageBaseService.listPolicyInsuredCoverageByValidFlag(coverageIds);
            if (AssertUtils.isNotEmpty(policyCoverageLevelPos)) {
                // 按险种ID分组
                Map<String, List<PolicyCoverageLevelPo>> coverageLevelPoMap =
                        policyCoverageLevelPos.parallelStream().collect(Collectors.groupingBy(PolicyCoverageLevelPo::getCoverageId));
                // 查询责任包括已失效
                List<PolicyCoverageDutyBo> policyCoverageDutyBos = policyCoverageBaseService.queryPolicyCoverageDutyValidFlagByCoverageIds(coverageIds);
                listCoverage.forEach(coverage -> {
                    if (AssertUtils.isNotEmpty(coverageLevelPoMap.get(coverage.getCoverageId()))) {
                        List<AppGroupInsuredProductExtResponse> productExtResponses = new ArrayList<>();
                        coverageLevelPoMap.get(coverage.getCoverageId()).forEach(coverageLevelPo -> {
                            AppGroupInsuredProductExtResponse policyCoverageExtResponse = new AppGroupInsuredProductExtResponse();
                            ClazzUtils.copyPropertiesIgnoreNull(coverageLevelPo, policyCoverageExtResponse);
                            if (AssertUtils.isNotEmpty(coverageLevelPo.getCoverageDutyId())) {
                                // 设置责任
                                policyCoverageDutyBos.stream()
                                        .filter(coverageDutyBo -> coverageDutyBo.getCoverageDutyId().equals(coverageLevelPo.getCoverageDutyId()))
                                        .findFirst().ifPresent(coverageDutyBo -> policyCoverageExtResponse.setDutyId(coverageDutyBo.getDutyId()));
                            }
                            productExtResponses.add(policyCoverageExtResponse);
                        });
                        coverage.setCoverageExt(productExtResponses);
                    }
                });
            }
            //排序
            if (AssertUtils.isNotEmpty(listCoverage)) {
                listCoverage.sort(Comparator.comparing(AppGroupInsuredProductResponse::getPrimaryFlag, Comparator.nullsLast(String::compareTo)).reversed()
                        .thenComparing(AppGroupInsuredProductResponse::getProductId, Comparator.nullsLast(String::compareTo)));
            }
            appGroupInsuredDetail.setInsuredProduct(listCoverage);
        }

        resultObject.setData(appGroupInsuredDetail);
        return resultObject;
    }

    @Override
    public AttachmentByteResponse generateGroupPolicyAllPdf(String printInfoId, String language) {
        PolicyPrintInfoBo policyPrintInfoBo = policyPrintDao.getPolicyPrintData(printInfoId, language);
        String policyId = policyPrintInfoBo.getPolicyId();
        AssertUtils.isNotNull(getLogger(), policyPrintInfoBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRINT_INFO_IS_NOT_FOUND_OBJECT);

        // 生成团险保险合同并保存
        List<String> attachmentIdList = getGroupPolicyAttachmentIdList(language, policyPrintInfoBo);
        getLogger().info("getGroupPolicyAttachmentIds|attachmentIds:" + JSON.toJSONString(attachmentIdList));

        // 合并保险合同为 POLICY_ALL_BOOK 并保存
        return policyBoService.getAttachmentByteResponse(language, policyId, attachmentIdList);
    }

    @Override
    public ResultObject groupCoiGenerate(String policyId) {
        PolicyThreadLocalBo.asyncFlag.set(true);
        this.getLogger().info("29号产品生成coi生成中文开始===========================");
        groupPolicyTransData.groupAttachmentPdf29CoiGenerate(policyId, ZH_CN.name());
        this.getLogger().info("29号产品生成coi生成英文开始===========================");
        groupPolicyTransData.groupAttachmentPdf29CoiGenerate(policyId, EN_US.name());
        this.getLogger().info("29号产品生成coi生成柬文开始===========================");
        groupPolicyTransData.groupAttachmentPdf29CoiGenerate(policyId, KM_KH.name());
        PolicyThreadLocalBo.asyncFlag.set(false);
        return ResultObject.success();
    }

    /**
     * 获取团险保险合同附件并保存: 投保单 保险证 首刊封面 客户服务指南 签收回执 被保险人清单 保险条款
     *
     * @param language
     * @param policyPrintInfoBo
     * @return
     */
    private List<String> getGroupPolicyAttachmentIdList(String language, PolicyPrintInfoBo policyPrintInfoBo) {
        String policyId = policyPrintInfoBo.getPolicyId();
        String applyId = policyPrintInfoBo.getApplyId();

        // 存放投保单、保单、被保人清单PDF附件ID
        Map<String, String> attachmentMap = new HashMap<>();
        List<PolicyAttachmentBo> policyAttachmentBoList = policyPrintDao.getPolicyAttachment(language, applyId, policyId);
        getLogger().info("========================生成保单附件========================");
        // 查询保单PDF附件ID
        this.putPolicyAttachmentId(attachmentMap, policyAttachmentBoList, language, policyId);
        getLogger().info("========================生成投保单附件========================");
        // 查询投保单PDF附件ID
        // 主险是17号产品 则无需再次打印投保单
        PolicyCoverageBo policyCoverageBo = policyListDao.queryPolicyMainCoverageByPolicyId(policyId);
        if (!ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(policyCoverageBo.getProductId())) {
            this.putApplyAttachmentId(attachmentMap, policyAttachmentBoList, language, applyId);
        }

        List<String> attachmentIdList = new ArrayList<>();
        attachmentIdList.add(language);
        PolicyTermEnum.ATTACHMENT_TYPE_FLAG[] attachmentTypeFlags = PolicyTermEnum.ATTACHMENT_TYPE_FLAG.values();
        for (PolicyTermEnum.ATTACHMENT_TYPE_FLAG attachmentTypeFlag : attachmentTypeFlags) {
            String attachmentId = attachmentMap.get(attachmentTypeFlag.name());
            if (!AssertUtils.isNotEmpty(attachmentId)) {
                continue;
            }
            // 条款修改页码
            if (POLICY_TERMS_BOOK.name().equals(attachmentTypeFlag.name())) {
                attachmentIdList.add("UPDATE_PAGE");
                List<String> strings = JSON.parseArray(attachmentId, String.class);
                attachmentIdList.addAll(strings);
                attachmentIdList.add("UPDATE_PAGE");
            } else {
                attachmentIdList.add(attachmentId);
            }
        }
        return attachmentIdList;
    }
}
