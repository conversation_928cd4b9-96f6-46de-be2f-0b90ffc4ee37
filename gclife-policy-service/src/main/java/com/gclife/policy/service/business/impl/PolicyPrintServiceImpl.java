package com.gclife.policy.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.app.api.AppPlanApi;
import com.gclife.apply.api.ApplyPaymentApi;
import com.gclife.apply.api.ApplyPlanApi;
import com.gclife.apply.model.respone.ApplyPlanResponse;
import com.gclife.apply.model.respone.AttachResponse;
import com.gclife.attachment.api.AttachmentPDFDocumentApi;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformInternationalApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.policy.core.jooq.tables.daos.PolicyCoverageDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyPrintInfoDao;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCoveragePo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPrintInfoPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyReceiptInfoPo;
import com.gclife.policy.dao.PolicyExtDao;
import com.gclife.policy.dao.PolicyPrintDao;
import com.gclife.policy.model.bo.PolicyAttachmentBo;
import com.gclife.policy.model.bo.PolicyBo;
import com.gclife.policy.model.bo.PolicyConditionPrintBo;
import com.gclife.policy.model.bo.PolicyPrintInfoBo;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.config.PolicyWorkflowTermEnum;
import com.gclife.policy.model.request.PolicyConditionPrintRequest;
import com.gclife.policy.model.response.PolicyPrintResponse;
import com.gclife.policy.service.base.PolicyBaseService;
import com.gclife.policy.service.business.MessageBusinessService;
import com.gclife.policy.service.business.PolicyPrintService;
import com.gclife.policy.service.business.group.GroupPolicyService;
import com.gclife.policy.service.data.PolicyBoService;
import com.gclife.policy.service.data.PolicyService;
import com.gclife.policy.validate.transfer.PolicyTransData;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.request.WaitingTaskRequest;
import com.gclife.workflow.model.response.WorkItemResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.*;
import static com.gclife.policy.model.config.PolicyTermEnum.ATTACHMENT_TYPE_FLAG.*;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL;

/**
 * <AUTHOR>
 * Created by cfw on 17-10-14.
 */
@Service
public class PolicyPrintServiceImpl extends BaseBusinessServiceImpl implements PolicyPrintService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PolicyPrintServiceImpl.class);

    @Autowired
    private PolicyPrintDao policyPrintDao;
    @Autowired
    private GroupPolicyService groupPolicyService;
    @Autowired
    private PolicyExtDao policyExtDao;
    @Autowired
    private PolicyCoverageDao policyCoverageDao;
    @Autowired
    public PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PolicyTransData policyTransData;
    @Autowired
    private PolicyPrintInfoDao policyPrintInfoDao;
    @Autowired
    private PolicyService policyService;
    @Autowired
    private AppPlanApi appPlanApi;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PlatformInternationalApi platformInternationalApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private ApplyPlanApi applyPlanApi;
    @Autowired
    private ApplyPaymentApi applyPaymentApi;
    @Autowired
    private AttachmentPDFDocumentApi attachmentPDFDocumentApi;
    @Autowired
    private PolicyBaseService policyBaseService;
    @Autowired
    private PolicyBoService policyBoService;

    @Override
    public ResultObject<BasePageResponse<PolicyPrintResponse>> getPrintPolicyList(String userId, PolicyConditionPrintRequest policyConditionPrintRequest) {
        ResultObject<BasePageResponse<PolicyPrintResponse>> resultObject = new ResultObject<>();

        try {
            policyConditionPrintRequest.setPolicyType(LIFE_INSURANCE_PERSONAL.name());
            BasePageResponse<PolicyPrintResponse> printPolicy = getPrintPolicy(null, userId, policyConditionPrintRequest);
            resultObject.setData(printPolicy);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_PRINT_ERROR);
            }
            e.printStackTrace();
        }

        return resultObject;
    }

    /**
     * 根据用户ID 和 查询多个条件 返回 查询打印保单查询数据
     *
     * @param userId ID用户 policyPrintRequest 查询条件封装类
     * @return 返回查询信息
     */
    @Override
    public ResultObject<BasePageResponse<PolicyPrintResponse>> getPolicyConditionPrintList(String userId, PolicyConditionPrintRequest policyConditionPrintRequest) {
        ResultObject<BasePageResponse<PolicyPrintResponse>> resultObject = new ResultObject<>();

        try {
            List<String> listPolicyIds = this.getWorkflowTaskRespList(userId);
            this.getLogger().error(JSON.toJSONString(listPolicyIds));
            if (!AssertUtils.isNotEmpty(listPolicyIds)) {
                return resultObject;
            }
            BasePageResponse<PolicyPrintResponse> printPolicy = getPrintPolicy(listPolicyIds, userId, policyConditionPrintRequest);
            resultObject.setData(printPolicy);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_PRINT_ERROR);
            }
            e.printStackTrace();
        }

        return resultObject;
    }

    public BasePageResponse<PolicyPrintResponse> getPrintPolicy(List<String> listPolicyIds, String userId, PolicyConditionPrintRequest policyConditionPrintRequest) {
        //获取当前用户子机构  id
        ResultObject<List<BranchResponse>> listResultObject = platformBranchApi.userManagerLeafBranchs();
        List<BranchResponse> branchRespFcList = listResultObject.getData();
        List<PolicyPrintResponse> policyPrintResponse = new ArrayList<>();
        if (!AssertUtils.isNotEmpty(branchRespFcList)) {
            return null;
        }
        List<String> branchIdList = branchRespFcList.stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());
        policyConditionPrintRequest.setPolicyType(LIFE_INSURANCE_PERSONAL.name());

        List<PolicyConditionPrintBo> policyConditionPrintList =
                policyPrintDao.getPolicyConditionPrintList(listPolicyIds, policyConditionPrintRequest, branchIdList);
        if (AssertUtils.isNotEmpty(policyConditionPrintList)) {
            //获取销售机构ID
            List<String> branchIds = policyConditionPrintList.stream()
                    .filter(policySeeBo -> AssertUtils.isNotEmpty(policySeeBo.getSalesBranchId()))
                    .map(PolicyConditionPrintBo::getSalesBranchId).distinct().collect(Collectors.toList());
            // 获取管理机构ID
            branchIds.addAll(policyConditionPrintList.stream()
                    .filter(policyConditionPrint -> AssertUtils.isNotEmpty(policyConditionPrint.getSalesBranchId()))
                    .map(PolicyConditionPrintBo::getManagerBranchId).distinct().collect(Collectors.toList()));
            //机构国际化
            List<BranchResponse> branchRespFcs = null;
            if (AssertUtils.isNotEmpty(branchIds)) {
                branchRespFcs = platformBranchApi.branchsPost(branchIds).getData();
            }
            List<String> agentIdList = policyConditionPrintList.stream()
                    .filter(policyConditionPrint -> AssertUtils.isNotEmpty(policyConditionPrint.getAgentId()))
                    .map(PolicyConditionPrintBo::getAgentId).distinct().collect(Collectors.toList());
            List<AgentResponse> applyAgentRespFcs = null;
            if (AssertUtils.isNotEmpty(agentIdList)) {
                AgentApplyQueryRequest agentApplyQueryReqFc = new AgentApplyQueryRequest();
                agentApplyQueryReqFc.setListAgentId(agentIdList);
                applyAgentRespFcs = agentApi.agentsGet(agentApplyQueryReqFc).getData();
            }

            for (PolicyConditionPrintBo policyConditionPrint : policyConditionPrintList) {
                //格式化时间
                if (AssertUtils.isNotNull(policyConditionPrint.getApproveDate())) {
                    policyConditionPrint.setApproveDate(DateUtils.timeStrToString(policyConditionPrint.getApproveDate(), DateUtils.FORMATE6));
                }
                PolicyPrintResponse policyPrintListResponse = (PolicyPrintResponse) this.converterObject(policyConditionPrint, PolicyPrintResponse.class);

                if (AssertUtils.isNotEmpty(branchRespFcs) && (AssertUtils.isNotEmpty(policyPrintListResponse.getSalesBranchId())
                        || AssertUtils.isNotEmpty(policyPrintListResponse.getManagerBranchId()))) {
                    branchRespFcs.forEach(branchRespFc -> {
                        if (AssertUtils.isNotEmpty(policyPrintListResponse.getManagerBranchId()) && policyPrintListResponse.getManagerBranchId().equals(branchRespFc.getBranchId())) {
                            policyPrintListResponse.setManagerBranchName(branchRespFc.getBranchShortname());
                        }
                        if (AssertUtils.isNotEmpty(policyPrintListResponse.getSalesBranchId()) && policyPrintListResponse.getSalesBranchId().equals(branchRespFc.getBranchId())) {
                            policyPrintListResponse.setSalesBranchName(branchRespFc.getBranchShortname());
                        }
                    });
                }
                //  设置代理人信息
                if (AssertUtils.isNotEmpty(applyAgentRespFcs) && AssertUtils.isNotEmpty(policyPrintListResponse.getAgentId())) {
                    applyAgentRespFcs.stream().filter(agentResponse -> agentResponse.getAgentId().equals(policyPrintListResponse.getAgentId())).findFirst().ifPresent(agentResponse -> {
                        policyPrintListResponse.setAgentName(agentResponse.getAgentName());
                        policyPrintListResponse.setAgentCode(agentResponse.getAgentCode());
                        policyPrintListResponse.setMobile(agentResponse.getMobile());
                    });
                }
                policyPrintResponse.add(policyPrintListResponse);
            }
        }

        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(policyConditionPrintList) ? policyConditionPrintList.get(0).getTotalLine() : null;

        BasePageResponse basePageResponse = BasePageResponse.getData(policyConditionPrintRequest.getCurrentPage(), policyConditionPrintRequest.getPageSize(), totalLine, policyPrintResponse);
        return basePageResponse;
    }


    /**
     * 保单打印
     *
     * @param printInfoId 保单打印ID
     * @param language    语言
     * @return ResultObject
     */
    @Override
    public ResultObject getPolicyPrintFile(HttpServletResponse response, String printInfoId, String language) throws Exception {
        ResultObject resultObject = new ResultObject<>();
        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), printInfoId, PolicyErrorConfigEnum.POLICY_PARAMETER_PRINT_INFO_ID_IS_NOT_NULL);
        if (!AssertUtils.isNotEmpty(language) || !(KM_KH.name().equals(language) || EN_US.name().equals(language) || ZH_CN.name().equals(language))) {
            // 语言为空或格式错误,默认打印英语
            language = EN_US.name();
        }
        // 查询打印数据
        //下载PDF保单文件
        PolicyPrintInfoBo policyPrintInfoBo = policyPrintDao.getPolicyPrintData(printInfoId, language);
        AssertUtils.isNotNull(this.getLogger(), policyPrintInfoBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRINT_INFO_IS_NOT_FOUND_OBJECT);
        if (LIFE_INSURANCE_GROUP.name().equals(policyPrintInfoBo.getPolicyType())) {
            groupPolicyService.getPolicyPrintFile(response, printInfoId, language);
            return resultObject;
        }
        AttachmentByteResponse data = this.getAttachmentByteResFc(language, policyPrintInfoBo);
        // 返回文件流
        if (!AssertUtils.isNotNull(data)) {
            return resultObject;
        }
        byte[] bytesFile = data.getFileByte();
        response.setHeader("Content-Type", "application/pdf");
        response.addHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode(policyPrintInfoBo.getPolicyNo() + ".pdf", "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST,GET");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        OutputStream outputStream = response.getOutputStream();
        outputStream.write(bytesFile);
        outputStream.close();
        return resultObject;
    }

    /**
     * 某语言保单打印完成
     *
     * @param printInfoId 保单打印ID
     * @param language    附件语言
     * @return ResultObject
     */
    @Override
    public ResultObject policyPrintFileComplete(String printInfoId, String language) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), printInfoId, PolicyErrorConfigEnum.POLICY_PARAMETER_PRINT_INFO_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), language, PolicyErrorConfigEnum.POLICY_PARAMETER_LANGUAGE_IS_NOT_NULL);
            if (!(KM_KH.name().equals(language)
                    || EN_US.name().equals(language)
                    || ZH_CN.name().equals(language))) {
                // 语言格式错误
                throw new RequestException(PolicyErrorConfigEnum.POLICY_PARAMETER_LANGUAGE_FORMAT_INVALID);
            }
            // 查询数据
            PolicyPrintInfoPo policyPrintInfoPo = policyPrintInfoDao.findById(printInfoId);
            // 数据校验
            AssertUtils.isNotNull(this.getLogger(), policyPrintInfoPo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRINT_INFO_IS_NOT_FOUND_OBJECT);
            if (KM_KH.name().equals(language)) {
                policyPrintInfoPo.setCambodianFlag(PolicyTermEnum.YES_NO.YES.name());
            } else if (EN_US.name().equals(language)) {
                policyPrintInfoPo.setEnglishFlag(PolicyTermEnum.YES_NO.YES.name());
            } else if (ZH_CN.name().equals(language)) {
                policyPrintInfoPo.setChineseFlag(PolicyTermEnum.YES_NO.YES.name());
            }
            // 保存数据
            policyService.savePolicyPrintInfoPo(policyPrintInfoPo);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 保单打印结束
     *
     * @param users       当前用户
     * @param printInfoId 保单打印ID
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject policyPrintFinish(Users users, String printInfoId) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), printInfoId, PolicyErrorConfigEnum.POLICY_PARAMETER_PRINT_INFO_ID_IS_NOT_NULL);
            // 查询打印数据
            PolicyPrintInfoBo policyPrintInfoBo = policyPrintDao.getPolicyPrintData(printInfoId);
            // 数据校验
            AssertUtils.isNotNull(this.getLogger(), policyPrintInfoBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRINT_INFO_IS_NOT_FOUND_OBJECT);

            LOGGER.error(JSON.toJSONString(policyPrintInfoBo));

            // 更新保单打印表
            PolicyPrintInfoPo policyPrintInfoPo = (PolicyPrintInfoPo) this.converterObject(policyPrintInfoBo, PolicyPrintInfoPo.class);
            policyPrintInfoPo.setPrintDate(DateUtils.getCurrentTime());
            policyPrintInfoPo.setPrintstatus(PolicyTermEnum.YES_NO.YES.name());
            policyPrintInfoDao.update(policyPrintInfoPo);

            // 更新回执信息
            PolicyReceiptInfoPo policyReceiptInfoPo = policyBaseService.queryPolicyReceiptInfo(policyPrintInfoPo.getPolicyId());
            if (AssertUtils.isNotNull(policyReceiptInfoPo)) {
                policyReceiptInfoPo.setReceiptStatus(PolicyTermEnum.RECEIPT_STATUS.WAIT_RECEIPT.name());
                policyBaseService.savePolicyReceiptInfo(users.getUserId(), policyReceiptInfoPo);
            }

            // 调用工作流
            resultObject = workFlowApi.claimTask(users.getUserId(), policyPrintInfoBo.getApplyId(), PolicyWorkflowTermEnum.WORKFLOW_STATUS.POLICY_PRINT_TASK.name(), "true");
            LOGGER.error(JSON.toJSONString(resultObject));
            AssertUtils.isResultObjectError(LOGGER, resultObject);

            try {
                //打印结束发消息推送给代理人
                PolicyBo policyBo = policyExtDao.loadPolicyByPolicyId(policyPrintInfoBo.getPolicyId());
                AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
                messageBusinessService.sendSingleMessage(policyBo, PolicyTermEnum.MSG_BUSINESS_TYPE.POLICY_PRINT_CP.name());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 删除已产生的计划书附件
     *
     * @param users       当前用户
     * @param applyPlanId 计划书ID
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject deletePlanBookByApplyPlanId(Users users, String applyPlanId) {
        ResultObject resultObject = new ResultObject();
        if (!AssertUtils.isNotEmpty(applyPlanId)) {
            return resultObject;
        }
        //删除原有附件
        policyBaseService.deletePolicyAttachment(applyPlanId, PolicyTermEnum.ATTACHMENT_TYPE_FLAG.PLAN_BOOK.name());
        return resultObject;
    }

    /**
     * 下载PDF附件
     *
     * @param language          附件语言
     * @param policyPrintInfoBo 打印ID
     * @return
     */
    private AttachmentByteResponse getAttachmentByteResFc(String language, PolicyPrintInfoBo policyPrintInfoBo) throws RequestException {
        // 获取单证或 生成单证
        List<String> attachmentIdList = getPolicyPrintAttachments(policyPrintInfoBo, language);
        // 下载PDF附件
        ResultObject<AttachmentByteResponse> attachmentByteResponseFcResultObject = attachmentPDFDocumentApi.electronicPolicyDownload(attachmentIdList.toArray(new String[attachmentIdList.size()]));
        //this.getLogger().info("保单合并打印:::::::" + JSON.toJSONString(attachmentByteResponseFcResultObject));
        AssertUtils.isResultObjectError(this.getLogger(), attachmentByteResponseFcResultObject);
        return attachmentByteResponseFcResultObject.getData();
    }


    /**
     * put 添加收据
     *
     * @param attachmentMap
     * @param policyAttachmentBoList
     * @param applyId
     * @param applyPlanId
     */
    private void putApplyReceiptAttachmentId(Map<String, String> attachmentMap, List<PolicyAttachmentBo> policyAttachmentBoList, String applyId, String applyPlanId) {
        Optional<PolicyAttachmentBo> applyReceiptAttachmentBoOptional = policyAttachmentBoList.stream().filter(bo -> applyPlanId.equals(bo.getPolicyId()) && EN_US.name().equals(bo.getLanguage()) && APPLY_RECEIPT_BOOK.name().equals(bo.getAttachmentTypeCode())).findFirst();
        if (applyReceiptAttachmentBoOptional.isPresent()) {
            attachmentMap.put(APPLY_RECEIPT_BOOK.name(), applyReceiptAttachmentBoOptional.get().getAttachmentId());
        } else {
            // 附件为空, 调微服务生成附件
            ResultObject<AttachResponse> attachRespFcResultObject = applyPaymentApi.attachmentApplyReceipt(applyId, EN_US.name());
            AssertUtils.isResultObjectError(this.getLogger(), attachRespFcResultObject);
            attachmentMap.put(APPLY_RECEIPT_BOOK.name(), attachRespFcResultObject.getData().getAttachmentId());
        }
    }

    /**
     * put 添加计划书附件
     *
     * @param attachmentMap
     * @param policyAttachmentBoList
     * @param language
     * @param policyId
     * @param applyPlanId
     */
    private void putPlanAttachmentId(Map<String, String> attachmentMap, List<PolicyAttachmentBo> policyAttachmentBoList, String language, String policyId, String applyPlanId, List<PolicyCoveragePo> coveragePoList) {
        if (AssertUtils.isNotEmpty(applyPlanId) && !AssertUtils.isNotEmpty(coveragePoList)) {
            this.getLogger().info("------------------查询保单对应的计划书附件------------------{}", JSON.toJSON(policyAttachmentBoList));
            Optional<PolicyAttachmentBo> applyPlanAttachmentBoOptional = policyAttachmentBoList.stream().filter(bo -> applyPlanId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && PLAN_BOOK.name().equals(bo.getAttachmentTypeCode())).findFirst();
            if (applyPlanAttachmentBoOptional.isPresent()) {
                this.getLogger().info("查询保单对应的计划书附件ID {}", applyPlanAttachmentBoOptional.get().getAttachmentId());
                attachmentMap.put(PLAN_BOOK.name(), applyPlanAttachmentBoOptional.get().getAttachmentId());
            } else {
                this.getLogger().info("------------------附件为空, 调微服务生成保险计划书附件------------------");
                // 附件为空, 调微服务生成附件
                ResultObject<com.gclife.app.model.response.AttachResponse> attachRespFcResultObject = appPlanApi.planPdfGenerate(applyPlanId, language);
                AssertUtils.isResultObjectError(this.getLogger(), attachRespFcResultObject);
                attachmentMap.put(PLAN_BOOK.name(), attachRespFcResultObject.getData().getAttachmentId());
            }
        }
    }

    /**
     * put 添加投保附件
     *
     * @param attachmentMap
     * @param policyAttachmentBoList
     * @param language
     * @param applyId
     */
    private void putApplyAttachmentId(Map<String, String> attachmentMap, List<PolicyAttachmentBo> policyAttachmentBoList, String language, String applyId) {
        Optional<PolicyAttachmentBo> applyAttachmentBoOptional = policyAttachmentBoList.stream().filter(bo -> applyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && APPLY_BOOK.name().equals(bo.getAttachmentTypeCode())).findFirst();
        if (applyAttachmentBoOptional.isPresent()) {
            attachmentMap.put(APPLY_BOOK.name(), applyAttachmentBoOptional.get().getAttachmentId());
            Optional<PolicyAttachmentBo> applicantHealthBookBoOptional = policyAttachmentBoList.stream().filter(bo -> applyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && APPLICANT_HEALTH_BOOK.name().equals(bo.getAttachmentTypeCode())).findFirst();
            if (applicantHealthBookBoOptional.isPresent()) {
                attachmentMap.put(APPLICANT_HEALTH_BOOK.name(), applicantHealthBookBoOptional.get().getAttachmentId());
            }
            Optional<PolicyAttachmentBo> insuredHealthBookBoOptional = policyAttachmentBoList.stream().filter(bo -> applyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && INSURED_HEALTH_BOOK.name().equals(bo.getAttachmentTypeCode())).findFirst();
            if (insuredHealthBookBoOptional.isPresent()) {
                attachmentMap.put(INSURED_HEALTH_BOOK.name(), insuredHealthBookBoOptional.get().getAttachmentId());
            }
        } else {
            // 附件为空, 调微服务生成附件
            ResultObject<List<AttachmentResponse>> listResultObject = applyPaymentApi.applyPdfGenerate(applyId, language);
            AssertUtils.isResultObjectError(this.getLogger(), listResultObject);
            List<AttachmentResponse> attachmentRespFcList = listResultObject.getData();
            Optional<AttachmentResponse> applyBookBo = attachmentRespFcList.stream().filter(bo -> APPLY_BOOK.name().equals(bo.getTemplateType())).findFirst();
            attachmentMap.put(APPLY_BOOK.name(), applyBookBo.get().getMediaId());
            Optional<AttachmentResponse> applicantHealthBookBo = attachmentRespFcList.stream().filter(bo -> APPLICANT_HEALTH_BOOK.name().equals(bo.getTemplateType())).findFirst();
            if (applicantHealthBookBo.isPresent()) {
                attachmentMap.put(APPLICANT_HEALTH_BOOK.name(), applicantHealthBookBo.get().getMediaId());
            }
            Optional<AttachmentResponse> insuredHealthBookBo = attachmentRespFcList.stream().filter(bo -> INSURED_HEALTH_BOOK.name().equals(bo.getTemplateType())).findFirst();
            if (insuredHealthBookBo.isPresent()) {
                attachmentMap.put(INSURED_HEALTH_BOOK.name(), insuredHealthBookBo.get().getMediaId());
            }
        }
    }

    /**
     * put 添加保单相关附件
     *
     * @param attachmentMap
     * @param policyAttachmentBoList
     * @param language
     * @param policyId
     */
    private void putPolicyAttachmentId(Map<String, String> attachmentMap, List<PolicyAttachmentBo> policyAttachmentBoList, String language, String policyId) {
        Optional<PolicyAttachmentBo> policyAttachmentBoOptional = policyAttachmentBoList.stream().filter(bo -> policyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && POLICY_BOOK.name().equals(bo.getAttachmentTypeCode())).findFirst();
        if (policyAttachmentBoOptional.isPresent()) {
            this.getLogger().info("--------------------保单已有附件--------------------");
            //有保单附件
            attachmentMap.put(POLICY_BOOK.name(), policyAttachmentBoOptional.get().getAttachmentId());
            /***保险条款***/
            List<PolicyAttachmentBo> policyTermsAttachmentBoList = policyAttachmentBoList.stream().filter(bo -> policyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && (POLICY_TERMS_BOOK.name().equals(bo.getAttachmentTypeCode()) || PREMIUM_RATE_AND_CASH_VALUE.name().equals(bo.getAttachmentTypeCode()))).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(policyTermsAttachmentBoList)) {
                Comparator<PolicyAttachmentBo> comparator = (p1, p2) -> (int) (p1.getAttachmentSeq() - p2.getAttachmentSeq());
                policyTermsAttachmentBoList.sort(comparator);
                List<String> attachmentIdList = policyTermsAttachmentBoList.stream().map(PolicyAttachmentBo::getAttachmentId).collect(Collectors.toList());
                attachmentMap.put(POLICY_TERMS_BOOK.name(), JSON.toJSONString(attachmentIdList));
            }
            /***客户服务指南***/
            Optional<PolicyAttachmentBo> customerServiceInstructionAttachmentBoOptional = policyAttachmentBoList.stream().filter(bo -> policyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && CUSTOMER_SERVICE_INSTRUCTION_BOOK.name().equals(bo.getAttachmentTypeCode())).findFirst();
            if (customerServiceInstructionAttachmentBoOptional.isPresent()) {
                attachmentMap.put(CUSTOMER_SERVICE_INSTRUCTION_BOOK.name(), customerServiceInstructionAttachmentBoOptional.get().getAttachmentId());
            }
            /***首刊***/
            Optional<PolicyAttachmentBo> firstIssueAttachmentBoOptional = policyAttachmentBoList.stream().filter(bo -> policyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && (FIRST_ISSUE_BOOK.name().equals(bo.getAttachmentTypeCode()) || FIRST_ISSUE_BOOK_ONLINE.name().equals(bo.getAttachmentTypeCode()))).findFirst();
            if (firstIssueAttachmentBoOptional.isPresent()) {
                attachmentMap.put(FIRST_ISSUE_BOOK.name(), firstIssueAttachmentBoOptional.get().getAttachmentId());
            }
            /***保险证确认书***/
            Optional<PolicyAttachmentBo> policyConfirmAttachmentBoOptional = policyAttachmentBoList.stream().filter(bo -> policyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && POLICY_CONFIRM.name().equals(bo.getAttachmentTypeCode())).findFirst();
            if (policyConfirmAttachmentBoOptional.isPresent()) {
                attachmentMap.put(POLICY_CONFIRM.name(), policyConfirmAttachmentBoOptional.get().getAttachmentId());
            }
        } else {
            this.getLogger().info("--------------------没有对应保单附件,生成附件并保存--------------------");
            // 没有对应保单附件,生成附件并保存
            PolicyBo policyBo = policyBaseService.queryPolicyBo(policyId);
            List<AttachmentResponse> attachmentResponseFcs = policyTransData.attachmentPdfGenerate(policyBo, language);
            Optional<AttachmentResponse> policyBookOptional = attachmentResponseFcs.stream().filter(bo -> POLICY_BOOK.name().equals(bo.getTemplateType())).findFirst();
            attachmentMap.put(POLICY_BOOK.name(), policyBookOptional.get().getMediaId());
            /***保险条款***/
            List<AttachmentResponse> policyTermsBookList = attachmentResponseFcs.stream().filter(bo -> POLICY_TERMS_BOOK.name().equals(bo.getTemplateType()) || PREMIUM_RATE_AND_CASH_VALUE.name().equals(bo.getTemplateType())).collect(Collectors.toList());
            if (AssertUtils.isNotEmpty(policyTermsBookList)) {
                Comparator<AttachmentResponse> comparator = (p1, p2) -> (int) (p1.getSeq() - p2.getSeq());
                policyTermsBookList.sort(comparator);
                List<String> attachmentIdList = policyTermsBookList.stream().map(AttachmentResponse::getMediaId).collect(Collectors.toList());
                attachmentMap.put(POLICY_TERMS_BOOK.name(), JSON.toJSONString(attachmentIdList));
            }
            /***客户服务指南***/
            Optional<AttachmentResponse> customerServiceInstructionBookOptional = attachmentResponseFcs.stream().filter(bo -> CUSTOMER_SERVICE_INSTRUCTION_BOOK.name().equals(bo.getTemplateType())).findFirst();
            if (customerServiceInstructionBookOptional.isPresent()) {
                attachmentMap.put(CUSTOMER_SERVICE_INSTRUCTION_BOOK.name(), customerServiceInstructionBookOptional.get().getMediaId());
            }
            /***首刊***/
            // 使用网销产品特有的首刊目录
            if (PolicyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(policyBo.getChannelTypeCode())) {
                Optional<AttachmentResponse> firstIssueBookOptional = attachmentResponseFcs.stream().filter(bo -> FIRST_ISSUE_BOOK_ONLINE.name().equals(bo.getTemplateType())).findFirst();
                if (firstIssueBookOptional.isPresent()) {
                    attachmentMap.put(FIRST_ISSUE_BOOK_ONLINE.name(), firstIssueBookOptional.get().getMediaId());
                }
            } else {
                Optional<AttachmentResponse> firstIssueBookOptional = attachmentResponseFcs.stream().filter(bo -> FIRST_ISSUE_BOOK.name().equals(bo.getTemplateType())).findFirst();
                if (firstIssueBookOptional.isPresent()) {
                    attachmentMap.put(FIRST_ISSUE_BOOK.name(), firstIssueBookOptional.get().getMediaId());
                }
            }
            /***保险证确认书***/
            Optional<AttachmentResponse> policyConfirmAttachmentBoOptional = attachmentResponseFcs.stream().filter(bo -> POLICY_CONFIRM.name().equals(bo.getTemplateType())).findFirst();
            if (policyConfirmAttachmentBoOptional.isPresent()) {
                attachmentMap.put(POLICY_CONFIRM.name(), policyConfirmAttachmentBoOptional.get().getMediaId());
            }
        }
    }


    /**
     * 根据 用户 ID 获取 当前工作流
     *
     * @param userId
     * @return 返回保单ID 集合
     */
    private List<String> getWorkflowTaskRespList(String userId) {
        WaitingTaskRequest tasksReqFc = new WaitingTaskRequest();
        tasksReqFc.setWorkflowItemType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.POLICY_PRINT_TASK.name());
        tasksReqFc.setWorkflowType(PolicyWorkflowTermEnum.WORKFLOW_STATUS.NEW_CONTRACT.name());
        tasksReqFc.setUserId(userId);
        //获取当前用户的流程中的待办任务
        ResultObject<List<WorkItemResponse>> resultObject = workFlowApi.queryWaitingTasks(tasksReqFc);
        AssertUtils.isResultObjectError(LOGGER, resultObject);
        List<WorkItemResponse> listWorkflowTaskResponse = resultObject.getData();

        List<String> listPolicyIds = null;
        if (null != listWorkflowTaskResponse) {
            listPolicyIds = listWorkflowTaskResponse.stream().map(WorkItemResponse::getBusinessId).distinct().collect(Collectors.toList());
        }
        return listPolicyIds;
    }

    /**
     * 保险证确认书打印
     *
     * @param response
     * @param printInfoId
     * @param language
     * @return
     * @throws Exception
     */
    @Override
    public ResultObject getPolicyConfirmPrintFile(HttpServletResponse response, String printInfoId, String language) throws Exception {
        ResultObject resultObject = new ResultObject<>();
        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), printInfoId, PolicyErrorConfigEnum.POLICY_PARAMETER_PRINT_INFO_ID_IS_NOT_NULL);
        if (!AssertUtils.isNotEmpty(language) || !(KM_KH.name().equals(language) || EN_US.name().equals(language) || ZH_CN.name().equals(language))) {
            // 语言为空或格式错误,默认打印英语
            language = EN_US.name();
        }
        // 查询打印数据
        PolicyPrintInfoBo policyPrintInfoBo = policyPrintDao.getPolicyPrintData(printInfoId, language);
        AssertUtils.isNotNull(this.getLogger(), policyPrintInfoBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRINT_INFO_IS_NOT_FOUND_OBJECT);
        AttachmentByteResponse data = this.getPolicyConfirmAttachmentByteResFc(language, policyPrintInfoBo);
        // 返回文件流
        if (!AssertUtils.isNotNull(data)) {
            return resultObject;
        }
        byte[] bytesFile = data.getFileByte();
        response.setHeader("Content-Type", "application/pdf");
        response.addHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode(policyPrintInfoBo.getPolicyNo() + ".pdf", "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST,GET");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        OutputStream outputStream = response.getOutputStream();
        outputStream.write(bytesFile);
        outputStream.close();
        return resultObject;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultObject<AttachmentByteResponse> generatePolicyAllPdf(String printInfoId, String language) {
        ResultObject<AttachmentByteResponse> resultObject = new ResultObject<>();
        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), printInfoId, PolicyErrorConfigEnum.POLICY_PARAMETER_PRINT_INFO_ID_IS_NOT_NULL);
        if (!AssertUtils.isNotEmpty(language) || !(KM_KH.name().equals(language) || EN_US.name().equals(language) || ZH_CN.name().equals(language))) {
            // 语言为空或格式错误,默认打印英语
            language = EN_US.name();
        }
        // 查询打印数据
        //下载PDF保单文件
        PolicyPrintInfoBo policyPrintInfoBo = policyPrintDao.getPolicyPrintData(printInfoId, language);
        AssertUtils.isNotNull(this.getLogger(), policyPrintInfoBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRINT_INFO_IS_NOT_FOUND_OBJECT);
        // 团险
        if (LIFE_INSURANCE_GROUP.name().equals(policyPrintInfoBo.getPolicyType())) {
            AttachmentByteResponse attachmentByteResponse = groupPolicyService.generateGroupPolicyAllPdf(printInfoId, language);
            resultObject.setData(attachmentByteResponse);
            return resultObject;
        }

        // 个险
        AttachmentByteResponse attachmentByteResponse = generatePolicyAllPdf(language, policyPrintInfoBo);
        resultObject.setData(attachmentByteResponse);
        return resultObject;
    }

    /**
     * 生成个险保单打印PDF POLICY_ALL_BOOK
     *
     * @param language
     * @param policyPrintInfoBo
     * @return
     */
    private AttachmentByteResponse generatePolicyAllPdf(String language, PolicyPrintInfoBo policyPrintInfoBo) {
        String policyId = policyPrintInfoBo.getPolicyId();
        List<String> attachmentIdList = getPolicyPrintAttachments(policyPrintInfoBo, language);
        // 下载PDF附件 并保存
        return policyBoService.getAttachmentByteResponse(language, policyId, attachmentIdList);
    }

    /**
     * 获取保单打印所需PDF附件
     *
     * @param policyPrintInfoBo
     * @param language
     * @return
     */
    private List<String> getPolicyPrintAttachments(PolicyPrintInfoBo policyPrintInfoBo, String language) {
        String policyId = policyPrintInfoBo.getPolicyId();
        String applyId = policyPrintInfoBo.getApplyId();
        ResultObject<ApplyPlanResponse> resultObject = applyPlanApi.getApplyPlan(applyId);
        AssertUtils.isResultObjectError(this.getLogger(), resultObject, PolicyErrorConfigEnum.POLICY_FEGIN_APPLY_ERROR);
        String applyPlanId = AssertUtils.isResultObjectDataNull(resultObject) ? null : resultObject.getData().getApplyPlanId();

        // 存放计划书、投保单、保单PDF附件ID
        Map<String, String> attachmentMap = new HashMap<>();

        //1 号产品不打印计划书 PRO88000000000003
        /************************************ start ********************************************/
        this.getLogger().info("language: {}, applyPlanId: {}, applyId: {}, policyId: {}", language, applyPlanId, applyId, policyId);
        List<PolicyAttachmentBo> policyAttachmentBoList = policyPrintDao.getPolicyAttachment(language, applyPlanId, applyId, policyId);
        this.getLogger().info("查询保单险种信息 policyCoveragePoList: {}", policyAttachmentBoList);

        List<PolicyCoveragePo> policyCoveragePoList = policyCoverageDao.fetchByPolicyId(policyId);

        /************************************ 查询保单PDF附件ID ********************************************/
        this.putPolicyAttachmentId(attachmentMap, policyAttachmentBoList, language, policyId);

        /************************************ 查询投保单PDF附件ID ********************************************/
        this.putApplyAttachmentId(attachmentMap, policyAttachmentBoList, language, applyId);

        /************************************ 查询计划书PDF附件ID ********************************************/
        List<PolicyCoveragePo> coveragePoList = policyCoveragePoList.stream().filter(policyCoveragePo -> ProductTermEnum.PRODUCT.PRODUCT_1.id().equals(policyCoveragePo.getProductId())).collect(Collectors.toList());
        LOGGER.info("保单附件集合 policyAttachIdList 111: {}", JSON.toJSON(attachmentMap));

        PolicyPo policyPo = policyExtDao.getPolicyPoByPolicyId(policyId);
        // 网销20号产品不需要计划书
        if (!PolicyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(policyPo.getChannelTypeCode())) {
            this.getLogger().info("进入生成保险计划书附件------------------");
            this.putPlanAttachmentId(attachmentMap, policyAttachmentBoList, language, policyId, applyPlanId, coveragePoList);
        }
        /************************************ 查询投保单收据PDF附件ID end ********************************************/
//        this.putApplyReceiptAttachmentId(attachmentMap, policyAttachmentBoList, applyId, applyPlanId);
        /************************************ end ********************************************/

        LOGGER.info("保单附件集合 policyAttachIdList 222: {}", JSON.toJSON(attachmentMap));
        boolean UPDATE_PAGE = true;
        if (AssertUtils.isNotEmpty(coveragePoList) && KM_KH.name().equals(language)) {
            UPDATE_PAGE = false;
        }

        List<String> attachmentIdList = new ArrayList<>();
        attachmentIdList.add(language);
        PolicyTermEnum.ATTACHMENT_TYPE_FLAG[] attachmentTypeFlags = PolicyTermEnum.ATTACHMENT_TYPE_FLAG.values();
        for (PolicyTermEnum.ATTACHMENT_TYPE_FLAG attachment_type_flag : attachmentTypeFlags) {
            String attachmentId = attachmentMap.get(attachment_type_flag.name());
            if (!AssertUtils.isNotEmpty(attachmentId)) {
                continue;
            }
            // 条款修改页码
            if (POLICY_TERMS_BOOK.name().equals(attachment_type_flag.name())) {
                if (UPDATE_PAGE) attachmentIdList.add("UPDATE_PAGE");
                List<String> strings = JSON.parseArray(attachmentId, String.class);
                attachmentIdList.addAll(strings);
                if (UPDATE_PAGE) attachmentIdList.add("UPDATE_PAGE");
            } else {
                attachmentIdList.add(attachmentId);
            }
        }
        return attachmentIdList;
    }

    /**
     * 下载PDF附件
     *
     * @param language
     * @param policyPrintInfoBo
     * @return
     */
    private AttachmentByteResponse getPolicyConfirmAttachmentByteResFc(String language, PolicyPrintInfoBo policyPrintInfoBo) {
        String policyId = policyPrintInfoBo.getPolicyId();

        // 存放保险证确实书PDF附件ID
        Map<String, String> attachmentMap = new HashMap<>();
        // 获取或者生成附件
        List<PolicyAttachmentBo> policyAttachmentBoList = policyPrintDao.getPolicyAttachment(language, policyId);
        this.putPolicyConfirmAttachmentId(attachmentMap, policyAttachmentBoList, language, policyId);

        LOGGER.info("policyAttachIdList:" + JSON.toJSONString(attachmentMap));
        // 下载PDF附件
        ResultObject<AttachmentByteResponse> attachmentByteResponseFcResultObject = attachmentPDFDocumentApi.electronicPolicyDownload(attachmentMap.get(POLICY_CONFIRM.name()));
        AssertUtils.isResultObjectError(this.getLogger(), attachmentByteResponseFcResultObject);
        return attachmentByteResponseFcResultObject.getData();
    }

    /**
     * 获取附件或生成附件
     *
     * @param attachmentMap
     * @param policyAttachmentBoList
     * @param language
     * @param policyId
     */
    private void putPolicyConfirmAttachmentId(Map<String, String> attachmentMap, List<PolicyAttachmentBo> policyAttachmentBoList, String language, String policyId) {
        Optional<PolicyAttachmentBo> policyAttachmentBoOptional = policyAttachmentBoList.stream().filter(bo -> policyId.equals(bo.getPolicyId()) && language.equals(bo.getLanguage()) && POLICY_CONFIRM.name().equals(bo.getAttachmentTypeCode())).findFirst();
        // 若已有保险证确认书附件则使用 否则生成
        if (policyAttachmentBoOptional.isPresent()) {
            attachmentMap.put(POLICY_CONFIRM.name(), policyAttachmentBoOptional.get().getAttachmentId());
        } else {
            PolicyBo policyBo = policyBaseService.queryPolicyBo(policyId);
            List<AttachmentResponse> attachmentResponseFcs = policyTransData.policyConfirmAttachmentPdfGenerate(policyBo, language);
            Optional<AttachmentResponse> policyBookOptional = attachmentResponseFcs.stream().filter(bo -> POLICY_CONFIRM.name().equals(bo.getTemplateType())).findFirst();
            attachmentMap.put(POLICY_CONFIRM.name(), policyBookOptional.get().getMediaId());
        }
    }
}
