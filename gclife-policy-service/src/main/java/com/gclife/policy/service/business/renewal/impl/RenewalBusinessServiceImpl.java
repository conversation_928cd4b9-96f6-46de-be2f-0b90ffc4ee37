package com.gclife.policy.service.business.renewal.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.dao.PolicyExtDao;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.request.PolicyPaymentUpdateRequest;
import com.gclife.policy.model.request.renewal.PolicyInvalidHandleRequest;
import com.gclife.policy.model.request.renewal.PolicyRenewalRequest;
import com.gclife.policy.model.request.renewal.RenewalReceivableRequest;
import com.gclife.policy.model.response.PaymentAuditDetailResponse;
import com.gclife.policy.model.response.PolicyCoverageExt;
import com.gclife.policy.model.response.PolicyInvalidHandleResponse;
import com.gclife.policy.model.response.renewal.RenewalReceivableResponse;
import com.gclife.policy.service.base.*;
import com.gclife.policy.service.business.MessageBusinessService;
import com.gclife.policy.service.business.renewal.RenewalBusinessService;
import com.gclife.policy.validate.business.RenewalBusinessValidate;
import com.gclife.policy.validate.transfer.PolicyToRenewalTransData;
import com.gclife.policy.validate.transfer.PolicyTransData;
import com.gclife.policy.validate.transfer.RenewalDataTransfer;
import com.gclife.renewal.api.RenewalApi;
import com.gclife.renewal.api.RenewalPaymentApi;
import com.gclife.renewal.model.request.RenewalBatchPaymentRequest;
import com.gclife.renewal.model.request.RenewalBatchRequest;
import com.gclife.renewal.model.request.RenewalRequest;
import com.gclife.renewal.model.response.RenewalResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.policy.model.config.PolicyErrorConfigEnum.*;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_STATUS_FLAG.*;

/**
 * <AUTHOR>
 * @version v2.0
 * Description: 续期业务服务类
 * @date 18-7-10
 */
@Service
public class RenewalBusinessServiceImpl extends BaseBusinessServiceImpl implements RenewalBusinessService {

    @Autowired
    private PolicyQueryBaseService policyQueryBaseService;
    @Autowired
    private RenewalDataTransfer renewalDataTransfer;
    @Autowired
    private PolicyBaseService policyBaseService;
    @Autowired
    private PolicyToRenewalTransData policyToRenewalTransData;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private RenewalBusinessValidate renewalBusinessValidate;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PolicyCoverageBaseService policyCoverageBaseService;
    @Autowired
    private PolicyPremiumBaseService policyPremiumBaseService;
    @Autowired
    private PolicyPaymentBaseService policyPaymentBaseService;
    @Autowired
    private PolicyExtDao policyExtDao;
    @Autowired
    private PolicyTransData policyTransData;
    @Autowired
    private RenewalApi renewalApi;
    @Autowired
    private PolicyRenewalGrabBaseService policyRenewalGrabBaseService;
    @Autowired
    private RenewalPaymentApi renewalPaymentApi;

    /**
     * 批量生成保单续期订单（批处理调用）
     *
     * @param basePageRequest 分页信息
     * @param appRequestHeads 请求头
     * @return 是否执行完成
     */
    @Override
    public String generateRenewalGrab(BasePageRequest basePageRequest, AppRequestHeads appRequestHeads) {
        try {
            List<PolicyCoverageBo> policyCoverageBos = policyQueryBaseService.listPolicyCoverage(basePageRequest, PolicyTermEnum.RENEWAL_TYPE.RENEWAL.name());
            // 筛选数据
            if (AssertUtils.isNotEmpty(policyCoverageBos)) {
                this.getLogger().info("=======generateRenewalGrab========本批次处理数据量:"+policyCoverageBos.size());
                // 续期发送队列数据
                List<PolicyRenewalRequest> policyRenewalRequests = new ArrayList<>();

                List<String> policyIds = policyCoverageBos.stream().map(PolicyCoverageBo::getPolicyId).collect(Collectors.toList());
                // 查询抢单表数据
                List<PolicyRenewalGrabPo> policyRenewalGrabPos = policyBaseService.listPolicyRenewalGrab(policyIds);
                Map<String, List<PolicyRenewalGrabPo>> renewalGrabMap = policyRenewalGrabPos.stream().collect(Collectors.groupingBy(PolicyRenewalGrabPo::getPolicyId));

                // 查询险种扩展表中缴费周期变更待生效的险种数据
                List<PolicyCoverageExtendPo> policyCoverageExtendPos = policyCoverageBaseService.listPendingCoverageExtend(policyIds, PolicyTermEnum.BUSINESS_TYPE.ENDORSE_MODIFY_PREMIUM_FREQUENCY.name());
                Map<String, List<PolicyCoverageExtendPo>> coverageExtendMap = policyCoverageExtendPos.stream().collect(Collectors.groupingBy(PolicyCoverageExtendPo::getPolicyId));

                policyCoverageBos.forEach(policyCoverageBo -> {
                    // 获取事物状态
                    TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
                    try {
                        String policyId = policyCoverageBo.getPolicyId();
                        List<Long> receivableDates = new ArrayList<>();
                        List<PolicyCoverageExtendPo> extendPos = coverageExtendMap.get(policyId);
                        if (AssertUtils.isNotEmpty(renewalGrabMap.get(policyId))) {
                            // 已生成应收数据的应收时间
                            receivableDates = renewalGrabMap.get(policyId).stream()
                                    .filter(policyRenewalGrabPo -> PolicyTermEnum.SUCCESS_FAIL_TYPE.SUCCESS.name().equals(policyRenewalGrabPo.getRenewalGenerateStatus()) ||
                                            (PolicyTermEnum.SUCCESS_FAIL_TYPE.INITIAL.name().equals(policyRenewalGrabPo.getRenewalGenerateStatus()) &&
                                                    DateUtils.timeToTimeLow(DateUtils.addStringDayRT(policyRenewalGrabPo.getReceivableGenerateDate(), 1)) > DateUtils.getCurrentTime()))
                                    .map(PolicyRenewalGrabPo::getReceivableDate).collect(Collectors.toList());
                            Long maxReceivableDate = policyCoverageBo.getPolicyEffectiveDate();
                            if (AssertUtils.isNotEmpty(receivableDates)) {
                                maxReceivableDate = receivableDates.stream().max(Long::compareTo).get();
                            }
                            // 缴费周期变更，大周期变小周期，添加已缴费应收时间
                            if (AssertUtils.isNotEmpty(extendPos)) {
                                int oldFrequencyValue = PolicyTermEnum.FREQUENCY_VALUE.valueOf(policyCoverageBo.getPremiumFrequency()).value();
                                int newFrequencyValue = PolicyTermEnum.FREQUENCY_VALUE.valueOf(extendPos.get(0).getPremiumFrequency()).value();
                                if (oldFrequencyValue > newFrequencyValue) {
                                    for (int i = newFrequencyValue; i < oldFrequencyValue; i = i + newFrequencyValue) {
                                        receivableDates.add(DateUtils.addStringMonthRT(maxReceivableDate, i));
                                    }
                                }
                            }
                        }

                        if (AssertUtils.isNotEmpty(extendPos)) {
                            // 存在待生效的险种扩展数据，更新险种缴费周期
                            extendPos.stream()
                                    .filter(coverageExtendPo -> policyCoverageBo.getProductId().equals(coverageExtendPo.getProductId()))
                                    .findFirst().ifPresent(coverageExtendPo -> policyCoverageBo.setPremiumFrequency(coverageExtendPo.getPremiumFrequency()));
                        }

                        // 获取应收时间
                        List<Long> receivableDateList = renewalDataTransfer.getReceivableDates(policyCoverageBo, receivableDates);
                        if (AssertUtils.isNotEmpty(receivableDateList)) {
                            getLogger().info("==========generateRenewalGrab======保单NO："+policyCoverageBo.getPolicyNo()+"|"+receivableDateList.size());
                            receivableDateList.forEach(receivableDate -> {
                                getLogger().info("==============generateRenewalGrab=============receivableDate："+DateUtils.timeStrToString(receivableDate));
                                // 生成应收
                                PolicyRenewalGrabPo policyRenewalGrabPo = new PolicyRenewalGrabPo();
                                if (AssertUtils.isNotEmpty(renewalGrabMap.get(policyId))) {
                                    for (PolicyRenewalGrabPo grabPo : renewalGrabMap.get(policyId)) {
                                        if (receivableDate.equals(grabPo.getReceivableDate())) {
                                            policyRenewalGrabPo = grabPo;
                                        }
                                    }
                                }
                                policyRenewalGrabPo.setPolicyId(policyId);
                                policyRenewalGrabPo.setReceivableDate(receivableDate);
                                policyRenewalGrabPo.setRenewalType(PolicyTermEnum.RENEWAL_TYPE.RENEWAL.name());
                                policyRenewalGrabPo.setRenewalGenerateStatus(PolicyTermEnum.SUCCESS_FAIL_TYPE.INITIAL.name());
                                policyBaseService.savePolicyRenewalGrab(policyRenewalGrabPo);
                                // 设置续期发送队列数据
                                PolicyRenewalRequest policyRenewalRequest = new PolicyRenewalRequest();
                                policyRenewalRequest.setPolicyNo(policyCoverageBo.getPolicyNo());
                                policyRenewalRequest.setRenewalYearMonth(DateUtils.getTimeYearMonth(receivableDate));
                                policyRenewalRequest.setReceivableDate(receivableDate);
                                policyRenewalRequests.add(policyRenewalRequest);
                            });
                        }
                        // 提交事物
                        platformTransactionManager.commit(transactionStatus);
                    } catch (Exception e) {
                        e.printStackTrace();
                        getLogger().error("===========generateRenewalGrab===error=============保单NO："+policyCoverageBo.getPolicyNo());
                        // 事务回滚
                        platformTransactionManager.rollback(transactionStatus);
                    }
                });
                if (AssertUtils.isNotEmpty(policyRenewalRequests)) {
                    // 续期数据发送队列
                    messageBusinessService.pushRenewalSequence(PolicyTermEnum.RENEWAL_TYPE.RENEWAL.name(), policyRenewalRequests);
                }
            }

            if (AssertUtils.isNotEmpty(policyCoverageBos) && policyCoverageBos.size() == basePageRequest.getPageSize()) {
                return TerminologyConfigEnum.WHETHER.NO.name();
            } else {
                return TerminologyConfigEnum.WHETHER.YES.name();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 批量生成保单续期记录
     *
     * @param policyRenewalRequests 请求参数
     * @return ResultObject
     */
    @Override
    public ResultObject generatePolicyRenewal(List<PolicyRenewalRequest> policyRenewalRequests) {
        ResultObject resultObject = new ResultObject();
        try {
            if (!AssertUtils.isNotEmpty(policyRenewalRequests)) {
                return resultObject;
            }
            getLogger().info("==============generatePolicyRenewal开始=============待生成续期数据保单数："+policyRenewalRequests.size());
            policyRenewalRequests.forEach(policyRenewalRequest -> {
                getLogger().info("==============generatePolicyRenewal=============保单NO："+policyRenewalRequest.getPolicyNo());
                //1.数据验证
                PolicyBo policyBo = renewalBusinessValidate.validRenewalPolicy(policyRenewalRequest);

                // 设置待生效信息到险种及保费
                renewalDataTransfer.setPendingInfo(policyBo);

                //获取事物状态
                TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
                boolean errorFlag = false;
                try {
                    // 设置应收时间
                    if (AssertUtils.isNotNull(policyBo.getPolicyPremium())) {
                        policyBo.getPolicyPremium().setReceivableDate(policyRenewalRequest.getReceivableDate());
                    }
                    //3.保单转续期
                    Users users = new Users();
                    users.setUserId("11111111");
                    AppRequestHeads appRequestHeads = new AppRequestHeads();
                    appRequestHeads.setDeviceChannel("gclife_agent_app");
                    policyToRenewalTransData.transferPolicyToRenewal(policyBo, policyRenewalRequest,users, appRequestHeads);

                    // 写入成功状态至抢单表
                    PolicyRenewalGrabPo policyRenewalGrabPo = policyBaseService.queryPolicyRenewalGrab(policyBo.getPolicyId(), policyRenewalRequest.getReceivableDate());
                    if (AssertUtils.isNotNull(policyRenewalGrabPo)) {
                        policyRenewalGrabPo.setRenewalGenerateStatus(PolicyTermEnum.SUCCESS_FAIL_TYPE.SUCCESS.name());
                        policyBaseService.savePolicyRenewalGrab(policyRenewalGrabPo);
                    }

                    getLogger().info("======generatePolicyRenewal=======转续期成功======保单NO："+policyRenewalRequest.getPolicyNo());
                    //提交事物
                    platformTransactionManager.commit(transactionStatus);
                    // 临时代码，续期支付测试用
                    try {
                        Thread.sleep(10000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                } catch (Exception e) {
                    errorFlag = true;
                    getLogger().info("======generatePolicyRenewal=======转续期失败======保单NO："+policyRenewalRequest.getPolicyNo());
                    e.printStackTrace();
                    //事务回滚
                    platformTransactionManager.rollback(transactionStatus);
                } finally {
                    if (errorFlag) {
                        TransactionStatus finallyTransactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
                        try {
                            //写入失败状态至抢单表
                            PolicyRenewalGrabPo policyRenewalGrabPo = policyBaseService.queryPolicyRenewalGrab(policyBo.getPolicyId(), policyRenewalRequest.getReceivableDate());
                            if (AssertUtils.isNotNull(policyRenewalGrabPo)) {
                                policyRenewalGrabPo.setRenewalGenerateStatus(PolicyTermEnum.SUCCESS_FAIL_TYPE.FAILED.name());
                                policyBaseService.savePolicyRenewalGrab(policyRenewalGrabPo);
                            }
                            //提交事物
                            platformTransactionManager.commit(finallyTransactionStatus);
                        } catch (Exception e) {
                            e.printStackTrace();
                            platformTransactionManager.rollback(finallyTransactionStatus);
                        }
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, POLICY_BUSINESS_GENERATE_POLICY_RENEWAL_ERROR);
        }
        return resultObject;
    }

    /**
     * 手工生成单个保单续期记录
     *
     * @param policyRenewalRequest 请求参数
     * @param currentLoginUsers    当前用户
     * @param appRequestHandler    请求头
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject generateOnePolicyRenewal(PolicyRenewalRequest policyRenewalRequest, Users currentLoginUsers, AppRequestHeads appRequestHandler) {
        ResultObject resultObject = new ResultObject();
        try {
            //1.数据业务校验
            PolicyBo policyBo = renewalBusinessValidate.validRenewalPolicy(policyRenewalRequest);
            String renewalYearMonth = policyRenewalRequest.getRenewalYearMonth();

            // 设置待生效信息到险种及保费
            renewalDataTransfer.setPendingInfo(policyBo);

            // 1.1.业务校验
            renewalBusinessValidate.validRenewalGenerate(policyBo, renewalYearMonth);

            // 计算当期应收
            //5.8.3 保单满期/续期应收都从生效日期算
            Long newReceivableDate = renewalDataTransfer.calculateNowReceivableDate(renewalYearMonth, policyBo.getEffectiveDate());
            AssertUtils.isNotNull(getLogger(), newReceivableDate, POLICY_GENERATE_RENEWAL_ERROR);
            policyRenewalRequest.setReceivableDate(newReceivableDate);

            //3.保单转续期
            policyToRenewalTransData.transferPolicyToRenewal(policyBo, policyRenewalRequest, currentLoginUsers, appRequestHandler);

            //4.将手工生产的数据插入抢单表
            PolicyRenewalGrabPo policyRenewalGrabPo = policyBaseService.queryPolicyRenewalGrab(policyBo.getPolicyId(), newReceivableDate);
            if (AssertUtils.isNotNull(policyRenewalGrabPo)) {
                policyRenewalGrabPo.setRenewalGenerateStatus(PolicyTermEnum.SUCCESS_FAIL_TYPE.SUCCESS.name());
            } else {
                policyRenewalGrabPo = new PolicyRenewalGrabPo();
                policyRenewalGrabPo.setPolicyId(policyBo.getPolicyId());
                policyRenewalGrabPo.setReceivableDate(newReceivableDate);
                policyRenewalGrabPo.setReceivableGenerateDate(DateUtils.getCurrentTime());
                policyRenewalGrabPo.setRenewalType(PolicyTermEnum.RENEWAL_TYPE.RENEWAL.name());
                policyRenewalGrabPo.setRenewalGenerateStatus(PolicyTermEnum.SUCCESS_FAIL_TYPE.SUCCESS.name());
            }
            policyBaseService.savePolicyRenewalGrab(policyRenewalGrabPo);
        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, POLICY_BUSINESS_GENERATE_POLICY_RENEWAL_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询保单剩余应收期数
     * @param renewalReceivableRequest
     * @return
     */
    @Override
    public ResultObject<List<RenewalReceivableResponse>> listRenewalReceivable(RenewalReceivableRequest renewalReceivableRequest) {
        ResultObject<List<RenewalReceivableResponse>> resultObject = new ResultObject<>();
        try {
            PolicyPo policyPo = null;
            if (AssertUtils.isNotEmpty(renewalReceivableRequest.getPolicyNo())) {
                policyPo = policyBaseService.queryPolicyByPolicyNo(renewalReceivableRequest.getPolicyNo());
            }
            if (AssertUtils.isNotEmpty(renewalReceivableRequest.getPolicyId())) {
                policyPo = policyBaseService.queryPolicyPo(renewalReceivableRequest.getPolicyId());
            }
            AssertUtils.isNotNull(this.getLogger(), policyPo, POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
            if (PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name().equals(policyPo.getPolicyType())) {
                return resultObject;
            }
            List<String> policyStatusList = Arrays.asList(
                    PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INDEMNITY_TERMINATION.name(),
                    PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_HESITATION_REVOKE.name(),
                    PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID_THOROUGH.name(),
                    PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECT_TERMINATION.name(),
                    PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_SURRENDER.name(),
                    PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_IEXPIRE.name()
            );
            if (policyStatusList.contains(policyPo.getPolicyStatus())
                    || PolicyTermEnum.VALID_FLAG.invalid.name().equals(policyPo.getValidFlag())) {
                return resultObject;
            }
            List<PolicyCoveragePo> policyCoveragePos = renewalDataTransfer.getPolicyCoverage(policyPo.getPolicyId());
            // 主险
            PolicyCoveragePo mainCoveragePo = policyCoveragePos.stream()
                    .filter(policyCoveragePo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoveragePo.getPrimaryFlag()))
                    .findFirst().get();
            if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(mainCoveragePo.getPremiumFrequency()) ||
                    ("1".equals(mainCoveragePo.getPremiumPeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(mainCoveragePo.getPremiumPeriodUnit()))) {
                return resultObject;
            }

            // 查询保单缴费信息
            List<PolicyPaymentPo> policyPaymentPos = policyPaymentBaseService.listPolicyPayment(policyPo.getPolicyId(), null);
            PolicyPaymentPo paymentPo = policyPaymentPos.stream()
                    .filter(policyPaymentPo -> PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_NEW_CONTRACT.name().equals(policyPaymentPo.getPaymentBusinessType())
                            || PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_RENEWAL.name().equals(policyPaymentPo.getPaymentBusinessType()))
                    .filter(policyPaymentPo -> PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(policyPaymentPo.getPaymentStatusCode())
                            || PolicyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name().equals(policyPaymentPo.getPaymentStatusCode()))
                    .collect(Collectors.toList()).get(0);
            int oldFrequencyValue = PolicyTermEnum.FREQUENCY_VALUE.valueOf(paymentPo.getPremiumFrequency()).value();
            int newFrequencyValue = PolicyTermEnum.FREQUENCY_VALUE.valueOf(mainCoveragePo.getPremiumFrequency()).value();
            Long maxReceivableDate = paymentPo.getReceivableDate();
            if (oldFrequencyValue > newFrequencyValue) {
                for (int i = newFrequencyValue; i < oldFrequencyValue; i = i + newFrequencyValue) {
                    maxReceivableDate = DateUtils.addStringMonthRT(maxReceivableDate, newFrequencyValue);
                }
            }

            // 续期期数
            int frequency = 0;
            List<RenewalReceivableResponse> renewalReceivableResponses = new ArrayList<>();

            long nextReceivableDate = policyPo.getEffectiveDate();
            // 最近的应收
            boolean nearestReceivableFlag = true;
            while (nextReceivableDate <= mainCoveragePo.getMaturityDate()) {
                boolean singleOnlyFlag = true;
                BigDecimal totalPremium = BigDecimal.ZERO;
                for (PolicyCoveragePo policyCoveragePo : policyCoveragePos) {
                    if (policyTransData.isNeedToPay(policyCoveragePo, mainCoveragePo, nextReceivableDate)) {
                        totalPremium = totalPremium.add(policyCoveragePo.getTotalPremium());
                        boolean isSingle = PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyCoveragePo.getPremiumFrequency()) ||
                                ("1".equals(policyCoveragePo.getPremiumPeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoveragePo.getPremiumPeriodUnit()));
                        if (!isSingle) {
                            singleOnlyFlag = false;
                        }
                    }
                }
                // 只有短期险需要缴费，缴费周期自动变为年缴
                if (singleOnlyFlag && !PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(mainCoveragePo.getPremiumFrequency())) {
                    totalPremium = totalPremium.divide(new BigDecimal(PolicyTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(mainCoveragePo.getPremiumFrequency()).value()), 2, BigDecimal.ROUND_HALF_UP);
                    policyTransData.transPremiumFrequency(policyCoveragePos);
                }
                if (BigDecimal.ZERO.compareTo(totalPremium) == 0) {
                    break;
                }
                if (nextReceivableDate > maxReceivableDate) {
                    RenewalReceivableResponse renewalReceivableResponse = new RenewalReceivableResponse();
                    renewalReceivableResponse.setPolicyId(policyPo.getPolicyId());
                    renewalReceivableResponse.setPolicyNo(policyPo.getPolicyNo());
                    renewalReceivableResponse.setReceivableDate(nextReceivableDate);
                    renewalReceivableResponse.setRenewalYearMonth(DateUtils.getTimeYearMonth(nextReceivableDate));
                    renewalReceivableResponse.setPeriodTotalPremium(totalPremium);
                    renewalReceivableResponse.setFrequency(frequency+1);
                    renewalReceivableResponse.setPremiumFrequency(mainCoveragePo.getPremiumFrequency());
                    if (PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID.name().equals(policyPo.getPolicyStatus())) {
                        renewalReceivableResponse.setPaymentStatusCode(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_TIMEOUT.name());
                    } else {
                        renewalReceivableResponse.setPaymentStatusCode(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name());
                    }
                    if (nearestReceivableFlag) {
                        renewalReceivableResponse.setRequiredFlag(TerminologyConfigEnum.WHETHER.YES.name());
                        nearestReceivableFlag = false;
                    } else {
                        renewalReceivableResponse.setRequiredFlag(TerminologyConfigEnum.WHETHER.NO.name());
                    }
                    renewalReceivableResponses.add(renewalReceivableResponse);
                }
                frequency++;
                // 下一期应收日期
                if (singleOnlyFlag) {
                    nextReceivableDate = DateUtils.addStringYearsRT(nextReceivableDate, 1);
                } else {
                    nextReceivableDate = renewalDataTransfer.calculateReceivableDate(mainCoveragePo.getPremiumFrequency(), policyPo.getEffectiveDate(), frequency);
                }
            }
            resultObject.setData(renewalReceivableResponses);
        } catch (Exception e) {
            e.printStackTrace();
            this.setResultObjectException(this.getLogger(), resultObject, e, POLICY_QUERY_RENEWAL_RECEIVABLE_ERROR);
        }
        return resultObject;
    }

    /**
     * 批量生成应收记录并发起支付
     * @param policyRenewalRequests
     * @param users 用户
     * @return
     */
    @Override
    @Transactional
    public ResultObject generateRenewalBatch(List<PolicyRenewalRequest> policyRenewalRequests, Users users) {
        ResultObject resultObject = new ResultObject();
        try {
            String policyId = policyRenewalRequests.get(0).getPolicyId();
            String generateBatchId = UUIDUtils.getUUIDShort();
            // 查询保单缴费信息
            List<PolicyPaymentPo> policyPaymentPos = policyPaymentBaseService.listPolicyPayment(policyId, null);

            // 查询保单数据
            PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);
            AssertUtils.isNotNull(this.getLogger(), policyPo, POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
            // 查询保单险种信息
            List<PolicyCoveragePo> policyCoveragePos = renewalDataTransfer.getPolicyCoverage(policyId);

            // 业务校验
            renewalBusinessValidate.validRenewalBatchGenerate(policyRenewalRequests, policyPaymentPos, policyPo, policyCoveragePos);

            // 批量生成应收记录处理
            generateRenewalBatchHandle(policyRenewalRequests, policyPo, generateBatchId, users);

            Map<String, String> map = new HashMap<>();
            map.put("generateBatchId", generateBatchId);
            resultObject.setData(map);
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(getLogger(), e, POLICY_BUSINESS_GENERATE_POLICY_RENEWAL_ERROR);
        }
        return resultObject;
    }

    /**
     * 查询批量生成续期应收状态
     * @param generateBatchId 生成批次ID
     * @return
     */
    @Override
    public ResultObject queryRenewalGenerateStatus(String generateBatchId) {
        ResultObject resultObject = new ResultObject();
        // 查询续期生成批次记录
        PolicyRenewalGenerateBatchPo renewalGenerateBatchPo = policyRenewalGrabBaseService.queryPolicyRenewalGenerateBatch(generateBatchId);
        if (AssertUtils.isNotNull(renewalGenerateBatchPo)) {
            Map<String, String> map = new HashMap<>();
            map.put("generateStatus", renewalGenerateBatchPo.getGenerateStatus());
            map.put("message", renewalGenerateBatchPo.getMessage());
            resultObject.setData(map);
        }
        return resultObject;
    }

    /**
     * 批量生成应收记录处理
     * @param policyRenewalRequests
     * @param policyPo 保单
     * @param generateBatchId 生成批次ID
     * @param users 用户
     */
    @Async
    public void generateRenewalBatchHandle(List<PolicyRenewalRequest> policyRenewalRequests, PolicyPo policyPo, String generateBatchId, Users users) {
        PolicyRenewalGenerateBatchPo renewalGenerateBatchPo = new PolicyRenewalGenerateBatchPo();
        try {
            String policyId = policyPo.getPolicyId();
            renewalGenerateBatchPo.setGenerateBatchId(generateBatchId);
            renewalGenerateBatchPo.setForceSave(true);
            renewalGenerateBatchPo.setPolicyId(policyId);
            renewalGenerateBatchPo.setPolicyNo(policyPo.getPolicyNo());
            renewalGenerateBatchPo.setTotalPeriod((long)policyRenewalRequests.size());
            renewalGenerateBatchPo.setStartYearMonth(policyRenewalRequests.get(0).getRenewalYearMonth());
            renewalGenerateBatchPo.setEndYearMonth(policyRenewalRequests.get(policyRenewalRequests.size()-1).getRenewalYearMonth());
            renewalGenerateBatchPo.setGenerateStatus(PolicyTermEnum.SUCCESS_FAIL_TYPE.SUCCESS.name());
            renewalGenerateBatchPo.setMessage("SUCCESS");

            // 查询抢单表数据
            List<PolicyRenewalGrabPo> policyRenewalGrabPos = policyBaseService.listPolicyRenewalGrab(policyId);
            // 已生成应收数据的应收时间
            List<Long> receivableDates = policyRenewalGrabPos.stream()
                    .filter(policyRenewalGrabPo -> PolicyTermEnum.SUCCESS_FAIL_TYPE.SUCCESS.name().equals(policyRenewalGrabPo.getRenewalGenerateStatus()))
                    .map(PolicyRenewalGrabPo::getReceivableDate).collect(Collectors.toList());

            // 组装数据
            List<PolicyPaymentPo> paymentPos = new ArrayList<>();
            List<RenewalRequest> renewalRequests = new ArrayList<>();
            List<PolicyRenewalGrabPo> updateRenewalGrabPos = new ArrayList<>();
            List<PolicyRenewalGrabPo> addRenewalGrabPos = new ArrayList<>();
            policyRenewalRequests.stream()
                    .filter(policyRenewalRequest -> !receivableDates.contains(policyRenewalRequest.getReceivableDate()))
                    .forEach(policyRenewalRequest -> {
                        // 查询保单数据
                        PolicyBo policyBo = policyBaseService.queryPolicyBo(policyId);
                        // 设置待生效信息到险种及保费
                        renewalDataTransfer.setPendingInfo(policyBo);
                        // 处理险种
                        policyToRenewalTransData.handlePolicyCoverage(policyBo, policyRenewalRequest.getReceivableDate());
                        // 产生新的缴费记录
                        PolicyPaymentBo policyPaymentBo = policyToRenewalTransData.transNewPayment(policyBo, policyRenewalRequest, users);
                        // 组装续期数据
                        RenewalRequest renewalRequest = policyToRenewalTransData.transferPolicyToRenewalData(
                                policyBo, policyRenewalRequest.getRenewalYearMonth(), policyRenewalRequest.getReceivableDate());
                        paymentPos.add(policyPaymentBo);
                        renewalRequests.add(renewalRequest);

                        // 将数据插入抢单表
                        Optional<PolicyRenewalGrabPo> renewalGrabPoOptional = policyRenewalGrabPos.stream()
                                .filter(policyRenewalGrabPo -> policyRenewalGrabPo.getReceivableDate().equals(policyRenewalRequest.getReceivableDate()))
                                .findFirst();
                        if (renewalGrabPoOptional.isPresent()) {
                            PolicyRenewalGrabPo policyRenewalGrabPo = renewalGrabPoOptional.get();
                            policyRenewalGrabPo.setRenewalGenerateStatus(PolicyTermEnum.SUCCESS_FAIL_TYPE.SUCCESS.name());
                            updateRenewalGrabPos.add(policyRenewalGrabPo);
                        } else {
                            PolicyRenewalGrabPo policyRenewalGrabPo = new PolicyRenewalGrabPo();
                            policyRenewalGrabPo.setPolicyId(policyBo.getPolicyId());
                            policyRenewalGrabPo.setReceivableDate(policyRenewalRequest.getReceivableDate());
                            policyRenewalGrabPo.setReceivableGenerateDate(DateUtils.getCurrentTime());
                            policyRenewalGrabPo.setRenewalType(PolicyTermEnum.RENEWAL_TYPE.RENEWAL.name());
                            policyRenewalGrabPo.setRenewalGenerateStatus(PolicyTermEnum.SUCCESS_FAIL_TYPE.SUCCESS.name());
                            addRenewalGrabPos.add(policyRenewalGrabPo);
                        }
                    });
            if (AssertUtils.isNotEmpty(updateRenewalGrabPos)) {
                policyRenewalGrabBaseService.updatePolicyRenewalGrab(updateRenewalGrabPos, users.getUserId());
            }
            if (AssertUtils.isNotEmpty(addRenewalGrabPos)) {
                policyRenewalGrabBaseService.addPolicyRenewalGrab(addRenewalGrabPos, users.getUserId());
            }

            if (AssertUtils.isNotEmpty(renewalRequests)) {
                // 保存保单操作表
                PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyId);
                if (!AssertUtils.isNotNull(policyOperationPo) || policyOperationPo.getOperationCode().equals(PolicyTermEnum.OPERATION_CODE.RENEWAL_FINISHED.name())) {
                    if (!AssertUtils.isNotNull(policyOperationPo)) {
                        policyOperationPo = new PolicyOperationPo();
                    }
                    policyOperationPo.setPolicyId(policyPo.getPolicyId());
                    policyOperationPo.setOperationCode(PolicyTermEnum.OPERATION_CODE.RENEWAL_PENDING_PAYMENT.name());
                    policyBaseService.savePolicyOperation(policyOperationPo);
                }
            }

            // 待发起支付的应收月份
            List<String> renewalYearMonths = policyRenewalRequests.stream().map(PolicyRenewalRequest::getRenewalYearMonth).collect(Collectors.toList());
            RenewalBatchRequest renewalBatchRequest = new RenewalBatchRequest();
            renewalBatchRequest.setPolicyId(policyId);
            renewalBatchRequest.setRenewals(renewalRequests);
            renewalBatchRequest.setRenewalYearMonths(renewalYearMonths);
            getLogger().info("保单批量转续期请求数据:" + JSON.toJSONString(renewalBatchRequest));
            ResultObject<List<RenewalResponse>> listResultObject = renewalApi.getTransformRenewal(renewalBatchRequest);
            getLogger().info("保单批量转续期返回数据:" + JSON.toJSONString(listResultObject));
            AssertUtils.isResultObjectError(this.getLogger(), listResultObject);
            AssertUtils.isResultObjectDataNull(this.getLogger(), listResultObject, POLICY_BUSINESS_GENERATE_POLICY_RENEWAL_ERROR);

            if (AssertUtils.isNotNull(paymentPos)) {
                paymentPos.forEach(policyPaymentPo -> {
                    listResultObject.getData().stream()
                            .filter(renewalResponse -> renewalResponse.getPolicyPaymentId().equals(policyPaymentPo.getPolicyPaymentId()))
                            .findFirst().ifPresent(renewalResponse -> {
                        policyPaymentPo.setBusinessId(renewalResponse.getRenewalId());
                        policyPaymentPo.setPaymentBusinessType(PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_RENEWAL.name());
                    });
                });
                policyPaymentBaseService.updatePolicyPayment(paymentPos, users.getUserId());
            }

            new Thread(() -> {
                // 批量续期发起支付
                try {
                    RenewalBatchPaymentRequest renewalBatchPaymentRequest = new RenewalBatchPaymentRequest();
                    renewalBatchPaymentRequest.setPolicyId(policyPo.getPolicyId());
                    renewalBatchPaymentRequest.setRenewalYearMonths(renewalYearMonths);
                    ResultObject resultObject = renewalPaymentApi.renewalBatchPayment(renewalBatchPaymentRequest);
                    AssertUtils.isResultObjectError(getLogger(), resultObject);
                } catch (Exception e) {
                    e.printStackTrace();
                    this.getLogger().error("批量续期发起支付出错");
                }
            }).start();
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error("批量生成应收记录处理出错");
            renewalGenerateBatchPo.setGenerateStatus(PolicyTermEnum.SUCCESS_FAIL_TYPE.FAILED.name());
            if (e instanceof RequestException) {
                RequestException error = (RequestException)e;
                renewalGenerateBatchPo.setMessage(error.getiEnum().getValue());
            } else {
                renewalGenerateBatchPo.setMessage(POLICY_BUSINESS_RENEWAL_GENERATE_BATCH_ERROR.getValue());
            }
            throwsException(getLogger(), e, POLICY_BUSINESS_RENEWAL_GENERATE_BATCH_ERROR);
        } finally {
            // 保存续期生成批次记录
            policyRenewalGrabBaseService.savePolicyRenewalGenerateBatch(renewalGenerateBatchPo, users.getUserId());
        }
    }

    @Override
    @Transactional
    public ResultObject deleteReceivable(Users users, AppRequestHeads appRequestHandler, String policyId, String policyPaymentId, boolean renewalDoneFlag, String renewalType) {
        ResultObject resultObject = new ResultObject();
        try {
            //1.数据校验
            AssertUtils.isNotEmpty(this.getLogger(), policyId, POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), policyPaymentId, POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
            PolicyBo policyBo = policyBaseService.queryPolicyBo(policyId);
            AssertUtils.isNotNull(this.getLogger(), policyBo, POLICY_BUSINESS_POLICY_IS_NOT_FOUND);

            String validFlag = PolicyTermEnum.VALID_FLAG.invalid.name();

            //2.查出保单保费和缴费记录
            //3.将对应policyPaymentId的payment和coverage_payment数据改成失效状态
            PolicyPaymentPo policyPaymentPo = policyBaseService.queryPolicyPaymentById(policyPaymentId);
            AssertUtils.isNotNull(this.getLogger(), policyPaymentPo, POLICY_BUSINESS_POLICY_PAYMENT_IS_NOT_FOUND);

            Long receivableDate = policyPaymentPo.getReceivableDate();

            policyPaymentPo.setValidFlag(validFlag);
            policyBaseService.savePolicyPayment(policyPaymentPo);
            List<PolicyCoveragePaymentBo> policyCoveragePaymentBos = policyCoverageBaseService.queryPolicyCoveragePaymentBo(appRequestHandler, policyPaymentPo.getPolicyPaymentId());
            AssertUtils.isNotEmpty(this.getLogger(), policyCoveragePaymentBos, POLICY_BUSINESS_POLICY_PAYMENT_IS_NOT_FOUND);
            policyCoveragePaymentBos.forEach(policyCoveragePaymentBo -> {
                policyCoveragePaymentBo.setValidFlag(validFlag);
                policyCoverageBaseService.savePolicyCoveragePayment(policyCoveragePaymentBo);
            });


            //4.删除抢单表中的记录
            PolicyRenewalGrabPo policyRenewalGrabPo = policyBaseService.queryPolicyRenewalGrab(policyId, receivableDate);
            if (AssertUtils.isNotNull(policyRenewalGrabPo) && renewalType.equals(policyRenewalGrabPo.getRenewalType())) {
                policyBaseService.deletePolicyRenewalGrab(policyRenewalGrabPo);
            }

            //5.还原premium中的应缴时间，回退一个周期
            PolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
            AssertUtils.isNotNull(this.getLogger(), policyPremium, POLICY_QUERY_POLICY_PREMIUM_IS_NOT_FOUND);
            // 计算上一期应收
            Long newReceivableDate = renewalDataTransfer.calculateSubReceivableDate(policyPremium.getPremiumFrequency(), policyPremium.getReceivableDate());
            policyPremium.setReceivableDate(newReceivableDate);
            policyPremiumBaseService.savePolicyPremium(policyPremium);

            // 还原险种保费表中的应缴时间
            List<PolicyCoveragePremiumPo> policyCoveragePremiumPos = policyPremiumBaseService.listPolicyCoveragePremium(policyId);
            policyCoveragePremiumPos.forEach(policyCoveragePremiumPo -> {
                policyCoveragePremiumPo.setReceivableDate(newReceivableDate);
                policyPremiumBaseService.savePolicyCoveragePremium(policyCoveragePremiumPo);
            });

            //5.若不存在续期待缴费,则修改保单操作表状态,续期完成
            PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyBo.getPolicyId());
            if (AssertUtils.isNotNull(policyOperationPo) && renewalDoneFlag) {
                String operationCode = PolicyTermEnum.OPERATION_CODE.RENEWAL_FINISHED.name();
                if (PolicyTermEnum.RENEWAL_TYPE.GROUP_INSTALLMENT.name().equals(renewalType)) {
                    operationCode = PolicyTermEnum.OPERATION_CODE.GROUP_INSTALLMENT_FINISHED.name();
                }
                policyOperationPo.setOperationCode(operationCode);
                policyBaseService.savePolicyOperation(policyOperationPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, POLICY_BUSINESS_DELETE_RENEWAL_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<PaymentAuditDetailResponse> paymentAuditDetail(PolicyPaymentUpdateRequest policyPaymentUpdateRequest) {
        ResultObject<PaymentAuditDetailResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), policyPaymentUpdateRequest.getPaymentIdList(), PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_PAYMENT_ID_LIST_IS_NOT_EMPTY);
            //续期总保费
            List<PolicyPaymentBo> policyPaymentBoList = policyBaseService.queryPolicyPayment(policyPaymentUpdateRequest.getPaymentIdList());
            AssertUtils.isNotEmpty(this.getLogger(),policyPaymentBoList, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_PAYMENT_IS_NOT_FOUND);

            PaymentAuditDetailResponse paymentAuditDetailResponse = new PaymentAuditDetailResponse();

            // 查询保单基本信息
            PolicyPo policyPo = policyBaseService.queryPolicyPo(policyPaymentBoList.get(0).getPolicyId());
            AssertUtils.isNotNull(this.getLogger(), policyPo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
            paymentAuditDetailResponse.setPolicyNo(policyPo.getPolicyNo());
            paymentAuditDetailResponse.setApplyNo(policyPo.getApplyNo());

            //查询保单投保人
            PolicyApplicantBo policyApplicantBo = policyBaseService.queryPolicyApplicant(policyPaymentBoList.get(0).getPolicyId());
            AssertUtils.isNotNull(this.getLogger(), policyApplicantBo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_APPLICANT_IS_NOT_FOUND_OBJECT);
            paymentAuditDetailResponse.setApplicantName(policyApplicantBo.getName());
            paymentAuditDetailResponse.setApplicantMobile(policyApplicantBo.getMobile());


            // 查询代理人信息
            PolicyAgentPo policyAgentPo=policyBaseService.queryPolicyAgent(policyPaymentBoList.get(0).getPolicyId());
            AssertUtils.isNotNull(this.getLogger(), policyAgentPo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_AGENT_IS_NOT_FOUND_OBJECT);
            AgentResponse applyAgentRespFc = agentApi.agentByIdGet(policyAgentPo.getAgentId()).getData();
            if (AssertUtils.isNotNull(applyAgentRespFc)) {
                paymentAuditDetailResponse.setAgentCode(applyAgentRespFc.getAgentCode());
                paymentAuditDetailResponse.setAgentName(applyAgentRespFc.getAgentName());
                paymentAuditDetailResponse.setAgentMobile(applyAgentRespFc.getMobile());
            }

            if (AssertUtils.isNotEmpty(policyPaymentBoList)) {
                policyPaymentBoList.sort(Comparator.comparing(PolicyPaymentBo::getFrequency));
                String[] paymentFrequency=new String[1];
                BigDecimal[] totalPremium=new BigDecimal[1];
                paymentFrequency[0]="";
                totalPremium[0]=new BigDecimal(0);
                policyPaymentBoList.forEach(policyPaymentBo -> {
                    //等待支付或者支付中
                    paymentFrequency[0]=paymentFrequency[0]+policyPaymentBo.getFrequency()+",";
                    totalPremium[0]=totalPremium[0].add(policyPaymentBo.getTotalPremium());
//                    if (policyPaymentBo.getPaymentStatusCode().equals(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name()) || policyPaymentBo.getPaymentStatusCode().equals(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name())) {
//
//                    }
                });
                if(paymentFrequency[0].contains(",")){
                    paymentAuditDetailResponse.setPaymentFrequency(paymentFrequency[0].substring(0,paymentFrequency[0].length()-1));
                }
                paymentAuditDetailResponse.setTotalPremium(totalPremium[0].toString());
            }
            // 查询险种信息
            List<PolicyCoveragePo> listCoverage = policyCoverageBaseService.listPolicyCoverageOfInsured(policyPo.getPolicyId());
            if (AssertUtils.isNotEmpty(listCoverage)) {
                List<PolicyCoverageExt> policyCoverageExtList = (List<PolicyCoverageExt>) this.converterList(
                        listCoverage, new TypeToken<List<PolicyCoverageExt>>() {
                        }.getType()
                );
                List<SyscodeRespFc> listPrimary = platformBaseInternationServiceApi.queryTerminology(TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name()).getData();
                policyCoverageExtList.forEach(policyCoverageExt -> {
                    if (AssertUtils.isNotEmpty(listPrimary)) {
                        listPrimary.stream().filter(syscodeRespFc -> syscodeRespFc.getCodeKey().equals(policyCoverageExt.getPrimaryFlag())).findFirst()
                                .ifPresent(attachmentInter -> {
                                    policyCoverageExt.setPrimaryFlagName(attachmentInter.getCodeName());
                                });
                    }
                });

                paymentAuditDetailResponse.setListCoverage(policyCoverageExtList);
            }
            resultObject.setData(paymentAuditDetailResponse);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
        }
        return resultObject;
    }

    /**
     * 保单中止处理
     * @param invalidHandleRequest 请求参数
     * @param users 用户
     * @return
     */
    @Override
    @Transactional
    public ResultObject<PolicyInvalidHandleResponse> handlePolicyInvalid(PolicyInvalidHandleRequest invalidHandleRequest, Users users) {
        this.getLogger().info("保单中止处理请求参数：{}", JSON.toJSONString(invalidHandleRequest));
        String policyId = invalidHandleRequest.getPolicyId();
        AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);

        // 查询保单数据
        PolicyBo policyBo = policyExtDao.loadPolicyByPolicyId(policyId);
        AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
        // 备份保单信息
        policyTransData.savePolicyBaseHistoryData(policyBo);

        List<String> strings = Arrays.asList(
                POLICY_STATUS_INDEMNITY_TERMINATION.name(),
                POLICY_STATUS_HESITATION_REVOKE.name(),
                POLICY_STATUS_INVALID_THOROUGH.name(),
                POLICY_STATUS_EFFECT_TERMINATION.name(),
                POLICY_STATUS_SURRENDER.name(),
                POLICY_STATUS_IEXPIRE.name(),
                POLICY_STATUS_INVALID.name()
        );
        if (!strings.contains(policyBo.getPolicyStatus()) && !AssertUtils.isNotEmpty(invalidHandleRequest.getCoverageIds())) {
            policyBo.setPolicyStatus(POLICY_STATUS_INVALID.name());
            policyBo.setInvalidDate(invalidHandleRequest.getInvalidDate());
        }
        //产生新保单版本
        String newVersionNo = DateUtils.getJobNumberByTime("", "", DateUtils.FORMATE53, false);
        policyBo.setVersionNo(newVersionNo);
        policyBo.setDataEffectiveDate(DateUtils.getCurrentTime());

        policyBaseService.savePolicy(policyBo, users.getUserId());

        // 查询保单险种
        List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.listPolicyCoverageOfInsured(policyId);
        List<String> coverageIds = new ArrayList<>();
        policyCoveragePos.stream().filter(coverage -> !AssertUtils.isNotEmpty(invalidHandleRequest.getCoverageIds())
                || invalidHandleRequest.getCoverageIds().contains(coverage.getCoverageId()))
                .forEach(coverage -> {
            if (coverage.getCoveragePeriodEndDate() < DateUtils.getCurrentTime() && !AssertUtils.isNotEmpty(invalidHandleRequest.getCoverageIds())) {
                // 险种满期
                coverage.setCoverageStatus(PolicyTermEnum.COVERAGE_STATUS.EXPIRED.name());
                coverageIds.add(coverage.getCoverageId());
            } else {
                coverage.setCoverageStatus(PolicyTermEnum.COVERAGE_STATUS.INVALID.name());
            }
        });
        // 更新险种
        policyCoverageBaseService.updatePolicyCoverage(policyCoveragePos, users.getUserId());

        // 查询保单缴费信息(含险种缴费)
        List<PolicyPaymentBo> policyPaymentBos = policyPaymentBaseService.listPolicyPaymentBo(policyId);
        AssertUtils.isNotEmpty(getLogger(), policyPaymentBos, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR);

        List<PolicyPaymentPo> policyPaymentPos = new ArrayList<>();
        List<PolicyCoveragePaymentPo> policyCoveragePaymentPos = new ArrayList<>();
        policyPaymentBos.stream()
                .filter(policyPaymentBo -> !PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(policyPaymentBo.getPaymentStatusCode())
                        && !PolicyTermEnum.PAYMENT_STATUS.PAYMENT_TIMEOUT.name().equals(policyPaymentBo.getPaymentStatusCode()))
                .forEach(policyPaymentBo -> {
            policyPaymentBo.setPaymentStatusCode(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_TIMEOUT.name());
            if (AssertUtils.isNotEmpty(coverageIds)) {
                policyPaymentBo.getListPolicyCoveragePayment().forEach(coveragePaymentBo -> {
                    if (coverageIds.contains(coveragePaymentBo.getCoverageId())) {
                        coveragePaymentBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                        policyPaymentBo.setPeriodOriginalPremium(policyPaymentBo.getPeriodOriginalPremium().subtract(coveragePaymentBo.getPeriodOriginalPremium()));
                        policyPaymentBo.setPeriodActualPremium(policyPaymentBo.getPeriodActualPremium().subtract(coveragePaymentBo.getPeriodActualPremium()));
                        policyPaymentBo.setTotalPremium(policyPaymentBo.getTotalPremium().subtract(coveragePaymentBo.getTotalPremium()));
                        policyCoveragePaymentPos.add(coveragePaymentBo);
                    }
                });
            }
            policyPaymentPos.add(policyPaymentBo);
        });
        // 更新保单缴费信息
        policyPaymentBaseService.updatePolicyPayment(policyPaymentPos, users.getUserId());
        // 更新险种缴费信息
        if (AssertUtils.isNotEmpty(policyCoveragePaymentPos)) {
            policyPaymentBaseService.updatePolicyCoveragePayment(policyCoveragePaymentPos, users.getUserId());
        }

        if (AssertUtils.isNotEmpty(coverageIds)) {
            // 查询保单保费信息
            PolicyPremiumBo policyPremiumBo = policyPremiumBaseService.queryPolicyPremiumBo(policyId);
            List<PolicyCoveragePremiumPo> policyCoveragePremiumPos = new ArrayList<>();
            policyPremiumBo.getListCoveragePremium().forEach(coveragePremium -> {
                if (coverageIds.contains(coveragePremium.getCoverageId())) {
                    coveragePremium.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                    policyPremiumBo.setPeriodOriginalPremium(policyPremiumBo.getPeriodOriginalPremium().subtract(coveragePremium.getPeriodOriginalPremium()));
                    policyPremiumBo.setPeriodTotalPremium(policyPremiumBo.getPeriodTotalPremium().subtract(coveragePremium.getPeriodTotalPremium()));
                    policyCoveragePremiumPos.add(coveragePremium);
                }
            });
            if (AssertUtils.isNotEmpty(policyCoveragePremiumPos)) {
                // 更新保单保费信息
                policyPremiumBaseService.savePolicyPremium(policyPremiumBo, users.getUserId());
                // 更新险种缴费信息
                policyPremiumBaseService.updatePolicyCoveragePremium(policyCoveragePremiumPos, users.getUserId());
            }
        }

        // 失效保单操作
        PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyId);
        if (AssertUtils.isNotNull(policyOperationPo)) {
            policyOperationPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
            policyBaseService.savePolicyOperation(policyOperationPo);
        }

        PolicyInvalidHandleResponse invalidHandleResponse = new PolicyInvalidHandleResponse();
        invalidHandleResponse.setCoverageIds(coverageIds);
        ResultObject<PolicyInvalidHandleResponse> resultObject = new ResultObject<>();
        resultObject.setData(invalidHandleResponse);
        return resultObject;
    }

    /**
     * 更新险种状态为有效(保费重算)
     * @param coverageIds 险种ID
     * @param users 用户
     * @return
     */
    @Override
    @Transactional
    public ResultObject updateCoverageEffective(List<String> coverageIds, Users users) {
        // 查询险种信息
        List<PolicyCoveragePo> coveragePos = policyCoverageBaseService.listPolicyCoverageByIds(coverageIds);
        if (AssertUtils.isNotEmpty(coveragePos)) {
            // 满期险种ID
            List<String> expiredCoverageIds = new ArrayList<>();
            coveragePos.forEach(coveragePo -> {
                if (PolicyTermEnum.COVERAGE_STATUS.EXPIRED.name().equals(coveragePo.getCoverageStatus())) {
                    expiredCoverageIds.add(coveragePo.getCoverageId());
                }
                coveragePo.setCoverageStatus(PolicyTermEnum.COVERAGE_STATUS.EFFECTIVE.name());
            });
            // 更新险种
            policyCoverageBaseService.updatePolicyCoverage(coveragePos, users.getUserId());

            if (!AssertUtils.isNotEmpty(expiredCoverageIds)) {
                // 没有满期险种，不需要重算保费
                return ResultObject.success();
            }

            String policyId = coveragePos.get(0).getPolicyId();
            // 查询险种缴费
            List<PolicyCoveragePaymentPo> coveragePaymentPos = policyPaymentBaseService.listCoveragePayment(expiredCoverageIds);
            if (AssertUtils.isNotEmpty(coveragePaymentPos)) {
                // 查询保单缴费信息(含险种缴费)
                List<PolicyPaymentBo> policyPaymentBos = policyPaymentBaseService.listPolicyPaymentBo(policyId);

                List<PolicyPaymentPo> policyPaymentPos = new ArrayList<>();
                List<PolicyCoveragePaymentPo> policyCoveragePaymentPos = new ArrayList<>();
                policyPaymentBos.stream()
                        .filter(policyPaymentBo -> PolicyTermEnum.PAYMENT_STATUS.PAYMENT_TIMEOUT.name().equals(policyPaymentBo.getPaymentStatusCode()))
                        .forEach(policyPaymentBo -> {
                            policyPaymentBo.setPaymentStatusCode(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name());
                            coveragePaymentPos.stream()
                                    .filter(coveragePaymentPo -> policyPaymentBo.getPolicyPaymentId().equals(coveragePaymentPo.getPolicyPaymentId()))
                                    .forEach(coveragePaymentPo -> {
                                        coveragePaymentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                                        policyPaymentBo.setPeriodOriginalPremium(policyPaymentBo.getPeriodOriginalPremium().add(coveragePaymentPo.getPeriodOriginalPremium()));
                                        policyPaymentBo.setTotalPremium(policyPaymentBo.getTotalPremium().add(coveragePaymentPo.getTotalPremium()));
                                        policyCoveragePaymentPos.add(coveragePaymentPo);
                                    });
                            policyPaymentPos.add(policyPaymentBo);
                        });
                // 更新保单缴费信息
                policyPaymentBaseService.updatePolicyPayment(policyPaymentPos, users.getUserId());
                // 更新险种缴费信息
                if (AssertUtils.isNotEmpty(policyCoveragePaymentPos)) {
                    policyPaymentBaseService.updatePolicyCoveragePayment(policyCoveragePaymentPos, users.getUserId());
                }
            }

            // 查询险种保费
            List<PolicyCoveragePremiumPo> policyCoveragePremiumPos = policyPremiumBaseService.listPolicyCoveragePremium(expiredCoverageIds);
            if (AssertUtils.isNotEmpty(policyCoveragePremiumPos)) {
                // 查询保单保费信息
                PolicyPremiumBo policyPremiumBo = policyPremiumBaseService.queryPolicyPremiumBo(policyId);

                policyCoveragePremiumPos.forEach(coveragePremiumPo -> {
                    coveragePremiumPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                    policyPremiumBo.setPeriodOriginalPremium(policyPremiumBo.getPeriodOriginalPremium().add(coveragePremiumPo.getPeriodOriginalPremium()));
                    policyPremiumBo.setPeriodTotalPremium(policyPremiumBo.getPeriodTotalPremium().add(coveragePremiumPo.getPeriodTotalPremium()));
                });
                // 更新保单保费信息
                policyPremiumBaseService.savePolicyPremium(policyPremiumBo, users.getUserId());
                // 更新险种缴费信息
                policyPremiumBaseService.updatePolicyCoveragePremium(policyCoveragePremiumPos, users.getUserId());
            }
        }

        return ResultObject.success();
    }

    /**
     * 批量删除续期相关数据
     * @param policyId 保单ID
     * @param policyPaymentIds 保单缴费ID
     * @param renewalDoneFlag 续期完成标志
     * @param users 用户
     * @return
     */
    @Override
    public ResultObject deleteRenewalBatch(String policyId, List<String> policyPaymentIds, boolean renewalDoneFlag, Users users) {
        ResultObject resultObject = new ResultObject();
        try {
            // 数据校验
            AssertUtils.isNotEmpty(this.getLogger(), policyId, POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), policyPaymentIds, POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
            PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);
            AssertUtils.isNotNull(this.getLogger(), policyPo, POLICY_BUSINESS_POLICY_IS_NOT_FOUND);

            // 查出保单缴费和险种缴费
            List<PolicyPaymentBo> policyPaymentBos = policyPaymentBaseService.listPolicyPaymentBo(policyPaymentIds);
            AssertUtils.isNotEmpty(this.getLogger(), policyPaymentBos, POLICY_BUSINESS_POLICY_PAYMENT_IS_NOT_FOUND);
            // 删除保单缴费和险种缴费
            policyPaymentBaseService.deletePolicyPaymentBo(policyPaymentBos);
            List<Long> receivableDates = policyPaymentBos.stream().map(PolicyPaymentBo::getReceivableDate).collect(Collectors.toList());

            // 删除抢单表中的记录
            List<PolicyRenewalGrabPo> policyRenewalGrabPos = policyRenewalGrabBaseService.listPolicyRenewalGrab(policyId, receivableDates);
            policyRenewalGrabBaseService.deletePolicyRenewalGrab(policyRenewalGrabPos);

            // 还原premium中的应缴时间
            PolicyPremiumBo policyPremiumBo = policyPremiumBaseService.queryPolicyPremiumBo(policyId);
            AssertUtils.isNotNull(this.getLogger(), policyPremiumBo, POLICY_QUERY_POLICY_PREMIUM_IS_NOT_FOUND);
            // 计算当前应收
            Long minReceivableDate = receivableDates.stream().min(Comparator.comparing(Long::longValue)).get();
            Long newReceivableDate = renewalDataTransfer.calculateSubReceivableDate(policyPremiumBo.getPremiumFrequency(), minReceivableDate);
            policyPremiumBo.setReceivableDate(newReceivableDate);

            // 还原险种保费表中的应缴时间
            policyPremiumBo.getListCoveragePremium().forEach(coveragePremiumPo -> {
                coveragePremiumPo.setReceivableDate(newReceivableDate);
            });
            policyPremiumBaseService.savePolicyPremium(policyPremiumBo, users.getUserId());

            // 若不存在续期待缴费,则修改保单操作表状态,续期完成
            PolicyOperationPo policyOperationPo = policyBaseService.queryPolicyOperation(policyPo.getPolicyId());
            if(AssertUtils.isNotNull(policyOperationPo) && renewalDoneFlag){
                policyOperationPo.setOperationCode(PolicyTermEnum.OPERATION_CODE.RENEWAL_FINISHED.name());
                policyBaseService.savePolicyOperation(policyOperationPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, POLICY_BUSINESS_DELETE_RENEWAL_ERROR);
        }
        return resultObject;
    }
}
