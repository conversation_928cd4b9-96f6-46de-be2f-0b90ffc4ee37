package com.gclife.policy.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.api.AttachmentPDFDocumentApi;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.claim.api.ClaimReportApi;
import com.gclife.claim.model.respone.ReportCustomerResponse;
import com.gclife.common.TerminologyConfigEnum.WHETHER;
import com.gclife.common.TerminologyTypeEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.BaseErrorConfigEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.endorse.api.EndorseAcceptApi;
import com.gclife.endorse.model.response.AcceptApplyResponse;
import com.gclife.message.api.MessageMailApi;
import com.gclife.message.model.request.EmailAttachmentRequest;
import com.gclife.message.model.request.SendMailRequest;
import com.gclife.party.api.CustomerManageApi;
import com.gclife.party.model.response.CustomerMessageResponse;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformTerminologyBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.BranchTreeResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.common.AsyncUtil;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.dao.PolicyExtDao;
import com.gclife.policy.dao.PolicyInsuredBaseDao;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.bo.PolicyQueryListBo;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.config.ciq.CiqPolicyTermEnum;
import com.gclife.policy.model.request.*;
import com.gclife.policy.model.request.group.PolicyListRequest;
import com.gclife.policy.model.response.PolicyStatusResponse;
import com.gclife.policy.model.response.*;
import com.gclife.policy.service.base.PolicyApplicantBaseService;
import com.gclife.policy.service.base.PolicyBaseService;
import com.gclife.policy.service.base.PolicyCoverageBaseService;
import com.gclife.policy.service.base.PolicyHistoryBaseService;
import com.gclife.policy.service.base.PolicyQueryBaseService;
import com.gclife.policy.service.business.MessageBusinessService;
import com.gclife.policy.service.business.PolicyBusinessService;
import com.gclife.policy.service.data.PolicyBoService;
import com.gclife.policy.transform.LanguageUtils;
import com.gclife.policy.validate.transfer.GroupPolicyTransData;
import com.gclife.policy.validate.transfer.PolicyDetailTransfer;
import com.gclife.policy.validate.transfer.PolicyReviewTransData;
import com.gclife.policy.validate.transfer.PolicyTransData;
import com.gclife.product.model.config.ProductTermEnum;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.EN_US;
import static com.gclife.policy.model.config.PolicyTermEnum.ATTACHMENT_TYPE_FLAG.POLICY_BOOK;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_STATUS_FLAG.*;

/**
 * <AUTHOR>
 * create 17-10-16
 * description:保单基础业务类
 */
@Service
public class PolicyBusinessServiceImpl extends BaseBusinessServiceImpl implements PolicyBusinessService {
    @Autowired
    private PolicyExtDao policyExtDao;
    @Autowired
    private PolicyDetailTransfer policyDetailTransfer;
    @Autowired
    private PolicyTransData policyTransData;
    @Autowired
    private PolicyBoService policyBoService;
    @Autowired
    private AttachmentPDFDocumentApi attachmentPDFDocumentApi;
    @Autowired
    private MessageMailApi messageMailApi;
    @Autowired
    private GroupPolicyTransData groupPolicyTransData;
    @Autowired
    private PolicyQueryBaseService policyQueryBaseService;
    @Autowired
    private PolicyBaseService policyBaseService;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private PlatformTerminologyBaseApi platformTerminologyBaseApi;
    @Autowired
    private PolicyCoverageBaseService policyCoverageBaseService;
    @Autowired
    private PolicyInsuredBaseDao policyInsuredBaseDao;
    @Autowired
    private PolicyHistoryBaseService policyHistoryBaseService;
    @Autowired
    private CustomerManageApi customerManageApi;
    @Autowired
    private PolicyReviewTransData policyReviewTransData;
    @Autowired
    private EndorseAcceptApi endorseAcceptApi;
    @Autowired
    private ClaimReportApi claimReportApi;

    @Autowired
    private PolicyApplicantBaseService policyApplicantBaseService;
    /**
     * 查询保单列表
     *
     * @param request 请求对象
     * @return 分页列表
     */
    @Override
    public ResultObject<BasePageResponse<PolicyQueryListResponse>> queryPolicyList(PolicyQueryListRequest request) {
        ResultObject<BasePageResponse<PolicyQueryListResponse>> resultObject = new ResultObject<>();
        try {
            // 调用平台微服务查询当前用户管理机构的叶子机构
            List<BranchResponse> branchRespFcs = platformBranchApi.userManagerLeafBranchs().getData();
            List<String> branchIds = branchRespFcs.stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());

            // 获取保费对账清单
            List<PolicyQueryListBo> policyQueryListBos = this.policyExtDao.getPolicyList(request, branchIds);

            // 数据转换
            List<PolicyQueryListResponse> policyQueryListResponses = policyTransData.transferPolicyList(policyQueryListBos);

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(policyQueryListBos) ? policyQueryListBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(
                    request.getCurrentPage(), request.getPageSize(), totalLine, policyQueryListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 根据保单ID查询保单详细信息
     *
     * @param policyId 　保单ID
     * @return ResultObject
     */
    @Override
    public ResultObject<PolicyDetailResponse> loadPolicyDetailByPolicyId(Users users, String policyId) {
        ResultObject<PolicyDetailResponse> resultObject = new ResultObject<>();
        try {
            //验证
            AssertUtils.isNotEmpty(this.getLogger(), policyId, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
            //查询保单信息
            PolicyBo policyBo = policyExtDao.loadPolicyByPolicyId(policyId);
            //数据转换
            PolicyDetailResponse policyDetailResponse = policyDetailTransfer.transferPolicyDetail(users, policyBo);
            //设置返回数据
            resultObject.setData(policyDetailResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyRenewalDetailResponse> loadRenewalPolicyDetailByPolicyId(AppRequestHeads appRequestHeads, String policyId) {
        ResultObject<PolicyRenewalDetailResponse> resultObject = new ResultObject<>();
        try {
            //验证
            AssertUtils.isNotEmpty(this.getLogger(), policyId, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
            //查询保单信息
            PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);
            AssertUtils.isNotNull(getLogger(), policyPo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
            PolicyRenewalDetailResponse policyRenewalDetailResponse = (PolicyRenewalDetailResponse) converterObject(policyPo, PolicyRenewalDetailResponse.class);
            policyRenewalDetailResponse.setPolicyApplicant(policyBaseService.queryPolicyApplicant(policyId));
            policyRenewalDetailResponse.setPolicyAgent(policyBaseService.queryPolicyAgent(policyId));
            policyRenewalDetailResponse.setListPolicyInsured(policyBaseService.listPolicyInsured(policyId));
            policyRenewalDetailResponse.setPolicyPaymentBo(policyBaseService.queryNewPolicyPayment(policyId));

            //设置返回数据
            resultObject.setData(policyRenewalDetailResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<BranchTreeResponse>> getPolicyBranchTree() {
        ResultObject<List<BranchTreeResponse>> resultObject = new ResultObject<>();
        try {
            List<BranchTreeResponse> branchTreeRespFcs = platformBranchApi.userManagerBranchs().getData();
            resultObject.setData(branchTreeRespFcs);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_RECEIPT_LIST_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 保存附件
     *
     * @param users   当前用户
     * @param request 附件请求对象
     * @return 保存结果
     */
    @Override
    @Transactional
    public ResultObject saveAttachment(Users users, AttachmentRequest request) {
        ResultObject resultObject = new ResultObject();
        try {
            PolicyAttachmentPo policyAttachmentPo = new PolicyAttachmentPo();
            policyAttachmentPo.setPolicyId(request.getPolicyId());
            policyAttachmentPo.setAttachmentTypeCode(request.getAttachmentTypeCode());
            policyAttachmentPo.setLanguage(request.getLanguage());
            PolicyAttachmentPo policyAttachment = policyExtDao.getPolicyAttachment(policyAttachmentPo);
            if (AssertUtils.isNotNull(policyAttachment)) {
                policyAttachmentPo = (PolicyAttachmentPo) this.converterObject(policyAttachment, PolicyAttachmentPo.class);
            }
            policyAttachmentPo.setAttachmentId(request.getAttachmentId());
            policyBoService.savePolicyAttachmentPo(policyAttachmentPo);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return null;
    }

    /**
     * 电子保单下载
     *
     * @param response
     * @param policyDownLoadRequest
     * @return
     */
    @Override
    public ResultObject<BaseResponse> downloadPolicy(HttpServletResponse response, PolicyDownLoadRequest policyDownLoadRequest) {
        // 语言默认英语
        String language = TerminologyConfigEnum.LANGUAGE.EN_US.name();
        // 数据校验
        AssertUtils.isNotEmpty(this.getLogger(), policyDownLoadRequest.getPolicyId(), PolicyErrorConfigEnum.POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            // 查询保单附件
            PolicyAttachmentPo policyAttachmentPo = policyExtDao.getPolicyAttachment(policyDownLoadRequest.getPolicyId(), language);
            String policyAttachId = null;
            if (!AssertUtils.isNotNull(policyAttachmentPo)) {
                // 查询保单详情
                PolicyBo policyBo = policyExtDao.loadPolicyByPolicyId(policyDownLoadRequest.getPolicyId());
                // 生成保单PDF
                List<AttachmentResponse> attachmentResponseFcs = policyTransData.attachmentPdfGenerate(policyBo, language);
                AttachmentResponse attachmentResponse = attachmentResponseFcs.stream().filter(attachmentResponseFc -> POLICY_BOOK.name().equals(attachmentResponseFc.getTemplateType())).findFirst().get();
                policyAttachId = attachmentResponse.getMediaId();
            } else {
                policyAttachId = policyAttachmentPo.getAttachmentId();
            }
            // 下载PDF文件
            AttachmentByteResponse data = null;
            List<String> attachmentIdList = new ArrayList<>();
            //增加 保单打印序列号
            attachmentIdList.add("UPDATE_PAGE");
            attachmentIdList.add(policyAttachId);
            ResultObject<AttachmentByteResponse> attachmentByteResponseFcResultObject = attachmentPDFDocumentApi.electronicPolicyDownload(attachmentIdList.toArray(new String[attachmentIdList.size()]));
            this.getLogger().error(attachmentByteResponseFcResultObject.getMessage());
            if (AssertUtils.isNotNull(attachmentByteResponseFcResultObject.getData()) && attachmentByteResponseFcResultObject.getError().equals(BaseErrorConfigEnum.SUCCESS.name())) {
                data = attachmentByteResponseFcResultObject.getData();
            }
            if (AssertUtils.isNotNull(data)) {
                byte[] bytesFile = data.getFileByte();
                response.reset();
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/pdf");
                response.addHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode("policy.pdf", "UTF-8"));
                response.setHeader("Access-Control-Allow-Origin", "*");
                response.setHeader("Access-Control-Allow-Methods", "POST,GET");
                response.setHeader("Access-Control-Allow-Credentials", "true");
                ServletOutputStream outputStream = response.getOutputStream();
                outputStream.write(bytesFile);

                outputStream.close();
            }

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<BaseResponse> sendPolicy(String policyId, String receiverEmail) {
        // 查询保单详情
        PolicyBo policyBo = policyExtDao.loadPolicyByPolicyId(policyId);
        return sendPolicy(policyBo, receiverEmail);
    }

    /**
     * 发送电子保单
     *
     * @param policyBo      保单ID
     * @param receiverEmail 接收邮箱
     * @return
     */
    @Override
    public ResultObject<BaseResponse> sendPolicy(PolicyBo policyBo, String receiverEmail) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();

        // 语言默认英语
        String language = TerminologyConfigEnum.LANGUAGE.EN_US.name();
        // 数据校验
        AssertUtils.isNotEmpty(this.getLogger(), policyBo.getPolicyId(), PolicyErrorConfigEnum.POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), receiverEmail, PolicyErrorConfigEnum.POLICY_PARAMETER_RECEIVER_EMAIL_IS_NOT_NULL);
        // 验证邮箱格式
        AssertUtils.isEmail(this.getLogger(), receiverEmail, PolicyErrorConfigEnum.POLICY_PARAMETER_RECEIVER_EMAIL_ERROR);

        // 查询保单附件
        PolicyAttachmentPo policyAttachmentPo = policyExtDao.getPolicyAttachment(policyBo.getPolicyId(), language);
        String policyAttachId = null;
        if (!AssertUtils.isNotNull(policyAttachmentPo)) {
            PolicyPo policyPo = policyBaseService.queryPolicyPo(policyBo.getPolicyId());
            // 生成保单PDF
            List<AttachmentResponse> attachmentResponseFcs = null;
            if (PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(policyPo.getPolicyType())) {
                attachmentResponseFcs = policyTransData.attachmentPdfGenerate(policyBo, language);
            } else {
                attachmentResponseFcs = groupPolicyTransData.groupAttachmentPdfGenerate(policyBo.getPolicyId(), EN_US.name());
            }
            AttachmentResponse attachmentResponse = attachmentResponseFcs.stream().filter(attachmentResponseFc -> POLICY_BOOK.name().equals(attachmentResponseFc.getTemplateType())).findFirst().get();
            policyAttachId = attachmentResponse.getMediaId();
        } else {
            policyAttachId = policyAttachmentPo.getAttachmentId();
        }
        // 查询保单
        String policyNo = policyBo.getPolicyNo();
        String effectiveDate = new SimpleDateFormat("MMM dd, yyyy", Locale.ENGLISH).format(new Date(policyBo.getEffectiveDate()));
        SendMailRequest sendMailReqFc = new SendMailRequest();
        List<EmailAttachmentRequest> emailAttachments = new ArrayList<>();
        EmailAttachmentRequest attachmentRequest = new EmailAttachmentRequest();
        attachmentRequest.setAttachmentId(policyAttachId);
        attachmentRequest.setAttachmentName(policyNo + ".pdf");
        emailAttachments.add(attachmentRequest);
        sendMailReqFc.setEmailAttachments(emailAttachments);
        sendMailReqFc.setReceiverEmail(receiverEmail);
        sendMailReqFc.setLanguage(language);
        sendMailReqFc.setApplicantName(policyBo.getPolicyApplicant().getName());
        sendMailReqFc.setPolicyNo(policyNo);
        sendMailReqFc.setEffectiveDate(effectiveDate);
        sendMailReqFc.setMailType(CiqPolicyTermEnum.MAIL_TYPE.MAIL_POLICY.name());
        // 发送电子保单
        ResultObject resultObject2 = messageMailApi.sendPolicyByEmail(sendMailReqFc);
        this.getLogger().info("发送保单电子邮件返回数据:{}", JSON.toJSONString(resultObject2));
        return resultObject;
    }

    @Override
    public ResultObject<PolicyAttachmentResponse> getAttachmentByType(Users currentLoginUsers, String id, String type, String language) {
        ResultObject<PolicyAttachmentResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), id, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
            PolicyAttachmentPo policyAttachmentPo = policyExtDao.getPolicyAttachmentByType(id, type, language);
            AssertUtils.isNotNull(getLogger(), policyAttachmentPo, PolicyErrorConfigEnum.POLICY_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT);
            resultObject.setData((PolicyAttachmentResponse) this.converterObject(policyAttachmentPo, PolicyAttachmentResponse.class));
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<PolicyListResponse>> queryPolicyListByAgentId(PolicyListRequest policyListRequest, Users currentLoginUsers) {
        ResultObject<BasePageResponse<PolicyListResponse>> resultObject = new ResultObject<>();
        try {
            if (!AssertUtils.isNotEmpty(policyListRequest.getAgentId())) {
                return resultObject;
            }
            List<PolicyListBo> policyListBos = policyQueryBaseService.loadPolicyListByUserId(policyListRequest.getAgentId(), policyListRequest, policyListRequest.getPolicyType());
            List<PolicyListResponse> policyListResponses = groupPolicyTransData.transPolicyList(currentLoginUsers, policyListBos, null);

            if (AssertUtils.isNotNull(policyListResponses)){
                policyListResponses.forEach(policyListResponse -> {
                    if (AssertUtils.isNotEmpty(policyListRequest.getPolicyType())){
                        if (PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(policyListRequest.getPolicyType())){
                            PolicyCoveragePo PolicyCoveragePo = policyCoverageBaseService.queryPolicyCoveragePo(policyListResponse.getPolicyId());
                            if (AssertUtils.isNotNull(PolicyCoveragePo)){
                                policyListResponse.setPremiumFrequency(PolicyCoveragePo.getPremiumFrequency());
                                policyListResponse.setCoveragePeriod(PolicyCoveragePo.getCoveragePeriod());
                                policyListResponse.setCoveragePeriodUnit(PolicyCoveragePo.getCoveragePeriodUnit());
                            }
                        }
                    }
                });
            }
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(policyListBos) ? policyListBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(policyListRequest.getCurrentPage(), policyListRequest.getPageSize(), totalLine, policyListResponses);
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_FAIL);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    @Transactional
    public String pushPolicyHesitation(BasePageRequest basePageRequest) {
        try {
            List<PolicyPo> policyPos = policyQueryBaseService.listPolicyPo(basePageRequest);
            if (AssertUtils.isNotEmpty(policyPos)) {
                policyPos.forEach(policyPo -> {
                    boolean flag = DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE3).equals(DateUtils.timeStrToString(policyPo.getHesitationEndDate(), DateUtils.FORMATE3));
                    //当前时间等于犹豫期日期时，发送消息
                    if (flag) {
                        messageBusinessService.pushPolicyHesitation(policyPo, PolicyTermEnum.MSG_BUSINESS_TYPE.POLICY_AFTER_HESITATION.name());
                    }
                });
            }

            if (AssertUtils.isNotEmpty(policyPos) && policyPos.size() == basePageRequest.getPageSize()) {
                return TerminologyConfigEnum.WHETHER.NO.name();
            } else {
                return TerminologyConfigEnum.WHETHER.YES.name();
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return null;
    }

    @Override
    public ResultObject<List<PolicyStatusResponse>> queryPolicyStatus(List<String> idList) {
        ResultObject<List<PolicyStatusResponse>> resultObject = new ResultObject<>();

        List<PolicyStatusBo> policyBoList = policyQueryBaseService.queryPolicyStatus(idList);
        List<SyscodeResponse> whetherList = platformTerminologyBaseApi.queryTerminology(TerminologyTypeEnum.WHETHER.name()).getData();

        List<PolicyStatusBo> policyStatusBoList = new ArrayList<>();
        idList.forEach(id -> {
            PolicyStatusBo policyStatusBo = new PolicyStatusBo();
            policyStatusBo.setCustomerId(id);
            AtomicReference<String> whether = new AtomicReference<>(WHETHER.NO.name());
            if (AssertUtils.isNotEmpty(policyBoList)) {
                List<PolicyStatusBo> policyStatusBos = policyBoList.stream().filter(bo -> id.equals(bo.getCustomerId()) || id.equals(bo.getDelegateCustomerId()))
                        .collect(Collectors.toList());
                whether.set(whether(policyStatusBos));
            }
            policyStatusBo.setWhether(LanguageUtils.getCodeNewName(whetherList, whether.get()));
            policyStatusBoList.add(policyStatusBo);


        });

        List<PolicyStatusResponse> policyStatusResponseList = (List<PolicyStatusResponse>) this.converterList(policyStatusBoList, new TypeToken<List<PolicyStatusResponse>>() {
        }.getType());
        resultObject.setData(policyStatusResponseList);
        return resultObject;
    }

    /**
     * 有效状态（除开犹豫期撤单、效力终止、退保、赔付终止和保单满期）下的话，则显示“是”，否则显示“否”
     *
     * @param policyStatusBos
     * @return
     */
    protected String whether(List<PolicyStatusBo> policyStatusBos) {
        for (PolicyStatusBo policyStatusBo : policyStatusBos) {
            String policyStatus = policyStatusBo.getPolicyStatus();
            List<String> strings = Arrays.asList(
                    POLICY_STATUS_INDEMNITY_TERMINATION.name(),
                    POLICY_STATUS_HESITATION_REVOKE.name(),
                    POLICY_STATUS_INVALID_THOROUGH.name(),
                    POLICY_STATUS_EFFECT_TERMINATION.name(),
                    POLICY_STATUS_SURRENDER.name(),
                    POLICY_STATUS_IEXPIRE.name()
            );
            if (!strings.contains(policyStatus)) {
                return WHETHER.YES.name();
            }
        }
        return WHETHER.NO.name();
    }

    @Override
    public  ResultObject<List<PolicyInsuredResponse>> queryInsuredList(List<String> policyIdList) {
        ResultObject<List<PolicyInsuredResponse>> resultObject = new ResultObject<>();
        List<PolicyInsuredBo> policyInsuredBoList = policyInsuredBaseDao.queryInsuredListByPolicyId(policyIdList);
        if(!AssertUtils.isNotEmpty(policyInsuredBoList)){
            return resultObject;
        }
        List<PolicyInsuredResponse> policyInsuredResponseList = (List<PolicyInsuredResponse>) this.converterList(policyInsuredBoList, new TypeToken<List<PolicyInsuredResponse>>() {
        }.getType());
        resultObject.setData(policyInsuredResponseList);
        return resultObject;

    }

    /**
     * 回滚保单信息
     *
     * @param policyId     保单ID
     * @param oldVersionNo 旧版本号
     * @return
     */
    @Override
    @Transactional
    public ResultObject<Void> rollbackPolicy(String policyId, String oldVersionNo) {
        ResultObject<Void> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), policyId, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), oldVersionNo, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_VERSION_NO_IS_NOT_NULL);
            // 保单基础
            policyHistoryBaseService.rollbackPolicyHistory(policyId, oldVersionNo);
            // 保单代理人
            policyHistoryBaseService.rollbackPolicyAgentHistory(policyId, oldVersionNo);
            // 保单投保人
            policyHistoryBaseService.rollbackPolicyApplicantHistory(policyId, oldVersionNo);
            // 保单被保人
            policyHistoryBaseService.rollbackPolicyInsuredHistory(policyId, oldVersionNo);
            // 保单受益人
            policyHistoryBaseService.rollbackPolicyBeneficiaryHistory(policyId, oldVersionNo);
            // 保单险种
            policyHistoryBaseService.rollbackPolicyCoverageHistory(policyId, oldVersionNo);
            // 保单险种红利
            policyHistoryBaseService.rollbackPolicyCoverageBonusHistory(policyId, oldVersionNo);
            // 保单险种责任
            policyHistoryBaseService.rollbackPolicyCoverageDutyHistory(policyId, oldVersionNo);
            // 保单险种保费
            policyHistoryBaseService.rollbackPolicyCoveragePremiumHistory(policyId, oldVersionNo);
            // 保单险种缴费
            policyHistoryBaseService.rollbackPolicyCoveragePaymentHistory(policyId, oldVersionNo);
            // 保单被保人统计
            policyHistoryBaseService.rollbackPolicyInsuredCollectHistory(policyId, oldVersionNo);
            // 保单付费人
            policyHistoryBaseService.rollbackPolicyPayorInfoHistory(policyId, oldVersionNo);
            // 保单保费
            policyHistoryBaseService.rollbackPolicyPremiumHistory(policyId, oldVersionNo);
            // 保单缴费
            policyHistoryBaseService.rollbackPolicyPaymentHistory(policyId, oldVersionNo);
            // 保单被保人扩展
            policyHistoryBaseService.rollbackPolicyInsuredExtendHistory(policyId, oldVersionNo);
            // 保单加费
            policyHistoryBaseService.rollbackPolicyAddPremiumHistory(policyId, oldVersionNo);
            // 保单打印
            policyHistoryBaseService.rollbackPolicyPrintInfoHistory(policyId, oldVersionNo);
            // 保单回执
            policyHistoryBaseService.rollbackPolicyReceiptInfoHistory(policyId, oldVersionNo);
            // 保单账户
            policyHistoryBaseService.rollbackPolicyAccountHistory(policyId, oldVersionNo);
            // 保单附件
            policyHistoryBaseService.rollbackPolicyAttachmentHistory(policyId, oldVersionNo);
            // 保单特别约定
            policyHistoryBaseService.rollbackPolicySpecialContractHistory(policyId, oldVersionNo);
        } catch (RequestException e) {
            e.printStackTrace();
            setTransactionalResultObjectException(getLogger(), resultObject, e, PolicyErrorConfigEnum.POLICY_BASE_ROLLBACK_POLICY_ERROR);
        }
        return resultObject;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultObject updateAttachmentGenerateStatus(String policyId, String attachmentTypeCode, String language, String status) {
        ResultObject resultObject = new ResultObject();
        try {
            PolicyAttachmentPo policyAttachmentPo = new PolicyAttachmentPo();
            policyAttachmentPo.setPolicyId(policyId);
            policyAttachmentPo.setAttachmentTypeCode(attachmentTypeCode);
            policyAttachmentPo.setLanguage(language);
            List<PolicyAttachmentPo> policyAttachmentPos = policyExtDao.listPolicyAttachmentPo(policyAttachmentPo);
            if (AssertUtils.isNotEmpty(policyAttachmentPos)) {
                for (PolicyAttachmentPo newPolicyAttachmentPo : policyAttachmentPos) {
                    newPolicyAttachmentPo.setGenerateStatus(status);
                    policyBoService.savePolicyAttachmentPo(newPolicyAttachmentPo);
                }
            }
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return null;
    }

    @Override
    public ResultObject<PolicyAttachmentBo> getAttachmentGenerateStatus(String policyId, String attachmentTypeCode, String language) {
        ResultObject<PolicyAttachmentBo> resultObject = new ResultObject();
        // 封装 policyAttachmentPo 查询条件
        PolicyAttachmentPo policyAttachmentPo = new PolicyAttachmentPo();
        policyAttachmentPo.setPolicyId(policyId);
        policyAttachmentPo.setAttachmentTypeCode(attachmentTypeCode);
        policyAttachmentPo.setLanguage(language);
        PolicyAttachmentPo policyAttachment = policyExtDao.getPolicyAttachment(policyAttachmentPo);
        PolicyAttachmentBo policyAttachmentBo = null;
        if (AssertUtils.isNotNull(policyAttachment)) {
            policyAttachmentBo = (PolicyAttachmentBo) converterObject(policyAttachment, PolicyAttachmentBo.class);
        }
        resultObject.setData(policyAttachmentBo);
        return resultObject;
    }

    @Override
    public ResultObject<SuspectedCustomerResponse> suspectedCustomerGet(SuspectedCustomerRequest suspectedCustomerRequest) {
        ResultObject<SuspectedCustomerResponse> resultObject = new ResultObject<>();
        SuspectedCustomerResponse suspectedCustomerResponse = new SuspectedCustomerResponse();

        if (AssertUtils.isNotEmpty(suspectedCustomerRequest.getFamilyName()) && AssertUtils.isNotEmpty(suspectedCustomerRequest.getGivenName())) {
            suspectedCustomerRequest.setName(suspectedCustomerRequest.getFamilyName() + " " + suspectedCustomerRequest.getGivenName());
        }

        AssertUtils.isNotEmpty(this.getLogger(), suspectedCustomerRequest.getBirthdayFormat(), PolicyErrorConfigEnum.POLICY_IMPORT_PARAM_VALID_APPLICANTBIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), suspectedCustomerRequest.getName(), PolicyErrorConfigEnum.POLICY_IMPORT_PARAM_VALID_APPLICANTNAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), suspectedCustomerRequest.getSex(), PolicyErrorConfigEnum.POLICY_IMPORT_PARAM_VALID_APPLICANTSEX_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), suspectedCustomerRequest.getIdNo(), PolicyErrorConfigEnum.POLICY_IMPORT_PARAM_VALID_APPLICANTIDNO_IS_NOT_NULL);

        if (!AssertUtils.isDateFormat(suspectedCustomerRequest.getBirthdayFormat()) && !AssertUtils.isDateFormat(suspectedCustomerRequest.getBirthdayFormat(), DateUtils.FORMATE18)) {
            throw new RequestException(PolicyErrorConfigEnum.POLICY_IMPORT_PARAM_VALID_APPLICANTBIRTHDAY_ERROR);
        }
        Long applicantBirthday = null;
        try {
            applicantBirthday = DateUtils.stringToTime(suspectedCustomerRequest.getBirthdayFormat(), DateUtils.FORMATE3);
        } catch (Exception e) {
        }
        try {
            if (!AssertUtils.isNotNull(applicantBirthday)) {
                applicantBirthday = DateUtils.stringToTime(suspectedCustomerRequest.getBirthdayFormat(), DateUtils.FORMATE18);
            }
        } catch (Exception e) {
        }
        //设置投保人生日时间戳
        suspectedCustomerRequest.setBirthday(applicantBirthday);
        this.getLogger().info("applicantBirthday:" + JSON.toJSONString(applicantBirthday));

        //查询20号产品再次投保客户
        PolicyApplicantBo policyApplicantBo = policyExtDao.queryApprovedAgainCustomer(suspectedCustomerRequest);
        this.getLogger().info("policyApplicantBo:" + JSON.toJSONString(policyApplicantBo));
        if (AssertUtils.isNotNull(policyApplicantBo)) {
           suspectedCustomerResponse.setApprovedAgainFlag(PolicyTermEnum.YES_NO.YES.name());
        }else {
            suspectedCustomerResponse.setApprovedAgainFlag(PolicyTermEnum.YES_NO.NO.name());
        }

        //查询疑似客户
        PolicyApplicantBo policyApplicantBo1 = policyExtDao.querySuspectedCustomer(suspectedCustomerRequest);
        this.getLogger().info("policyApplicantBo1:" + JSON.toJSONString(policyApplicantBo1));
        if (AssertUtils.isNotNull(policyApplicantBo1)) {
            suspectedCustomerResponse.setSuspectedCustomerFlag(PolicyTermEnum.YES_NO.YES.name());
        }else {
            suspectedCustomerResponse.setSuspectedCustomerFlag(PolicyTermEnum.YES_NO.NO.name());
        }

        resultObject.setData(suspectedCustomerResponse);
        return resultObject;
    }

    @Override
    public ResultObject<PolicyOnlineResponse> getPolicyExistByPolicyNo(String policyNo) {
        ResultObject<PolicyOnlineResponse> resultObject = new ResultObject<>();
        PolicyOnlineResponse policyOnlineResponse = new PolicyOnlineResponse();

        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), policyNo, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_NO_IS_NOT_NULL);

        //查询保单是否存在
        PolicyPo policyPo = policyExtDao.loadPolicyPoByPolicyNo(policyNo);
        this.getLogger().info("policyPo:" + JSON.toJSONString(policyPo));
        if (AssertUtils.isNotNull(policyPo)) {
            //只针对于20A产品才能通过
            boolean is20A = ProductTermEnum.PRODUCT.PRODUCT_20A.id().equals(policyReviewTransData.getMainProductId(policyPo.getPolicyId()));
            if (!is20A) {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRODUCT_TYPE_IS_NOT_RIGHT);
            }

            //保全重复提交限制
            ResultObject<AcceptApplyResponse> acceptApplyResponseResultObject = endorseAcceptApi.queryEndorseAcceptInfo(policyPo.getPolicyNo());
            if (!AssertUtils.isResultObjectDataNull(acceptApplyResponseResultObject)) {
                throw new RequestException(PolicyErrorConfigEnum.CLAIM_BUSINESS_HAVE_EXIST);
            }

            policyOnlineResponse.setPolicyId(policyPo.getPolicyId());
            policyOnlineResponse.setPolicyNo(policyPo.getPolicyNo());

            //查询保单投保人信息
            PolicyApplicantBo policyApplicantBo = policyExtDao.loadPolicyApplicantByPolicyId(policyPo.getPolicyId());
            if (AssertUtils.isNotNull(policyApplicantBo)) {
                //调用party服务查询客户信息
                ResultObject<CustomerMessageResponse> customerMessageResponseResultObject = customerManageApi.queryRealCustomerByCustomerAgentId(policyApplicantBo.getCustomerId());
                if (!AssertUtils.isResultObjectDataNull(customerMessageResponseResultObject)) {
                    policyOnlineResponse.setCustomerId(customerMessageResponseResultObject.getData().getCustomerId());
                    policyOnlineResponse.setCustomerNo(customerMessageResponseResultObject.getData().getCustomerNo());
                    policyOnlineResponse.setIdType(customerMessageResponseResultObject.getData().getIdType());
                    //policyOnlineResponse.setSex(customerMessageResponseResultObject.getData().getSex());
                    policyOnlineResponse.setIdNo(customerMessageResponseResultObject.getData().getIdNo());
                    //policyOnlineResponse.setMobile(customerMessageResponseResultObject.getData().getMobile());
                    //policyOnlineResponse.setEmail(customerMessageResponseResultObject.getData().getEmail());
                }else {
                    throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
                }
            }else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
            }

            resultObject.setData(policyOnlineResponse);
        }else {
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyOnlineResponse> getPolicyExistByPolicyNoEndorse(String policyNo) {
        ResultObject<PolicyOnlineResponse> resultObject = new ResultObject<>();
        PolicyOnlineResponse policyOnlineResponse = new PolicyOnlineResponse();

        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), policyNo, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_NO_IS_NOT_NULL);

        //查询保单是否存在
        PolicyPo policyPo = policyExtDao.loadPolicyPoByPolicyNo(policyNo);
        this.getLogger().info("policyPo:" + JSON.toJSONString(policyPo));
        if (AssertUtils.isNotNull(policyPo)) {
            policyOnlineResponse.setPolicyId(policyPo.getPolicyId());
            policyOnlineResponse.setPolicyNo(policyPo.getPolicyNo());
        }
        resultObject.setData(policyOnlineResponse);
        return resultObject;
    }

    @Override
    public ResultObject<PolicyOnlineResponse> getPolicyExist(PolicyOnlineRequest policyOnlineRequest) {
        ResultObject<PolicyOnlineResponse> resultObject = new ResultObject<>();
        PolicyOnlineResponse policyOnlineResponse = new PolicyOnlineResponse();

        if (AssertUtils.isNotEmpty(policyOnlineRequest.getFamilyName()) && AssertUtils.isNotEmpty(policyOnlineRequest.getGivenName())) {
            policyOnlineRequest.setApplicantName(policyOnlineRequest.getFamilyName() + policyOnlineRequest.getGivenName());
        }

        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), policyOnlineRequest.getPolicyNo(), PolicyErrorConfigEnum.POLICY_QUERY_POLICY_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), policyOnlineRequest.getApplicantName(), PolicyErrorConfigEnum.POLICY_IMPORT_PARAM_VALID_APPLICANTNAME_IS_NOT_NULL);
        //AssertUtils.isNotEmpty(this.getLogger(), policyOnlineRequest.getApplicantBirthdayFormat(), PolicyErrorConfigEnum.POLICY_IMPORT_PARAM_VALID_APPLICANTBIRTHDAY_IS_NOT_NULL);

        //查询保单是否存在
        PolicyPo policyPo = policyExtDao.loadPolicyPoByPolicyNo(policyOnlineRequest.getPolicyNo());
        this.getLogger().info("policyPo:" + JSON.toJSONString(policyPo));
        if (AssertUtils.isNotNull(policyPo)) {
            //所有产品都可以申请理赔 除旅游
            boolean flag = PolicyTermEnum.POLICY_TYPE.STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name().equals(policyPo.getPolicyType());
            if (flag) {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRODUCT_TYPE_IS_NOT_RIGHT);
            }
            //理赔重复提交限制
            ResultObject<ReportCustomerResponse> reportCustomerResponseResultObject = claimReportApi.queryReportDetailNew(policyPo.getPolicyNo());
            if (!AssertUtils.isResultObjectDataNull(reportCustomerResponseResultObject)) {
                throw new RequestException(PolicyErrorConfigEnum.CLAIM_BUSINESS_HAVE_EXIST);
            }

            policyOnlineResponse.setPolicyId(policyPo.getPolicyId());
            policyOnlineResponse.setPolicyNo(policyPo.getPolicyNo());

            //查询保单投保人信息
            PolicyApplicantBo policyApplicantBo = policyExtDao.loadPolicyApplicantByPolicyId(policyPo.getPolicyId());
            if (AssertUtils.isNotNull(policyApplicantBo)) {
                String name = "";
                String customerId = "";
                // 个险取投保人姓名名称 默认都是大写，且去除空格
                if (PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(policyPo.getPolicyType())) {
                    name = policyApplicantBo.getName().replaceAll("\\s+", "").toUpperCase();
                    policyOnlineResponse.setBirthday(policyApplicantBo.getBirthday());
                    customerId = policyApplicantBo.getCustomerId();
                }
                // 团险获取代理人姓名 默认都是大写，且去除空格
                if (PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name().equals(policyPo.getPolicyType())) {
                    name = policyApplicantBo.getDelegateName().replaceAll("\\s+", "").toUpperCase();
                    policyOnlineResponse.setBirthday(policyApplicantBo.getDelegateBirthday());
                    customerId = policyApplicantBo.getDelegateCustomerId();
                }

                //Long applicantBirthday = DateUtils.stringToTime(policyOnlineRequest.getApplicantBirthdayFormat(), DateUtils.FORMATE18);
                String applicantName = policyOnlineRequest.getApplicantName().replaceAll("\\s+", "").toUpperCase();
                if (!name.equals(applicantName) && PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(policyPo.getPolicyType())) {
                    throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_APPLICANT_INFO_IS_NOT_RIGHT);
                }
                // 团险返回信息
                if (!name.equals(applicantName) && PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name().equals(policyPo.getPolicyType())) {
                   resultObject.setError(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_GROUP_APPLICANT_INFO_IS_NOT_RIGHT.getCode());
                   resultObject.setErrorCode(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_GROUP_APPLICANT_INFO_IS_NOT_RIGHT.name());
                   resultObject.setMessage("");
                   return resultObject;
                }
                //调用party服务查询客户信息
                ResultObject<CustomerMessageResponse> customerMessageResponseResultObject = customerManageApi.queryRealCustomerByCustomerAgentId(customerId);
                if (!AssertUtils.isResultObjectDataNull(customerMessageResponseResultObject)) {
                    policyOnlineResponse.setCustomerNo(customerMessageResponseResultObject.getData().getCustomerNo());
                    policyOnlineResponse.setCustomerId(customerMessageResponseResultObject.getData().getCustomerId());
                    policyOnlineResponse.setIdType(customerMessageResponseResultObject.getData().getIdType());
                    policyOnlineResponse.setIdNo(customerMessageResponseResultObject.getData().getIdNo());
                } else {
                    throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_APPLICANT_INFO_IS_NOT_RIGHT);
                }
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_APPLICANT_INFO_IS_NOT_RIGHT);
            }

            resultObject.setData(policyOnlineResponse);
        } else {
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_APPLICANT_INFO_IS_NOT_RIGHT);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<PolicyQueryListBo>> querySystemWarningPolicyList(String applyId) {
        ResultObject<List<PolicyQueryListBo>> resultObject = new ResultObject<>();
        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), applyId, PolicyErrorConfigEnum.POLICY_QUERY_APPLY_ID_IS_NOT_NULL);

        List<PolicyQueryListBo> systemWarningPolicyList = policyExtDao.getSystemWarningPolicyList(applyId);
        //this.getLogger().info("系统预警查询已承保的保单列表:::" + JSON.toJSONString(systemWarningPolicyList));
        resultObject.setData(systemWarningPolicyList);
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject updateClientMobile(ClientMobileUpdateRequest clientMobileUpdateRequest) {
        ResultObject<Object> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), clientMobileUpdateRequest.getMobile(), PolicyErrorConfigEnum.POLICY_CUSTOMER_MOBILE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), clientMobileUpdateRequest.getCustomerIds(), PolicyErrorConfigEnum.POLICY_QUERY_POLICY_CUSTOMER_ID_IS_NOT_NULL);

            String mobile = clientMobileUpdateRequest.getMobile();
            List<String> customerIds = clientMobileUpdateRequest.getCustomerIds();
            List<PolicyApplicantPo> policyApplicantPos = policyExtDao.loadPolicyApplicantByCustomerIds(customerIds);

            policyApplicantPos.forEach(policyApplicantPo -> {
                policyApplicantPo.setMobile(mobile);

                //保存保单投保人信息，修改手机号
                policyBaseService.savePolicyApplicant(policyApplicantPo);

                String policyId = policyApplicantPo.getPolicyId();

                if (AssertUtils.isNotEmpty(policyId)) {
                    List<PolicyInsuredBo> policyInsuredBos = policyExtDao.loadPolicyInsuredListByPolicyId(policyId);
                    if (AssertUtils.isNotEmpty(policyInsuredBos)) {
                        policyInsuredBos.forEach(policyInsuredBo -> {
                            if (AssertUtils.isNotNull(policyInsuredBo.getRelationship()) &&
                                    PolicyTermEnum.RELATIONSHIP_WITH_THE_INSURED.ONESELF.name().equals(policyInsuredBo.getRelationship())) {
                                policyInsuredBo.setMobile(mobile);
                                policyBoService.savePolicyInsuredPo(policyInsuredBo);
                            }
                        });
                    }
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_POLICY_APPLICANT_SAVE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyDetailMonthResponse> queryPolicyDetailByMonth(Users users,String agentId, String approveDate) {
        ResultObject<PolicyDetailMonthResponse> resultObject = new ResultObject<>();
        PolicyDetailMonthResponse policyDetailMonthResponse = new PolicyDetailMonthResponse();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), agentId, PolicyErrorConfigEnum.POLICY_QUERY_AGENT_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), approveDate, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_BIZ_DATE_IS_NOT_NULL);


            List<PolicyPremiumPo> policyPremiumPos = policyExtDao.loadPolicyPremiumByAgentId(users, agentId, approveDate);

            if (AssertUtils.isNotEmpty(policyPremiumPos)) {
                int size = policyPremiumPos.size();
                policyDetailMonthResponse.setPolicyNum(size+"");
                policyDetailMonthResponse.setPolicyTotalPremium(new BigDecimal(policyPremiumPos.stream().mapToDouble(value -> value.getActualPremium().doubleValue()).sum()).setScale(2, BigDecimal.ROUND_HALF_UP));
            }else {
                policyDetailMonthResponse.setPolicyNum(0+"");
                policyDetailMonthResponse.setPolicyTotalPremium(new BigDecimal("0.00"));
            }

            resultObject.setData(policyDetailMonthResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_QUERY_POLICY_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 获取存在客户保单列表
     *
     * @param allCustomerIds 客户ID
     * @return PolicyRealClientListResponses
     */
    @Override
    public ResultObject<List<PolicyRealClientListResponse>> getPolicyRealClient(List<String> allCustomerIds) {
        ResultObject<List<PolicyRealClientListResponse>> resultObject = new ResultObject<>();
        resultObject.setData(policyExtDao.getPolicyRealClient(allCustomerIds));
        return resultObject;
    }

    @Override
    public ResultObject<List<PolicyApplicantListResponse>> policyApplicantList(List<String> policyNos) {
        ResultObject<List<PolicyApplicantListResponse>> resultObject = new ResultObject<>();
        List<PolicyApplicantListBo> policyApplicantBos = policyApplicantBaseService.policyApplicantList(policyNos);
        if (AssertUtils.isNotEmpty(policyApplicantBos)) {
            List<PolicyApplicantListResponse> ApplyApplicantListResponses = (List<PolicyApplicantListResponse>) this.converterList(
                    policyApplicantBos, new TypeToken<List<PolicyApplicantListResponse>>() {
                    }.getType()
            );
            resultObject.setData(ApplyApplicantListResponses);
        }
        return resultObject;
    }
}
