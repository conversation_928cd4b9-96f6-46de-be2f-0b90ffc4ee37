package com.gclife.policy.validate.transfer;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.certify.api.CertifyApplyApi;
import com.gclife.certify.model.response.CertifyNumberResponse;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.dao.PolicyBaseDao;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.request.RenewalInsuranceCoverageRequest;
import com.gclife.policy.model.request.RenewalInsuranceRequest;
import com.gclife.product.api.ProductRateApi;
import com.gclife.product.model.request.insurance.policy.PolicyPaymentRequest;
import com.gclife.product.model.response.insurnce.policy.PolicyPaymentResponse;
import com.gclife.renewal.model.request.RenewalCoveragePremiumRequest;
import com.gclife.renewal.model.request.RenewalCoverageRequest;
import com.gclife.renewal.model.request.RenewalPremiumRequest;
import com.gclife.renewal.model.request.RenewalRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version v2.0
 * Description: 续保数据转换
 * @date 18-11-30
 */
@Component
public class RenewalInsuranceTransfer extends BaseBusinessServiceImpl {
    @Autowired
    private RenewalDataTransfer renewalDataTransfer;
    @Autowired
    private ProductRateApi productRateApi;
    @Autowired
    private CertifyApplyApi certifyApplyApi;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PolicyBaseDao policyBaseDao;

    public void transPolicyData(PolicyBo policyBo, RenewalInsuranceRequest renewalInsuranceRequest) {
        Long effectiveDate = renewalInsuranceRequest.getReceivableDate();
        Long maturityDate = DateUtils.addStringYearsRT(effectiveDate - 1, 1);
        //String policyNo = policyBo.getFirstPolicyNo() + getPeriodString(policyBo.getPolicyPeriod());
        policyBo.setPolicyId(null);
        policyBo.setPolicyNo(renewalInsuranceRequest.getRenewalPolicyNo());
        //policyBo.setPolicyNo(policyNo);
        policyBo.setApplyId(renewalInsuranceRequest.getRenewalId());
        policyBo.setPolicyPeriod(policyBo.getPolicyPeriod() + 1);
        policyBo.setPolicyStatus(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name());
        policyBo.setApplyDate(renewalInsuranceRequest.getApplyDate());
        policyBo.setApproveDate(effectiveDate);
        policyBo.setEffectiveDate(effectiveDate);
        policyBo.setDataEffectiveDate(effectiveDate);
        policyBo.setBizDate(effectiveDate);
        policyBo.setHesitation(0L);
        policyBo.setHesitationEndDate(effectiveDate);
        policyBo.setMaturityDate(maturityDate);
        policyBo.setRiskCommencementDate(effectiveDate);
    }

    /**
     * 获取保单期数String
     * @param policyPeriod
     * @return
     */
    private String getPeriodString(Long policyPeriod) {
        if (!AssertUtils.isNotNull(policyPeriod)) {
            return "(01)";
        }
        if (policyPeriod < 10) {
            return "(0" + policyPeriod + ")";
        }
        return "(" + policyPeriod + ")";
    }

    public PolicyCoveragePo transPolicyCoverage(PolicyBo policyBo, RenewalInsuranceCoverageRequest coverageRequest, boolean isEffective, String insuredId) {
        PolicyCoveragePo coveragePo = (PolicyCoveragePo) this.converterObject(coverageRequest, PolicyCoveragePo.class);
        coveragePo.setInsuredId(insuredId);
        coveragePo.setPolicyId(policyBo.getPolicyId());
        coveragePo.setPolicyNo(policyBo.getPolicyNo());
        coveragePo.setEffectiveDate(policyBo.getEffectiveDate());
        coveragePo.setCoveragePeriodStartDate(policyBo.getEffectiveDate());
        coveragePo.setCoveragePeriodEndDate(policyBo.getMaturityDate());
        coveragePo.setMaturityDate(policyBo.getMaturityDate());
        coveragePo.setCoverageStatus(PolicyTermEnum.COVERAGE_STATUS.EFFECTIVE.name());
        if (!isEffective) {
            coveragePo.setCoverageStatus(PolicyTermEnum.COVERAGE_STATUS.PENDING_EFFECT.name());
        }
        coveragePo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
        coveragePo.setForceSave(true);
        return coveragePo;
    }

    public PolicyCoveragePremiumPo transCoveragePremium(PolicyPo policyPo, PolicyCoveragePo coveragePo) {
        PolicyCoveragePremiumPo coveragePremiumPo = new PolicyCoveragePremiumPo();
        ClazzUtils.copyPropertiesIgnoreNull(policyPo, coveragePremiumPo);
        ClazzUtils.copyPropertiesIgnoreNull(coveragePo, coveragePremiumPo);
        coveragePremiumPo.setPolicyCoveragePremiumId(UUIDUtils.getUUIDShort());
        coveragePremiumPo.setCoverageId(coveragePo.getCoverageId());
        coveragePremiumPo.setPolicyId(coveragePo.getPolicyId());
        coveragePremiumPo.setPeriodOriginalPremium(coveragePo.getOriginalPremium());
        coveragePremiumPo.setPeriodTotalPremium(coveragePo.getTotalPremium());
        coveragePremiumPo.setActualPremium(coveragePo.getActualPremium());
        coveragePremiumPo.setTotalActualPremium(coveragePo.getActualPremium());
        coveragePremiumPo.setRefundAmount(BigDecimal.ZERO);
        coveragePremiumPo.setForceSave(true);
        return coveragePremiumPo;
    }

    public PolicyCoveragePaymentBo transCoveragePayment(PolicyCoveragePo policyCoveragePo, PolicyCoveragePremiumPo coveragePremiumPo, String insuredId) {
        PolicyCoveragePaymentBo coveragePaymentBo = new PolicyCoveragePaymentBo();
        ClazzUtils.copyPropertiesIgnoreNull(policyCoveragePo, coveragePaymentBo);
        ClazzUtils.copyPropertiesIgnoreNull(coveragePremiumPo, coveragePaymentBo);
        coveragePaymentBo.setPolicyCoveragePaymentId(null);
        coveragePaymentBo.setInsuredId(insuredId);
        coveragePaymentBo.setPeriodActualPremium(coveragePremiumPo.getPeriodTotalPremium());
        coveragePaymentBo.setTotalPremium(coveragePremiumPo.getPeriodTotalPremium());
        coveragePaymentBo.setFrequency(1L);
        return coveragePaymentBo;
    }

    public void transBeneficiaryData(List<PolicyBeneficiaryInfoPo> beneficiaryInfoPos, List<PolicyBeneficiaryPo> beneficiaryPos, PolicyInsuredBo insuredBo) {
        if (AssertUtils.isNotEmpty(insuredBo.getListPolicyBeneficiary())) {
            insuredBo.getListPolicyBeneficiary().forEach(beneficiaryInfoBo -> {
                beneficiaryInfoBo.setPolicyBeneficiaryId(null);
                beneficiaryInfoBo.setInsuredId(insuredBo.getInsuredId());
                beneficiaryInfoBo.setBeneficiaryId(UUIDUtils.getUUIDShort());
                beneficiaryInfoPos.add(beneficiaryInfoBo);
                beneficiaryInfoBo.getPolicyBeneficiary().setBeneficiaryId(beneficiaryInfoBo.getBeneficiaryId());
                beneficiaryInfoBo.getPolicyBeneficiary().setPolicyId(insuredBo.getPolicyId());
                beneficiaryInfoBo.getPolicyBeneficiary().setForceSave(true);
                beneficiaryPos.add(beneficiaryInfoBo.getPolicyBeneficiary());
            });
        }
    }

    /**
     * 设置保单保费数据
     *
     * @param policyBo
     * @param policyCoveragePos
     */
    public void transPolicyPremiumData(PolicyBo policyBo, List<PolicyCoveragePo> policyCoveragePos) {
        BigDecimal originalPremium = policyCoveragePos.stream().map(PolicyCoveragePo::getOriginalPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalPremium = policyCoveragePos.stream().map(PolicyCoveragePo::getTotalPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actualPremium = policyCoveragePos.stream().map(PolicyCoveragePo::getActualPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 保单保费
        policyBo.getPolicyPremium().setPolicyPremiumId(null);
        policyBo.getPolicyPremium().setPolicyId(policyBo.getPolicyId());
        policyBo.getPolicyPremium().setPolicyNo(policyBo.getPolicyNo());
        policyBo.getPolicyPremium().setPeriodOriginalPremium(originalPremium);
        policyBo.getPolicyPremium().setPeriodTotalPremium(totalPremium);
        policyBo.getPolicyPremium().setActualPremium(actualPremium);
        policyBo.getPolicyPremium().setTotalActualPremium(actualPremium);
        policyBo.getPolicyPremium().setTotalRefundAmount(BigDecimal.ZERO);
        policyBo.getPolicyPremium().setTotalDeductPremium(BigDecimal.ZERO);
        policyBo.getPolicyPremium().setTotalDeductRefundAmount(BigDecimal.ZERO);
    }

    /**
     * 组装保单缴费数据
     *
     * @param renewalInsuranceRequest
     * @param policyBo
     * @param policyCoveragePos
     * @return
     */
    public PolicyPaymentBo tranPolicyPaymentData(RenewalInsuranceRequest renewalInsuranceRequest, PolicyBo policyBo, List<PolicyCoveragePo> policyCoveragePos) {
        Long receivableDate = renewalInsuranceRequest.getReceivableDate();
        PolicyPaymentBo policyPaymentBo = new PolicyPaymentBo();
        ClazzUtils.copyPropertiesIgnoreNull(policyBo, policyPaymentBo);
        policyCoveragePos.stream()
                .filter(coveragePo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coveragePo.getPrimaryFlag()))
                .findFirst().ifPresent(coveragePo -> ClazzUtils.copyPropertiesIgnoreNull(coveragePo, policyPaymentBo));
        ClazzUtils.copyPropertiesIgnoreNull(policyBo.getPolicyPremium(), policyPaymentBo);
        policyPaymentBo.setPolicyPaymentId(UUIDUtils.getUUIDShort());
        policyPaymentBo.setPolicyPremiumId(policyBo.getPolicyPremium().getPolicyPremiumId());
        policyPaymentBo.setPaymentStatusCode(renewalInsuranceRequest.getPaymentStatus());
        policyPaymentBo.setReceivableDate(receivableDate);
        policyPaymentBo.setTotalPremium(policyBo.getPolicyPremium().getPeriodTotalPremium());
        policyPaymentBo.setValuePremium(policyPaymentBo.getTotalPremium());
        policyPaymentBo.setGainedDate(renewalInsuranceRequest.getGainedDate());
        if (AssertUtils.isNotNull(receivableDate) &&
                AssertUtils.isNotNull(renewalInsuranceRequest.getGainedDate()) &&
                renewalInsuranceRequest.getGainedDate() > receivableDate) {
            // 到账日期大于应收日期，业绩日期取到账日期
            policyPaymentBo.setBizDate(renewalInsuranceRequest.getGainedDate());
        } else {
            policyPaymentBo.setBizDate(receivableDate);
        }
        policyPaymentBo.setBizYearMonth(DateUtils.getTimeYearMonth(policyPaymentBo.getBizDate()));
        policyPaymentBo.setBizBranchId(policyBo.getSalesBranchId());
        policyPaymentBo.setPaymentModeCode(renewalInsuranceRequest.getPaymentModeCode());
        policyPaymentBo.setFrequency(1L);
        policyPaymentBo.setPolicyYear(1L);
        policyPaymentBo.setBusinessId(renewalInsuranceRequest.getRenewalId());
        policyPaymentBo.setPaymentBusinessType(PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_RENEWAL_INSURANCE.name());
        // 设置佣金生成标志
        policyPaymentBo.setCommissionGenerateFlag(PolicyTermEnum.COMMISSION_GENERATE_FLAG.GENERATED.name());
        return policyPaymentBo;
    }

    /**
     * 计算佣金
     * @param policyBo
     * @param policyPaymentBo
     */
    public void transferPolicyPaymentRateData(PolicyBo policyBo, PolicyPaymentBo policyPaymentBo) {
        PolicyPaymentRequest policyPaymentRequest = (PolicyPaymentRequest) this.converterObject(policyPaymentBo, PolicyPaymentRequest.class);
        policyPaymentRequest.setApplyDate(policyPaymentRequest.getReceivableDate());
        //佣金机构只和出单时的机构有关 6.8.6->对于这个销售执行渠道，销售人员将来可能会变更渠道，因此佣金率将按照最新的渠道
        if (AssertUtils.isNotEmpty(policyBo.getFirstSalesBranchId()) && !PolicyTermEnum.CHANNEL_TYPE.GMSE.name().equals(policyBo.getChannelTypeCode())) {
            policyPaymentRequest.setBizBranchId(policyBo.getFirstSalesBranchId());
        }
        this.getLogger().info("计算费率请求数据:{}", JSON.toJSONString(policyPaymentRequest));
        ResultObject<PolicyPaymentResponse> reqFcResultObject = productRateApi.rateCalculationRate(policyPaymentRequest);
        this.getLogger().info("计算费率返回数据:{}", JSON.toJSONString(reqFcResultObject));
        PolicyPaymentResponse policyPaymentResponse = reqFcResultObject.getData();
        if (AssertUtils.isNotNull(policyPaymentResponse)) {
            // 数据匹配
            policyPaymentBo.setValuePremium(policyPaymentResponse.getValuePremium());
            policyPaymentBo.setCommissionFee(policyPaymentResponse.getCommissionFee());
            policyPaymentBo.setServiceChargeFee(policyPaymentResponse.getServiceChargeFee());
            // 险种缴费
            policyPaymentBo.getListPolicyCoveragePayment().forEach(coveragePaymentBo -> {
                policyPaymentResponse.getListPolicyCoveragePayment().stream()
                        .filter(coveragePaymentRequest -> coveragePaymentBo.getCoverageId().equals(coveragePaymentRequest.getCoverageId()))
                        .findFirst().ifPresent(coveragePaymentRequest -> {
                    ClazzUtils.copyPropertiesIgnoreNull(coveragePaymentRequest, coveragePaymentBo);
                });
            });
        }
    }

    /**
     * 转换续保数据
     *
     * @param policyBo       保单数据
     * @param receivableDate 应收时间
     * @return RenewalReqFc
     */
    public RenewalRequest transferRenewalInsuranceData(PolicyBo policyBo, Long receivableDate) {
        RenewalRequest renewalReqFc = new RenewalRequest();
        // 转换续保基础信息
        transferRenewalData(policyBo, renewalReqFc, receivableDate);
        // 转换续保险种
        transferRenewalCoverage(policyBo, renewalReqFc);
        // 转换续保保费
        transferRenewalPremiumData(policyBo, renewalReqFc);
        // 续保险种缴费信息
        transferRenewalCoveragePremiumData(policyBo, renewalReqFc);

        return renewalReqFc;
    }

    /**
     * 转换续保基础信息
     *
     * @param policyBo       保单数据
     * @param renewalReqFc   续保数据
     * @param receivableDate 应收时间
     */
    private void transferRenewalData(PolicyBo policyBo, RenewalRequest renewalReqFc, Long receivableDate) {
        Long approveDate = null;
        String approveDate1 = null;
        renewalReqFc.setPolicyId(policyBo.getPolicyId());
        renewalReqFc.setPolicyNo(policyBo.getPolicyNo());
        renewalReqFc.setRenewalDate(receivableDate);
        renewalReqFc.setRenewalYearMonth(DateUtils.getTimeYearMonth(receivableDate));
        renewalReqFc.setRenewalStatus(PolicyTermEnum.RENEWAL_STATUS.APPLYING.name());
        renewalReqFc.setRenewalType(PolicyTermEnum.RENEWAL_TYPE.RENEWAL_INSURANCE.name());
        renewalReqFc.setAgentId(policyBo.getPolicyAgent().getAgentId());
        renewalReqFc.setAgentCode(policyBo.getPolicyAgent().getAgentCode());
        renewalReqFc.setBranchId(policyBo.getSalesBranchId());
        renewalReqFc.setFirstPolicyNo(policyBo.getFirstPolicyNo());
        ResultObject<AgentResponse> agentResponseResultObject = agentApi.agentByIdGet(policyBo.getPolicyAgent().getAgentId());
        AssertUtils.isResultObjectDataNull(this.getLogger(), agentResponseResultObject, PolicyErrorConfigEnum.POLICY_AGENT_IS_NOT_FOUND_OBJECT);
        AgentResponse agentResponse = agentResponseResultObject.getData();
        String channelType = agentResponse.getChannelTypeCode();
        //查询最初承保保单的承保日期
        if (AssertUtils.isNotNull(policyBo.getFirstPolicyNo())) {
            PolicyPo policyByPolicyNo = policyBaseDao.getPolicyByPolicyNo(policyBo.getFirstPolicyNo());
            if (AssertUtils.isNotNull(policyByPolicyNo)) {
                approveDate = policyByPolicyNo.getApproveDate();
            }
        }
        Long policyPeriod = policyBo.getPolicyPeriod();
        int policyPeriod1 = policyPeriod.intValue();
        int policyPeriod2 = policyPeriod1 + 1;
        if (AssertUtils.isNotNull(approveDate)) {
            approveDate1 = approveDate.toString();
        }

        // 查询机构信息
        ResultObject<BranchResponse> branchResultObject = platformBranchBaseApi.queryOneBranchById(agentResponse.getBranchId());
        AssertUtils.isResultObjectError(getLogger(), branchResultObject);
        BranchResponse branchResponse = branchResultObject.getData();
        ResultObject<CertifyNumberResponse> certifyNumberRespFcResultObject = certifyApplyApi.certifyNumberGetNew(channelType,
                PolicyTermEnum.BUSINESS_TYPE.POLICY.name(),
                policyBo.getPolicyApplicant().getApplicantType(),
                AssertUtils.isNotNull(branchResponse.getBranchBank()) ? branchResponse.getBranchBank().getBankAbbreviation() : null,
                branchResponse.getBranchId(), policyBo.getPolicyNo(), policyPeriod2, approveDate1);
        AssertUtils.isResultObjectDataNull(getLogger(), certifyNumberRespFcResultObject, PolicyErrorConfigEnum.POLICY_QUERY_CERTIFY_INFO_IS_ERROR);
        //String renewalPolicyNo = policyBo.getFirstPolicyNo() + getPeriodString(policyBo.getPolicyPeriod());
        renewalReqFc.setRenewalPolicyNo(certifyNumberRespFcResultObject.getData().getCertifyNumber());
    }

    /**
     * 转换续保险种
     *
     * @param policyBo     保单数据
     * @param renewalReqFc 续保数据
     */
    private void transferRenewalCoverage(PolicyBo policyBo, RenewalRequest renewalReqFc) {
        List<RenewalCoverageRequest> renewalCoverageReqFcs = new ArrayList<>();
        policyBo.getListInsuredCoverage().forEach(policyCoverageBo -> {
            RenewalCoverageRequest renewalCoverageReqFc = new RenewalCoverageRequest();
            ClazzUtils.copyPropertiesIgnoreNull(policyCoverageBo, renewalCoverageReqFc);
            if (AssertUtils.isNotEmpty(policyCoverageBo.getTotalAmount())) {
                renewalCoverageReqFc.setTotalAmount(new BigDecimal(policyCoverageBo.getTotalAmount()));
            }
            // 3.9.0个险续保按新单调整
            renewalCoverageReqFc.setFrequency(policyBo.getPolicyPeriod());
            renewalCoverageReqFcs.add(renewalCoverageReqFc);
        });
        renewalReqFc.setListCoverage(renewalCoverageReqFcs);
    }

    /**
     * 转换续保保费
     *
     * @param policyBo     保单数据
     * @param renewalReqFc 续保数据
     */
    private void transferRenewalPremiumData(PolicyBo policyBo, RenewalRequest renewalReqFc) {
        RenewalPremiumRequest premiumRequest = new RenewalPremiumRequest();
        // 3.9.0个险续保按新单调整
        premiumRequest.setPolicyYear(policyBo.getPolicyPeriod() + 1 + "");
        premiumRequest.setPolicyPeriod(policyBo.getPolicyPeriod() + "");
        premiumRequest.setCurrencyCode(policyBo.getPolicyPremium().getCurrencyCode());
        premiumRequest.setAddPremium(BigDecimal.ZERO);
        premiumRequest.setDiscountPremium(BigDecimal.ZERO);
        premiumRequest.setOriginalPremium(policyBo.getListInsuredCoverage().stream().map(PolicyCoverageBo::getOriginalPremium).reduce(BigDecimal.ZERO, BigDecimal::add));
        premiumRequest.setTotalPremium(policyBo.getListInsuredCoverage().stream().map(PolicyCoverageBo::getTotalPremium).reduce(BigDecimal.ZERO, BigDecimal::add));
        premiumRequest.setReceivablePremium(premiumRequest.getTotalPremium());

        renewalReqFc.setPremium(premiumRequest);
    }

    /**
     * 续保险种缴费信息
     *
     * @param policyBo       保单数据
     * @param renewalRequest 续保数据
     */
    private void transferRenewalCoveragePremiumData(PolicyBo policyBo, RenewalRequest renewalRequest) {
        List<RenewalCoveragePremiumRequest> coveragePremiums = new ArrayList<>();
        List<PolicyCoverageBo> listInsuredCoverage = policyBo.getListInsuredCoverage();
        listInsuredCoverage.forEach(policyCoverageBo -> {
            RenewalCoveragePremiumRequest renewalCoveragePremiumRequest = new RenewalCoveragePremiumRequest();
            ClazzUtils.copyPropertiesIgnoreNull(policyCoverageBo, renewalCoveragePremiumRequest);
            // 3.9.0个险续保按新单调整
            renewalCoveragePremiumRequest.setFrequency(policyBo.getPolicyPeriod());
            // 实缴置为0
            renewalCoveragePremiumRequest.setActualPremium(BigDecimal.ZERO);

            coveragePremiums.add(renewalCoveragePremiumRequest);
        });
        renewalRequest.setListCoveragePremium(coveragePremiums);
    }
}
