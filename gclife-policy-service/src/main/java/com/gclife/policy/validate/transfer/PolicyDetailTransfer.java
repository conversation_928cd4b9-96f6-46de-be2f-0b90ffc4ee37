package com.gclife.policy.validate.transfer;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.model.respone.ApplyLoanBeneficiaryResponse;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.claim.api.ClaimApi;
import com.gclife.claim.model.respone.ClaimPreviousListResponse;
import com.gclife.common.InternationalTypeEnum;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.TerminologyTypeEnum;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformAreaApi;
import com.gclife.platform.api.PlatformCareerApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.api.PlatformTerminologyBaseApi;
import com.gclife.platform.model.request.SyscodeRequest;
import com.gclife.platform.model.response.AreaNameResponse;
import com.gclife.platform.model.response.CareerNameResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.core.jooq.tables.pojos.PolicyServiceAgentPo;
import com.gclife.policy.dao.PolicyQueryBaseDao;
import com.gclife.policy.interfaces.impl.AgentServiceInterfaceImpl;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.response.*;
import com.gclife.policy.service.ClazzBusinessService;
import com.gclife.policy.service.base.PolicyBaseService;
import com.gclife.policy.service.base.PolicyOtherInfoService;
import com.gclife.policy.service.base.PolicyPremiumBaseService;
import com.gclife.product.api.ProductApi;
import com.gclife.product.model.response.duty.DutyResponse;
import com.gclife.product.model.response.manager.ProductDetailedInfoResponse;
import com.gclife.product.model.response.paramter.ParameterFieldResponse;
import org.jooq.tools.StringUtils;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.policy.model.config.PolicyErrorConfigEnum.POLICY_QUERY_POLICY_PREMIUM_IS_NOT_FOUND;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_STATUS_FLAG.*;

/**
 * <AUTHOR>
 * create 17-11-14
 * description:　保单详情数据转换
 */
@Component
public class PolicyDetailTransfer extends BaseBusinessServiceImpl {

    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private PlatformTerminologyBaseApi platformTerminologyBaseApi;

    @Autowired
    private AgentServiceInterfaceImpl agentServiceInterface;

    @Autowired
    private PolicyBaseService policyBaseService;

    @Autowired
    private LanguageCodeTransData languageCodeTransData;

    @Autowired
    private ClazzBusinessService clazzBusinessService;
    @Autowired
    private RenewalDataTransfer renewalDataTransfer;
    @Autowired
    private PlatformCareerApi platformCareerApi;
    @Autowired
    private PlatformAreaApi platformAreaApi;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private ClaimApi claimApi;
    @Autowired
    private PolicyOtherInfoService policyOtherInfoService;
    @Autowired
    private PolicyPremiumBaseService policyPremiumBaseService;
    @Autowired
    private PolicyTransData policyTransData;
    @Autowired
    private PolicyQueryBaseDao policyQueryBaseDao;

    @Autowired
    private PolicyReviewTransData policyReviewTransData;

    /**
     * 险种字段
     */
    private List<Field> listFieldCoverage = new ArrayList<>();
    /**
     * 责任字段
     */
    private List<Field> listFieldDuty = new ArrayList<>();

    private List<SyscodeResponse> listCoverageTitle = new ArrayList<>();


    /**
     * 转换保单详情数据
     *
     * @return
     */
    public PolicyDetailResponse transferPolicyDetail(Users users, PolicyBo policyBo) {
        //数据保单数据
        //术语查询
        transferPolicyInternational(users);
        //保单信息转换
        PolicyDetailResponse policyDetailResponse = transferPolicy(policyBo);
        //代理人信息
        transferPolicyAgent(policyDetailResponse, policyBo);
        //保单联系信息
        transferPolicyContact(policyDetailResponse, policyBo);
        //保单账号信息
        transferPolicyAccount(policyDetailResponse, policyBo);
        //投保人信息
        transferPolicyApplicant(policyDetailResponse, policyBo,users);
        //附件信息
        transferPolicyAttachment(policyDetailResponse, policyBo);
        //被保人信息
        transferPolicyInsured(users, policyDetailResponse, policyBo);
        //回执信息
        transferPolicyReceipt(policyDetailResponse, policyBo);
        //缴费信息
        transferPolicyPayment(policyDetailResponse, policyBo);
        //理赔信息
        transferClaimInfo(policyDetailResponse, policyBo);
        //贷款合同信息
        transPolicyLoanContract(policyDetailResponse, policyBo);
        //贷款受益人信息
        transPolicyLoanBeneficiary(policyDetailResponse, policyBo);
        //推荐信息
        transPolicyReferralInfo(policyDetailResponse, policyBo);
        //保单持有人信息
        transPolicyHolder(policyDetailResponse, policyBo);
        //如果是网销产品并且有折扣,才展示
        if (PolicyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(policyBo.getChannelTypeCode()) && AssertUtils.isNotNull(policyBo.getPolicyPremium().getSpecialDiscount())) {
            policyDetailResponse.setPromotionalCodeShowFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }
        return policyDetailResponse;
    }

    private void transPolicyHolder(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        PolicyHolderPo holder = policyBo.getHolder();
        if (AssertUtils.isNotNull(holder) && AssertUtils.isNotEmpty(holder.getPolicyHolderId())) {
            policyDetailResponse.setHolderFlag(TerminologyConfigEnum.WHETHER.YES.name());
            PolicyHolderResponse policyHolderResponse = new PolicyHolderResponse();
            ClazzUtils.copyPropertiesIgnoreNull(holder, policyHolderResponse);
            policyDetailResponse.setHolder(policyHolderResponse);
        }
    }

    private void transPolicyReferralInfo(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        PolicyReferralInfoPo referralInfo = policyBo.getReferralInfo();
        if (AssertUtils.isNotNull(referralInfo) && AssertUtils.isNotEmpty(referralInfo.getPolicyReferralInfoId())) {
            policyDetailResponse.setReferralInfoFlag(TerminologyConfigEnum.WHETHER.YES.name());
            PolicyReferralInfoResponse policyReferralInfoResponse = new PolicyReferralInfoResponse();
            ClazzUtils.copyPropertiesIgnoreNull(referralInfo, policyReferralInfoResponse);
            policyDetailResponse.setReferralInfo(policyReferralInfoResponse);
        }
    }

    private void transPolicyLoanBeneficiary(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        if (!TerminologyConfigEnum.WHETHER.YES.name().equals(policyDetailResponse.getContractLoanFlag())) {
            return;
        }
        //第一机构受益人信息
        ApplyLoanBeneficiaryResponse loanBeneficiary = new ApplyLoanBeneficiaryResponse();
        List<PolicyBeneficiaryInfoBo> applyBeneficiaryInfoBos = policyBaseService.queryPolicyLoanBeneficiary(policyBo.getPolicyId(), TerminologyConfigEnum.WHETHER.NO.name());
        loanBeneficiary.setBeneficiaryBranchId(applyBeneficiaryInfoBos.get(0).getPolicyBeneficiary().getBeneficiaryBranchId());
        loanBeneficiary.setBeneficiaryBranchCode(applyBeneficiaryInfoBos.get(0).getPolicyBeneficiary().getBeneficiaryBranchCode());
        loanBeneficiary.setBeneficiaryBranchName(applyBeneficiaryInfoBos.get(0).getPolicyBeneficiary().getBeneficiaryBranchName());
        loanBeneficiary.setBeneficiaryNoOrder(applyBeneficiaryInfoBos.get(0).getBeneficiaryNoOrder());
        loanBeneficiary.setBeneficiaryProportion(applyBeneficiaryInfoBos.get(0).getBeneficiaryProportion());
        policyDetailResponse.setLoanBeneficiary(loanBeneficiary);
    }

    private void transPolicyLoanContract(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        PolicyLoanPo loanContract = policyBo.getLoanContract();
        if (AssertUtils.isNotNull(loanContract) && AssertUtils.isNotEmpty(loanContract.getPolicyLoanId())) {
            policyDetailResponse.setContractLoanFlag(TerminologyConfigEnum.WHETHER.YES.name());
            PolicyLoanResponse policyLoanResponse = new PolicyLoanResponse();
            ClazzUtils.copyPropertiesIgnoreNull(loanContract, policyLoanResponse);
            policyLoanResponse.setExchangeRate(TerminologyConfigEnum.CURRENCY.USD.name().equals(loanContract.getCurrency()) ? "1" : loanContract.getExchangeRate().stripTrailingZeros().toString());
            policyDetailResponse.setLoanContract(policyLoanResponse);
        }
        Optional<PolicyCoverageBo> first = policyBo.getListInsuredCoverage().stream().filter(applyCoverageResponse ->
                PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageResponse.getPrimaryFlag())
        ).findFirst();
        if (first.isPresent()) {
            String productId = first.get().getProductId();
            ResultObject<ProductDetailedInfoResponse> queryOneProductDetail = productApi.queryOneProductDetail(productId);
            if (!AssertUtils.isResultObjectDataNull(queryOneProductDetail)) {
                ProductDetailedInfoResponse productInfoData = queryOneProductDetail.getData();
                policyDetailResponse.setLegalBeneficialFlag(productInfoData.getLegalBeneficialFlag());
            }
        }
    }

    private void transferClaimInfo(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        ResultObject<List<ClaimPreviousListResponse>> listResultObject = claimApi.queryClaimPreviousListByPolicyId(policyBo.getPolicyId());
        if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
            policyDetailResponse.setListClaimPrevious(listResultObject.getData());
        }
    }

    private void transferPolicyPayment(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        List<PolicyPaymentBo> policyPaymentBos = policyBaseService.listPolicyPayment(policyBo.getPolicyId());
        AssertUtils.isNotEmpty(this.getLogger(), policyPaymentBos, POLICY_QUERY_POLICY_PREMIUM_IS_NOT_FOUND);
        PolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
        List<PolicyPaymentResponse> listPaidPayment = new ArrayList<>();
        List<PolicyPaymentResponse> listArrearsPayment = new ArrayList<>();

        policyPaymentBos.forEach(policyPaymentBo -> {
            if (PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name().equals(policyPaymentBo.getPaymentStatusCode())
                    || PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_TIMEOUT.name().equals(policyPaymentBo.getPaymentStatusCode())
                    || PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT.name().equals(policyPaymentBo.getPaymentStatusCode())
                    || PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT_NOPASS.name().equals(policyPaymentBo.getPaymentStatusCode())
                    || PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT_PASS.name().equals(policyPaymentBo.getPaymentStatusCode())) {
                PolicyPaymentResponse policyPaymentResponse = this.transPolicyPayment(policyPaymentBo, policyBo);
                listArrearsPayment.add(policyPaymentResponse);
            } else if (PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS.name().equals(policyPaymentBo.getPaymentStatusCode()) || PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED.name().equals(policyPaymentBo.getPaymentStatusCode())) {
                PolicyPaymentResponse policyPaymentResponse = this.transPolicyPayment(policyPaymentBo, policyBo);
                listPaidPayment.add(policyPaymentResponse);
            }
        });
        // 保单年度
        Long policyYear = policyPaymentBos.get(0).getPolicyYear();
        if (policyYear == null) {
            policyYear = 0L; // 检查 policyYear 是否为空，如果为空赋值为 0
        }
        Optional<PolicyPaymentBo> maxOptional = policyPaymentBos.stream()
                .filter(policyPaymentBo -> PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(policyPaymentBo.getPaymentStatusCode())
                        && policyPaymentBo.getReceivableDate() < DateUtils.getCurrentTime()
                        && AssertUtils.isNotNull(policyPaymentBo.getPolicyYear())
                )
                .max(Comparator.comparingLong(PolicyPaymentPo::getPolicyYear));
        if (maxOptional.isPresent()) {
            policyYear = maxOptional.get().getPolicyYear();
        }
        // 已全部交清,检查保单是否已支付所有期数的保费，并在满足条件时更新 policyYear 为基于当前时间和保单生效日期计算的保单年度
        if (!PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_IEXPIRE.name().equals(policyBo.getPolicyStatus())
                && AssertUtils.isNotNull(policyPremium.getPaymentCompleteDate())
                && AssertUtils.isNotNull(policyPaymentBos.get(0).getReceivableDate())
                && AssertUtils.isNotNull(policyBo.getEffectiveDate())
                && DateUtils.timeStrToString(renewalDataTransfer.getLastReceivableDate(policyPremium, policyBo.getEffectiveDate()), DateUtils.FORMATE17)
                .equalsIgnoreCase(DateUtils.timeStrToString(policyPaymentBos.get(0).getReceivableDate(), DateUtils.FORMATE17))) {
            //已交清所有期数
            policyYear = renewalDataTransfer.calculatePolicyYear(DateUtils.getCurrentDateToTime(), policyBo.getEffectiveDate());
        }
        policyDetailResponse.setPolicyYear(policyYear);
        policyDetailResponse.setPaymentFrequency(policyPaymentBos.size());
        policyDetailResponse.setListPaidPayment(listPaidPayment);
        policyDetailResponse.setListArrearsPayment(listArrearsPayment);
    }

    private PolicyPaymentResponse transPolicyPayment(PolicyPaymentBo policyPaymentBo, PolicyBo policyBo) {
        PolicyPaymentResponse policyPaymentResponse = new PolicyPaymentResponse();
        policyPaymentResponse.setBizDate(DateUtils.timeStrToString(policyPaymentBo.getBizDate(), DateUtils.FORMATE3));
        policyPaymentResponse.setBizYearMonth(policyPaymentBo.getBizYearMonth());
        policyPaymentResponse.setCurrencyCode(policyPaymentBo.getCurrencyCode());
//        policyPaymentResponse.setCurrencyName(languageCodeTransData.verifyCodeNameByKey(TerminologyTypeEnum.CURRENCY.name(), policyPaymentBo.getCurrencyCode()));
        policyPaymentResponse.setGainedDate(DateUtils.timeStrToString(policyPaymentBo.getGainedDate(), DateUtils.FORMATE3));
        policyPaymentResponse.setPaymentStatusCode(policyPaymentBo.getPaymentStatusCode());
//        policyPaymentResponse.setPaymentStatusName(languageCodeTransData.verifyCodeNameByKey(TerminologyTypeEnum.PAYMENT_STATUS.name(), policyPaymentBo.getPaymentStatusCode()));
        policyPaymentResponse.setPolicyId(policyPaymentBo.getPolicyId());
        policyPaymentResponse.setPolicyYear(policyPaymentBo.getPolicyYear().toString());
        policyPaymentResponse.setPremiumSource(policyPaymentBo.getPremiumSource());
        policyPaymentResponse.setReceivableDate(DateUtils.timeStrToString(policyPaymentBo.getReceivableDate(), DateUtils.FORMATE3));
        policyPaymentResponse.setTotalPremium(policyPaymentBo.getTotalPremium());
        policyPaymentResponse.setActualPremium(policyPaymentBo.getActualPremium());
        policyPaymentResponse.setReceivableYearMonth(DateUtils.dateStringFormatTransfer(DateUtils.getTimeYearMonth(policyPaymentBo.getReceivableDate()), DateUtils.FORMATE16, DateUtils.FORMATE2));

        //设置折扣信息
        BigDecimal originalReceivablePremium = policyPaymentBo.getTotalPremium();
        PolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
        Map<String, Boolean> discountPremiumFlag = policyTransData.isDiscountPremiumFlagSingle(policyPremium, policyPaymentBo, (List<PolicyCoveragePo>) this.converterList(policyBo.getListInsuredCoverage(), new TypeToken<List<PolicyCoveragePo>>() {
        }.getType()));
        boolean percentageFlag = discountPremiumFlag.get("percentage");
        if (percentageFlag) {
            policyPaymentResponse.setDiscountPremiumFlag(TerminologyConfigEnum.WHETHER.YES.name());
            policyPaymentResponse.setDiscountType(policyPremium.getDiscountType());
            policyPaymentResponse.setPromotionType(policyPremium.getPromotionType());
            policyPaymentResponse.setDiscountModel(policyPremium.getDiscountModel());
            originalReceivablePremium = originalReceivablePremium
                    .multiply(new BigDecimal("1").subtract(policyPremium.getSpecialDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
//            if (policyPaymentBo.getTotalPremium().subtract(originalReceivablePremium).abs().compareTo(new BigDecimal("0.05")) < 0) {
//                originalReceivablePremium = policyPaymentBo.getTotalPremium();
//            }
            policyPaymentResponse.setSpecialDiscount(new BigDecimal(new BigDecimal("100").multiply(policyPremium.getSpecialDiscount()).stripTrailingZeros().toPlainString()));
        }
        policyPaymentResponse.setOriginalReceivablePremium(originalReceivablePremium);
        // 设置加费信息
        this.transPaymentAddPremium(policyPaymentResponse, policyBo);
        return policyPaymentResponse;
    }

    private void transPaymentAddPremium(PolicyPaymentResponse policyPaymentResponse, PolicyBo policyBo) {

        List<PolicyCoverageBo> listInsuredCoverage = policyBo.getListInsuredCoverage();
        final BigDecimal[] totalConversionFactorAddPremium = {BigDecimal.ZERO};
        List<PolicyAddPremiumPo> policyAddPremiumPos = policyBo.getListPolicyAddPremium();
        if (AssertUtils.isNotEmpty(policyAddPremiumPos)) {
            listInsuredCoverage.forEach(policyCoverageBo -> {
                BigDecimal coverageAddPremium = policyAddPremiumPos.stream()
                        .filter(policyAddPremiumPo -> policyCoverageBo.getCoverageId().equals(policyAddPremiumPo.getCoverageId())
                                && AssertUtils.isNotNull(policyAddPremiumPo.getAddPremiumEndDate())
                                && AssertUtils.isNotNull(policyAddPremiumPo.getAddPremiumPeriod())
                                && DateUtils.getCurrentTime() <= policyAddPremiumPo.getAddPremiumEndDate() && policyAddPremiumPo.getAddPremiumStatus().equals(PolicyTermEnum.ADD_PREMIUM_STATUS.EFFECTIVE.name()))
                        .map(PolicyAddPremiumPo::getTotalAddPremium).reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal conversionFactor = BigDecimal.valueOf(PolicyTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(policyCoverageBo.getPremiumFrequency()).value());
                BigDecimal conversionFactorAddPremium = coverageAddPremium.multiply(conversionFactor).setScale(2, BigDecimal.ROUND_HALF_UP);
                totalConversionFactorAddPremium[0] = totalConversionFactorAddPremium[0].add(conversionFactorAddPremium);
            });
        }
        policyPaymentResponse.setOriginalAddPremium(totalConversionFactorAddPremium[0]);
        policyPaymentResponse.setOriginalStandardPremium(policyPaymentResponse.getTotalPremium().subtract(totalConversionFactorAddPremium[0]));
    }

    /**
     * 国际化术语
     */
    public void transferPolicyInternational(Users users) {
        //术语
        this.listCoverageTitle = platformInternationalBaseApi.queryInternational(InternationalTypeEnum.COVERAGE_TITLE.name(), users.getLanguage()).getData();
        //字段
        this.listFieldCoverage = ClazzUtils.getFiledList(PolicyCoveragePo.class);
        this.listFieldDuty = ClazzUtils.getFiledList(PolicyCoverageDutyPo.class);
    }

    private void transferPolicyReceipt(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        PolicyReceiptInfoResponse policyReceiptInfoResponse = (PolicyReceiptInfoResponse) this.converterObject(policyBo.getPolicyReceiptInfo(), PolicyReceiptInfoResponse.class);
        if (AssertUtils.isNotNull(policyReceiptInfoResponse)) {
            policyDetailResponse.setPolicyReceipt(policyReceiptInfoResponse);
        }
    }

    /**
     * 转换保单账号信息
     *
     * @param policyDetailResponse 　保单返回信息
     * @param policyBo             　保单对象
     */
    public void transferPolicyAccount(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        //数据保单数据
        List<PolicyAccountResponse> accountResponseList = (List<PolicyAccountResponse>) this.converterList(policyBo.getListPolicyAccount(), new TypeToken<List<PolicyAccountResponse>>() {
        }.getType());
        //地址拼接
        if (AssertUtils.isNotEmpty(accountResponseList)) {
            List<String> cities = accountResponseList.stream().filter(policyAccountResponse -> AssertUtils.isNotEmpty(policyAccountResponse.getCity()))
                    .map(PolicyAccountResponse::getCity).distinct().collect(Collectors.toList());
            ResultObject<List<AreaNameResponse>> listResultObject = platformAreaApi.areaNameGetBatch(cities);
            accountResponseList.forEach(account -> {
                //投保人地址拼接
                if (!AssertUtils.isResultObjectListDataNull(listResultObject) && AssertUtils.isNotEmpty(account.getCity())) {
                    listResultObject.getData().stream().filter(areaNameResponse -> areaNameResponse.getAreaId().equals(account.getCity())).findFirst()
                            .ifPresent(areaNameResponse -> account.setCity(areaNameResponse.getAreaName()));
                }
            });
            policyDetailResponse.setListPolicyAccount(accountResponseList);
        }

    }


    /**
     * 转换保单联系信息
     *
     * @param policyDetailResponse 　保单返回信息
     * @param policyBo             　保单对象
     */
    public void transferPolicyContact(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        //数据保单数据
        PolicyContactResponse policyContactResponse = (PolicyContactResponse) this.converterObject(policyBo.getPolicyContactInfo(), PolicyContactResponse.class);
        if (AssertUtils.isNotNull(policyContactResponse)) {
            //投保人地址拼接
            if (AssertUtils.isNotEmpty(policyContactResponse.getSendAddrAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(policyContactResponse.getSendAddrAreaCode());
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                    policyContactResponse.setContractAddress(respFcResultObject.getData().getAreaName() + " " + policyContactResponse.getContractAddress());
                }
            }
            policyDetailResponse.setPolicyContactInfo(policyContactResponse);
        }
    }


    /**
     * 转换保单数据
     *
     * @param policyBo 　保单对象
     */
    public PolicyDetailResponse transferPolicy(PolicyBo policyBo) {
        PolicyDetailResponse policyDetailResponse = (PolicyDetailResponse) converterObject(policyBo, PolicyDetailResponse.class);
        //设置网销优惠码信息
        policyDetailResponse.setPromotionalCode(policyBo.getPolicyPremium().getPromotionalCode());
        policyDetailResponse.setPromotionalPremium(policyBo.getPolicyPremium().getPromotionalPremium());
        if (AssertUtils.isNotNull(policyBo.getHesitation()) && policyBo.getHesitation() != 0) {
            policyDetailResponse.setHesitationEndDateFormat(DateUtils.timeStrToString(policyDetailResponse.getHesitationEndDate(), DateUtils.FORMATE3));
        } else {
            policyDetailResponse.setHesitationEndDate(null);
            policyDetailResponse.setHesitationEndDateFormat(null);
        }
        //增加保单年度，保单状态
        List<PolicyPaymentBo> listPolicyPayment = policyBo.getListPolicyPayment();
        if (AssertUtils.isNotEmpty(listPolicyPayment)) {
            Optional<PolicyPaymentBo> max = listPolicyPayment.stream().filter(policyPaymentBo -> AssertUtils.isNotNull(policyPaymentBo.getPolicyYear())).max(Comparator.comparingLong(PolicyPaymentPo::getPolicyYear));
            max.ifPresent(bo -> policyDetailResponse.setPolicyYear(bo.getPolicyYear()));
        }
        // 如果保单状态是
        if (POLICY_STATUS_HESITATION_REVOKE.name().equals(policyBo.getPolicyStatus())) {
            // 犹豫期撤单 展示犹豫期撤单日期
            policyDetailResponse.setStatusDate(policyBo.getThoroughInvalidDate());
        } else if (POLICY_STATUS_SURRENDER.name().equals(policyBo.getPolicyStatus())) {
            // 客户退保 展示退保日期
            policyDetailResponse.setStatusDate(policyBo.getThoroughInvalidDate());
        } else if (POLICY_STATUS_INVALID.name().equals(policyBo.getPolicyStatus())) {
            // 暂时失效 展示失效日期
            policyDetailResponse.setStatusDate(policyBo.getInvalidDate());
        } else if (POLICY_STATUS_INVALID_THOROUGH.name().equals(policyBo.getPolicyStatus())) {
            // 永久失效 展示失效日期
            policyDetailResponse.setStatusDate(policyBo.getThoroughInvalidDate());
        } else if (POLICY_STATUS_IEXPIRE.name().equals(policyBo.getPolicyStatus())) {
            // 满期 展示终止日期
            policyDetailResponse.setStatusDate(policyBo.getMaturityDate());
        }
        return policyDetailResponse;
    }

    /**
     * 转换投保人
     *
     * @param policyDetailResponse 　保单返回信息
     * @param policyBo             　保单对象
     */
    public void transferPolicyApplicant(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo,Users users) {
        //数据保单数据
        PolicyApplicantResponse policyApplicantResponse = (PolicyApplicantResponse) this.converterObject(policyBo.getPolicyApplicant(), PolicyApplicantResponse.class);

        //针对20A网销产品国籍国际化特殊处理
        policyApplicantResponse.setNationalityName(policyReviewTransData.getNationalityCodeTypeName(policyBo.getPolicyId(), policyApplicantResponse.getNationality(), users.getLanguage()));

        if (AssertUtils.isNotEmpty(policyApplicantResponse.getExpectedPremiumSources())) {
            policyApplicantResponse.setListExpectedPremiumSources(JSON.parseArray(policyApplicantResponse.getExpectedPremiumSources(), String.class));
            policyApplicantResponse.setExpectedPremiumSourcesName(this.transExpectedPremiumSourcesName(policyApplicantResponse.getListExpectedPremiumSources()));
        }
        PolicyOccupationNaturePo policyOccupationNaturePo = policyOtherInfoService.queryPolicyOccupationNaturePo(policyBo.getPolicyId(), PolicyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
        if (AssertUtils.isNotNull(policyOccupationNaturePo)) {
            PolicyOccupationNatureResponse policyOccupationNatureResponse = new PolicyOccupationNatureResponse();
            ClazzUtils.copyPropertiesIgnoreNull(policyOccupationNaturePo, policyOccupationNatureResponse);
            policyApplicantResponse.setOccupationNature(policyOccupationNatureResponse);
        }

        //咨询医生所在地区编码名称
        if (AssertUtils.isNotEmpty(policyApplicantResponse.getDoctorAreaCode())) {
            ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(policyApplicantResponse.getDoctorAreaCode());
            if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                policyApplicantResponse.setDoctorAreaCodeName(respFcResultObject.getData().getAreaName());
            }
        }

        if (AssertUtils.isNotEmpty(policyApplicantResponse.getCompanyAreaCode())) {
            ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(policyApplicantResponse.getCompanyAreaCode());
            if (!AssertUtils.isResultObjectDataNull(respFcResultObject)) {
                policyApplicantResponse.setCompanyAreaName(respFcResultObject.getData().getAreaName());
            }
        }

        //投保人地址拼接
        if (AssertUtils.isNotEmpty(policyApplicantResponse.getHomeAreaCode())) {
            ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(policyApplicantResponse.getHomeAreaCode());
            if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                policyApplicantResponse.setHomeAreaName(respFcResultObject.getData().getAreaName());
            }
        }

        //投保人职业
        if (AssertUtils.isNotEmpty(policyApplicantResponse.getOccupationCode())) {
            ResultObject<CareerNameResponse> respFcResultObject = platformCareerApi.careerNameGet(policyApplicantResponse.getOccupationCode());
            if (!AssertUtils.isResultObjectDataNull(respFcResultObject)) {
                policyApplicantResponse.setOccupationName(respFcResultObject.getData().getCareerName());
            }
        }

        //投保單複核展示bmi，有值直接展示，沒值根據身高體重算，在重新設置，反之，直接返回空
        if (!AssertUtils.isNotEmpty(policyApplicantResponse.getBmi())) {
            if (AssertUtils.isNotEmpty(policyApplicantResponse.getStature())
                    && AssertUtils.isNotEmpty(policyApplicantResponse.getAvoirdupois())) {
                BigDecimal powStature = new BigDecimal(policyApplicantResponse.getStature()).divide(new BigDecimal("100")).pow(2);
                BigDecimal bmi = new BigDecimal(policyApplicantResponse.getAvoirdupois()).divide(powStature, 2, BigDecimal.ROUND_HALF_UP);
                policyApplicantResponse.setBmi(bmi+"");
            }
        }

        //設置投保年齡
        Integer applyAge = getAgeYear(policyApplicantResponse.getBirthday(), policyBo.getApplyDate());
        policyApplicantResponse.setApplyAge(applyAge+"");

        policyDetailResponse.setPolicyApplicant(policyApplicantResponse);
    }

    private Integer getAgeYear(Long insuredBirthday, Long approveDate) {
        try {
            if (AssertUtils.isNotNull(insuredBirthday) && AssertUtils.isNotNull(approveDate)) {
                return DateUtils.getAgeYear(new Date(insuredBirthday), new Date(approveDate));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private String transExpectedPremiumSourcesName(List<String> listExpectedPremiumSources) {
        if (!AssertUtils.isNotEmpty(listExpectedPremiumSources)) {
            return null;
        }
        String expectedPremiumSourcesName = null;
        SyscodeRequest syscodeRequest = new SyscodeRequest();
        syscodeRequest.setCodeType(com.gclife.common.TerminologyTypeEnum.EXPECTED_PREMIUM_SOURCES.name());
        syscodeRequest.setCodeKeys(listExpectedPremiumSources);
        ResultObject<List<SyscodeResponse>> listResultObject = platformInternationalBaseApi.queryInternationalByCodeKeys(syscodeRequest);
        if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
            List<String> list = new ArrayList<>();
            listExpectedPremiumSources.forEach(s -> {
                listResultObject.getData().stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals(s))
                        .findAny().ifPresent(syscodeResponse -> {
                    String codeName = syscodeResponse.getCodeName();
                    list.add(codeName);
                });
            });
            expectedPremiumSourcesName = StringUtils.join(list.toArray(), ",");
        }
        return expectedPremiumSourcesName;
    }

    /**
     * 转换代理人
     *
     * @param policyDetailResponse 　保单返回信息
     * @param policyBo             　保单对象
     */
    public void transferPolicyAgent(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        //数据保单数据
        PolicyAgentResponse policyAgentResponse = (PolicyAgentResponse) this.converterObject(policyBo.getPolicyAgent(), PolicyAgentResponse.class);
        //查询代理人信息
        AgentResponse agentResponse = agentServiceInterface.getAgentInfoById(policyAgentResponse.getAgentId());
        if (AssertUtils.isNotNull(agentResponse)) {
            ClazzUtils.copyPropertiesIgnoreNull(agentResponse, policyAgentResponse);
        }
        //推荐人信息
        if (AssertUtils.isNotEmpty(agentResponse.getRecommendAgentId()) && !TerminologyTypeEnum.ROOT.name().equals(agentResponse.getRecommendAgentId())) {
            AgentResponse recommendAgent = agentServiceInterface.getAgentInfoById(agentResponse.getRecommendAgentId());
            if (AssertUtils.isNotNull(recommendAgent)) {
                policyAgentResponse.setRecommendLevelCode(recommendAgent.getAgentLevelCode());
                policyAgentResponse.setRecommendLevelName(recommendAgent.getAgentLevelCodeName());
                policyAgentResponse.setRecommendMobile(recommendAgent.getMobile());
                policyAgentResponse.setRecommendName(recommendAgent.getAgentName());
                policyAgentResponse.setRecommendCode(recommendAgent.getAgentCode());
                policyAgentResponse.setRecommendAgentStatus(recommendAgent.getAgentStatus());
            }
        }
        // 没有分配服务业务人员则取当前业务员充当服务业务员
        PolicyServiceAgentPo policyServiceAgentPo = policyQueryBaseDao.getNewestPolicyServiceAgentPo(policyBo.getPolicyId());
        if (AssertUtils.isNotNull(policyServiceAgentPo)) {
            AgentResponse serviceAgent = agentServiceInterface.getAgentInfoById(policyServiceAgentPo.getServiceAgentId());
            if (AssertUtils.isNotNull(serviceAgent)) {
                policyAgentResponse.setServiceAgentName(serviceAgent.getAgentName());
                policyAgentResponse.setServiceAgentCode(serviceAgent.getAgentCode());
                policyAgentResponse.setServiceAgentLevelCode(serviceAgent.getAgentLevelCode());
                policyAgentResponse.setServiceMobile(serviceAgent.getMobile());
                policyAgentResponse.setServiceAgentStatus(serviceAgent.getAgentStatus());
            }
        } else {
            policyAgentResponse.setServiceAgentName(policyAgentResponse.getAgentName());
            policyAgentResponse.setServiceAgentCode(policyAgentResponse.getAgentCode());
            policyAgentResponse.setServiceAgentLevelCode(policyAgentResponse.getAgentLevelCode());
            policyAgentResponse.setServiceMobile(policyAgentResponse.getMobile());
            policyAgentResponse.setServiceAgentStatus(policyAgentResponse.getAgentStatus());
        }
        //设置代理人
        policyDetailResponse.setPolicyAgent(policyAgentResponse);
    }


    /**
     * 转换附件
     *
     * @param policyDetailResponse 　保单返回信息
     * @param policyBo             　保单对象
     */
    public void transferPolicyAttachment(PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        List<PolicyAttachmentBo> policyAttachmentBos = policyBo.getListPolicyAttachment();
        if (AssertUtils.isNotEmpty(policyAttachmentBos)) {
            List<PolicyAttachmentTypeResponse> attachmentTypeResponseList = new ArrayList<>();
            policyAttachmentBos.forEach(attachment -> {
                //过滤保单书
                if (!(
                        PolicyTermEnum.ATTACHMENT_TYPE_FLAG.FIRST_ISSUE_BOOK.name().equals(attachment.getAttachmentTypeCode()) ||
                        PolicyTermEnum.ATTACHMENT_TYPE_FLAG.FIRST_ISSUE_BOOK_ONLINE.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.POLICY_BOOK.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.POLICY_TERMS_BOOK.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.PREMIUM_RATE_AND_CASH_VALUE.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.APPLY_BOOK.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.APPLICANT_HEALTH_BOOK.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.INSURED_HEALTH_BOOK.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.PLAN_BOOK.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.ACKNOWLEDGMENT_LETTER_BOOK.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.APPLY_RECEIPT_BOOK.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.POLICY_CONFIRM.name().equals(attachment.getAttachmentTypeCode()) ||
                                AttachmentPolicyEnum.POLICY_ALL_BOOK.name().equals(attachment.getAttachmentTypeCode()) ||
                                PolicyTermEnum.ATTACHMENT_TYPE_FLAG.CUSTOMER_SERVICE_INSTRUCTION_BOOK.name().equals(attachment.getAttachmentTypeCode())
                )) {
                    //附件类型
                    PolicyAttachmentTypeResponse policyAttachmentTypeResponse = attachmentTypeResponseList.stream()
                            .filter(attachmentType -> attachmentType.getAttachmentTypeCode().equals(attachment.getAttachmentTypeCode()))
                            .findFirst().orElse(new PolicyAttachmentTypeResponse());

                    if (!AssertUtils.isNotEmpty(policyAttachmentTypeResponse.getAttachmentTypeCode())) {
                        policyAttachmentTypeResponse.setAttachmentTypeCode(attachment.getAttachmentTypeCode());
                        policyAttachmentTypeResponse.setAttachmentTypeSeq(attachment.getAttachmentSeq());
                        policyAttachmentTypeResponse.setTotalPage(0L);
                        attachmentTypeResponseList.add(policyAttachmentTypeResponse);
                    }
                    //统计总记录数
                    policyAttachmentTypeResponse.setTotalPage(policyAttachmentTypeResponse.getTotalPage() + 1);
                    //附件
                    PolicyAttachmentResponse policyAttachmentResponse = new PolicyAttachmentResponse();
                    policyAttachmentResponse.setPolicyAttachmentId(attachment.getPolicyAttachmentId());
                    policyAttachmentResponse.setAttachmentId(attachment.getAttachmentId());
                    policyAttachmentResponse.setAttachmentSeq(attachment.getAttachmentSeq());
                    policyAttachmentTypeResponse.getListAttachment().add(policyAttachmentResponse);
                }
            });
            //设置附件类型
            policyDetailResponse.setListAttachmentType(attachmentTypeResponseList);
        }
    }


    /**
     * 转换被保险人
     *
     * @param policyDetailResponse 　保单返回信息
     * @param policyBo             　保单对象
     */
    public void transferPolicyInsured(Users users, PolicyDetailResponse policyDetailResponse, PolicyBo policyBo) {
        policyDetailResponse.setListPolicyInsured(new ArrayList<>());
        //国际化
        policyBo.getListPolicyInsured().forEach(insured -> {
            //转换
            PolicyInsuredResponse policyInsuredResponse = (PolicyInsuredResponse) this.converterObject(insured, PolicyInsuredResponse.class);

            //针对20A网销产品国籍国际化特殊处理
            policyInsuredResponse.setNationality(policyReviewTransData.getNationalityCodeTypeName(policyBo.getPolicyId(), policyInsuredResponse.getNationality(), users.getLanguage()));

            if (AssertUtils.isNotEmpty(policyInsuredResponse.getExpectedPremiumSources())) {
                policyInsuredResponse.setListExpectedPremiumSources(JSON.parseArray(policyInsuredResponse.getExpectedPremiumSources(), String.class));
                policyInsuredResponse.setExpectedPremiumSourcesName(this.transExpectedPremiumSourcesName(policyInsuredResponse.getListExpectedPremiumSources()));
            }
            PolicyOccupationNaturePo policyOccupationNaturePo = policyOtherInfoService.queryPolicyOccupationNaturePo(policyBo.getPolicyId(), PolicyTermEnum.CUSTOMER_TYPE.INSURED.name());
            if (AssertUtils.isNotNull(policyOccupationNaturePo)) {
                PolicyOccupationNatureResponse policyOccupationNatureResponse = new PolicyOccupationNatureResponse();
                ClazzUtils.copyPropertiesIgnoreNull(policyOccupationNaturePo, policyOccupationNatureResponse);
                policyInsuredResponse.setOccupationNature(policyOccupationNatureResponse);
            }

            policyInsuredResponse.setRelationshipCode(insured.getRelationship());
            //投保人地址拼接
            if (AssertUtils.isNotEmpty(policyInsuredResponse.getHomeAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(policyInsuredResponse.getHomeAreaCode());
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                    policyInsuredResponse.setHomeAreaName(respFcResultObject.getData().getAreaName());
                }
            }

            //咨询医生所在地区编码名称
            if (AssertUtils.isNotEmpty(policyInsuredResponse.getDoctorAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(policyInsuredResponse.getDoctorAreaCode());
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                    policyInsuredResponse.setDoctorAreaCodeName(respFcResultObject.getData().getAreaName());
                }
            }

            if (AssertUtils.isNotEmpty(policyInsuredResponse.getCompanyAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(policyInsuredResponse.getCompanyAreaCode());
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject)) {
                    policyInsuredResponse.setCompanyAreaName(respFcResultObject.getData().getAreaName());
                }
            }

            //投保人职业
            if (AssertUtils.isNotEmpty(policyInsuredResponse.getOccupationCode())) {
                ResultObject<CareerNameResponse> respFcResultObject = platformCareerApi.careerNameGet(policyInsuredResponse.getOccupationCode());
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject)) {
                    policyInsuredResponse.setOccupationName(respFcResultObject.getData().getCareerName());
                }
            }

            //投保單複核展示bmi，有值直接展示，沒值根據身高體重算，在重新設置，反之，直接返回空
            if (!AssertUtils.isNotEmpty(policyInsuredResponse.getBmi())) {
                if (AssertUtils.isNotEmpty(policyInsuredResponse.getStature())
                        && AssertUtils.isNotEmpty(policyInsuredResponse.getAvoirdupois())) {
                    BigDecimal powStature = new BigDecimal(policyInsuredResponse.getStature()).divide(new BigDecimal("100")).pow(2);
                    BigDecimal bmi = new BigDecimal(policyInsuredResponse.getAvoirdupois()).divide(powStature, 2, BigDecimal.ROUND_HALF_UP);
                    policyInsuredResponse.setBmi(bmi+"");
                }
            }

            //設置投保年齡
            Integer applyAge = getAgeYear(policyInsuredResponse.getBirthday(), policyBo.getApplyDate());
            policyInsuredResponse.setApplyAge(applyAge+"");

            //转换险种数据
            if (AssertUtils.isNotEmpty(insured.getListPolicyCoverage())) {
                List<PolicyCoverageBo> policyCoverageBos = insured.getListPolicyCoverage();
                List<PolicyCoverageResponse> policyCoverageResponses = policyCoverageBos.stream()
                        .filter(policyCoverageBo -> AssertUtils.isNotNull(policyCoverageBo.getPolicyCoveragePremium()))
                        .map(policyCoverageBo -> {
                            PolicyCoverageResponse policyCoverageResponse = new PolicyCoverageResponse();
                            policyCoverageResponse.setCoverageId(policyCoverageBo.getCoverageId());
                            policyCoverageResponse.setProductId(policyCoverageBo.getProductId());
                            //查询产品信息
                            ProductDetailedInfoResponse productDetailedInfoRespFc = productApi.queryOneProductDetail(policyCoverageBo.getProductId()).getData();
                            AssertUtils.isNotNull(this.getLogger(), productDetailedInfoRespFc, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PRODUCT_ERROR);
                            //转换险种字段信息
                            transferPolicyCoverage(users, policyCoverageResponse, policyCoverageBo, productDetailedInfoRespFc);
                            //增加加费信息
                            List<AddPremiumResponse> addPremiumResponses = this.getAddPremiumResponses(policyCoverageBo);
                            if (AssertUtils.isNotEmpty(addPremiumResponses)) {
                                policyCoverageResponse.setListAddPremium(addPremiumResponses);
                                policyCoverageResponse.setAddPremiumFlag("YES");
                            }
                            return policyCoverageResponse;
                        }).collect(Collectors.toList());
                //设置险种
                policyInsuredResponse.setListPolicyCoverage(policyCoverageResponses);
            }


            //转换受益人数据
            if (AssertUtils.isNotEmpty(insured.getListPolicyBeneficiary())) {
                List<PolicyBeneficiaryInfoBo> policyBeneficiaryInfoBos = insured.getListPolicyBeneficiary();
                List<PolicyBeneficiaryResponse> policyBeneficiaryResponseList = new ArrayList<>();
                policyBeneficiaryInfoBos.forEach(policyBeneficiaryInfoBo -> {
                    if (TerminologyConfigEnum.WHETHER.YES.name().equals(policyBeneficiaryInfoBo.getModifyFlag())) {
                        PolicyBeneficiaryResponse policyBeneficiaryResponse = (PolicyBeneficiaryResponse) this.converterObject(policyBeneficiaryInfoBo.getPolicyBeneficiary(), PolicyBeneficiaryResponse.class);
                        policyBeneficiaryResponse.setBeneficiaryProportion(policyBeneficiaryInfoBo.getBeneficiaryProportion());
                        policyBeneficiaryResponse.setBeneficiaryNo(policyBeneficiaryInfoBo.getBeneficiaryNo());
                        policyBeneficiaryResponse.setBeneficiaryNoOrder(policyBeneficiaryInfoBo.getBeneficiaryNoOrder());
                        policyBeneficiaryResponse.setRelationship(policyBeneficiaryInfoBo.getRelationship());
                        policyBeneficiaryResponse.setRelationshipInstructions(policyBeneficiaryInfoBo.getRelationshipInstructions());
                        policyBeneficiaryResponse.setRelationshipCode(policyBeneficiaryInfoBo.getRelationship());
                        policyBeneficiaryResponseList.add(policyBeneficiaryResponse);
                    }
                });
                //设置受益人返回
                policyInsuredResponse.setListPolicyBeneficiary(policyBeneficiaryResponseList);
            }
            //设置被保人信息
            policyDetailResponse.getListPolicyInsured().add(policyInsuredResponse);

        });
    }


    /**
     * 转换保单险种数据
     *
     * @param policyCoverageResponse    　保单险种返回对象
     * @param policyCoverageBo          　保单业务对象
     * @param productDetailedInfoRespFc 　保单产品详细信息
     */
    public void transferPolicyCoverage(Users users, PolicyCoverageResponse policyCoverageResponse, PolicyCoverageBo policyCoverageBo, ProductDetailedInfoResponse productDetailedInfoRespFc) {
        this.getLogger().info("用户语言:{}", JSON.toJSONString(users.getLanguage()));
        if (!AssertUtils.isNotEmpty(this.listCoverageTitle) || !users.getLanguage().equals(listCoverageTitle.get(0).getLanguage())) {
            // 为空或变换语言
            this.listCoverageTitle = platformInternationalBaseApi.queryInternational(InternationalTypeEnum.COVERAGE_TITLE.name(), users.getLanguage()).getData();
            this.getLogger().info("重新查询险种列表标题");
        }
        this.getLogger().info("查询险种列表标题:{}", JSON.toJSONString(listCoverageTitle));

        //添加基础的险种字段
        addBaseProductField(productDetailedInfoRespFc, policyCoverageResponse);

        //险种参数
        if (AssertUtils.isNotEmpty(productDetailedInfoRespFc.getParameterFields())) {
            productDetailedInfoRespFc.getParameterFields().forEach(parameterFieldRespFc -> {
                transferCoverageFields(policyCoverageBo, parameterFieldRespFc, policyCoverageResponse);
            });
        }

        String premiumFrequencyName = "缴费周期";
        Optional<SyscodeResponse> optionalPremiumFrequency = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("PREMIUM_FREQUENCY")).findFirst();
        if (optionalPremiumFrequency.isPresent()) {
            premiumFrequencyName = optionalPremiumFrequency.get().getCodeName();
        }
        // 缴费周期
        PolicyProductFieldResponse premiumFrequency = policyCoverageResponse.getListProductFields().stream()
                .filter(policyProductFieldResponse -> "premiumFrequency".equals(policyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new PolicyProductFieldResponse(premiumFrequencyName, "premiumFrequency", "DEFAULT", ""));
        if (!policyCoverageResponse.getListProductFields().contains(premiumFrequency) || !AssertUtils.isNotNull(premiumFrequency.getParameterValue())) {
            if (AssertUtils.isNotEmpty(policyCoverageBo.getPremiumFrequency())) {
                SyscodeResponse syscodeRespFc = platformInternationalBaseApi.queryOneInternational(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(), policyCoverageBo.getPremiumFrequency(), null).getData();
                if (AssertUtils.isNotNull(syscodeRespFc)) {
                    premiumFrequency.setFieldParam(syscodeRespFc.getCodeName(), null, policyCoverageBo.getPremiumFrequency());
                } else {
                    premiumFrequency.setFieldParam(policyCoverageBo.getPremiumFrequency(), null, policyCoverageBo.getPremiumFrequency());
                }
            }
            if (!policyCoverageResponse.getListProductFields().contains(premiumFrequency)) {
                policyCoverageResponse.getListProductFields().add(premiumFrequency);
            }
        }
        premiumFrequency.setIndex(104);


        // 缴费期限
        String premiumPeriodName = "缴费期限";
        Optional<SyscodeResponse> optionalPremiumPeriod = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("PREMIUM_PERIOD")).findFirst();
        if (optionalPremiumPeriod.isPresent()) {
            premiumPeriodName = optionalPremiumPeriod.get().getCodeName();
        }
        PolicyProductFieldResponse premiumPeriod = policyCoverageResponse.getListProductFields().stream()
                .filter(policyProductFieldResponse -> "premiumPeriod".equals(policyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new PolicyProductFieldResponse(premiumPeriodName, "premiumPeriod", "DEFAULT", ""));
        if (!policyCoverageResponse.getListProductFields().contains(premiumPeriod)) {
            if (AssertUtils.isNotEmpty(policyCoverageBo.getPremiumPeriodUnit())) {
                //需要国际化
                SyscodeResponse syscodeRespFc = platformInternationalBaseApi.queryOneInternational(TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name(), policyCoverageBo.getPremiumPeriodUnit(), null).getData();
                if (AssertUtils.isNotNull(syscodeRespFc)) {
                    premiumPeriod.setFieldParam(policyCoverageBo.getPremiumPeriod() + syscodeRespFc.getCodeName(), policyCoverageBo.getPremiumPeriodUnit(), policyCoverageBo.getPremiumPeriod());
                    //20190102 缴费期限趸交特殊处理
                    if (("1".equals(policyCoverageBo.getPremiumPeriod()) || !AssertUtils.isNotNull(policyCoverageBo.getPremiumPeriod())) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyCoverageBo.getPremiumPeriodUnit())) {
                        premiumPeriod.setFieldParam("--", policyCoverageBo.getPremiumPeriodUnit(), policyCoverageBo.getPremiumPeriod());
                    }
                } else {
                    premiumPeriod.setFieldParam(policyCoverageBo.getPremiumPeriod() + policyCoverageBo.getPremiumPeriodUnit(), policyCoverageBo.getPremiumPeriodUnit(), policyCoverageBo.getPremiumPeriod());
                }
            } else {
                premiumPeriod.setFieldParam(policyCoverageBo.getPremiumPeriod(), null, policyCoverageBo.getPremiumPeriod());
            }
            policyCoverageResponse.getListProductFields().add(premiumPeriod);
        }
        premiumPeriod.setIndex(105);

        // 保险期间
        String coveragePeriodName = "保险期间";
        Optional<SyscodeResponse> optionalCoveragePeriod = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("COVERAGE_PERIOD")).findFirst();
        if (optionalCoveragePeriod.isPresent()) {
            coveragePeriodName = optionalCoveragePeriod.get().getCodeName();
        }
        PolicyProductFieldResponse coveragePeriod = policyCoverageResponse.getListProductFields().stream()
                .filter(policyProductFieldResponse -> "coveragePeriod".equals(policyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new PolicyProductFieldResponse(coveragePeriodName, "coveragePeriod", "DEFAULT", ""));
        if (!policyCoverageResponse.getListProductFields().contains(coveragePeriod)) {
            if (AssertUtils.isNotEmpty(policyCoverageBo.getCoveragePeriodUnit())) {
                //需要国际化
                SyscodeResponse syscodeRespFc = platformInternationalBaseApi.queryOneInternational(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), policyCoverageBo.getCoveragePeriodUnit(), null).getData();
                if (AssertUtils.isNotNull(syscodeRespFc)) {
                    coveragePeriod.setFieldParam(policyCoverageBo.getCoveragePeriod() + syscodeRespFc.getCodeName(), policyCoverageBo.getCoveragePeriodUnit(), policyCoverageBo.getCoveragePeriod());
                } else {
                    coveragePeriod.setFieldParam(policyCoverageBo.getCoveragePeriod() + policyCoverageBo.getCoveragePeriodUnit(), policyCoverageBo.getCoveragePeriodUnit(), policyCoverageBo.getCoveragePeriod());
                }
            } else {
                coveragePeriod.setFieldParam(policyCoverageBo.getCoveragePeriod(), null, policyCoverageBo.getCoveragePeriod());
            }
            policyCoverageResponse.getListProductFields().add(coveragePeriod);
        }
        coveragePeriod.setIndex(106);

        // 保额
        String amountName = "保险金额";
        Optional<SyscodeResponse> optionalAmount = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("AMOUNT")).findFirst();
        if (optionalAmount.isPresent()) {
            amountName = optionalAmount.get().getCodeName();
        }
        PolicyProductFieldResponse amount = policyCoverageResponse.getListProductFields().stream()
                .filter(policyProductFieldResponse -> "amount".equals(policyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new PolicyProductFieldResponse(amountName, "amount", "DEFAULT", ""));
//        if (!policyCoverageResponse.getListProductFields().contains(amount)) {
            amount.setFieldParam(AssertUtils.isNotNull(policyCoverageBo.getAmount()) ? policyCoverageBo.getAmount() + "" : "", null, policyCoverageBo.getAmount() + "");
            amount.setIndex(107);
//            policyCoverageResponse.getListProductFields().add(amount);
//        }

        //financingMethod
        Optional<PolicyProductFieldResponse> first = policyCoverageResponse.getListProductFields().stream()
                .filter(applyProductFieldResponse -> "financingMethod".equals(applyProductFieldResponse.getFieldName()))
                .findFirst();
        if (first.isPresent()) {
            SyscodeResponse financingMethod = platformInternationalBaseApi.queryOneInternational("PRODUCT_FINANCING_METHOD", policyCoverageBo.getFinancingMethod(), null).getData();
            String parameterName = AssertUtils.isNotNull(financingMethod) ? financingMethod.getCodeName() : policyCoverageBo.getFinancingMethod();
            PolicyProductFieldResponse policyProductFieldResponse = first.get();
            policyProductFieldResponse.setFieldParam(parameterName, null, policyCoverageBo.getFinancingMethod());
            policyProductFieldResponse.setIndex(108);
        }

        String accSiMultipleName = "额外意外保额倍数";
        Optional<SyscodeResponse> optionalAccSiMultipleFirst = this.listCoverageTitle.stream().filter(syscodeResponse -> "ACC_SI_MULTIPLE".equals(syscodeResponse.getCodeKey())).findFirst();
        if (optionalAccSiMultipleFirst.isPresent()) {
            accSiMultipleName = optionalAccSiMultipleFirst.get().getCodeName();
        }
        Optional<PolicyProductFieldResponse> accSiMultipleFirst = policyCoverageResponse.getListProductFields().stream().filter(applyProductFieldResponse -> "accSiMultiple".equals(applyProductFieldResponse.getFieldName())).findFirst();
        if (accSiMultipleFirst.isPresent()) {
            PolicyProductFieldResponse applyProductFieldResponse = accSiMultipleFirst.get();
            applyProductFieldResponse.setFieldLabel(accSiMultipleName);
            applyProductFieldResponse.setFieldParam(policyCoverageBo.getAccSiMultiple() + "", null, policyCoverageBo.getAccSiMultiple() + "");
            applyProductFieldResponse.setIndex(109);
        }

        // 份数
        String multName = "份数";
        Optional<SyscodeResponse> optionalMult = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("MULT")).findFirst();
        if (optionalMult.isPresent()) {
            multName = optionalMult.get().getCodeName();
        }
        PolicyProductFieldResponse mult = policyCoverageResponse.getListProductFields().stream()
                .filter(policyProductFieldResponse -> "mult".equals(policyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new PolicyProductFieldResponse(multName, "mult", "DEFAULT", ""));
        if (!policyCoverageResponse.getListProductFields().contains(mult)) {
            mult.setFieldParam(policyCoverageBo.getMult(), null, policyCoverageBo.getMult());
            policyCoverageResponse.getListProductFields().add(mult);
        }
        mult.setIndex(122);

        // 保费
        String premiumName = "期缴保险费";
        Optional<SyscodeResponse> optionalPremium = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("PREMIUM")).findFirst();
        if (optionalPremium.isPresent()) {
            premiumName = optionalPremium.get().getCodeName();
        }
        PolicyProductFieldResponse totalPremium = policyCoverageResponse.getListProductFields().stream()
                .filter(policyProductFieldResponse -> "totalPremium".equals(policyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new PolicyProductFieldResponse(premiumName, "totalPremium", "DEFAULT", ""));
        if (AssertUtils.isNotNull(policyCoverageBo.getPolicyCoveragePremium()) &&
                AssertUtils.isNotNull(policyCoverageBo.getPolicyCoveragePremium().getPeriodTotalPremium())) {
            totalPremium.setFieldParam(policyCoverageBo.getPolicyCoveragePremium().getPeriodTotalPremium() + "",
                    null, policyCoverageBo.getPolicyCoveragePremium().getPeriodTotalPremium() + "");
        }
        if (AssertUtils.isNotNull(policyCoverageBo.getOriginalPremium()) && policyCoverageBo.getOriginalPremium().compareTo(BigDecimal.ZERO) != 0) {
            totalPremium.setFieldParam(policyCoverageBo.getOriginalPremium() + "",
                    null, policyCoverageBo.getOriginalPremium() + "");
        }
        if (!policyCoverageResponse.getListProductFields().contains(totalPremium)) {
            //增加加费信息
            this.transPolicyCoverageAddPremium(policyCoverageBo, totalPremium);
            policyCoverageResponse.getListProductFields().add(totalPremium);
        }
        totalPremium.setIndex(130);
        //字段排序
        policyCoverageResponse.getListProductFields().sort(Comparator.comparingInt(PolicyProductFieldResponse::getIndex));
//        Collections.sort(policyCoverageResponse.getListProductFields(), new Comparator<PolicyProductFieldResponse>() {
//            @Override
//            public int compare(PolicyProductFieldResponse o1, PolicyProductFieldResponse o2) {
//                return o1.getIndex() - o2.getIndex();
//            }
//        });

//        //险种责任参数
//        if (AssertUtils.isNotEmpty(productDetailedInfoRespFc.getDutys())){
//            //循环责任
//            productDetailedInfoRespFc
//            .getDutys()
//            .forEach(dutyRespFc -> {
//                if(AssertUtils.isNotEmpty(policyCoverageBo.getListCoverageDuty())){
//                    //判断责任是否匹配上
//                    policyCoverageBo.getListCoverageDuty()
//                            .stream()
//                            .filter(dutyBo->dutyBo.getDutyId().equals(dutyRespFc.getDutyId()))
//                            .forEach(dutyBo->{
//                                //循环责任字段
//                                transferDutyFields(dutyRespFc, dutyBo,policyCoverageResponse);
//                            });
//                }
//            });
//        }

    }

    private void transPolicyCoverageAddPremium(PolicyCoverageBo policyCoverageBo, PolicyProductFieldResponse totalPremium) {
        List<PolicyProductFieldResponse> listChildProductFields = new ArrayList<>();
        BigDecimal originalStandardPremium = policyCoverageBo.getTotalPremium();
        BigDecimal originalAddPremium = BigDecimal.ZERO;

        List<PolicyAddPremiumPo> policyAddPremiumPos = policyPremiumBaseService.getPolicyCoverageAddPremium(policyCoverageBo.getPolicyId(), null, policyCoverageBo.getCoverageId());
        if (AssertUtils.isNotEmpty(policyAddPremiumPos)) {
            BigDecimal annualAddPremium = policyAddPremiumPos.stream().filter(policyAddPremiumPo -> AssertUtils.isNotNull(policyAddPremiumPo.getAddPremiumEndDate()) && AssertUtils.isNotNull(policyAddPremiumPo.getAddPremiumPeriod()) &&
                    DateUtils.getCurrentTime() <= policyAddPremiumPo.getAddPremiumEndDate() && policyAddPremiumPo.getAddPremiumStatus().equals(PolicyTermEnum.ADD_PREMIUM_STATUS.EFFECTIVE.name()))
                    .map(PolicyAddPremiumPo::getTotalAddPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal conversionFactor = BigDecimal.valueOf(PolicyTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(policyCoverageBo.getPremiumFrequency()).value());
            originalAddPremium = annualAddPremium.multiply(conversionFactor).setScale(2, BigDecimal.ROUND_HALF_UP);
            originalStandardPremium = originalStandardPremium.subtract(originalAddPremium);
        }

        String standardPremium = "期缴标准保费";
        Optional<SyscodeResponse> standardPremiumName = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("STANDARD_PREMIUM")).findFirst();
        if (standardPremiumName.isPresent()) {
            standardPremium = standardPremiumName.get().getCodeName();
        }
        PolicyProductFieldResponse originalStandardPremiumField = new PolicyProductFieldResponse(standardPremium, "originalStandardPremium", "DEFAULT", "P", "");
        originalStandardPremiumField.setFieldParam(originalStandardPremium + "", null, originalStandardPremium + "");
        listChildProductFields.add(originalStandardPremiumField);

        String extraPremium = "期缴加费保费";
        Optional<SyscodeResponse> extraPremiumName = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("EXTRA_PREMIUM")).findFirst();
        if (extraPremiumName.isPresent()) {
            extraPremium = extraPremiumName.get().getCodeName();
        }
        PolicyProductFieldResponse originalAddPremiumField = new PolicyProductFieldResponse(extraPremium, "originalAddPremium", "DEFAULT", "P", "");
        originalAddPremiumField.setFieldParam(originalAddPremium + "", null, originalAddPremium + "");
        listChildProductFields.add(originalAddPremiumField);

        PolicyProductFieldResponse totalPremiumField = new PolicyProductFieldResponse();
        ClazzUtils.copyPropertiesIgnoreNull(totalPremium, totalPremiumField);
        totalPremiumField.setShowFlag(TerminologyConfigEnum.WHETHER.NO.name());
        listChildProductFields.add(totalPremiumField);
        totalPremium.setListChildProductFields(listChildProductFields);
    }

    /**
     * 添加险种的基础信息
     *
     * @param productDetailedInfoRespFc 　产品详情
     * @param policyCoverageResponse    　险种返回数据
     */
    private void addBaseProductField(ProductDetailedInfoResponse productDetailedInfoRespFc, PolicyCoverageResponse policyCoverageResponse) {
        String coverageName = "险种名称";
        Optional<SyscodeResponse> optionalCoverageName = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("COVERAGE_NAME")).findFirst();
        if (optionalCoverageName.isPresent()) {
            coverageName = optionalCoverageName.get().getCodeName();
        }
        String coverageCode = "险种编号";
        Optional<SyscodeResponse> optionalCoverageCode = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("COVERAGE_CODE")).findFirst();
        if (optionalCoverageCode.isPresent()) {
            coverageCode = optionalCoverageCode.get().getCodeName();
        }
        String coverageType = "险种类型";
        Optional<SyscodeResponse> optionalCoverageType = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("COVERAGE_TYPE")).findFirst();
        if (optionalCoverageType.isPresent()) {
            coverageType = optionalCoverageType.get().getCodeName();
        }
        String currency = "币种";
        Optional<SyscodeResponse> optionalCurency = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("CURRENCY")).findFirst();
        if (optionalCurency.isPresent()) {
            currency = optionalCurency.get().getCodeName();
        }
        //添加产品信息
        PolicyProductFieldResponse productFieldResponse = new PolicyProductFieldResponse(coverageName, "coverageName", "DEFAULT", null, productDetailedInfoRespFc.getProductName());
        productFieldResponse.setIndex(100);
        policyCoverageResponse.getListProductFields().add(productFieldResponse);
        // 险种编号
        PolicyProductFieldResponse productFieldResponse1 = new PolicyProductFieldResponse(coverageCode, "coverageCode", "DEFAULT", null, productDetailedInfoRespFc.getProductCode());
        productFieldResponse1.setIndex(101);
        policyCoverageResponse.getListProductFields().add(productFieldResponse1);
        //险种类型
        PolicyProductFieldResponse productFieldResponse2 = new PolicyProductFieldResponse(coverageType, "coverageType", "DEFAULT", null, productDetailedInfoRespFc.getMainProductFlagName());
        productFieldResponse2.setIndex(102);
        policyCoverageResponse.getListProductFields().add(productFieldResponse2);
        // 设置主险标志
        policyCoverageResponse.setPrimaryFlag(productDetailedInfoRespFc.getMainProductFlag());
        //币种
        PolicyProductFieldResponse productFieldResponse3 = new PolicyProductFieldResponse(currency, "currency", "DEFAULT", null, productDetailedInfoRespFc.getCurrencyName());
        productFieldResponse3.setIndex(103);
        policyCoverageResponse.getListProductFields().add(productFieldResponse3);
    }


    /**
     * 转换险种字段参数
     *
     * @param coverageBo           　投保单险种对象
     * @param parameterFieldRespFc 　产品参数字段对象
     */
    private void transferCoverageFields(PolicyCoverageBo coverageBo, ParameterFieldResponse parameterFieldRespFc, PolicyCoverageResponse policyCoverageResponse) {

//        // 缴费周期
//        if (parameterFieldRespFc.getFieldName().equals("premiumFrequency")) {
//            //新建字段(设置字段的基本属性)
//            PolicyProductFieldResponse applyProductFieldResponse =
//                    new PolicyProductFieldResponse(
//                            parameterFieldRespFc.getFieldLabel(),
//                            parameterFieldRespFc.getFieldName(),
//                            parameterFieldRespFc.getInputFlag(),
//                            null);
//            //获取相应的参数值
//            transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse, coverageBo.getPolicyCoveragePremium().getPremiumFrequency());
//
//            //添加字段
//            policyCoverageResponse.getListProductFields().add(applyProductFieldResponse);
//        }
//
//        // 保费
//        if (parameterFieldRespFc.getFieldName().equals("totalPremium")) {
//            //新建字段(设置字段的基本属性)
//            PolicyProductFieldResponse applyProductFieldResponse =
//                    new PolicyProductFieldResponse(
//                            parameterFieldRespFc.getFieldLabel(),
//                            parameterFieldRespFc.getFieldName(),
//                            parameterFieldRespFc.getInputFlag(),
//                            null);
//            //获取相应的参数值
//            transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse, coverageBo.getPolicyCoveragePremium().getPeriodTotalPremium() + "");
//
//            //添加字段
//            policyCoverageResponse.getListProductFields().add(applyProductFieldResponse);
//        }
//
//        // 缴费期限
//        if (parameterFieldRespFc.getFieldName().equals("premiumPeriod")) {
//            //新建字段(设置字段的基本属性)
//            PolicyProductFieldResponse applyProductFieldResponse =
//                    new PolicyProductFieldResponse(
//                            parameterFieldRespFc.getFieldLabel(),
//                            parameterFieldRespFc.getFieldName(),
//                            parameterFieldRespFc.getInputFlag(),
//                            null);
//            //获取相应的参数值
//            transferParameterSelect(parameterFieldRespFc, applyProductFieldResponse, coverageBo.getPolicyCoveragePremium().getPremiumPeriod(), coverageBo.getPolicyCoveragePremium().getPremiumPeriodType());
//
//            //添加字段
//            policyCoverageResponse.getListProductFields().add(applyProductFieldResponse);
//        }
//
//        // 保险期间
//        if (parameterFieldRespFc.getFieldName().equals("coveragePeriod")) {
//            //新建字段(设置字段的基本属性)
//            PolicyProductFieldResponse applyProductFieldResponse =
//                    new PolicyProductFieldResponse(
//                            parameterFieldRespFc.getFieldLabel(),
//                            parameterFieldRespFc.getFieldName(),
//                            parameterFieldRespFc.getInputFlag(),
//                            null);
//            //获取相应的参数值
//            transferParameterSelect(parameterFieldRespFc, applyProductFieldResponse, coverageBo.getCoveragePeriod(), coverageBo.getCoveragePeriodUnit());
//
//            //添加字段
//            policyCoverageResponse.getListProductFields().add(applyProductFieldResponse);
//        }
//
//        // 保额
//        if (parameterFieldRespFc.getFieldName().equals("amount")) {
//            //新建字段(设置字段的基本属性)
//            PolicyProductFieldResponse applyProductFieldResponse =
//                    new PolicyProductFieldResponse(
//                            parameterFieldRespFc.getFieldLabel(),
//                            parameterFieldRespFc.getFieldName(),
//                            parameterFieldRespFc.getInputFlag(),
//                            null);
//            //获取相应的参数值
//            transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse, coverageBo.getBaseSumAmount());
//
//            //添加字段
//            policyCoverageResponse.getListProductFields().add(applyProductFieldResponse);
//        }
//
//        // 份数
//        if (parameterFieldRespFc.getFieldName().equals("mult")) {
//            //新建字段(设置字段的基本属性)
//            PolicyProductFieldResponse applyProductFieldResponse =
//                    new PolicyProductFieldResponse(
//                            parameterFieldRespFc.getFieldLabel(),
//                            parameterFieldRespFc.getFieldName(),
//                            parameterFieldRespFc.getInputFlag(),
//                            null);
//            //获取相应的参数值
//            transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse, coverageBo.getMult());
//
//            //添加字段
//            policyCoverageResponse.getListProductFields().add(applyProductFieldResponse);
//        }
        //新建字段(设置字段的基本属性)
        PolicyProductFieldResponse applyProductFieldResponse4 =
                new PolicyProductFieldResponse(
                        parameterFieldRespFc.getFieldLabel(),
                        parameterFieldRespFc.getFieldName(),
                        parameterFieldRespFc.getInputFlag(),
                        null);
        applyProductFieldResponse4.setIndex(policyCoverageResponse.getListProductFields().size() + 120);
        //动态字段相同
        listFieldCoverage.stream()
                .filter(coveragefield -> coveragefield.getName().equals(parameterFieldRespFc.getFieldName()))
                .forEach(coverageField -> {
                    String value = null;
                    String unit = null;
                    if (AssertUtils.isNotNull(clazzBusinessService.getFieldValueByName(coverageField.getName(), coverageBo))) {
                        value = clazzBusinessService.getFieldValueByName(coverageField.getName(), coverageBo) + "";
                    }
                    if (AssertUtils.isNotNull(clazzBusinessService.getFieldValueByName(coverageField.getName() + "Unit", coverageBo))) {
                        unit = clazzBusinessService.getFieldValueByName(coverageField.getName() + "Unit", coverageBo) + "";
                    }
                    if (AssertUtils.isNotEmpty(value)) {
                        //选择框(单位)
                        if (PolicyTermEnum.PRODUCT_FIELD_INPUT.SELECT.name().equals(parameterFieldRespFc.getInputFlag())) {
                            //判断单位是否为空
                            if (AssertUtils.isNotEmpty(parameterFieldRespFc.getParameterValues().get(0).getParameterUnit())) {
                                String valueUnit = clazzBusinessService.getFieldValueByName(coverageField.getName() + "Unit", coverageBo) + "";
                                transferParameterSelect(parameterFieldRespFc, applyProductFieldResponse4, value, valueUnit);
                            } else {
                                transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse4, value);
                            }
                        }
                        //默认框,
                        if (PolicyTermEnum.PRODUCT_FIELD_INPUT.DEFAULT.name().equals(parameterFieldRespFc.getInputFlag())) {
                            //获取相应的参数值
                            transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse4, value);
                        }
                        //输入框,
                        if (PolicyTermEnum.PRODUCT_FIELD_INPUT.INPUT.name().equals(parameterFieldRespFc.getInputFlag())) {
                            //判断是否有单位
                            if (AssertUtils.isNotNull(parameterFieldRespFc.getUnitObj())) {
                                //TODO:未处理
                            } else {
                                applyProductFieldResponse4.setFieldParam(value, unit, value);
                            }
                        }
                        //不录入
                        if (PolicyTermEnum.PRODUCT_FIELD_INPUT.NOT.name().equals(parameterFieldRespFc.getInputFlag())) {
                            applyProductFieldResponse4.setFieldParam(value, null, value);
                        }
                    }
                });
//
//        //字段相同
//        this.listFieldCoverage.stream()
//        .filter(coveragefield -> coveragefield.getName().equals(parameterFieldRespFc.getFieldName()))
//        .forEach(coverageField->{
//            String value = (String)ClazzUtils.getFieldValueByName(coverageField.getName(),coverageBo);
//            if(AssertUtils.isNotEmpty(value)){
//                //选择框(单位)
//                if(PolicyTermEnum.PRODUCT_FIELD_INPUT.SELECT.name().equals(parameterFieldRespFc.getInputFlag())){
//                    //判断单位是否为空
//                    if(AssertUtils.isNotEmpty(parameterFieldRespFc.getParameterValues().get(0).getParameterUnit())){
//                        String valueUnit = (String)ClazzUtils.getFieldValueByName(coverageField.getName()+"Unit",coverageBo);
//                        transferParameterSelect(parameterFieldRespFc, applyProductFieldResponse4, value, valueUnit);
//                    }else{
//                        transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse4, value);
//                    }
//                }
//                //默认框,
//                if(PolicyTermEnum.PRODUCT_FIELD_INPUT.DEFAULT.name().equals(parameterFieldRespFc.getInputFlag())){
//                    //获取相应的参数值
//                    transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse4, value);
//                }
//                //输入框,
//                if(PolicyTermEnum.PRODUCT_FIELD_INPUT.INPUT.name().equals(parameterFieldRespFc.getInputFlag())){
//                    //判断是否有单位
//                    if(AssertUtils.isNotNull(parameterFieldRespFc.getUnitObj())){
//                        //TODO:未处理(输入框有参数)
//                    }else{
//                        applyProductFieldResponse4.setFieldParam(value,null,value);
//                    }
//                }
//                //不录入框
//                if(PolicyTermEnum.PRODUCT_FIELD_INPUT.NOT.name().equals(parameterFieldRespFc.getInputFlag())){
//                    applyProductFieldResponse4.setFieldParam(value,null,value);
//                }
//            }
//        });
        //添加字段
        policyCoverageResponse.getListProductFields().add(applyProductFieldResponse4);
    }


    /**
     * 转换责任字段
     *
     * @param dutyRespFc 　产品责任对象
     * @param dutyBo     　投保单责任对象
     */
    private void transferDutyFields(DutyResponse dutyRespFc, PolicyCoverageDutyBo dutyBo, PolicyCoverageResponse policyCoverageResponse) {
        //责任字段
        dutyRespFc.getParameterFields()
                .stream()
                .forEach(parameterFieldRespFc -> {
                    //新建字段(设置字段的基本属性)
                    PolicyProductFieldResponse applyProductFieldResponse4 =
                            new PolicyProductFieldResponse(
                                    dutyRespFc.getDutyName() + parameterFieldRespFc.getFieldLabel(),
                                    parameterFieldRespFc.getFieldName(),
                                    parameterFieldRespFc.getInputFlag(),
                                    dutyRespFc.getDutyId());
                    //字段相同
                    this.listFieldDuty.stream()
                            .filter(dutyField -> dutyField.getName().equals(parameterFieldRespFc.getFieldName()))
                            .forEach(dutyField -> {
                                String value = (String) ClazzUtils.getFieldValueByName(dutyField.getName(), dutyBo);
                                if (AssertUtils.isNotEmpty(value)) {
                                    //选择框(单位)
                                    if (PolicyTermEnum.PRODUCT_FIELD_INPUT.SELECT.name().equals(parameterFieldRespFc.getInputFlag())) {
                                        if (AssertUtils.isNotEmpty(parameterFieldRespFc.getParameterValues().get(0).getParameterUnit())) {
                                            String valueUnit = (String) ClazzUtils.getFieldValueByName(dutyField.getName() + "Unit", dutyRespFc);
                                            //获取相应的参数值
                                            transferParameterSelect(parameterFieldRespFc, applyProductFieldResponse4, value, valueUnit);
                                        } else {
                                            transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse4, value);
                                        }

                                    }
                                    //默认框
                                    if (PolicyTermEnum.PRODUCT_FIELD_INPUT.DEFAULT.name().equals(parameterFieldRespFc.getInputFlag())) {
                                        //获取相应的参数值
                                        transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse4, value);
                                    }
                                    //输入框,
                                    if (PolicyTermEnum.PRODUCT_FIELD_INPUT.INPUT.name().equals(parameterFieldRespFc.getInputFlag())) {
                                        if (AssertUtils.isNotNull(parameterFieldRespFc.getUnitObj())) {
                                            //TODO:未处理(输入框有参数)

                                        } else {
                                            applyProductFieldResponse4.setFieldParam(value, null, value);
                                        }

                                    }
                                    //不录入
                                    if (PolicyTermEnum.PRODUCT_FIELD_INPUT.NOT.name().equals(parameterFieldRespFc.getInputFlag())) {
                                        applyProductFieldResponse4.setFieldParam(value, null, value);
                                    }
                                }
                            });
                    //设置险种字段
                    policyCoverageResponse.getListProductFields().add(applyProductFieldResponse4);
                });
    }


    /**
     * 匹配默框参数
     *
     * @param parameterFieldRespFc       字段对象
     * @param applyProductFieldResponse4 　字段返回对象
     * @param value                      　值
     */
    private void transferParameterDefault(ParameterFieldResponse parameterFieldRespFc, PolicyProductFieldResponse applyProductFieldResponse4, String value) {
        parameterFieldRespFc
                .getParameterValues()
                .stream()
                .filter(parameterValueRespFc -> parameterValueRespFc.getParameterValue().equals(value))
                .forEach(parameterValueRespFc -> {
                    applyProductFieldResponse4.setFieldParam(parameterValueRespFc.getParameterName(), parameterValueRespFc.getParameterUnit(), parameterValueRespFc.getParameterValue());
                });
    }


    /**
     * 　匹配选择框参数
     *
     * @param parameterFieldRespFc       　字段对象
     * @param applyProductFieldResponse4 　字段返回对象
     * @param value                      　值
     * @param valueUnit                  　单位
     */
    private void transferParameterSelect(ParameterFieldResponse parameterFieldRespFc, PolicyProductFieldResponse applyProductFieldResponse4, String value, String valueUnit) {
        parameterFieldRespFc
                .getParameterValues()
                .stream()
                .filter(parameterValueRespFc -> parameterValueRespFc.getParameterValue().equals(value))
                .filter(parameterValueRespFc -> parameterValueRespFc.getParameterUnit().equals(valueUnit))
                .forEach(parameterValueRespFc -> {
                    applyProductFieldResponse4.setFieldParam(parameterValueRespFc.getParameterName(), parameterValueRespFc.getParameterUnit(), parameterValueRespFc.getParameterValue());
                });
    }

    public List<AddPremiumResponse> getAddPremiumResponses(PolicyCoverageBo policyCoverageBo) {
        //险种加费信息
        List<AddPremiumResponse> listAddPremium = new ArrayList<>();
        List<PolicyAddPremiumPo> addPremiumPos = policyPremiumBaseService.getPolicyCoverageAddPremium(policyCoverageBo.getPolicyId(), null, policyCoverageBo.getCoverageId());
        if (AssertUtils.isNotEmpty(addPremiumPos)) {
            LinkedHashMap<String, List<PolicyAddPremiumPo>> map = addPremiumPos.parallelStream().filter(applyAddPremiumPo -> AssertUtils.isNotEmpty(applyAddPremiumPo.getRatingsName()))
                    .collect(Collectors.groupingBy(PolicyAddPremiumPo::getRatingsName, LinkedHashMap::new, Collectors.toList()));
            map.keySet().forEach(ratingsName -> {
                List<PolicyAddPremiumPo> policyAddPremiumPos = map.get(ratingsName);
                AddPremiumResponse addPremiumResponse = new AddPremiumResponse();
                ClazzUtils.copyPropertiesIgnoreNull(policyCoverageBo, addPremiumResponse);

                addPremiumResponse.setRatingsName(ratingsName);
                policyAddPremiumPos.forEach(policyAddPremiumPo -> {
                    if ("EM(%)".equals(policyAddPremiumPo.getAddPremiumObjectCode())) {
                        addPremiumResponse.setEm(policyAddPremiumPo.getAddPremiumObjectValue());
                        addPremiumResponse.setEmp(policyAddPremiumPo.getTotalAddPremium());
                        addPremiumResponse.setEpy(policyAddPremiumPo.getAddPremiumPeriod());
                    }

                    if ("FER(‰)".equals(policyAddPremiumPo.getAddPremiumObjectCode())) {
                        addPremiumResponse.setFer(policyAddPremiumPo.getAddPremiumObjectValue());
                        addPremiumResponse.setFep(policyAddPremiumPo.getTotalAddPremium());
                        addPremiumResponse.setFey(policyAddPremiumPo.getAddPremiumPeriod());
                    }
                });
                listAddPremium.add(addPremiumResponse);
            });
        }
        return listAddPremium;
    }

}
