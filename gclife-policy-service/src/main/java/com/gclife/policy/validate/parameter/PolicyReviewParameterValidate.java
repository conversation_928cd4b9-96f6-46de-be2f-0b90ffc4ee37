package com.gclife.policy.validate.parameter;

import com.gclife.agent.model.response.AgentBaseResponse;
import com.gclife.agent.model.response.AgentIdentityBaseResponse;
import com.gclife.apply.api.BaseApplyApi;
import com.gclife.common.error.ApplyErrorConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.StringUtil;
import com.gclife.party.model.request.CustomerBusinessRequest;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPo;
import com.gclife.policy.model.bo.PolicyApplicantBo;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.request.review.*;
import com.gclife.policy.model.vo.PolicyContactInfoVo;
import com.gclife.policy.service.base.PolicyBaseService;
import com.gclife.policy.validate.transfer.PolicyReviewTransData;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.response.manager.ProductDetailedInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gclife.common.error.ApplyErrorConfigEnum.*;

/**
 * <AUTHOR>
 * create 2021/6/15 10:57
 * description:
 */
@Component
@Slf4j
public class PolicyReviewParameterValidate extends BaseBusinessServiceImpl {
    @Autowired
    private BaseApplyApi baseApplyApi;
    @Autowired
    private PolicyReviewTransData policyReviewTransData;
    @Autowired
    private PolicyBaseService policyBaseService;

    public void validParameterApplicant(PolicyReviewSaveRequest policyReviewSaveRequest) {
        //投保人信息
        PolicyReviewApplicantRequest applicantInfo = policyReviewSaveRequest.getApplicant();
        AssertUtils.isNotNull(log, applicantInfo, ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_INFO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, applicantInfo.getName(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, applicantInfo.getIdType(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, applicantInfo.getIdNo(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, applicantInfo.getMobile(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_MOBILE_IS_NOT_NULL);
        String policyId = policyReviewSaveRequest.getPolicyId();
        boolean is20A = ProductTermEnum.PRODUCT.PRODUCT_20A.id().equals(policyReviewTransData.getMainProductId(policyId));
        // 查询保单基本信息
        PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);
        AssertUtils.isNotNull(this.getLogger(), policyPo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
        if (!is20A && !AssertUtils.isNotEmpty(policyPo.getActivationCode())) {
            AssertUtils.isNotEmpty(log, applicantInfo.getMarriage(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_MARRIAGE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, applicantInfo.getIncome(), ApplyErrorConfigEnum.APPLY_APP_APPLICANT_INCOME_IS_NOT_NULL);
            //客户来源判断
            AssertUtils.isNotEmpty(log, applicantInfo.getCustomerSource(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_CUSTOMER_SOURCE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, applicantInfo.getHomeAddress(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_HOME_ADDRESS_IS_NOT_NULL);
            //验证职业性质
            this.validOccupationNature(applicantInfo.getOccupationNature());
        }
        AssertUtils.isNotEmpty(log, applicantInfo.getAddressType(), ApplyErrorConfigEnum.APPLY_APP_ADDRESS_TYPE_IS_NOT_NULL);

        AssertUtils.isNotEmpty(log, applicantInfo.getOccupationCode(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_OCCUPATION_CODE_IS_NOT_NULL);

        AssertUtils.isNotEmpty(log, applicantInfo.getNationality(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_NATIVE_PLACE_IS_NOT_NULL);

        //格式校验
        validParameterApplicantFormat(applicantInfo);
    }

    private void validOccupationNature(PolicyOccupationNatureRequest occupationNature) {
        AssertUtils.isNotNull(log, occupationNature, ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, occupationNature.getOccupationNature(), ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_IS_NOT_NULL);
        List<String> requiredOccupationNature = Arrays.asList(PolicyTermEnum.OCCUPATION_NATURE.EMPLOYED_IN_GOVERNMENT_OR_PUBLIC_SECTOR.name(),
                PolicyTermEnum.OCCUPATION_NATURE.EMPLOYED_IN_PRIVATE_SECTOR.name(),
                PolicyTermEnum.OCCUPATION_NATURE.SELF_EMPLOYED.name());
        if (requiredOccupationNature.contains(occupationNature.getOccupationNature())) {
            AssertUtils.isNotEmpty(log, occupationNature.getOccupation(), ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_OCCUPATION_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, occupationNature.getExactDuties(), ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_EXACTDUTIES_IS_NOT_NULL);
        }
    }

    public void validParameterApplicantFormat(PolicyReviewApplicantRequest applicantInfo) throws RequestException {
        //证件有效期
        if (AssertUtils.isNotEmpty(applicantInfo.getIdExpDateFormat())) {
            AssertUtils.isDateFormat(log, applicantInfo.getIdExpDateFormat(), DateUtils.FORMATE3);
            applicantInfo.setIdExpDate(DateUtils.stringToTime(applicantInfo.getIdExpDateFormat(), DateUtils.FORMATE3));
        }
        if (AssertUtils.isNotNull(applicantInfo.getIdExpDate())) {
            AssertUtils.isDateTimestamp(log, applicantInfo.getIdExpDate());
            // 1.保单复核去掉证件有效期的检验
        }
        if (AssertUtils.isNotEmpty(applicantInfo.getMobile())) {
            AssertUtils.isNotPureDigital(log, applicantInfo.getMobile(), ApplyErrorConfigEnum.APPLY_APP_APPLICANT_MOBILE_FORMAT_ERROR);
        }

        if (AssertUtils.isNotEmpty(applicantInfo.getZipCode())) {
            AssertUtils.isNotPureDigital(log, applicantInfo.getZipCode(), ApplyErrorConfigEnum.APPLY_APP_APPLICANT_HOME_ZIP_CODE_FORMAT_ERROR);
        }
        if (AssertUtils.isNotEmpty(applicantInfo.getHomePhone())) {
            AssertUtils.isNotPureDigital(log, applicantInfo.getHomePhone(), ApplyErrorConfigEnum.APPLY_APP_APPLICANT_HOME_MOBILE_FORMAT_ERROR);
        }
        //验证邮箱
        if (AssertUtils.isNotEmpty(applicantInfo.getEmail())) {
            AssertUtils.isEmail(log, applicantInfo.getEmail(), ApplyErrorConfigEnum.APPLY_APP_APPLICANT_EMAIL_FORMAT_ERROR);
        }
    }

    public void validParameterInsured(PolicyReviewSaveRequest policyReviewSaveRequest) {
        //被保人信息
        List<PolicyReviewInsuredRequest> listInsuredInfo = policyReviewSaveRequest.getListInsured();
        AssertUtils.isNotEmpty(log, listInsuredInfo, ApplyErrorConfigEnum.APPLY_INPUT_INSURED_INFO_IS_NOT_NULL);
        String policyId = policyReviewSaveRequest.getPolicyId();
        boolean is20A = ProductTermEnum.PRODUCT.PRODUCT_20A.id().equals(policyReviewTransData.getMainProductId(policyId));
        // 查询保单基本信息
        PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);
        AssertUtils.isNotNull(this.getLogger(), policyPo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
        boolean scFlag = AssertUtils.isNotEmpty(policyPo.getActivationCode());
        listInsuredInfo.forEach(insuredInfo -> {
            AssertUtils.isNotNull(log, insuredInfo, ApplyErrorConfigEnum.APPLY_INPUT_INSURED_INFO_IS_NOT_NULL);
            //被保人
            if (!insuredInfo.getRelationship().equals(PolicyTermEnum.RELATIONSHIP_WITH_THE_INSURED.ONESELF.name())) {
                AssertUtils.isNotEmpty(log, insuredInfo.getName(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_NAME_IS_NOT_NULL);
                AssertUtils.isNotEmpty(log, insuredInfo.getIdType(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_ID_TYPE_IS_NOT_NULL);
                AssertUtils.isNotEmpty(log, insuredInfo.getIdNo(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_ID_NO_IS_NOT_NULL);
                if (!is20A && !scFlag) {
                    AssertUtils.isNotEmpty(log, insuredInfo.getIncome(), ApplyErrorConfigEnum.APPLY_APP_INSURED_INCOME_IS_NOT_NULL);
                }
                AssertUtils.isNotEmpty(log, insuredInfo.getAddressType(), ApplyErrorConfigEnum.APPLY_APP_ADDRESS_TYPE_IS_NOT_NULL);
                //校验微信和facebook和其他必选字段
                AssertUtils.isNotEmpty(log, insuredInfo.getNationality(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_NATIVE_PLACE_IS_NOT_NULL);
                AssertUtils.isNotEmpty(log, insuredInfo.getHomeAddress(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_HOME_ADDRESS_IS_NOT_NULL);
                //被保人数据格式验证
                validParameterInsuredFormat(insuredInfo);
            }

            ResultObject<ProductDetailedInfoResponse> baseApplyProductInfo = baseApplyApi.getBaseApplyProductInfo(policyReviewSaveRequest.getApplyId());
            if (!AssertUtils.isResultObjectDataNull(baseApplyProductInfo)) {
                ProductDetailedInfoResponse productInfoData = baseApplyProductInfo.getData();
                //受益人不可为法定时必须录入受益人
                if (TerminologyConfigEnum.WHETHER.NO.name().equals(productInfoData.getLegalBeneficialFlag())) {
                    AssertUtils.isNotEmpty(log, insuredInfo.getListPolicyBeneficiary(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_IS_NOT_NULL);
                }

//                //再保产品验证职业性质
//                if (TerminologyConfigEnum.WHETHER.YES.name().equals(productInfoData.getReinsuranceFlag())) {
//                    this.validOccupationNature(insuredInfo.getOccupationNature());
//                }

                //银保产品验证推荐信息
                if (TerminologyConfigEnum.WHETHER.YES.name().equals(productInfoData.getReferralInfoFlag())) {
                    this.validReferralInfo(policyReviewSaveRequest.getReferralInfo());
                } else {
                    policyReviewSaveRequest.setReferralInfo(null);
                }
            }
            //验证受益人数据格式
            if (AssertUtils.isNotEmpty(insuredInfo.getListPolicyBeneficiary())) {
                validParameterBeneficialFormat(insuredInfo.getListPolicyBeneficiary(), is20A, scFlag);
            }
        });
    }

    private void validReferralInfo(PolicyReferralInfoRequest referralInfo) {
        AssertUtils.isNotNull(log, referralInfo, APPLY_REFERRAL_INFO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, referralInfo.getReferralSources(), APPLY_SOURCES_OF_REFERRAL_IS_NOT_NULL);
        //推荐类型选择银行需选择分行并填写推荐人姓名及推荐人岗位
        if (PolicyTermEnum.REFERRAL_SOURCES.BANK.name().equals(referralInfo.getReferralSources())) {
            AssertUtils.isNotEmpty(log, referralInfo.getBankBranchId(), APPLY_BANK_BRANCH_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, referralInfo.getIntroducerName(), APPLY_INTRODUCER_NAME_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, referralInfo.getIntroducerPosition(), APPLY_INTRODUCER_POSITION_IS_NOT_NULL);
        }
    }

    public void validParameterInsuredFormat(PolicyReviewInsuredRequest insuredInfo) {

        //证件有效期
        if (AssertUtils.isNotEmpty(insuredInfo.getIdExpDateFormat())) {
            AssertUtils.isDateFormat(log, insuredInfo.getIdExpDateFormat(), DateUtils.FORMATE3);
            insuredInfo.setIdExpDate(DateUtils.stringToTime(insuredInfo.getIdExpDateFormat(), DateUtils.FORMATE3));
        }
        if (AssertUtils.isNotNull(insuredInfo.getIdExpDate())) {
            AssertUtils.isDateTimestamp(log, insuredInfo.getIdExpDate());
            if (insuredInfo.getIdExpDate() <= DateUtils.getCurrentDateToTime()) {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_APPLY_ID_EXP_DATE_ERROR);
            }
        }

        if (AssertUtils.isNotEmpty(insuredInfo.getMobile())) {
            AssertUtils.isNotPureDigital(log, insuredInfo.getMobile(), ApplyErrorConfigEnum.APPLY_APP_INSURED_MOBILE_FORMAT_ERROR);
        }

        if (AssertUtils.isNotEmpty(insuredInfo.getZipCode())) {
            AssertUtils.isNotPureDigital(log, insuredInfo.getZipCode(), ApplyErrorConfigEnum.APPLY_APP_INSURED_HOME_ZIP_CODE_FORMAT_ERROR);
        }

        if (AssertUtils.isNotEmpty(insuredInfo.getHomePhone())) {
            AssertUtils.isNotPureDigital(log, insuredInfo.getHomePhone(), ApplyErrorConfigEnum.APPLY_APP_INSURED_HOME_MOBILE_FORMAT_ERROR);
        }

        //验证邮箱
        if (AssertUtils.isNotEmpty(insuredInfo.getEmail())) {
            AssertUtils.isEmail(log, insuredInfo.getEmail(), ApplyErrorConfigEnum.APPLY_APP_INSURED__EMAIL_FORMAT_ERROR);
        }
    }

    private void validParameterBeneficialFormat(List<PolicyReviewBeneficiaryInfoRequest> listBeneficiaryInfo, boolean is20A, boolean scFlag) {
        //校验受益人顺序
        List<String> beneficiaryNoOrderList = listBeneficiaryInfo.stream().map(PolicyReviewBeneficiaryInfoRequest::getBeneficiaryNoOrder).distinct().collect(Collectors.toList());
        AssertUtils.isNotEmpty(log, beneficiaryNoOrderList, ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_IS_NOT_NULL);
        if (!beneficiaryNoOrderList.contains(PolicyTermEnum.BENEFICIARY_NO.ORDER_ONE.name())) {
//            //机构默认第一顺序,无需校验
//            if (!AssertUtils.isNotNull(policyReviewSaveRequest.getLoanContract())) {
//                throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_BENEFICIAL_NO_ERROR);
//            }
        }
        //过滤掉第一
        List<String> beneficiaryNoList = beneficiaryNoOrderList.stream().filter(s -> !PolicyTermEnum.BENEFICIARY_NO.ORDER_ONE.name().equals(s)).collect(Collectors.toList());
        //如果集合不为空，则判断是否存在第二，如果不存在第二，则顺位选择有误
        if (AssertUtils.isNotEmpty(beneficiaryNoList) && !beneficiaryNoList.contains(PolicyTermEnum.BENEFICIARY_NO.ORDER_TWO.name())) {
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_BENEFICIAL_NO_ERROR);
        }

        listBeneficiaryInfo.forEach(beneficiaryInfoRequest -> {
            AssertUtils.isNotEmpty(log, beneficiaryInfoRequest.getBeneficiaryNoOrder(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_NO_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, beneficiaryInfoRequest.getName(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_NAME_IS_NOT_NULL);
            if (!is20A && !scFlag) {
                AssertUtils.isNotEmpty(log, beneficiaryInfoRequest.getSex(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_SEX_IS_NOT_NULL);
            }
            //受益人个险填写取消必填项：出生日期、证件类型、证件号码
            if (AssertUtils.isNotEmpty(beneficiaryInfoRequest.getBirthdayFormat())) {
                AssertUtils.isDateFormat(log, beneficiaryInfoRequest.getBirthdayFormat(), DateUtils.FORMATE3);
                beneficiaryInfoRequest.setBirthday(DateUtils.stringToTime(beneficiaryInfoRequest.getBirthdayFormat(), DateUtils.FORMATE3) + "");
            }
            AssertUtils.isNotEmpty(log, beneficiaryInfoRequest.getBeneficiaryProportion(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_PROPORTION_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, beneficiaryInfoRequest.getRelationship(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_RELATIONSHIP_IS_NOT_NULL);

            if (PolicyTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER.name().equals(beneficiaryInfoRequest.getRelationship())) {
                AssertUtils.isNotEmpty(log, beneficiaryInfoRequest.getRelationshipInstructions(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_RELATIONSHIP_INSTRUCTIONS_IS_NOT_NULL);
            }
        });
        //校验各受益人顺位总份额
        Map<String, List<PolicyReviewBeneficiaryInfoRequest>> map = listBeneficiaryInfo.stream().collect(Collectors.groupingBy(PolicyReviewBeneficiaryInfoRequest::getBeneficiaryNoOrder));
        map.forEach((s, beneficiaryInfoRequests) -> {
            final BigDecimal[] totalProportion = new BigDecimal[1];
            final Boolean[] beneficiaryFlag = new Boolean[1];
            beneficiaryFlag[0] = true;
            beneficiaryInfoRequests.forEach(beneficiaryInfoRequest -> {
                if (!AssertUtils.isNotEmpty(beneficiaryInfoRequest.getRelationship()) & !AssertUtils.isNotEmpty(beneficiaryInfoRequest.getBeneficiaryProportion())
                        & !AssertUtils.isNotEmpty(beneficiaryInfoRequest.getBirthday()) & !AssertUtils.isNotEmpty(beneficiaryInfoRequest.getIdType()) & !AssertUtils.isNotEmpty(beneficiaryInfoRequest.getSex())
                        & !AssertUtils.isNotEmpty(beneficiaryInfoRequest.getIdNo()) && !AssertUtils.isNotEmpty(beneficiaryInfoRequest.getName())) {
                    beneficiaryFlag[0] = false;
                    return;
                }
                AssertUtils.isNotPureDigital(log, beneficiaryInfoRequest.getBeneficiaryProportion(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_PROPORTION_FORMAT_ERROR);
                //验证收益份额
                BigDecimal proportion = new BigDecimal(beneficiaryInfoRequest.getBeneficiaryProportion());
                if (proportion.compareTo(new BigDecimal(0)) == -1 || proportion.compareTo(new BigDecimal(100)) == 1) {
                    throw new RequestException(ApplyErrorConfigEnum.APPLY_APPLY_BENEFICIARY_PROPORTION_ERROR);
                }
                totalProportion[0] = (AssertUtils.isNotNull(totalProportion[0]) ? totalProportion[0] : new BigDecimal(0)).add(proportion);
            });
            if (beneficiaryFlag[0]) {
                if (totalProportion[0].compareTo(new BigDecimal(100)) != 0) {
                    throw new RequestException(ApplyErrorConfigEnum.APPLY_BENEFICIARY_PROPORTION_ERROR);
                }
            }
        });
    }


    public void validParameterAccount(PolicyReviewSaveRequest policyReviewSaveRequest) throws RequestException {
        //投保单账户信息
        List<PolicyReviewAccountRequest> listApplyAccountInfo = policyReviewSaveRequest.getListPolicyAccount();

        if (AssertUtils.isNotEmpty(listApplyAccountInfo)) {
            listApplyAccountInfo.forEach(policyAccountRequest -> {
                if (AssertUtils.isNotNull(policyAccountRequest) && !AssertUtils.checkObjFieldAllIsNull(policyAccountRequest)) {
                    if (!PolicyTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(policyAccountRequest.getInitialPaymentMode())
                            && !PolicyTermEnum.PAYMENT_METHODS.BANK_DEDUCT.name().equals(policyAccountRequest.getInitialPaymentMode())) {
                        return;
                    }
                    AssertUtils.isNotEmpty(log, policyAccountRequest.getAccountNo(), ApplyErrorConfigEnum.APPLY_INPUT_ACCOUNT_NO_IS_NOT_NULL);
                    AssertUtils.isNotEmpty(log, policyAccountRequest.getBankCode(), ApplyErrorConfigEnum.APPLY_INPUT_ACCOUNT_BANK_CODE_IS_NOT_NULL);
                    AssertUtils.isNotEmpty(log, policyAccountRequest.getAccountOwner(), ApplyErrorConfigEnum.APPLY_INPUT_ACCOUNT_OWNER_IS_NOT_NULL);
                    AssertUtils.isNotDigital(log, policyAccountRequest.getAccountNo(), ApplyErrorConfigEnum.APPLY_INPUT_ACCOUNT_NO_FORMAT_ERROR);
                }
            });
        }
    }

    public void validParameterContactInfo(PolicyReviewSaveRequest policyReviewSaveRequest) {
        PolicyContactInfoVo policyContact = policyReviewSaveRequest.getPolicyContact();
        if (AssertUtils.isNotNull(policyContact)) {
            if (AssertUtils.isNotEmpty(policyContact.getContractPhone())) {
                AssertUtils.isNotPureDigital(log, policyContact.getContractPhone(), ApplyErrorConfigEnum.APPLY_CONTRACT_PHONE_FORMAT_ERROR);
            }

            if (AssertUtils.isNotEmpty(policyContact.getContractMobile())) {
                AssertUtils.isNotPureDigital(log, policyContact.getContractMobile(), ApplyErrorConfigEnum.APPLY_CONTRACT_MOBILE_FORMAT_ERROR);
            }
        }
    }

    public void validParameterCustomerAndAgent(CustomerBusinessRequest customerBusinessRequest, AgentBaseResponse agentBaseResponse) {
        if (!AssertUtils.isNotNull(agentBaseResponse)) {
            return;
        }
        AgentIdentityBaseResponse agentIdentityBaseRespFc = agentBaseResponse.getAgentIdentity();
        AssertUtils.isNotNull(log, agentBaseResponse.getAgentDetail(), ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_DETAIL_IS_NOT_FOUND_OBJECT);
        AssertUtils.isNotNull(log, agentIdentityBaseRespFc, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IDENTITY_IS_NOT_FOUND_OBJECT);
        if (AssertUtils.isNotEmpty(agentBaseResponse.getAgentDetail().getMobile()) && AssertUtils.isNotEmpty(customerBusinessRequest.getMobile()) &&
                customerBusinessRequest.getMobile().replaceAll("\\s*", "").equals(agentBaseResponse.getAgentDetail().getMobile().replaceAll("\\s*", ""))) {
            //客户与代理人手机号相同，其他信息也需要相同
            if (!agentIdentityBaseRespFc.getName().replaceAll("\\s*", "").equalsIgnoreCase(customerBusinessRequest.getName().replaceAll("\\s*", "")) || !agentIdentityBaseRespFc.getIdTypeCode().equals(customerBusinessRequest.getIdType())
                    || !agentIdentityBaseRespFc.getIdNo().replaceAll("\\s*", "").equals(customerBusinessRequest.getIdNo().replaceAll("\\s*", ""))) {
                throwsException(log, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_AGENT_MOBILE_SAME_ERROR);
            }
        }
        if (AssertUtils.isNotEmpty(agentIdentityBaseRespFc.getIdTypeCode()) && AssertUtils.isNotEmpty(agentIdentityBaseRespFc.getIdNo())
                && agentIdentityBaseRespFc.getIdTypeCode().equals(customerBusinessRequest.getIdType()) && agentIdentityBaseRespFc.getIdNo().replaceAll("\\s*", "").equals(customerBusinessRequest.getIdNo().replaceAll("\\s*", ""))) {
            if (!customerBusinessRequest.getName().replaceAll("\\s*", "").equalsIgnoreCase(agentIdentityBaseRespFc.getName().replaceAll("\\s*", "")) || (AssertUtils.isNotEmpty(customerBusinessRequest.getMobile()) && !customerBusinessRequest.getMobile().replaceAll("\\s*", "").equals(agentBaseResponse.getAgentDetail().getMobile().replaceAll("\\s*", "")))) {
                throwsException(log, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_AGENT_MOBILE_SAME_ERROR);
            }
        }
    }

    public void validPolicyCustomerMerger(PolicyCustomerMergerRequest policyCustomerMergerRequest, String type) {
        AssertUtils.isNotNull(log, policyCustomerMergerRequest.getPolicyId(), PolicyErrorConfigEnum.POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL);
        PolicyCustomerMergerDetailRequest mergerApplicant = policyCustomerMergerRequest.getMergerApplicant();
        if (AssertUtils.isNotNull(mergerApplicant)) {
            if ("SUBMIT".equals(type)) {
                AssertUtils.isNotEmpty(log, mergerApplicant.getMergerConclusion(), ApplyErrorConfigEnum.APPLY_APPLICANT_MERGER_CONCLUSION_IS_NOT_NULL);
            }
            if ("SAME_CUSTOMER".equals(mergerApplicant.getMergerConclusion())) {
                if (!AssertUtils.isNotEmpty(mergerApplicant.getDataCalibrationCustomerId())) {
                    throwsException(log, com.gclife.common.error.ApplyErrorConfigEnum.APPLY_PLEASE_CHECK_THE_CUSTOMER_ERROR);
                }
                if (!AssertUtils.isNotEmpty(mergerApplicant.getCurrentPolicyFlag())) {
                    throwsException(log, com.gclife.common.error.ApplyErrorConfigEnum.APPLY_APPLICANT_DATA_CALIBRATION_IS_NOT_NULL);
                }
            }
        }

        PolicyCustomerMergerDetailRequest mergerInsured = policyCustomerMergerRequest.getMergerInsured();
        if (AssertUtils.isNotNull(mergerInsured)) {
            if ("SUBMIT".equals(type)) {
                AssertUtils.isNotEmpty(log, mergerInsured.getMergerConclusion(), ApplyErrorConfigEnum.APPLY_INSURED_MERGER_CONCLUSION_IS_NOT_NULL);
            }
            if ("SAME_CUSTOMER".equals(mergerInsured.getMergerConclusion())) {
                if (!AssertUtils.isNotEmpty(mergerInsured.getDataCalibrationCustomerId())) {
                    throwsException(log, com.gclife.common.error.ApplyErrorConfigEnum.APPLY_PLEASE_CHECK_THE_CUSTOMER_ERROR);
                }
                if (!AssertUtils.isNotEmpty(mergerInsured.getCurrentPolicyFlag())) {
                    throwsException(log, com.gclife.common.error.ApplyErrorConfigEnum.APPLY_INSURED_DATA_CALIBRATION_IS_NOT_NULL);
                }
            }
        }
    }

    public void validPolicyHolder(PolicyReviewSaveRequest policyReviewSaveRequest) {
        PolicyHolderRequest holderRequest = policyReviewSaveRequest.getHolder();
        if (!AssertUtils.isNotNull(holderRequest) || !AssertUtils.isNotEmpty(holderRequest.getRelationship())) {
            return;
        }
        AssertUtils.isNotEmpty(log, holderRequest.getRelationship(), ApplyErrorConfigEnum.APPLY_HOLDER_RELATIONSHIP_IS_NOT_NULL);
        if (PolicyTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER.name().equals(holderRequest.getRelationship())) {
            AssertUtils.isNotEmpty(log, holderRequest.getRelationshipInstructions(), APPLY_APP_BENEFICIAL_RELATIONSHIP_INSTRUCTIONS_IS_NOT_NULL);
        }
        AssertUtils.isNotEmpty(log, holderRequest.getName(), ApplyErrorConfigEnum.AGENT_AGENT_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, holderRequest.getBirthdayFormat1(), AGENT_AGENT_BIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, holderRequest.getSex(), AGENT_ID_SEX_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, holderRequest.getIdType(), AGENT_ID_TYPE_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, holderRequest.getIdNo(), AGENT_AGENT_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, holderRequest.getIdExpDateFormat(), AGENT_VALIDITY_OF_CERTIFICATE_IS_NOT_NULL);
        long birthday = DateUtils.stringToTime(holderRequest.getBirthdayFormat1(), DateUtils.FORMATE3);
        if (StringUtil.getAgeByBirthday(birthday) < 18) {
            throw new RequestException(ApplyErrorConfigEnum.APPLY_HOLDER_IS_NOT_ADULT);
        }

        // 查询投保人
        PolicyApplicantBo policyApplicantBo = policyBaseService.queryPolicyApplicant(policyReviewSaveRequest.getPolicyId());
        AssertUtils.isNotNull(getLogger(), policyApplicantBo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_APPLICANT_IS_NOT_FOUND_OBJECT);

        PolicyReviewApplicantRequest applicant = policyReviewSaveRequest.getApplicant();
        // 保单持有人与投保人为同一人时，弹窗提示：“投保人已默认为保单持有人，请重新录入或跳过”
        if (applicant.getName().trim().equalsIgnoreCase(holderRequest.getName().trim())
                && DateUtils.timeToTimeLow(policyApplicantBo.getBirthday()) == DateUtils.timeToTimeLow(birthday)
                && policyApplicantBo.getSex().equals(holderRequest.getSex())
                && applicant.getIdNo().trim().equalsIgnoreCase(holderRequest.getIdNo().trim())
        ) {
            throw new RequestException(ApplyErrorConfigEnum.APPLY_HOLDER_IS_REPEAT);
        }
    }
}
