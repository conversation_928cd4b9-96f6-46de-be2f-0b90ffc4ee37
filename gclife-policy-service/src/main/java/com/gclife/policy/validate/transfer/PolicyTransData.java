package com.gclife.policy.validate.transfer;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.api.ApplyAcceptApi;
import com.gclife.apply.model.respone.AcceptUserInfoResponse;
import com.gclife.apply.model.respone.ApplyHolderResponse;
import com.gclife.apply.model.respone.ApplyLoanBeneficiaryResponse;
import com.gclife.attachment.api.AttachmentPDFDocumentApi;
import com.gclife.attachment.model.policy.policy.ProductCashValueBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.function.GcFactorFunction;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.BaseTermEnum;
import com.gclife.common.model.config.InternationalTypeEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.platform.api.PlatformAreaApi;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.AreaNameResponse;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.model.bo.PolicyQueryListBo;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.response.*;
import com.gclife.policy.service.base.*;
import com.gclife.policy.service.business.PolicyHookBusinessService;
import com.gclife.policy.service.data.PolicyBoService;
import com.gclife.policy.transform.LanguageUtils;
import com.gclife.product.api.ProductApi;
import com.gclife.product.api.ProviderApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.calculate.ApplyRequest;
import com.gclife.product.model.response.ProviderMiddleResponse;
import com.gclife.product.model.response.insurnce.policy.PolicyCashValueResponse;
import com.gclife.renewal.api.RenewalInsuranceApi;
import com.gclife.renewal.api.RenewalPaymentApi;
import com.gclife.renewal.model.response.RenewalAppDetailResponse;
import com.gclife.renewal.model.response.RenewalInsuranceAppDetailResponse;
import com.gclife.renewal.model.vo.RenewalCoverageVo;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.gclife.policy.model.config.PolicyTermEnum.ATTACHMENT_TYPE_FLAG.POLICY_CONFIRM;

/**
 * <AUTHOR>
 * create 17-11-14
 * description:
 */
@Component
public class PolicyTransData extends BaseBusinessServiceImpl {

    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private PlatformAreaApi platformAreaApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private LanguageCodeTransData languageCodeTransData;
    @Autowired
    private AttachmentPDFDocumentApi attachmentPDFDocumentApi;
    @Autowired
    private PolicyBoService policyBoService;
    @Autowired
    private PolicyBaseService policyBaseService;
    @Autowired
    private RenewalInsuranceApi renewalInsuranceApi;
    @Autowired
    PolicyHistoryBaseService policyHistoryBaseService;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private ProviderApi providerApi;
    @Autowired
    private ApplyAcceptApi applyAcceptApi;
    @Autowired
    private PolicyHookBusinessService policyHookBusinessService;
    @Autowired
    private PolicyPremiumBaseService policyPremiumBaseService;
    @Autowired
    private PolicyCoverageBaseService policyCoverageBaseService;
    @Autowired
    private RenewalPaymentApi renewalPaymentApi;
    @Autowired
    private PolicyLoanBaseService policyLoanBaseService;
    @Autowired
    private PolicyReferralInfoBaseService policyReferralInfoBaseService;
    @Autowired
    private PolicyOtherInfoService policyOtherInfoService;
    @Autowired
    private PolicyReviewTransData policyReviewTransData;

    public static final DecimalFormat decimalFormat = new DecimalFormat("###,###,###,###,##0");


    /**
     * 转换险种缴费周期为年缴
     * @param policyCoveragePos 险种
     */
    public void transPremiumFrequency(List<PolicyCoveragePo> policyCoveragePos) {
        policyCoveragePos.forEach(policyCoveragePo -> {
            BigDecimal factor = new BigDecimal(PolicyTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(policyCoveragePo.getPremiumFrequency()).value());
            policyCoveragePo.setPremium(policyCoveragePo.getPremium().divide(factor, 2, BigDecimal.ROUND_HALF_UP));
            policyCoveragePo.setOriginalPremium(policyCoveragePo.getOriginalPremium().divide(factor, 2, BigDecimal.ROUND_HALF_UP));
            policyCoveragePo.setTotalPremium(policyCoveragePo.getTotalPremium().divide(factor, 2, BigDecimal.ROUND_HALF_UP));
            policyCoveragePo.setPremiumFrequency(PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name());
        });
    }

    /**
     * 是否需要缴费
     * @param policyCoveragePo 险种
     * @param mainCoveragePo 主险
     * @param nextReceivableDate 下期应缴日期
     * @return
     */
    public boolean isNeedToPay(PolicyCoveragePo policyCoveragePo, PolicyCoveragePo mainCoveragePo, long nextReceivableDate) {
        if (TerminologyConfigEnum.WHETHER.NO.name().equals(policyCoveragePo.getRenewalPermitFlag())) {
            return false;
        }
        if (PolicyTermEnum.COVERAGE_STATUS.EXPIRED.name().equals(policyCoveragePo.getCoverageStatus())
                || PolicyTermEnum.COVERAGE_STATUS.INDEMNITY_TERMINATION.name().equals(policyCoveragePo.getCoverageStatus())) {
            return false;
        }
        // 应缴月份与承保日期间隔月份
        int months = DateUtils.getMonthSpace(DateUtils.timeStrToString(mainCoveragePo.getEffectiveDate(), DateUtils.FORMATE16), DateUtils.getTimeYearMonth(nextReceivableDate));
        if (months % 12 != 0) {
            if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyCoveragePo.getPremiumFrequency())) {
                return false;
            }
            if ("1".equals(policyCoveragePo.getPremiumPeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoveragePo.getPremiumFrequency())) {
                return false;
            }
        }
        if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyCoveragePo.getPremiumFrequency()) ||
                ("1".equals(policyCoveragePo.getPremiumPeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoveragePo.getPremiumPeriodUnit()))) {
            // 短期险
            return nextReceivableDate <= mainCoveragePo.getMaturityDate() && nextReceivableDate >= policyCoveragePo.getEffectiveDate();
        } else {
            // 获取险种缴至日期
            long paymentCompleteDate = DateUtils.timeToTimeTop(this.getPaymentCompleteDate(
                    mainCoveragePo.getEffectiveDate(), policyCoveragePo.getPremiumPeriod(), mainCoveragePo.getPremiumFrequency()));
            return nextReceivableDate <= paymentCompleteDate && nextReceivableDate >= policyCoveragePo.getEffectiveDate();
        }
    }

    /**
     * 获取保单缴至日期
     *
     * @param effectiveDate    保单生效日期
     * @param premiumPeriod    缴费年期
     * @param premiumFrequency 缴费周期
     * @return
     */
    public Long getPaymentCompleteDate(Long effectiveDate, String premiumPeriod, String premiumFrequency) {
        // 缴至日期 = 保单生效日期 + 缴费年期 - 缴费周期对应月份值
        long time = DateUtils.addStringYearsRT(effectiveDate, Integer.parseInt(premiumPeriod));
        int frequencyValue = PolicyTermEnum.FREQUENCY_VALUE.valueOf(premiumFrequency).value();
        return DateUtils.timeToTimeLow(DateUtils.addStringMonthRT(time, -frequencyValue));
    }

    /**
     * 备份保单
     *
     * @param policyBo          保单数据
     * @param dataEffectiveDate 数据生效日期
     */
    public String backupPolicyData(PolicyBo policyBo, Long dataEffectiveDate) {
        return backupPolicyData(policyBo, dataEffectiveDate, null);
    }

    /**
     * 备份保单
     *
     * @param policyBo          保单数据
     * @param dataEffectiveDate 数据生效日期
     */
    public String backupPolicyData(PolicyBo policyBo, Long dataEffectiveDate, String policyStatus) {
        AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT);
        // 旧版本号
        String oldVersionNo = policyBo.getVersionNo();
        // 备份保单信息
        savePolicyBaseHistoryData(policyBo);

        // 产生新保单版本
        String newVersionNo = DateUtils.getJobNumberByTime("", "", DateUtils.FORMATE53, false);
        policyBo.setVersionNo(newVersionNo);
        policyBo.setDataEffectiveDate(AssertUtils.isNotNull(dataEffectiveDate) ? dataEffectiveDate : DateUtils.getCurrentTime());
        if (AssertUtils.isNotEmpty(policyStatus)) {
            policyBo.setPolicyStatus(policyStatus);
        }
        // 更新保单信息
        policyBaseService.savePolicyPo(policyBo);

        return oldVersionNo;
    }

    /**
     * 备份保单信息至历史表
     *
     * @param policyBo 保单信息
     */
    public void savePolicyBaseHistoryData(PolicyBo policyBo) {
        try {
            //保单基础
            synchronizePolicyHistory(policyBo);
            //保单代理人
            synchronizePolicyAgentHistory(policyBo);
            //保单投保人
            synchronizePolicyApplicantHistory(policyBo);
            //保单被保人
            synchronizePolicyInsuredHistory(policyBo);
            //保单被保人统计
            synchronizePolicyInsuredCollectHistory(policyBo);
            //保单付费人
            synchronizePolicyPayorInfoHistory(policyBo);
            //保单保费
            synchronizePolicyPremiumHistory(policyBo);
            //保单缴费
            synchronizePolicyPaymentHistory(policyBo);
            //保单被保人扩展
            synchronizePolicyInsuredExtendHistory(policyBo);
            //保单加费
            synchronizePolicyAddPremiumHistory(policyBo);
            //保单打印
            synchronizePolicyPrintHistory(policyBo);
            //保单回执
            synchronizePolicyReceiptInfoHistory(policyBo);
            //保单账户
            synchronizePolicyAccountHistory(policyBo);
            //保单附件
            synchronizePolicyAttachmentHistory(policyBo);
            //保单附件
            synchronizePolicySpecialContractHistory(policyBo);
        } catch (RequestException e) {
            e.printStackTrace();
            throwsException(getLogger(), e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_BASE_HISTORY_DATA_ERROR);
        }
    }

    private void synchronizePolicyHistory(PolicyBo policyBo) {
        PolicyHistoryPo policyHistoryPo = (PolicyHistoryPo) converterObject(policyBo, PolicyHistoryPo.class);
        policyHistoryBaseService.savePolicyHistoryPo(policyHistoryPo);
    }

    private void synchronizePolicyAgentHistory(PolicyBo policyBo) {
        PolicyAgentHistoryPo policyAgentHistoryPo = (PolicyAgentHistoryPo) converterObject(policyBo.getPolicyAgent(), PolicyAgentHistoryPo.class);
        policyAgentHistoryPo.setVersionNo(policyBo.getVersionNo());
        policyHistoryBaseService.savePolicyAgentHistoryPo(policyAgentHistoryPo);
    }

    private void synchronizePolicyApplicantHistory(PolicyBo policyBo) {
        PolicyApplicantHistoryPo policyApplicantHistoryPo = (PolicyApplicantHistoryPo) converterObject(policyBo.getPolicyApplicant(), PolicyApplicantHistoryPo.class);
        policyApplicantHistoryPo.setVersionNo(policyBo.getVersionNo());
        policyHistoryBaseService.savePolicyApplicantHistoryPo(policyApplicantHistoryPo);
    }

    private void synchronizePolicyInsuredHistory(PolicyBo policyBo) {
        // 险种
        List<PolicyCoverageBo> policyCoverageBos = new ArrayList<>();
        // 受益信息
        List<PolicyBeneficiaryInfoBo> beneficiaryInfoBos = new ArrayList<>();

        List<PolicyInsuredHistoryPo> insuredHistoryPos = new ArrayList<>();
        policyBo.getListPolicyInsured().forEach(policyInsuredBo -> {
            //保存被保人
            PolicyInsuredHistoryPo policyInsuredHistoryPo = (PolicyInsuredHistoryPo) converterObject(policyInsuredBo, PolicyInsuredHistoryPo.class);
            policyInsuredHistoryPo.setVersionNo(policyBo.getVersionNo());
            insuredHistoryPos.add(policyInsuredHistoryPo);
            //受益人相关
            if (AssertUtils.isNotEmpty(policyInsuredBo.getListPolicyBeneficiary())) {
                beneficiaryInfoBos.addAll(policyInsuredBo.getListPolicyBeneficiary());
            }

            //险种相关
            if (AssertUtils.isNotEmpty(policyInsuredBo.getListPolicyCoverage())) {
                policyCoverageBos.addAll(policyInsuredBo.getListPolicyCoverage());
            }
        });
        policyHistoryBaseService.addPolicyInsuredHistoryPo(insuredHistoryPos);
        // 受益信息
        synchronizePolicyBeneficiaryInfoHistory(beneficiaryInfoBos, policyBo);
        // 险种
        synchronizePolicyCoverageHistory(policyCoverageBos, policyBo);
    }

    private void synchronizePolicyBeneficiaryInfoHistory(List<PolicyBeneficiaryInfoBo> beneficiaryInfoBos, PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(beneficiaryInfoBos)) {
            return;
        }
        // 受益人信息
        List<PolicyBeneficiaryBo> policyBeneficiaryBos = new ArrayList<>();

        List<PolicyBeneficiaryInfoHistoryPo> beneficiaryInfoHistoryPos = new ArrayList<>();
        beneficiaryInfoBos.forEach(policyBeneficiaryInfoBo -> {
            PolicyBeneficiaryInfoHistoryPo policyBeneficiaryInfoHistoryPo = (PolicyBeneficiaryInfoHistoryPo) converterObject(policyBeneficiaryInfoBo, PolicyBeneficiaryInfoHistoryPo.class);
            policyBeneficiaryInfoHistoryPo.setVersionNo(policyBo.getVersionNo());
            beneficiaryInfoHistoryPos.add(policyBeneficiaryInfoHistoryPo);
            //受益人
            policyBeneficiaryBos.add(policyBeneficiaryInfoBo.getPolicyBeneficiary());
        });
        policyHistoryBaseService.addPolicyBeneficiaryInfoHistoryPo(beneficiaryInfoHistoryPos);
        // 受益人
        synchronizePolicyBeneficiaryHistory(policyBeneficiaryBos, policyBo);
    }

    private void synchronizePolicyBeneficiaryHistory(List<PolicyBeneficiaryBo> policyBeneficiaryBos, PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(policyBeneficiaryBos)) {
            return;
        }
        List<PolicyBeneficiaryHistoryPo> beneficiaryHistoryPos = new ArrayList<>();
        policyBeneficiaryBos.forEach(policyBeneficiaryBo -> {
            PolicyBeneficiaryHistoryPo policyBeneficiaryHistoryPo = (PolicyBeneficiaryHistoryPo) converterObject(policyBeneficiaryBo, PolicyBeneficiaryHistoryPo.class);
            policyBeneficiaryHistoryPo.setVersionNo(policyBo.getVersionNo());
            beneficiaryHistoryPos.add(policyBeneficiaryHistoryPo);
        });
        policyHistoryBaseService.addPolicyBeneficiaryHistoryPo(beneficiaryHistoryPos);
    }


    private void synchronizePolicyCoverageHistory(List<PolicyCoverageBo> policyCoverageBos, PolicyBo policyBo) {
        // 险种责任
        List<PolicyCoverageDutyBo> coverageDutyBos = new ArrayList<>();
        // 险种档次
        List<PolicyCoverageLevelPo> coverageLevelPos = new ArrayList<>();
        // 险种保费
        List<PolicyCoveragePremiumBo> coveragePremiumBos = new ArrayList<>();
        // 险种缴费
        List<PolicyCoveragePaymentBo> coveragePaymentBos = new ArrayList<>();

        // 险种历史
        List<PolicyCoverageHistoryPo> coverageHistoryPos = new ArrayList<>();
        policyCoverageBos.forEach(coverageBo -> {
            //保存险种
            PolicyCoverageHistoryPo policyCoverageHistoryPo = (PolicyCoverageHistoryPo) converterObject(coverageBo, PolicyCoverageHistoryPo.class);
            policyCoverageHistoryPo.setVersionNo(policyBo.getVersionNo());
            coverageHistoryPos.add(policyCoverageHistoryPo);

            //险种红利
            synchronizePolicyCoverageBonusHistory(coverageBo.getPolicyCoverageBonus(), policyBo);

            if (TerminologyConfigEnum.WHETHER.YES.name().equals(coverageBo.getDutyChooseFlag()) &&
                    AssertUtils.isNotEmpty(coverageBo.getListCoverageDuty())) {
                coverageDutyBos.addAll(coverageBo.getListCoverageDuty());
                coverageBo.getListCoverageDuty().forEach(coverageDutyBo -> {
                    if (AssertUtils.isNotEmpty(coverageDutyBo.getListCoverageLevel())) {
                        coverageLevelPos.addAll(coverageDutyBo.getListCoverageLevel());
                    }
                });
            } else if (AssertUtils.isNotEmpty(coverageBo.getListCoverageLevel())) {
                coverageLevelPos.addAll(coverageBo.getListCoverageLevel());
            }

            if (AssertUtils.isNotNull(coverageBo.getPolicyCoveragePremium())) {
                coveragePremiumBos.add(coverageBo.getPolicyCoveragePremium());
            }

            if (AssertUtils.isNotEmpty(coverageBo.getListPolicyCoveragePayment())) {
                coveragePaymentBos.addAll(coverageBo.getListPolicyCoveragePayment());
            }
        });
        policyHistoryBaseService.addPolicyCoverageHistoryPo(coverageHistoryPos);

        // 险种责任
        synchronizePolicyCoverageDutyHistory(coverageDutyBos, policyBo);
        // 险种档次
        synchronizePolicyCoverageLevelHistory(coverageLevelPos, policyBo);
        // 险种保费
        synchronizePolicyCoveragePremiumHistory(coveragePremiumBos, policyBo);
        // 险种缴费
        synchronizePolicyCoveragePaymentHistory(coveragePaymentBos, policyBo);
    }


    private void synchronizePolicyCoverageBonusHistory(PolicyCoverageBonusBo policyCoverageBonus, PolicyBo policyBo) {
        if (!AssertUtils.isNotNull(policyCoverageBonus)) {
            return;
        }
        PolicyCoverageBonusHistoryPo policyCoverageBonusHistoryPo = (PolicyCoverageBonusHistoryPo) converterObject(policyCoverageBonus, PolicyCoverageBonusHistoryPo.class);
        policyCoverageBonusHistoryPo.setVersionNo(policyBo.getVersionNo());
        policyHistoryBaseService.savePolicyCoverageBonusHistoryPo(policyCoverageBonusHistoryPo);
    }


    private void synchronizePolicyCoverageDutyHistory(List<PolicyCoverageDutyBo> listPolicyCoverageDuty, PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(listPolicyCoverageDuty)) {
            return;
        }
        List<PolicyCoverageDutyHistoryPo> coverageDutyHistoryPos = new ArrayList<>();
        listPolicyCoverageDuty.forEach(coverageDutyBo -> {
            PolicyCoverageDutyHistoryPo coverageDutyHistoryPo = (PolicyCoverageDutyHistoryPo) converterObject(coverageDutyBo, PolicyCoverageDutyHistoryPo.class);
            coverageDutyHistoryPo.setVersionNo(policyBo.getVersionNo());
            coverageDutyHistoryPos.add(coverageDutyHistoryPo);
        });
        policyHistoryBaseService.addPolicyCoverageDutyHistoryPo(coverageDutyHistoryPos);
    }


    private void synchronizePolicyCoverageLevelHistory(List<PolicyCoverageLevelPo> policyCoverageLevelPos, PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(policyCoverageLevelPos)) {
            return;
        }
        List<PolicyCoverageLevelHistoryPo> coverageLevelHistoryPos = new ArrayList<>();
        policyCoverageLevelPos.forEach(coverageLevelPo -> {
            PolicyCoverageLevelHistoryPo coverageLevelHistoryPo = (PolicyCoverageLevelHistoryPo) converterObject(coverageLevelPo, PolicyCoverageLevelHistoryPo.class);
            coverageLevelHistoryPo.setVersionNo(policyBo.getVersionNo());
            coverageLevelHistoryPos.add(coverageLevelHistoryPo);
        });
        policyHistoryBaseService.addPolicyCoverageLevelHistoryPo(coverageLevelHistoryPos);
    }

    private void synchronizePolicyCoveragePremiumHistory(List<PolicyCoveragePremiumBo> policyCoveragePremiumBos, PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(policyCoveragePremiumBos)) {
            return;
        }
        List<PolicyCoveragePremiumHistoryPo> coveragePremiumHistoryPos = new ArrayList<>();
        policyCoveragePremiumBos.forEach(coveragePremiumBo -> {
            PolicyCoveragePremiumHistoryPo coveragePremiumHistoryPo = (PolicyCoveragePremiumHistoryPo) converterObject(coveragePremiumBo, PolicyCoveragePremiumHistoryPo.class);
            coveragePremiumHistoryPo.setVersionNo(policyBo.getVersionNo());
            coveragePremiumHistoryPos.add(coveragePremiumHistoryPo);
        });
        coveragePremiumHistoryPos.forEach(policyCoveragePremiumHistoryPo -> policyCoveragePremiumHistoryPo.setVersionNo(policyBo.getVersionNo()));

        policyHistoryBaseService.addPolicyCoveragePremiumHistoryPo(coveragePremiumHistoryPos);
    }


    private void synchronizePolicyCoveragePaymentHistory(List<PolicyCoveragePaymentBo> listPolicyCoveragePayment, PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(listPolicyCoveragePayment)) {
            return;
        }
        List<PolicyCoveragePaymentHistoryPo> coveragePaymentHistoryPos = new ArrayList<>();
        listPolicyCoveragePayment.forEach(policyCoveragePaymentBo -> {
            PolicyCoveragePaymentHistoryPo policyCoveragePaymentHistoryPo = (PolicyCoveragePaymentHistoryPo) converterObject(policyCoveragePaymentBo, PolicyCoveragePaymentHistoryPo.class);
            policyCoveragePaymentHistoryPo.setVersionNo(policyBo.getVersionNo());
            coveragePaymentHistoryPos.add(policyCoveragePaymentHistoryPo);
        });
        policyHistoryBaseService.addPolicyCoveragePaymentHistoryPo(coveragePaymentHistoryPos);
    }

    private void synchronizePolicyInsuredCollectHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotNull(policyBo.getPolicyInsuredCollect())) {
            return;
        }
        PolicyInsuredCollectHistoryPo policyInsuredCollectHistoryPo = (PolicyInsuredCollectHistoryPo) converterObject(policyBo.getPolicyInsuredCollect(), PolicyInsuredCollectHistoryPo.class);
        policyInsuredCollectHistoryPo.setVersionNo(policyBo.getVersionNo());
        policyHistoryBaseService.savePolicyInsuredCollectHistoryPo(policyInsuredCollectHistoryPo);
    }

    private void synchronizePolicyPayorInfoHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotNull(policyBo.getPolicyPayorInfo())) {
            return;
        }
        PolicyPayorInfoHistoryPo policyPayorInfoHistoryPo = (PolicyPayorInfoHistoryPo) converterObject(policyBo.getPolicyPayorInfo(), PolicyPayorInfoHistoryPo.class);
        policyPayorInfoHistoryPo.setVersionNo(policyBo.getVersionNo());
        policyHistoryBaseService.savePolicyPayorInfoHistoryPo(policyPayorInfoHistoryPo);
    }

    private void synchronizePolicyPremiumHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotNull(policyBo.getPolicyPremium())) {
            return;
        }
        PolicyPremiumHistoryPo policyPremiumHistoryPo = (PolicyPremiumHistoryPo) converterObject(policyBo.getPolicyPremium(), PolicyPremiumHistoryPo.class);
        policyPremiumHistoryPo.setVersionNo(policyBo.getVersionNo());
        policyHistoryBaseService.savePolicyPremiumHistoryPo(policyPremiumHistoryPo);
    }

    private void synchronizePolicyPaymentHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(policyBo.getListPolicyPayment())) {
            return;
        }
        policyBo.getListPolicyPayment().forEach(policyPaymentBo -> {
            PolicyPaymentHistoryPo policyPaymentHistoryPo = (PolicyPaymentHistoryPo) converterObject(policyPaymentBo, PolicyPaymentHistoryPo.class);
            policyPaymentHistoryPo.setVersionNo(policyBo.getVersionNo());
            policyHistoryBaseService.savePolicyPaymentHistoryPo(policyPaymentHistoryPo);
        });
    }

    private void synchronizePolicyInsuredExtendHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(policyBo.getListPolicyInsuredExtend())) {
            return;
        }
        policyBo.getListPolicyInsuredExtend().forEach(policyInsuredExtendPo -> {
            PolicyInsuredExtendHistoryPo policyInsuredExtendHistoryPo = (PolicyInsuredExtendHistoryPo) converterObject(policyInsuredExtendPo, PolicyInsuredExtendHistoryPo.class);
            policyInsuredExtendHistoryPo.setVersionNo(policyBo.getVersionNo());
            policyHistoryBaseService.savePolicyInsuredExtendHistoryPo(policyInsuredExtendHistoryPo);
        });
    }

    private void synchronizePolicyAddPremiumHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(policyBo.getListPolicyAddPremium())) {
            return;
        }
        policyBo.getListPolicyAddPremium().forEach(policyAddPremiumPo -> {
            PolicyAddPremiumHistoryPo policyAddPremiumHistoryPo = (PolicyAddPremiumHistoryPo) converterObject(policyAddPremiumPo, PolicyAddPremiumHistoryPo.class);
            policyAddPremiumHistoryPo.setVersionNo(policyBo.getVersionNo());
            policyHistoryBaseService.savePolicyAddPremiumHistoryPo(policyAddPremiumHistoryPo);
        });
    }

    private void synchronizePolicyPrintHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotNull(policyBo.getPolicyPrintInfo())) {
            return;
        }
        PolicyPrintInfoHistoryPo policyPrintInfoHistoryPo = (PolicyPrintInfoHistoryPo) converterObject(policyBo.getPolicyPrintInfo(), PolicyPrintInfoHistoryPo.class);
        policyPrintInfoHistoryPo.setVersionNo(policyBo.getVersionNo());
        policyHistoryBaseService.savePolicyPrintInfoHistoryPo(policyPrintInfoHistoryPo);
    }

    private void synchronizePolicyReceiptInfoHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotNull(policyBo.getPolicyReceiptInfo())) {
            return;
        }
        PolicyReceiptInfoHistoryPo policyReceiptInfoHistoryPo = (PolicyReceiptInfoHistoryPo) converterObject(policyBo.getPolicyReceiptInfo(), PolicyReceiptInfoHistoryPo.class);
        policyReceiptInfoHistoryPo.setVersionNo(policyBo.getVersionNo());
        policyHistoryBaseService.savePolicyReceiptInfoHistoryPo(policyReceiptInfoHistoryPo);
    }

    private void synchronizePolicyAccountHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(policyBo.getListPolicyAccount())) {
            return;
        }
        policyBo.getListPolicyAccount().forEach(policyAccountBo -> {
            PolicyAccountHistoryPo policyAccountHistoryPo = (PolicyAccountHistoryPo) converterObject(policyAccountBo, PolicyAccountHistoryPo.class);
            policyAccountHistoryPo.setVersionNo(policyBo.getVersionNo());
            policyHistoryBaseService.savePolicyAccountHistoryPo(policyAccountHistoryPo);
        });
    }

    private void synchronizePolicyAttachmentHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(policyBo.getListPolicyAttachment())) {
            return;
        }
        policyBo.getListPolicyAttachment().forEach(policyAttachmentBo -> {
            PolicyAttachmentHistoryPo policyAttachmentHistoryPo = (PolicyAttachmentHistoryPo) converterObject(policyAttachmentBo, PolicyAttachmentHistoryPo.class);
            policyAttachmentHistoryPo.setVersionNo(policyBo.getVersionNo());
            policyHistoryBaseService.savePolicyAttachmentHistoryPo(policyAttachmentHistoryPo);
        });
    }

    private void synchronizePolicySpecialContractHistory(PolicyBo policyBo) {
        if (!AssertUtils.isNotEmpty(policyBo.getListPolicySpecialContract())) {
            return;
        }
        policyBo.getListPolicySpecialContract().forEach(policySpecialContractPo -> {
            PolicySpecialContractHistoryPo policySpecialContractHistoryPo = (PolicySpecialContractHistoryPo) converterObject(policySpecialContractPo, PolicySpecialContractHistoryPo.class);
            policySpecialContractHistoryPo.setVersionNo(policyBo.getVersionNo());
            policyHistoryBaseService.savePolicySpecialContractHistoryPo(policySpecialContractHistoryPo);
        });
    }


    public List<PolicyListResponse> transPolicyListData(List<PolicyExtBo> listPolicyExtBo, String language) {
        List<PolicyListResponse> listPolicyListResponse = new ArrayList<>();
        listPolicyExtBo.forEach(policyExtBo -> {
            PolicyListResponse policyListResponse = new PolicyListResponse();
            ClazzUtils.copyPropertiesIgnoreNull(policyExtBo, policyListResponse);
            if (AssertUtils.isNotNull(policyExtBo.getUpdatedDate())) {
                policyListResponse.setCreateDate(String.valueOf(policyExtBo.getUpdatedDate()));
            } else if (AssertUtils.isNotNull(policyExtBo.getCreatedDate())) {
                policyListResponse.setCreateDate(String.valueOf(policyExtBo.getCreatedDate()));
            }
            if (AssertUtils.isNotEmpty(policyExtBo.getProviderId())) {
                ResultObject<ProviderMiddleResponse> providerMiddleResponseResultObject = providerApi.queryOneProviderBoInfo(policyExtBo.getProviderId());
                if (!AssertUtils.isResultObjectDataNull(providerMiddleResponseResultObject)) {
                    policyListResponse.setProviderId(providerMiddleResponseResultObject.getData().getProviderId());
                    policyListResponse.setProviderCode(providerMiddleResponseResultObject.getData().getProviderCode());
                    policyListResponse.setProviderName(providerMiddleResponseResultObject.getData().getProviderName());
                }
            }
            policyListResponse.setClientPolicyStatus(PolicyTermEnum.POLICY_STATUS_FLAG.valueOf(policyExtBo.getPolicyStatus()).code());
            PolicyCoverageBo policyCoverageBo = policyExtBo.getPolicyCoverageBo();
            if (AssertUtils.isNotNull(policyCoverageBo)) {
                policyListResponse.setPremiumFrequency(policyCoverageBo.getPremiumFrequency());
                policyListResponse.setCoveragePeriod(policyCoverageBo.getCoveragePeriod());
                policyListResponse.setCoveragePeriodUnit(policyCoverageBo.getCoveragePeriodUnit());
                policyListResponse.setProductName(policyCoverageBo.getProductName());
                policyListResponse.setProductId(policyCoverageBo.getProductId());
                //保障期限
                if (AssertUtils.isNotEmpty(policyCoverageBo.getCoveragePeriodUnit())) {
                    policyListResponse.setSecurityDate(policyCoverageBo.getCoveragePeriod() + languageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), policyCoverageBo.getCoveragePeriodUnit()));
                }
            }
            if (AssertUtils.isNotNull(policyExtBo.getPolicyApplicantBo())) {
                policyListResponse.setApplicantName(policyExtBo.getPolicyApplicantBo().getName());
            }
            //保单状态
            if (AssertUtils.isNotEmpty(policyExtBo.getPolicyStatus())) {
                policyListResponse.setPolicyStatus(policyExtBo.getPolicyStatus());
                policyListResponse.setPolicyStatusName(languageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.POLICY_STATUS.name(), policyExtBo.getPolicyStatus()));
            }

//            //保单币种符号
//            String currencyName = "";
//            if (AssertUtils.isNotEmpty(policyExtBo.getCurrencyCode())) {
//                currencyName = languageCodeTransData.getSymbolByKey(TerminologyTypeEnum.CURRENCY.name(), policyExtBo.getCurrencyCode());
//            }

            //按钮状态
            if (AssertUtils.isNotNull(policyExtBo.getPolicyOperationPo()) && PolicyTermEnum.VALID_FLAG.effective.name().equals(policyExtBo.getPolicyOperationPo().getValidFlag())
                    && AssertUtils.isNotEmpty(policyExtBo.getPolicyOperationPo().getOperationCode())) {
                BaseOperationPo baseOperationPo = policyBaseService.queryBaseOperationPoByOperationCode(policyExtBo.getPolicyOperationPo().getOperationCode());
                if (AssertUtils.isNotNull(baseOperationPo) && PolicyTermEnum.YES_NO.NO.name().equals(baseOperationPo.getFinishFlag())) {
                    //有操作，需要展示按钮
                    policyListResponse.setButtonCode(baseOperationPo.getOperationCode());
                    policyListResponse.setButtonName(languageCodeTransData.getCodeNameByInternationalKey(InternationalTypeEnum.OPERATION_CODE.name(), baseOperationPo.getOperationCode(), language));
                    policyListResponse.setColorValue(baseOperationPo.getColorValue());
                    policyListResponse.setPaymentBusinessType(baseOperationPo.getBusinessType());
                }
            }

            //设置上传回执凭证按钮编码
            if (!AssertUtils.isNotEmpty(policyListResponse.getButtonCode())) {
                //回执凭证上传标识
                PolicyReceiptInfoPo policyReceiptInfoPo = policyBaseService.queryPolicyReceiptInfo(policyExtBo.getPolicyId());
                List<PolicyAttachmentPo> policyAttachmentPos = policyBaseService.listPolicyAttachment(policyExtBo.getPolicyId(), PolicyTermEnum.CERTIFY_ATTACHMENT_TYPE.RECEIPT_IMAGE.name());
                // 3.9.0调整 回执凭证上传按钮展示条件：回执新不为空且状态为待回执，回执影像为空，保单状态“有效”或“待生效”
                boolean pendingReceiptFlag = AssertUtils.isNotNull(policyReceiptInfoPo)
                        && PolicyTermEnum.RECEIPT_STATUS.WAIT_RECEIPT.name().equals(policyReceiptInfoPo.getReceiptStatus())
                        && !AssertUtils.isNotEmpty(policyAttachmentPos)
                        && (PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name().equals(policyExtBo.getPolicyStatus())
                        || PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_PENDING_EFFECT.name().equals(policyExtBo.getPolicyStatus()));
                if (pendingReceiptFlag) {
                    policyListResponse.setColorValue("#2656A6");
                    policyListResponse.setButtonCode("PENDING_RECEIPT");
                    policyListResponse.setButtonName(languageCodeTransData.queryOneInternational("BUTTON_CODE", "PENDING_RECEIPT", null));
                }
            }

            //保单费用
            if (AssertUtils.isNotNull(policyExtBo.getPolicyPremiumBo())) {
                String totalOriginalPremium = AssertUtils.isNotNull(policyExtBo.getPolicyPremiumBo().getPeriodOriginalPremium()) ?
                        policyExtBo.getPolicyPremiumBo().getPeriodOriginalPremium().toString() : null;
                policyListResponse.setPremium(totalOriginalPremium);
            }
            listPolicyListResponse.add(policyListResponse);
        });
        return listPolicyListResponse;
    }

    public PolicyInfoResponse transformPolicyData(PolicyInfoExtBo policyInfoExtBo, Users currentLoginUsers) {
        String policyId = policyInfoExtBo.getPolicyId();
        PolicyInfoResponse policyInfoResponse = new PolicyInfoResponse();
        policyInfoResponse.setPolicyId(policyInfoExtBo.getPolicyId());
        policyInfoResponse.setPolicyNo(policyInfoExtBo.getPolicyNo());
        policyInfoResponse.setApplyId(policyInfoExtBo.getApplyId());
        policyInfoResponse.setApplyNo(policyInfoExtBo.getApplyNo());
        policyInfoResponse.setPolicyType(policyInfoExtBo.getPolicyType());
        policyInfoResponse.setPolicyStartTime(AssertUtils.isNotNull(policyInfoExtBo.getEffectiveDate()) ? policyInfoExtBo.getEffectiveDate() : 0);
        policyInfoResponse.setPolicyEndTime(AssertUtils.isNotNull(policyInfoExtBo.getMaturityDate()) ? policyInfoExtBo.getMaturityDate() : 0);
        policyInfoResponse.setPaymentBusinessType(PolicyTermEnum.POLICY_BUSINESS_TYPE.POLICY.name());
        policyInfoResponse.setClientPolicyStatus(PolicyTermEnum.POLICY_STATUS_FLAG.valueOf(policyInfoExtBo.getPolicyStatus()).code());
        if (PaymentTermEnum.CHANNEL_TYPE.ONLINE.name().equals(policyInfoExtBo.getChannelTypeCode())) {
            policyInfoResponse.setOnlineFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }
        policyInfoResponse.setEffectiveDate(policyInfoExtBo.getEffectiveDate());
        policyInfoResponse.setMaturityDate(policyInfoExtBo.getMaturityDate());
        //保单状态
        if (AssertUtils.isNotEmpty(policyInfoExtBo.getPolicyStatus())) {
            policyInfoResponse.setPolicyStatus(policyInfoExtBo.getPolicyStatus());
            policyInfoResponse.setPolicyStatusName(languageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.POLICY_STATUS.name(), policyInfoExtBo.getPolicyStatus()));
        }
        policyInfoResponse.setCoveragePeriodDays(DateUtils.intervalDay(policyInfoExtBo.getEffectiveDate(),DateUtils.getCurrentTime()));
        //保单投保人
        String mainProductId = policyReviewTransData.getMainProductId(policyInfoExtBo.getPolicyId());
        ResultObject<ProviderMiddleResponse> providerMiddleResponseResultObject = providerApi.queryOneProviderBoInfo(policyInfoExtBo.getProviderId());
        if (!AssertUtils.isResultObjectDataNull(providerMiddleResponseResultObject)) {
            policyInfoResponse.setProviderName(providerMiddleResponseResultObject.getData().getProviderName());
            policyInfoResponse.setLogoUrl(providerMiddleResponseResultObject.getData().getLogoUrl());
        }
        if (AssertUtils.isNotNull(policyInfoExtBo.getPolicyApplicant())) {
            policyInfoResponse.setCustomerApplicant((CustomerResponse) this.converterObject(policyInfoExtBo.getPolicyApplicant(), CustomerResponse.class));
            policyInfoResponse.setApplicant(policyInfoExtBo.getPolicyApplicant().getName());

            //针对20A网销产品国籍国际化特殊处理
            policyInfoResponse.getCustomerApplicant().setNationalityName(policyReviewTransData.getNationalityCodeTypeName(policyInfoExtBo.getPolicyId(), policyInfoResponse.getCustomerApplicant().getNationality(), currentLoginUsers.getLanguage()));
        }
        PolicyPremiumBo policyPremium = policyInfoExtBo.getPolicyPremium();
        List<PolicyPaymentBo> policyPaymentBos = policyBaseService.listPolicyPayment(policyId);
        AssertUtils.isNotEmpty(getLogger(), policyPaymentBos, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_PAYMENT_IS_NOT_FOUND);
        List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.listPolicyCoverageOfInsured(policyId);
        AssertUtils.isNotNull(this.getLogger(), policyCoveragePos, PolicyErrorConfigEnum.POLICY_QUERY_POLICY_COVERAGE_IS_NOT_FOUND);

        //贷款合同信息
        PolicyReferralInfoPo referralInfo = policyReferralInfoBaseService.queryPolicyReferralInfoPo(policyId);
        if (AssertUtils.isNotNull(referralInfo)) {
            policyInfoResponse.setReferralInfoFlag(TerminologyConfigEnum.WHETHER.YES.name());
            PolicyReferralInfoResponse policyReferralInfoResponse = new PolicyReferralInfoResponse();
            ClazzUtils.copyPropertiesIgnoreNull(referralInfo, policyReferralInfoResponse);
            policyInfoResponse.setReferralInfo(policyReferralInfoResponse);
        }

        //保单持有人
        PolicyHolderPo policyHolderPo = policyOtherInfoService.queryPolicyHolderPo(policyId);
        if (AssertUtils.isNotNull(policyHolderPo)) {
            ApplyHolderResponse applyHolderResponse = new ApplyHolderResponse();
            ClazzUtils.copyPropertiesIgnoreNull(policyHolderPo, applyHolderResponse);
            policyInfoResponse.setHolder(applyHolderResponse);
        }

        /*
            新单首期展示保费、折扣系数、应缴保费；(首期/9.13.14.15的三期以内，有其他数据则不代表新单)
            非新单首期展示保费（即现有页面）；
        */
        Map<String, Boolean> discountPremiumFlag = this.isDiscountPremiumFlag(policyPremium, policyPaymentBos, policyCoveragePos);
        boolean percentageFlag = discountPremiumFlag.get("percentage");
        boolean fixedAmountFlag = discountPremiumFlag.get("fixedAmount");
        //附件
        if (AssertUtils.isNotNull(policyInfoExtBo.getPolicyAttachmentBo())) {
            policyInfoResponse.setPolicyAttachId(policyInfoExtBo.getPolicyAttachmentBo().getAttachmentId());
        }

        if (AssertUtils.isNotNull(policyInfoExtBo.getListPolicyInsured())) {
            List<AdditionCoverageResponse> listAdditionCoverage = new ArrayList<>();
            List<CustomerResponse> listInsuredResponse = new ArrayList<>();
            List<CustomerResponse> listBeneficialResponse = new ArrayList<>();

            policyInfoExtBo.getListPolicyInsured().forEach(policyInsuredBo -> {
                //被保人信息
                listInsuredResponse.add((CustomerResponse) this.converterObject(policyInsuredBo, CustomerResponse.class));
                //险种
                if (AssertUtils.isNotNull(policyInsuredBo.getListPolicyCoverage())) {
                    List<String> policyStatuses = Arrays.asList(
                            PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name(),
                            PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_REINSTATEMENT.name(),
                            PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID.name());
                    if (policyStatuses.contains(policyInfoExtBo.getPolicyStatus())) {
                        // 保单未终止，移除满期或终止的险种
                        policyInsuredBo.getListPolicyCoverage().removeIf(policyCoverageBo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(policyCoverageBo.getPrimaryFlag())
                                && (PolicyTermEnum.COVERAGE_STATUS.EXPIRED.name().equals(policyCoverageBo.getCoverageStatus())
                                || PolicyTermEnum.COVERAGE_STATUS.INDEMNITY_TERMINATION.name().equals(policyCoverageBo.getCoverageStatus())));
                    }
                    policyInsuredBo.getListPolicyCoverage().forEach(policyCoverageBo -> {

                        if (PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverageBo.getPrimaryFlag())) {
                            policyInfoResponse.setProductName(policyCoverageBo.getProductName());
                            policyInfoResponse.setProductId(policyCoverageBo.getProductId());
                            policyInfoResponse.setPrimaryFlag(policyCoverageBo.getPrimaryFlag());
                            policyInfoResponse.setPremiumFrequency(policyCoverageBo.getPremiumFrequency());
                            if (AssertUtils.isNotNull(policyCoverageBo.getTotalAmount())) {
                                policyInfoResponse.setAmount(decimalFormat.format(new BigDecimal(policyCoverageBo.getTotalAmount())));
                            }
                            policyInfoResponse.setCoveragePeriodUnit(policyCoverageBo.getCoveragePeriodUnit());
                            policyInfoResponse.setCoveragePeriod(policyCoverageBo.getCoveragePeriod());
                            policyInfoResponse.setProductLevel(policyCoverageBo.getProductLevel());
                            //保障期限
                            if (AssertUtils.isNotEmpty(policyCoverageBo.getCoveragePeriodUnit())) {
                                policyInfoResponse.setSecurityDate(policyCoverageBo.getCoveragePeriod() + languageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), policyCoverageBo.getCoveragePeriodUnit()));
                            }
                        } else if (PolicyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(policyCoverageBo.getPrimaryFlag())) {
                            AdditionCoverageResponse additionCoverageResponse = new AdditionCoverageResponse();
                            ClazzUtils.copyPropertiesIgnoreNull(policyCoverageBo, additionCoverageResponse);
                            if (percentageFlag) {
                                additionCoverageResponse.setDiscountPremiumFlag(com.gclife.common.TerminologyConfigEnum.WHETHER.YES.name());
                                additionCoverageResponse.setDiscountType(policyPremium.getDiscountType());
                                additionCoverageResponse.setPromotionType(policyPremium.getPromotionType());
                                additionCoverageResponse.setDiscountModel(policyPremium.getDiscountModel());
                                BigDecimal premiumBeforeDiscount = policyCoverageBo.getActualPremium()
                                        .divide(new BigDecimal("1").subtract(policyPremium.getSpecialDiscount()), 2, BigDecimal.ROUND_HALF_UP);
                                if (policyCoverageBo.getTotalPremium().subtract(premiumBeforeDiscount).abs().compareTo(new BigDecimal("0.05")) < 0) {
                                    premiumBeforeDiscount = policyCoverageBo.getTotalPremium();
                                }
                                additionCoverageResponse.setPremiumBeforeDiscount(premiumBeforeDiscount);
                                additionCoverageResponse.setSpecialDiscount(new BigDecimal(new BigDecimal("100").multiply(policyPremium.getSpecialDiscount()).stripTrailingZeros().toPlainString()));
                                additionCoverageResponse.setTotalPremium(policyCoverageBo.getActualPremium());
                            } else {
                                additionCoverageResponse.setTotalPremium(policyCoverageBo.getTotalPremium());
                            }

                            if (AssertUtils.isNotNull(policyCoverageBo.getAmount())) {
                                additionCoverageResponse.setAmount(policyCoverageBo.getAmount() + "");
                            }
                            String coveragePeriodName;
                            if (policyCoverageBo.getCoveragePeriod().equals("120") && "AGE".equals(policyCoverageBo.getCoveragePeriodUnit())) {
                                coveragePeriodName = languageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), "LIFELONG");
                            } else {
                                coveragePeriodName = policyCoverageBo.getCoveragePeriod() + languageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), policyCoverageBo.getCoveragePeriodUnit());
                            }
                            additionCoverageResponse.setCoveragePeriodName(coveragePeriodName);
                            listAdditionCoverage.add(additionCoverageResponse);
                        }
                    });
                }

                //受益人
                if (AssertUtils.isNotNull(policyInsuredBo.getListPolicyBeneficiary())) {
                    policyInsuredBo.getListPolicyBeneficiary().forEach(policyBeneficiaryInfoBo -> {
                        if (AssertUtils.isNotEmpty(policyBeneficiaryInfoBo.getPolicyBeneficiary().getName())) {
                            policyInfoResponse.setBeneficial(policyInfoResponse.getBeneficial() + "," + policyBeneficiaryInfoBo.getPolicyBeneficiary().getName());
                        }
                        if (policyBeneficiaryInfoBo.getModifyFlag().equals(TerminologyConfigEnum.WHETHER.YES.name())) {
                            CustomerResponse customerResponse = (CustomerResponse) this.converterObject(policyBeneficiaryInfoBo.getPolicyBeneficiary(), CustomerResponse.class);
                            customerResponse.setBeneficiaryNo(policyBeneficiaryInfoBo.getBeneficiaryNo());
                            customerResponse.setBeneficiaryNoOrder(policyBeneficiaryInfoBo.getBeneficiaryNoOrder());
                            if (AssertUtils.isNotNull(policyBeneficiaryInfoBo.getBeneficiaryProportion())) {
                                customerResponse.setBeneficiaryProportion(policyBeneficiaryInfoBo.getBeneficiaryProportion().longValue());
                            }
                            customerResponse.setRelationship(policyBeneficiaryInfoBo.getRelationship());
                            customerResponse.setRelationshipInstructions(policyBeneficiaryInfoBo.getRelationshipInstructions());
                            customerResponse.setRelationshipCode(policyBeneficiaryInfoBo.getRelationship());
                            listBeneficialResponse.add(customerResponse);
                        }
                    });
                }
                //被保人
                if (AssertUtils.isNotEmpty(policyInsuredBo.getName())) {
                    policyInfoResponse.setInsured(policyInfoResponse.getInsured() + "," + policyInsuredBo.getName());
                }
            });
            policyInfoResponse.setPremium(policyPremium.getPeriodTotalPremium().toString());
            if (AssertUtils.isNotEmpty(policyInfoResponse.getBeneficial()) && policyInfoResponse.getBeneficial().length() > 1) {
                policyInfoResponse.setBeneficial(policyInfoResponse.getBeneficial().substring(policyInfoResponse.getBeneficial().indexOf(",") + 1));

            }
            if (AssertUtils.isNotEmpty(policyInfoResponse.getInsured()) && policyInfoResponse.getInsured().length() > 1) {
                policyInfoResponse.setInsured(policyInfoResponse.getInsured().substring(policyInfoResponse.getInsured().indexOf(",") + 1));
            }

            //针对20A网销产品国籍国际化特殊处理
            listInsuredResponse.forEach(customerResponse -> {
                customerResponse.setNationalityName(policyReviewTransData.getNationalityCodeTypeName(policyInfoExtBo.getPolicyId(), customerResponse.getNationality(), currentLoginUsers.getLanguage()));
            });

            policyInfoResponse.setListCustomerInsured(listInsuredResponse);

            policyInfoResponse.setListAdditionCoverage(listAdditionCoverage);

            policyInfoResponse.setListCustomerBeneficial(listBeneficialResponse);

            //第一机构受益人信息
            List<PolicyBeneficiaryInfoBo> policyBeneficiaryInfoBos = policyBaseService.queryPolicyLoanBeneficiary(policyId, TerminologyConfigEnum.WHETHER.NO.name());
            if (AssertUtils.isNotEmpty(policyBeneficiaryInfoBos)) {
                ApplyLoanBeneficiaryResponse loanBeneficiary = new ApplyLoanBeneficiaryResponse();
                loanBeneficiary.setBeneficiaryBranchId(policyBeneficiaryInfoBos.get(0).getPolicyBeneficiary().getBeneficiaryBranchId());
                loanBeneficiary.setBeneficiaryBranchName(policyBeneficiaryInfoBos.get(0).getPolicyBeneficiary().getBeneficiaryBranchName());
                loanBeneficiary.setBeneficiaryBranchCode(policyBeneficiaryInfoBos.get(0).getPolicyBeneficiary().getBeneficiaryBranchCode());
                loanBeneficiary.setBeneficiaryNoOrder(policyBeneficiaryInfoBos.get(0).getBeneficiaryNoOrder());
                loanBeneficiary.setBeneficiaryProportion(policyBeneficiaryInfoBos.get(0).getBeneficiaryProportion());
                policyInfoResponse.setLoanBeneficiary(loanBeneficiary);

                loanBeneficiary = JSON.parseObject(JackSonUtils.toJson(loanBeneficiary, currentLoginUsers.getLanguage()), ApplyLoanBeneficiaryResponse.class);
                policyInfoResponse.setBeneficial(AssertUtils.isNotEmpty(policyInfoResponse.getBeneficial()) ?
                        loanBeneficiary.getBeneficiaryBranchName() + "," + policyInfoResponse.getBeneficial() : loanBeneficiary.getBeneficiaryBranchName()
                );
            }
            //贷款合同信息
            PolicyLoanPo policyLoanPo = policyLoanBaseService.queryPolicyLoanPo(policyId);
            if (AssertUtils.isNotNull(policyLoanPo)) {
                PolicyLoanResponse policyLoanResponse = new PolicyLoanResponse();
                ClazzUtils.copyPropertiesIgnoreNull(policyLoanPo, policyLoanResponse);
                policyLoanResponse.setExchangeRate(TerminologyConfigEnum.CURRENCY.USD.name().equals(policyLoanPo.getCurrency()) ? "1" : policyLoanPo.getExchangeRate().stripTrailingZeros().toString());
                policyInfoResponse.setLoanContract(policyLoanResponse);
                policyInfoResponse.setContractLoanFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }
        }

        //回执凭证上传标识
        PolicyReceiptInfoPo policyReceiptInfoPo = policyBaseService.queryPolicyReceiptInfo(policyId);
        List<PolicyAttachmentPo> policyAttachmentPos = policyBaseService.listPolicyAttachment(policyId, PolicyTermEnum.CERTIFY_ATTACHMENT_TYPE.RECEIPT_IMAGE.name());
        // 3.9.0调整 回执凭证上传按钮展示条件：回执新不为空且状态为待回执，回执影像为空，保单状态“有效”或“待生效”
        boolean pendingReceiptFlag = AssertUtils.isNotNull(policyReceiptInfoPo)
                && PolicyTermEnum.RECEIPT_STATUS.WAIT_RECEIPT.name().equals(policyReceiptInfoPo.getReceiptStatus())
                && !AssertUtils.isNotEmpty(policyAttachmentPos)
                && (PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name().equals(policyInfoExtBo.getPolicyStatus())
                || PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_PENDING_EFFECT.name().equals(policyInfoExtBo.getPolicyStatus()));
        if (pendingReceiptFlag) {
            policyInfoResponse.setReceiptImageFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }

        //查询险种
        if (AssertUtils.isNotNull(policyInfoExtBo.getPolicyOperationPo()) && AssertUtils.isNotEmpty(policyInfoExtBo.getPolicyOperationPo().getOperationCode())
                && PolicyTermEnum.VALID_FLAG.effective.name().equals(policyInfoExtBo.getPolicyOperationPo().getValidFlag())) {
            String premiumStatus = null;
            //有动作产生
            BaseOperationPo baseOperationPo = policyBaseService.queryBaseOperationPoByOperationCode(policyInfoExtBo.getPolicyOperationPo().getOperationCode());
            AssertUtils.isNotNull(getLogger(), baseOperationPo, PolicyErrorConfigEnum.POLICY_QUERY_BASE_OPERATION_ERROR);
            if (AssertUtils.isNotEmpty(baseOperationPo.getFinishFlag()) && PolicyTermEnum.YES_NO.NO.name().equals(baseOperationPo.getFinishFlag())) {
                policyInfoResponse.setPayFlag(TerminologyConfigEnum.WHETHER.YES.name());

                policyInfoResponse.setPaymentBusinessType(baseOperationPo.getBusinessType());
                //备注
                String remarks = null;
                //续保可否支付
                String payFlag = null;
                //支付方式
                String paymentMethodCode = null;
                //续保附加险
                List<RenewalCoverageVo> listAdditionCoverage = new ArrayList<>();
                if (PolicyTermEnum.POLICY_BUSINESS_TYPE.RENEWAL_INSURANCE.name().equals(baseOperationPo.getBusinessType())) {
                    //设置续保应缴保费
                    ResultObject<RenewalInsuranceAppDetailResponse> renewalInsuranceApplyRespFcResultObject = renewalInsuranceApi.queryRenewalInsuranceAppDetail(policyId);
                    AssertUtils.isResultObjectError(getLogger(), renewalInsuranceApplyRespFcResultObject, PolicyErrorConfigEnum.POLICY_QUERY_RENEWAL_INSURANCE_ERROR);
                    RenewalInsuranceAppDetailResponse renewalInsuranceAppDetailResponse = renewalInsuranceApplyRespFcResultObject.getData();
                    renewalInsuranceAppDetailResponse.getListCoverage().stream()
                            .filter(renewalCoverageVo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(renewalCoverageVo.getPrimaryFlag()))
                            .findFirst().ifPresent(renewalCoverageVo -> {
                        policyInfoResponse.setRenewalPolicyStartTime(AssertUtils.isNotNull(renewalCoverageVo.getCoveragePeriodStartDate()) ? renewalCoverageVo.getCoveragePeriodStartDate() : 0);
                        policyInfoResponse.setRenewalPolicyEndTime(AssertUtils.isNotNull(renewalCoverageVo.getCoveragePeriodEndDate()) ? renewalCoverageVo.getCoveragePeriodEndDate() : 0);
                    });
                    payFlag = renewalInsuranceAppDetailResponse.getPayFlag();
                    listAdditionCoverage = renewalInsuranceAppDetailResponse.getListCoverage().stream()
                            .filter(renewalCoverageVo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(renewalCoverageVo.getPrimaryFlag()))
                            .collect(Collectors.toList());
                    policyInfoResponse.setDuePremium(renewalInsuranceAppDetailResponse.getReceivablePremium().toString());
                    remarks = renewalInsuranceAppDetailResponse.getRemarkContent();
                    paymentMethodCode = renewalInsuranceAppDetailResponse.getPaymentMethodCode();
                    policyInfoResponse.setPaymentMethodCode(paymentMethodCode);
                    if (PolicyTermEnum.OPERATION_CODE.RENEWAL_INSURANCE_RE_APPLY.name().equals(baseOperationPo.getOperationCode())) {
                        //重新申请续保，展示审核结论
                        policyInfoResponse.setRemarks(remarks);
                    }
                    policyInfoResponse.setBusinessId(renewalInsuranceAppDetailResponse.getRenewalId());
                    premiumStatus = renewalInsuranceAppDetailResponse.getPremiumStatus();
                }
                this.setRenewalCoverage(policyInfoResponse, baseOperationPo, policyCoveragePos, listAdditionCoverage);
                //设置按钮
                if (PolicyTermEnum.YES_NO.NO.name().equals(baseOperationPo.getQueryPayFlag())) {
                    //不查询支付
                    policyInfoResponse.setButtonCode(baseOperationPo.getOperationCode());
                    policyInfoResponse.setButtonName(languageCodeTransData.getCodeNameByInternationalKey(InternationalTypeEnum.OPERATION_CODE.name(), baseOperationPo.getOperationCode(), null));
                    policyInfoResponse.setColorValue(baseOperationPo.getColorValue());
                } else {
                    //设置颜色
                    GcFactorFunction gcFuction = () -> {
                        policyInfoResponse.setColorValue("#2656A6");
                    };
                    GcFactorFunction thFuction = () -> {
                        policyInfoResponse.setColorValue("#ff594a");
                    };
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put(BaseTermEnum.BASE_FACTOR_CONFIG_VALUE.GC.name(), gcFuction);
                    map.put(BaseTermEnum.BASE_FACTOR_CONFIG_VALUE.TH.name(), thFuction);
                    this.handleDifferent(map, PolicyTermEnum.BASE_FACTOR_CONFIG_CODE.APP_POLICY_BUTTON_COLOR.name());
                    //判断之前的成功支付有没有wing线下支付

                    //查询支付信息
                    if (PolicyTermEnum.POLICY_BUSINESS_TYPE.RENEWAL_INSURANCE.name().equals(baseOperationPo.getBusinessType())) {

                        if (this.policyHookExist(premiumStatus, policyId, paymentMethodCode)) {
                            //展示支付中  置灰
                            premiumStatus = PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_PROCESSING.name();
                            policyInfoResponse.setButtonCode(premiumStatus);
                            policyInfoResponse.setButtonName(languageCodeTransData.queryOneInternational(TerminologyTypeEnum.PAYMENT_STATUS.name(), premiumStatus, null));
                            policyInfoResponse.setColorValue("#808080");
                            return policyInfoResponse;
                        }
                        if (AssertUtils.isNotEmpty(payFlag) && payFlag.equalsIgnoreCase(PolicyTermEnum.YES_NO.NO.name())) {
                            //展示支付中  置灰
                            policyInfoResponse.setButtonCode(PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_HOOK.name());
                            policyInfoResponse.setButtonName(languageCodeTransData.queryOneInternational(TerminologyTypeEnum.PAYMENT_STATUS.name(), premiumStatus, null));
                            policyInfoResponse.setColorValue("#808080");
                            return policyInfoResponse;
                        }
                        //设置支付审核信息
                        if (PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT_NOPASS.name().equals(premiumStatus)) {
                            //审核不通过
                            policyInfoResponse.setAdultTitle("Payment request has been denied");
                            policyInfoResponse.setAdultContent(remarks);
                        } else if (PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FAILED.name().equals(premiumStatus)) {
                            //支付失败
                            policyInfoResponse.setAdultTitle("Reason for return");
                            policyInfoResponse.setAdultContent(remarks);
                        } else if (PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED_ADJUST.name().equals(premiumStatus)) {
                            //支付完成调整
                            policyInfoResponse.setAdultTitle("Reason for return");
                            policyInfoResponse.setAdultContent(remarks);
                            premiumStatus = PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name();
                        } else if (PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT.name().equals(premiumStatus) || PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED.name().equals(premiumStatus)) {
                            //等待支付按钮置灰
                            policyInfoResponse.setColorValue("#808080");
                        }
                        //设置按钮
                        policyInfoResponse.setButtonCode(premiumStatus);
                        if (AssertUtils.isNotEmpty(premiumStatus)) {
                            policyInfoResponse.setButtonName(languageCodeTransData.queryOneInternational(TerminologyTypeEnum.PAYMENT_STATUS.name(), premiumStatus, null));
                        }
                    } else if (PolicyTermEnum.POLICY_BUSINESS_TYPE.RENEWAL.name().equals(baseOperationPo.getBusinessType())) {
                        //续期设置待缴费列表
                        transformPaymentData(policyInfoResponse, policyId, policyPaymentBos);
                    }

                }
            }
        }
        //设置上传回执凭证按钮编码
        if (!AssertUtils.isNotEmpty(policyInfoResponse.getButtonCode()) && TerminologyConfigEnum.WHETHER.YES.name().equals(policyInfoResponse.getReceiptImageFlag())) {
            policyInfoResponse.setColorValue("#2656A6");
            policyInfoResponse.setButtonCode("UPLOAD_RECEIPT_IMAGE");
            policyInfoResponse.setButtonName(languageCodeTransData.queryOneInternational("BUTTON_CODE", "UPLOAD_RECEIPT_IMAGE", null));
        }

        if (percentageFlag) {
            policyInfoResponse.setDiscountPremiumFlag(TerminologyConfigEnum.WHETHER.YES.name());
            policyInfoResponse.setDiscountType(policyPremium.getDiscountType());
            policyInfoResponse.setPromotionType(policyPremium.getPromotionType());
            policyInfoResponse.setDiscountModel(policyPremium.getDiscountModel());
            policyInfoResponse.setPremiumBeforeDiscount(policyPremium.getActualPremium());
            BigDecimal premiumAfterDiscount = policyPremium.getActualPremium()
                    .multiply(new BigDecimal("1").subtract(policyPremium.getSpecialDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
            policyInfoResponse.setPremium(premiumAfterDiscount.toString());
            policyInfoResponse.setSpecialDiscount(new BigDecimal(new BigDecimal("100").multiply(policyPremium.getSpecialDiscount()).stripTrailingZeros().toPlainString()));
        }
        if (fixedAmountFlag) {
            policyInfoResponse.setDiscountPremiumFlag(TerminologyConfigEnum.WHETHER.YES.name());
            policyInfoResponse.setDiscountType(policyPremium.getDiscountType());
            policyInfoResponse.setPromotionType(policyPremium.getPromotionType());
            policyInfoResponse.setDiscountModel(policyPremium.getDiscountModel());
            policyInfoResponse.setPremiumBeforeDiscount(policyPremium.getActualPremium());
            BigDecimal premiumAfterDiscount = policyPremium.getActualPremium().subtract(policyPremium.getSpecialDiscount());
            policyInfoResponse.setPremium(premiumAfterDiscount.toString());
            policyInfoResponse.setSpecialDiscount(new BigDecimal(policyPremium.getSpecialDiscount().stripTrailingZeros().toPlainString()));
        }
        return policyInfoResponse;
    }

    public Map<String, Boolean> isDiscountPremiumFlag(PolicyPremiumBo policyPremium, List<PolicyPaymentBo> policyPaymentBos, List<PolicyCoveragePo> policyCoveragePos) {
        List<Long> frequency = new ArrayList<>();
        List<String> paymentBusinessType = new ArrayList<>();
        extracted(policyCoveragePos, frequency, paymentBusinessType);
        Optional<PolicyPaymentBo> first = policyPaymentBos.stream().filter(policyPaymentBo -> !frequency.contains(policyPaymentBo.getFrequency()) && paymentBusinessType.contains(policyPaymentBo.getPaymentBusinessType()))
                .findFirst();
        Map<String, Boolean> isDiscountPremiumFlagMap = new HashMap<>();
        boolean percentage = AssertUtils.isNotNull(policyPremium)
                && AssertUtils.isNotNull(policyPremium.getPremiumBeforeDiscount())
                && AssertUtils.isNotNull(policyPremium.getDiscountType())
                && AssertUtils.isNotNull(policyPremium.getSpecialDiscount())
                && (!AssertUtils.isNotEmpty(policyPremium.getDiscountModel()) || ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(policyPremium.getDiscountModel()))
                && !first.isPresent();
        boolean fixedAmount = AssertUtils.isNotNull(policyPremium)
                && AssertUtils.isNotNull(policyPremium.getPremiumBeforeDiscount())
                && AssertUtils.isNotNull(policyPremium.getDiscountType())
                && AssertUtils.isNotNull(policyPremium.getSpecialDiscount())
                && ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(policyPremium.getDiscountModel())
                && !first.isPresent();
        isDiscountPremiumFlagMap.put("fixedAmount", fixedAmount);
        isDiscountPremiumFlagMap.put("percentage", percentage);
        return isDiscountPremiumFlagMap;
    }

    public Map<String, Boolean> isDiscountPremiumFlagSingle(PolicyPremiumBo policyPremium, PolicyPaymentBo policyPaymentBo, List<PolicyCoveragePo> policyCoveragePos) {
        List<Long> frequency = new ArrayList<>();
        List<String> paymentBusinessType = new ArrayList<>();
        extracted(policyCoveragePos, frequency, paymentBusinessType);
        boolean b = frequency.contains(policyPaymentBo.getFrequency()) && paymentBusinessType.contains(policyPaymentBo.getPaymentBusinessType());

        Map<String, Boolean> isDiscountPremiumFlagMap = new HashMap<>();
        boolean percentage = AssertUtils.isNotNull(policyPremium)
                && AssertUtils.isNotNull(policyPremium.getPremiumBeforeDiscount())
                && AssertUtils.isNotNull(policyPremium.getDiscountType())
                && AssertUtils.isNotNull(policyPremium.getSpecialDiscount())
                && (!AssertUtils.isNotEmpty(policyPremium.getDiscountModel()) || ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(policyPremium.getDiscountModel()))
                && b;
        boolean fixedAmount = AssertUtils.isNotNull(policyPremium)
                && AssertUtils.isNotNull(policyPremium.getPremiumBeforeDiscount())
                && AssertUtils.isNotNull(policyPremium.getDiscountType())
                && AssertUtils.isNotNull(policyPremium.getSpecialDiscount())
                && ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(policyPremium.getDiscountModel())
                && b;
        isDiscountPremiumFlagMap.put("fixedAmount", fixedAmount);
        isDiscountPremiumFlagMap.put("percentage", percentage);

        return isDiscountPremiumFlagMap;
    }

    private void extracted(List<PolicyCoveragePo> policyCoveragePos, List<Long> frequency, List<String> paymentBusinessType) {
        frequency.add(1L);
        paymentBusinessType.add(PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_NEW_CONTRACT.name());
        paymentBusinessType.add(PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_RENEWAL.name());

        List<String> monthlyTripleProductId = Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_13.id());
        Optional<PolicyCoveragePo> first = policyCoveragePos.stream()
                .filter(coverageBo -> monthlyTripleProductId.contains(coverageBo.getProductId()) &&
                        PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag()) &&
                        PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(coverageBo.getPremiumFrequency()))
                .findFirst();
        if (first.isPresent()) {
            frequency.add(2L);
            frequency.add(3L);
        }
    }

    private void setRenewalCoverage(PolicyInfoResponse policyInfoResponse, BaseOperationPo baseOperationPo, List<PolicyCoveragePo> policyCoveragePos, List<RenewalCoverageVo> listAdditionCoverage) {
        List<AdditionCoverageResponse> listAdditionCoverageRenewal = new ArrayList<>();
        policyCoveragePos.forEach(policyCoveragePo -> {
            if (PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoveragePo.getPrimaryFlag())) {
                policyInfoResponse.setProductId(policyCoveragePo.getProductId());
                policyInfoResponse.setProductName(policyCoveragePo.getProductName());
                policyInfoResponse.setPolicyStartTime(AssertUtils.isNotNull(policyCoveragePo.getCoveragePeriodStartDate()) ? policyCoveragePo.getCoveragePeriodStartDate() : 0);
                policyInfoResponse.setPolicyEndTime(AssertUtils.isNotNull(policyCoveragePo.getCoveragePeriodEndDate()) ? policyCoveragePo.getCoveragePeriodEndDate() : 0);
            } else if (PolicyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(policyCoveragePo.getPrimaryFlag())) {
                AdditionCoverageResponse additionCoverageResponse = new AdditionCoverageResponse();
                ClazzUtils.copyPropertiesIgnoreNull(policyCoveragePo, additionCoverageResponse);
                //是否续保标识来自续保数据库
                if (PolicyTermEnum.POLICY_BUSINESS_TYPE.RENEWAL_INSURANCE.name().equals(baseOperationPo.getBusinessType())
                        && AssertUtils.isNotEmpty(listAdditionCoverage)) {
                    listAdditionCoverage.stream().filter(renewalCoverageVo -> renewalCoverageVo.getProductId().equals(policyCoveragePo.getProductId()))
                            .findFirst().ifPresent(renewalCoverageVo -> {
                        additionCoverageResponse.setRenewalPermitFlag(renewalCoverageVo.getRenewalPermitFlag());
                        additionCoverageResponse.setShowRenewalPermitFlag(TerminologyConfigEnum.WHETHER.YES.name());
                    });
                }
                listAdditionCoverageRenewal.add(additionCoverageResponse);
            }
        });
        policyInfoResponse.setListAdditionCoverage(listAdditionCoverageRenewal);
    }

    private boolean policyHookExist(String premiumStatus, String policyId, String paymentModeCode) {
        ResultObject<PolicyHookResponse> policyHookExist = policyHookBusinessService.getPolicyHookExist(policyId);
        //判断是否挂起
//        boolean contains = !AssertUtils.isNotEmpty(paymentModeCode) || Arrays.asList(PolicyTermEnum.PAYMENT_METHODS.WING_OFFLINE.name(), PolicyTermEnum.PAYMENT_METHODS.ABA_PAYMENTS.name()).contains(paymentModeCode);
//        boolean b = !(PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name().equals(premiumStatus) && contains);
        return PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED.name().equals(premiumStatus) && !AssertUtils.isResultObjectDataNull(policyHookExist);
    }


    private void transformPaymentData(PolicyInfoResponse policyInfoResponse, String policyId, List<PolicyPaymentBo> policyPaymentBos) {
        ResultObject<RenewalAppDetailResponse> renewalAppDetailResponseResultObject = renewalPaymentApi.queryRenewalAppDetail(policyId);
        AssertUtils.isResultObjectDataNull(getLogger(), renewalAppDetailResponseResultObject, PolicyErrorConfigEnum.POLICY_QUERY_RENEWAL_INSURANCE_ERROR);
        policyInfoResponse.setPaymentMethodCode(renewalAppDetailResponseResultObject.getData().getPaymentMethodCode());
        policyInfoResponse.setBusinessId(renewalAppDetailResponseResultObject.getData().getBusinessId());
        policyInfoResponse.setDuePremium(AssertUtils.isNotNull(renewalAppDetailResponseResultObject.getData().getTotalPremium()) ? renewalAppDetailResponseResultObject.getData().getTotalPremium().toString() : null);
        //保单 查询保费记录
        String premiumStatus;
        List<PolicyPaymentResponse> policyPaymentResponses = (List<PolicyPaymentResponse>) this.converterList(
                policyPaymentBos, new TypeToken<List<PolicyPaymentResponse>>() {
                }.getType());
        List<PolicyPaymentResponse> listFailedPayment = this.getPolicyPayment(policyPaymentResponses, PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FAILED.name());
        List<PolicyPaymentResponse> listArrearsPayment = this.getPolicyPayment(policyPaymentResponses, PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name());
        List<PolicyPaymentResponse> listFinishPayment = this.getPolicyPayment(policyPaymentResponses, PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED.name());
        List<PolicyPaymentResponse> policyPaymentResponseList = new ArrayList<>();
        //待支付、审核通过/不通过的、支付完成支付状态都为续期支付
        //先展示待审核、审核通过/不通过的续期
        if (AssertUtils.isNotEmpty(listFinishPayment)) {
            policyPaymentResponseList.addAll(listFinishPayment);
        } else if (AssertUtils.isNotEmpty(listFailedPayment)) {
            policyPaymentResponseList.addAll(listFailedPayment);
        } else if (AssertUtils.isNotEmpty(listArrearsPayment)) {
            policyPaymentResponseList.addAll(listArrearsPayment);
        }
        policyPaymentResponseList.sort(Comparator.comparing(PolicyPaymentResponse::getReceivableDate));
        if (AssertUtils.isNotEmpty(renewalAppDetailResponseResultObject.getData().getPaymentStatusCode())) {
            premiumStatus = renewalAppDetailResponseResultObject.getData().getPaymentStatusCode();
        } else {
            premiumStatus = policyPaymentResponseList.get(0).getPaymentStatusCode();
        }

        //清空列表
        policyPaymentResponseList.clear();

        if (this.policyHookExist(premiumStatus, policyId, policyInfoResponse.getPaymentMethodCode())) {
            //展示支付中  置灰
            premiumStatus = PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_PROCESSING.name();
            policyInfoResponse.setColorValue("#808080");
            policyInfoResponse.setButtonCode(premiumStatus);
            policyInfoResponse.setButtonName(languageCodeTransData.queryOneInternational(TerminologyTypeEnum.PAYMENT_STATUS.name(), premiumStatus, null));
            //支付中重设为续期数据的附加险的标识
            this.resetPolicyInfoAddition(renewalAppDetailResponseResultObject.getData(), policyInfoResponse);
            return;
        }

        if (PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED.name().equals(premiumStatus)) {
            policyInfoResponse.setColorValue("#808080");
        } else if (PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name().equals(premiumStatus)) {
            policyPaymentResponseList.addAll(listFailedPayment);
            policyPaymentResponseList.addAll(listArrearsPayment);
        } else if (PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FAILED.name().equals(premiumStatus)) {
            PolicyPremiumBo policyPremiumBo = policyPremiumBaseService.queryPolicyPremium(policyId);
            policyInfoResponse.setAdultTitle("Reason for return");
            if (AssertUtils.isNotNull(policyPremiumBo)) {
                policyInfoResponse.setAdultContent(policyPremiumBo.getRemark());
            }
            policyPaymentResponseList.addAll(listFailedPayment);
            policyPaymentResponseList.addAll(listArrearsPayment);
        }

        this.transformPaymentRecode(policyInfoResponse, policyPaymentResponseList, null, renewalAppDetailResponseResultObject.getData(), policyId);
        policyInfoResponse.setButtonCode(premiumStatus);
        if (AssertUtils.isNotEmpty(premiumStatus)) {
            policyInfoResponse.setButtonName(languageCodeTransData.queryOneInternational(TerminologyTypeEnum.PAYMENT_STATUS.name(), premiumStatus, null));
        }
    }


    private void transformPaymentRecode(PolicyInfoResponse policyInfoResponse, List<PolicyPaymentResponse> listArrearsPayment, String language, RenewalAppDetailResponse renewalAppDetailResponse, String policyId) {
        if (!AssertUtils.isNotEmpty(listArrearsPayment)) {
            return;
        }
        //排序
        listArrearsPayment.sort(Comparator.comparing(PolicyPaymentResponse::getReceivableDate));
        for (PolicyPaymentResponse policyPaymentResponse : listArrearsPayment) {
            this.transPaymentRecord(policyPaymentResponse, language);
        }

        //若待支付列表中都是未支付的数据，则默认选中第一条
        Optional<PolicyPaymentResponse> first = listArrearsPayment.stream().filter(policyPaymentResponse -> TerminologyConfigEnum.WHETHER.YES.name().equals(policyPaymentResponse.getIsPayingFlag())).findFirst();
        if (!first.isPresent()) {
            listArrearsPayment.get(0).setIsPayingFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }
        PolicyPo policyPo = policyBaseService.queryPolicyPo(policyId);

        //待支付险种页面默认展示第一条的支付记录的标识
        //本次续期支付中，只要存在有待缴费的短期附加险，就设置其标识
        if (AssertUtils.isNotEmpty(renewalAppDetailResponse.getPolicyPaymentIds())) {
            listArrearsPayment.removeIf(policyPaymentResponse -> !renewalAppDetailResponse.getPolicyPaymentIds().contains(policyPaymentResponse.getPolicyPaymentId()));
        }
        List<PolicyCoveragePaymentBo> policyCoveragePaymentBos = policyCoverageBaseService.queryPolicyCoveragePaymentBos(listArrearsPayment.stream().map(PolicyPaymentResponse::getPolicyPaymentId).distinct().collect(Collectors.toList()));
        if (AssertUtils.isNotEmpty(policyCoveragePaymentBos)) {
            policyCoveragePaymentBos.stream().filter(policyCoveragePaymentBo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(policyCoveragePaymentBo.getPrimaryFlag())).forEach(policyCoveragePaymentBo -> {
                //判断是否为一年期的趸交短期附加险 或者是一年期的在保单年度上的短期附加险
                boolean shortCoverageA = PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyCoveragePaymentBo.getPremiumFrequency())
                        && ("1".equals(policyCoveragePaymentBo.getCoveragePeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoveragePaymentBo.getCoveragePeriodUnit()));

                int months = DateUtils.getMonthSpace(DateUtils.timeStrToString(policyPo.getEffectiveDate(), DateUtils.FORMATE16),
                        DateUtils.timeStrToString(policyCoveragePaymentBo.getReceivableDate(), DateUtils.FORMATE16));
                boolean shortCoverageB = (months % 12 == 0)
                        && ("1".equals(policyCoveragePaymentBo.getCoveragePeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoveragePaymentBo.getCoveragePeriodUnit()));
                if (shortCoverageA || shortCoverageB) {
                    policyInfoResponse.getListAdditionCoverage().stream().filter(additionCoverageResponse ->
                            additionCoverageResponse.getProductId().equals(policyCoveragePaymentBo.getProductId())).findFirst()
                            .ifPresent(additionCoverageResponse -> {
                                additionCoverageResponse.setShowRenewalPermitFlag(TerminologyConfigEnum.WHETHER.YES.name());
                            });
                    renewalAppDetailResponse.getListAdditionCoverage().forEach(renewalAdditionCoverageResponse -> {
                        policyInfoResponse.getListAdditionCoverage().stream().filter(additionCoverageResponse ->
                                additionCoverageResponse.getProductId().equals(renewalAdditionCoverageResponse.getProductId())).findFirst()
                                .ifPresent(additionCoverageResponse -> additionCoverageResponse.setRenewalPermitFlag(renewalAdditionCoverageResponse.getRenewalPermitFlag()));
                    });
                }
            });
        }
        if (!AssertUtils.isNotEmpty(policyInfoResponse.getDuePremium())) {
            policyInfoResponse.setDuePremium(AssertUtils.isNotNull(listArrearsPayment.get(0).getTotalPremium()) ? listArrearsPayment.get(0).getTotalPremium().toString() : null);
        }
        policyInfoResponse.setListArrearsPayment(listArrearsPayment);

    }

    public void transPaymentRecord(PolicyPaymentResponse policyPaymentResponse, String language) {
        policyPaymentResponse.setGainedDate(DateUtils.timeStrToString(policyPaymentResponse.getGainedDate(), DateUtils.FORMATE18));
        policyPaymentResponse.setPaymentStatusName(languageCodeTransData.queryOneInternational(TerminologyTypeEnum.PAYMENT_STATUS.name(), policyPaymentResponse.getPaymentStatusCode(), language));
        policyPaymentResponse.setPremiumSource(policyPaymentResponse.getPremiumSource());
        policyPaymentResponse.setReceivableDate(DateUtils.timeStrToString(policyPaymentResponse.getReceivableDate(), DateUtils.FORMATE18));
        policyPaymentResponse.setReceivableYearMonth(DateUtils.dateStringFormatTransfer(policyPaymentResponse.getReceivableDate(), DateUtils.FORMATE18, DateUtils.FORMATE2));

        List<String> list = Arrays.asList(PolicyTermEnum.PAYMENT_METHODS.WING_OFFLINE.name(), PolicyTermEnum.PAYMENT_METHODS.ABA_PAYMENTS.name());
        //支付中的数据
        List<String> paymentStatus = Arrays.asList(PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED.name(), PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FAILED.name());
        if ((paymentStatus.contains(policyPaymentResponse.getPaymentStatusCode()) && AssertUtils.isNotEmpty(policyPaymentResponse.getPaymentModeCode()))
                || (PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name().equals(policyPaymentResponse.getPaymentStatusCode())
                && list.contains(policyPaymentResponse.getPaymentModeCode()))
        ) {
            policyPaymentResponse.setIsPayingFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }
        List<PolicyCoveragePaymentBo> policyCoveragePaymentBos = policyCoverageBaseService.queryPolicyCoveragePaymentBo(null, policyPaymentResponse.getPolicyPaymentId());
        if (!AssertUtils.isNotEmpty(policyCoveragePaymentBos)) {
            return;
        }
        PolicyPo policyPo = policyBaseService.queryPolicyPo(policyPaymentResponse.getPolicyId());

        //筛选主险为长期险，附加险为短期险的支付记录
        Optional<PolicyCoveragePaymentBo> any = policyCoveragePaymentBos.stream().filter(policyCoveragePaymentBo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoveragePaymentBo.getPrimaryFlag())
                && !PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyCoveragePaymentBo.getPremiumFrequency())
                && !("1".equals(policyCoveragePaymentBo.getCoveragePeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoveragePaymentBo.getCoveragePeriodUnit()))
        ).findAny();
        if (any.isPresent()) {
            List<AdditionCoverageResponse> additionCoverageResponses = new ArrayList<>();
            policyCoveragePaymentBos.forEach(policyCoveragePaymentBo -> {
                //判断是否为一年期的趸交短期附加险 或者是一年期的在保单年度上的短期附加险
                boolean shortCoverageA = PolicyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(policyCoveragePaymentBo.getPrimaryFlag())
                        && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyCoveragePaymentBo.getPremiumFrequency())
                        && ("1".equals(policyCoveragePaymentBo.getCoveragePeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoveragePaymentBo.getCoveragePeriodUnit()));

                int months = DateUtils.getMonthSpace(DateUtils.timeStrToString(policyPo.getEffectiveDate(), DateUtils.FORMATE16),
                        DateUtils.timeStrToString(policyCoveragePaymentBo.getReceivableDate(), DateUtils.FORMATE16));
                boolean shortCoverageB = PolicyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(policyCoveragePaymentBo.getPrimaryFlag())
                        && (months % 12 == 0)
                        && ("1".equals(policyCoveragePaymentBo.getCoveragePeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoveragePaymentBo.getCoveragePeriodUnit()));
                if (shortCoverageA || shortCoverageB) {
                    AdditionCoverageResponse additionCoverageResponse = new AdditionCoverageResponse();
                    ClazzUtils.copyPropertiesIgnoreNull(policyCoveragePaymentBo, additionCoverageResponse);
                    additionCoverageResponse.setShowRenewalPermitFlag(TerminologyConfigEnum.WHETHER.YES.name());
                    additionCoverageResponses.add(additionCoverageResponse);
                }
            });
            //是否展示是否续保的附加险
            if (AssertUtils.isNotEmpty(additionCoverageResponses)) {
                policyPaymentResponse.setAdditionCoverages(additionCoverageResponses);
            }
        }
    }

    private void resetPolicyInfoAddition(RenewalAppDetailResponse renewalAppDetailResponse, PolicyInfoResponse policyInfoResponse) {
        //筛选主险为长期险，附加险为短期险的支付记录
        boolean mainProduct = PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyInfoResponse.getPrimaryFlag())
                && !PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyInfoResponse.getPremiumFrequency())
                && !("1".equals(policyInfoResponse.getCoveragePeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyInfoResponse.getCoveragePeriodUnit()));

        if (mainProduct) {
            if (!AssertUtils.isNotEmpty(policyInfoResponse.getListAdditionCoverage()) || !AssertUtils.isNotEmpty(renewalAppDetailResponse.getListAdditionCoverage())) {
                return;
            }
            policyInfoResponse.getListAdditionCoverage().forEach(coverageResponse -> {
                //附加险为短期险
                boolean shortCoverage = PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(coverageResponse.getPremiumFrequency()) ||
                        ("1".equals(coverageResponse.getCoveragePeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(coverageResponse.getCoveragePeriodUnit()));
                if (shortCoverage) {
                    //重新设置附加险的标识
                    renewalAppDetailResponse.getListAdditionCoverage().stream().filter(additionCoverageResponse ->
                            additionCoverageResponse.getProductId().equals(coverageResponse.getProductId())).findFirst()
                            .ifPresent(additionCoverageResponse -> {
                                coverageResponse.setShowRenewalPermitFlag(TerminologyConfigEnum.WHETHER.YES.name());
                                coverageResponse.setRenewalPermitFlag(additionCoverageResponse.getRenewalPermitFlag());
                            });
                }
            });
        }
    }

    public List<PolicyPaymentResponse> getPolicyPayment(List<PolicyPaymentResponse> policyPaymentResponses, String paymentStatus) {
        List<PolicyPaymentResponse> listArrearsPayment = new ArrayList<>();
        for (PolicyPaymentResponse policyPaymentResponse : policyPaymentResponses) {
            if (paymentStatus.equals(policyPaymentResponse.getPaymentStatusCode())) {
                listArrearsPayment.add(policyPaymentResponse);
            }
        }
        return listArrearsPayment;
    }

    /**
     * 保单查询列表 数据转换
     *
     * @param policyQueryListBos
     * @return
     */
    public List<PolicyQueryListResponse> transferPolicyList(List<PolicyQueryListBo> policyQueryListBos) {
        List<PolicyQueryListResponse> policyQueryListResponses = (List<PolicyQueryListResponse>) this.converterList(
                policyQueryListBos, new TypeToken<List<PolicyQueryListResponse>>() {
                }.getType()
        );

        if (AssertUtils.isNotEmpty(policyQueryListResponses)) {
            // 调apply微服务查受理人信息
            List<String> applys = policyQueryListResponses.stream().map(PolicyQueryListResponse::getApplyId).collect(Collectors.toList());
            List<AcceptUserInfoResponse> applyAcceptRespFcs = applyAcceptApi.getAcceptUserInfos(applys).getData();

            // 调agent微服务查业务员信息
            List<String> agentIds = policyQueryListResponses.stream().map(PolicyQueryListResponse::getAgentId).distinct().collect(Collectors.toList());
            AgentApplyQueryRequest applyAgentReqFc = new AgentApplyQueryRequest();
            applyAgentReqFc.setListAgentId(agentIds);
            List<AgentResponse> applyAgentRespFcs = agentApi.agentsGet(applyAgentReqFc).getData();

            // 调用platform微服务查询机构信息
            List<String> managerBranchIds = policyQueryListResponses.stream()
                    .map(PolicyQueryListResponse::getManagerBranchId).distinct().collect(Collectors.toList());
            List<String> salesBranchIds = policyQueryListResponses.stream()
                    .map(PolicyQueryListResponse::getSalesBranchId).distinct().collect(Collectors.toList());
            List<String> branchIdList = Stream.concat(managerBranchIds.stream(), salesBranchIds.stream()).distinct().collect(Collectors.toList());
            List<BranchResponse> branchRespFcList = platformBranchApi.branchsPost(branchIdList).getData();

            // 调用platform微服务查询数据字典
            // 保单状态
            List<SyscodeRespFc> policyStatusSyscodes =
                    platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.POLICY_STATUS.name()).getData();
            // 销售渠道
            List<SyscodeRespFc> channelTypeSyscodes =
                    platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.CHANNEL_TYPE.name()).getData();

            policyQueryListResponses.forEach(response -> {
                // 受理人
                if (AssertUtils.isNotEmpty(applyAcceptRespFcs)) {
                    applyAcceptRespFcs.stream()
                            .filter(applyAcceptRespFc -> applyAcceptRespFc.getApplyId().equals(response.getApplyId()))
                            .findFirst().ifPresent(applyAcceptRespFc -> {
                        response.setAcceptDate(applyAcceptRespFc.getAcceptDate());
                        response.setAcceptUserName(applyAcceptRespFc.getAcceptUserName());
                        response.setAcceptUserId(applyAcceptRespFc.getAcceptUserId());
                    });
                }

                // 业务员
                if (AssertUtils.isNotEmpty(applyAgentRespFcs)) {
                    applyAgentRespFcs.stream()
                            .filter(applyAgentRespFc -> applyAgentRespFc.getAgentId().equals(response.getAgentId()))
                            .findFirst().ifPresent(applyAgentRespFc -> {
                        response.setAgentCode(applyAgentRespFc.getAgentCode());
                        response.setAgentName(applyAgentRespFc.getAgentName());
                    });
                }

                // 机构
                if (AssertUtils.isNotEmpty(branchRespFcList)) {
                    branchRespFcList.stream()
                            .filter(branchRespFc -> branchRespFc.getBranchId().equals(response.getManagerBranchId()))
                            .findFirst().ifPresent(branchRespFc -> response.setManagerBranchName(branchRespFc.getBranchName()));

                    branchRespFcList.stream()
                            .filter(branchRespFc -> branchRespFc.getBranchId().equals(response.getSalesBranchId()))
                            .findFirst().ifPresent(branchRespFc -> response.setSalesBranchName(branchRespFc.getBranchName()));
                }

                // 保单状态
                if (AssertUtils.isNotEmpty(policyStatusSyscodes) && AssertUtils.isNotEmpty(response.getPolicyStatus())) {
                    policyStatusSyscodes.stream()
                            .filter(syscode -> response.getPolicyStatus().equals(syscode.getCodeKey()))
                            .findFirst().ifPresent(syscode -> response.setPolicyStatus(syscode.getCodeName()));
                }

                // 销售渠道
                if (AssertUtils.isNotEmpty(channelTypeSyscodes) && AssertUtils.isNotEmpty(response.getChannelTypeCode())) {
                    channelTypeSyscodes.stream()
                            .filter(syscode -> response.getChannelTypeCode().equals(syscode.getCodeKey()))
                            .findFirst().ifPresent(syscode -> response.setChannelTypeCode(syscode.getCodeName()));
                }
            });
        }

        return policyQueryListResponses;
    }

    /**
     * 生成PDF保单文件
     *
     * @param policyBo 保单ID
     * @param language 附件语言
     * @return 附件ID
     */
    public List<AttachmentResponse> attachmentPdfGenerate(PolicyBo policyBo, String language) {
        ElectronicPolicyGeneratorRequest policyGeneratorReqFc = this.packagePolicyPrintData(policyBo, language);

        ResultObject<List<AttachmentResponse>> respFcResultObject = attachmentPDFDocumentApi.electronicPolicyGenerator(policyGeneratorReqFc);
        AssertUtils.isResultObjectDataNull(this.getLogger(), respFcResultObject);
        List<AttachmentResponse> attachmentResponseFcList = respFcResultObject.getData();
        attachmentResponseFcList.forEach(attachmentResponseFc -> {
            //保存保单附件
            PolicyAttachmentPo policyAttachmentPo = new PolicyAttachmentPo();
            policyAttachmentPo.setPolicyId(policyBo.getPolicyId());
            policyAttachmentPo.setAttachmentId(attachmentResponseFc.getMediaId());
            policyAttachmentPo.setAttachmentTypeCode(attachmentResponseFc.getTemplateType());
            policyAttachmentPo.setAttachmentSeq(attachmentResponseFc.getSeq());
            policyAttachmentPo.setLanguage(language);
            policyBoService.savePolicyAttachmentPo(policyAttachmentPo);
        });
        return attachmentResponseFcList;
    }

    public ElectronicPolicyGeneratorRequest packagePolicyPrintData(PolicyBo policyBo, String language) {
        // 校验数据
        AssertUtils.isNotNull(this.getLogger(), policyBo, PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        // AssertUtils.isNotEmpty(this.getLogger(), policyBo.getListPolicyInsured(), PolicyErrorConfigEnum.POLICY_POLICY_INSURED_IS_NOT_NULL);
//        if (!PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name().equals(policyBo.getPolicyStatus())) {
//            throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_POLICY_IS_INVALID);
//        }
        // 修复新单承保之后再次打印保单时 policyPaymentBo.getGainedDate() 为null
        List<PolicyPaymentBo> policyPaymentBos = policyBo.getListPolicyPayment();
        if (AssertUtils.isNotEmpty(policyPaymentBos)) {
            PolicyPaymentBo policyPaymentBo = policyPaymentBos.get(policyPaymentBos.size() - 1);
            // 始终取新单承保时的 policyPaymentBo
            policyBo.setPolicyPayment(policyPaymentBo);
            policyBo.getPolicyPremium().setPolicyPayment(policyPaymentBo);
        }

        PolicyInsuredBo policyInsuredBo = policyBo.getListPolicyInsured().stream().filter(insuredBo -> AssertUtils.isNotEmpty(insuredBo.getListPolicyCoverage())).findFirst().get();
        PolicyCoverageBo policyCoverageBo = policyInsuredBo.getListPolicyCoverage().stream().filter(coverageBo -> PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag())).findFirst().get();
        String productMainId = policyCoverageBo.getProductId();

        // 调用代理人微服务查询代理人信息
        ResultObject<AgentResponse> resultObject = agentApi.agentByIdGet(policyBo.getPolicyAgent().getAgentId());

        if (!AssertUtils.isResultObjectDataNull(resultObject)) {
            // 代理人姓名
            policyBo.getPolicyAgent().setAgentName(resultObject.getData().getAgentName());
        }

        // 调用产品微服务, 查询产品现金价值
        ApplyRequest applyRequest = (ApplyRequest) converterObject(policyBo, ApplyRequest.class);
        applyRequest.setBusinessType("APPLY");
        this.getLogger().info("applyRequest:{}", JSON.toJSONString(applyRequest));
        ResultObject<List<PolicyCashValueResponse>> cashValueResultObject = productApi.getCashValue(applyRequest);
        this.getLogger().info(JSON.toJSONString("cashValueResultObject:" + cashValueResultObject));
        if (!AssertUtils.isResultObjectDataNull(cashValueResultObject)) {
            List<PolicyCashValueResponse> productCashValueRespFcs = cashValueResultObject.getData();
            List<ProductCashValueBo> productCashValueBos = (List<ProductCashValueBo>) this.converterList(
                    productCashValueRespFcs, new TypeToken<List<ProductCashValueBo>>() {
                    }.getType()
            );
            policyBo.setListCashValue(productCashValueBos);
        }

        // 国际化
        List<String> codeTypes = Arrays.asList(InternationalTypeEnum.HEALTH_NOTICE.name(),
                TerminologyTypeEnum.GENDER.name(),
                TerminologyTypeEnum.NATIONALITY.name(),
                TerminologyTypeEnum.RELATIONSHIP_WITH_THE_APPLICANT.name(),
                TerminologyTypeEnum.RELATIONSHIP_WITH_THE_INSURED.name(),
                TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(),
                TerminologyTypeEnum.ID_TYPE.name(),
                TerminologyTypeEnum.INSURANCE_TYPE.name(),
                TerminologyTypeEnum.PAYMENT_METHODS.name(),
                TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name());
        ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(language, codeTypes);
        Map<String, List<SyscodeResponse>> mapSyscodeList = mapResultObject.getData();
        // 证件类型
        List<SyscodeResponse> idTypeSyscodes = mapSyscodeList.get(TerminologyTypeEnum.ID_TYPE.name());
        // 性别
        List<SyscodeResponse> genderSyscodes = mapSyscodeList.get(TerminologyTypeEnum.GENDER.name());
        // 与投保人关系
        List<SyscodeResponse> relationshipWithApplicantSyscodes = mapSyscodeList.get(TerminologyTypeEnum.RELATIONSHIP_WITH_THE_APPLICANT.name());
        // 与被保人关系
        List<SyscodeResponse> relationshipWithInsuredSyscodes = mapSyscodeList.get(TerminologyTypeEnum.RELATIONSHIP_WITH_THE_INSURED.name());
        // 缴费周期
        List<SyscodeResponse> premiumFrequencySyscodes = mapSyscodeList.get(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name());
        // 缴费方式
        List<SyscodeResponse> paymentMethodsSyscodes = mapSyscodeList.get(TerminologyTypeEnum.PAYMENT_METHODS.name());
        // 缴费期限类型
        List<SyscodeResponse> premiumPeriodUnitSyscodes = mapSyscodeList.get(TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name());
        // 投保人
        if (AssertUtils.isNotNull(policyBo.getPolicyApplicant())) {
            policyBo.getPolicyApplicant().setIdTypeName(LanguageUtils.getCodeNameLanguage(idTypeSyscodes, policyBo.getPolicyApplicant().getIdType()));
            policyBo.getPolicyApplicant().setSexName(LanguageUtils.getCodeNameLanguage(genderSyscodes, policyBo.getPolicyApplicant().getSex()));
            // 地址拼接
            if (AssertUtils.isNotEmpty(policyBo.getPolicyApplicant().getHomeAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(policyBo.getApplicant().getHomeAreaCode(), language);
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                    policyBo.getApplicant().setFullAddress(respFcResultObject.getData().getAreaName() + " " + policyBo.getApplicant().getHomeAddress());
                }
            }
        }

        // 被保人
        if (AssertUtils.isNotEmpty(policyBo.getListPolicyInsured())) {
            policyBo.getListPolicyInsured().forEach(insuredBo -> {
                // 证件类型
                insuredBo.setIdTypeName((LanguageUtils.getCodeNameLanguage(idTypeSyscodes, insuredBo.getIdType())));
                // 性别
                insuredBo.setSexName((LanguageUtils.getCodeNameLanguage(genderSyscodes, insuredBo.getSex())));
                // 与投保人关系
                insuredBo.setRelationshipName((LanguageUtils.getCodeNameLanguage(relationshipWithApplicantSyscodes, insuredBo.getRelationship())));
                // 地址拼接
                if (AssertUtils.isNotEmpty(insuredBo.getHomeAreaCode())) {
                    ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(insuredBo.getHomeAreaCode(), language);
                    if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                        insuredBo.setFullAddress(respFcResultObject.getData().getAreaName() + " " + insuredBo.getHomeAddress());
                    }
                }
                // 险种信息
                if (AssertUtils.isNotEmpty(insuredBo.getListPolicyCoverage())) {

                    insuredBo.getListPolicyCoverage().forEach(coverageBo -> {
                        // 险种支付信息
                        if (AssertUtils.isNotNull(coverageBo.getPolicyCoveragePremium())) {
                            // 缴费周期
                            coverageBo.getPolicyCoveragePremium().setPremiumFrequencyName((LanguageUtils.getCodeNameLanguage(premiumFrequencySyscodes, coverageBo.getPolicyCoveragePremium().getPremiumFrequency())));
                        }
                        // 缴费期限
                        coverageBo.setPremiumPeriodUnitName(LanguageUtils.getCodeNameLanguage(premiumPeriodUnitSyscodes, coverageBo.getPremiumPeriodUnit()));
                        // 缴费周期
                        coverageBo.setPremiumFrequencyName(LanguageUtils.getCodeNameLanguage(premiumFrequencySyscodes, coverageBo.getPremiumFrequency()));
                    });
                }
                // 受益人信息
                if (AssertUtils.isNotEmpty(insuredBo.getListPolicyBeneficiary())) {
                    insuredBo.getListPolicyBeneficiary().forEach(policyBeneficiaryInfoBo -> {
                        if (AssertUtils.isNotNull(policyBeneficiaryInfoBo.getPolicyBeneficiary())) {
                            // 证件类型
                            policyBeneficiaryInfoBo.getPolicyBeneficiary().setIdTypeName((LanguageUtils.getCodeNameLanguage(idTypeSyscodes, policyBeneficiaryInfoBo.getPolicyBeneficiary().getIdType())));
                        }
                        // 与被保人关系
                        policyBeneficiaryInfoBo.setRelationshipName(LanguageUtils.getCodeNameLanguage(relationshipWithInsuredSyscodes, policyBeneficiaryInfoBo.getRelationship()));
                    });
                }
            });

            // 保单缴费信息
            if (AssertUtils.isNotNull(policyBo.getPolicyPremium())) {
                // 缴费信息
                if (AssertUtils.isNotNull(policyBo.getPolicyPremium().getPolicyPayment())) {
                    // 缴费方式
                    policyBo.getPolicyPremium().getPolicyPayment().setPayModeCodeName(LanguageUtils.getCodeNameLanguage(paymentMethodsSyscodes, policyBo.getPolicyPremium().getPolicyPayment().getPayModeCode()));
                }
            }

        }

        ElectronicPolicyGeneratorRequest policyGeneratorReqFc = new ElectronicPolicyGeneratorRequest();
        policyGeneratorReqFc.setPdfType(PolicyTermEnum.PDF_TYPE.POLICY.name());
        policyGeneratorReqFc.setProductId(productMainId);
        policyGeneratorReqFc.setLanguage(language);
        policyGeneratorReqFc.setContent(JackSonUtils.toJson(policyBo, language));

        return policyGeneratorReqFc;
    }

    public List<AttachmentResponse> policyConfirmAttachmentPdfGenerate(PolicyBo policyBo, String language) {
        ElectronicPolicyGeneratorRequest policyGeneratorReqFc = this.packagePolicyPrintData(policyBo, language);
        policyGeneratorReqFc.setPdfType(POLICY_CONFIRM.name());
        policyGeneratorReqFc.setProductMainId(policyGeneratorReqFc.getProductId());
        //网销,34号产品单独模板生成确认书
        if ("PRO880000000000020A".equals(policyGeneratorReqFc.getProductMainId())) {
            policyGeneratorReqFc.setProductId("PRO880000000000020A");
        }
        else if ("PRO880000000000034".equals(policyGeneratorReqFc.getProductMainId())) {
            policyGeneratorReqFc.setProductId("PRO880000000000034");
        }
        else {
            policyGeneratorReqFc.setProductId(null);
        }

        ResultObject<List<AttachmentResponse>> respFcResultObject = attachmentPDFDocumentApi.electronicPolicyGenerator(policyGeneratorReqFc);
        AssertUtils.isResultObjectDataNull(this.getLogger(), respFcResultObject);
        List<AttachmentResponse> attachmentResponseFcList = respFcResultObject.getData();
        attachmentResponseFcList.forEach(attachmentResponseFc -> {
            //保存保单附件
            PolicyAttachmentPo policyAttachmentPo = new PolicyAttachmentPo();
            policyAttachmentPo.setPolicyId(policyBo.getPolicyId());
            policyAttachmentPo.setAttachmentId(attachmentResponseFc.getMediaId());
            policyAttachmentPo.setAttachmentTypeCode(attachmentResponseFc.getTemplateType());
            policyAttachmentPo.setAttachmentSeq(attachmentResponseFc.getSeq());
            policyAttachmentPo.setLanguage(language);
            policyBoService.savePolicyAttachmentPo(policyAttachmentPo);
        });
        return attachmentResponseFcList;
    }
}
