package com.gclife.endorse.base.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.endorse.base.dao.EndorseBaseDao;
import com.gclife.endorse.base.model.bo.EndorseBo;
import com.gclife.endorse.base.model.bo.ReportEndorseBo;
import com.gclife.endorse.base.model.bo.ReportEndorsePaymentBo;
import com.gclife.endorse.base.model.config.EndorseTermEnum;
import com.gclife.endorse.core.jooq.tables.pojos.EndorsePo;
import com.gclife.endorse.core.jooq.tables.pojos.EndorseRemarkPo;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.gclife.endorse.base.model.config.EndorseTermEnum.CHANGE_FLAG.ADD;
import static com.gclife.endorse.base.model.config.EndorseTermEnum.CHANGE_FLAG.SUBTRACT;
import static com.gclife.endorse.base.model.config.EndorseTermEnum.ENDORSE_PROJECT.*;
import static com.gclife.endorse.base.model.config.EndorseTermEnum.ENDORSE_STATUS.ENDORSE_STATUS_EFFETIVE;
import static com.gclife.endorse.base.model.config.EndorseTermEnum.ENDORSE_TYPE.GROUP_POLICY;
import static com.gclife.endorse.base.model.config.EndorseTermEnum.ENDORSE_TYPE.POLICY;
import static com.gclife.endorse.base.model.config.EndorseTermEnum.FEE_TYPE.GET;
import static com.gclife.endorse.base.model.config.EndorseTermEnum.FEE_TYPE.PAY;
import static com.gclife.endorse.base.model.config.EndorseTermEnum.INSURED_STATUS.EFFECTIVE;
import static com.gclife.endorse.core.jooq.Tables.*;
import static com.gclife.endorse.core.jooq.tables.BaseProject.BASE_PROJECT;
import static com.gclife.endorse.core.jooq.tables.Endorse.ENDORSE;
import static com.gclife.endorse.core.jooq.tables.EndorseItem.ENDORSE_ITEM;

/**
 * <AUTHOR>
 * @version v2.0
 * Description: 保全基础DAO
 * @date 18-8-31
 */
@Repository
public class EndorseBaseDaoImpl extends BaseDaoImpl implements EndorseBaseDao {

    /**
     * 根据保全ID查询保全
     *
     * @param endorseId 保全ID
     * @return 保全
     */
    @Override
    public EndorsePo queryOneEndorse(String endorseId) {
        return this.getDslContext().selectFrom(ENDORSE)
                .where(ENDORSE.ENDORSE_ID.eq(endorseId))
                .and(ENDORSE.VALID_FLAG.eq(EndorseTermEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(EndorsePo.class);
    }

    @Override
    public List<EndorsePo> queryEndorse(List<String> endorseIds) {
        return this.getDslContext().selectFrom(ENDORSE)
                .where(ENDORSE.ENDORSE_ID.in(endorseIds))
                .and(ENDORSE.VALID_FLAG.eq(EndorseTermEnum.VALID_FLAG.effective.name()))
                .fetchInto(EndorsePo.class);
    }

    @Override
    public List<EndorsePo> queryEndorseList(BasePageRequest basePageRequest) {
        return this.getDslContext().select(ENDORSE.fields()).from(ENDORSE)
                .where(ENDORSE.VALID_FLAG.eq(EndorseTermEnum.VALID_FLAG.effective.name()))
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize()).fetchInto(EndorsePo.class);
    }

    @Override
    public List<EndorsePo> listTimeOutUnpaidEndorsePo(BasePageRequest basePageRequest) {
        List<String> paymentStatus = Arrays.asList(
                EndorseTermEnum.PAYMENT_STATUS.PAYMENT_INITIAL.name(),
                EndorseTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name(),
                EndorseTermEnum.PAYMENT_STATUS.PAYMENT_FAILED.name());

        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(ENDORSE.fields())
                .from(ENDORSE)
                .leftJoin(ENDORSE_ACCEPT).on(ENDORSE_ACCEPT.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID).and(ENDORSE_ACCEPT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_FEE).on(ENDORSE_FEE.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID).and(ENDORSE_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM).on(ENDORSE_ITEM.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID).and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM_FEE).on(ENDORSE_ITEM_FEE.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID).and(ENDORSE_ITEM_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(CHG_GROUP_ADD_SUBTRACT_INSURED).on(ENDORSE_ITEM.ENDORSE_ITEM_ID.eq(CHG_GROUP_ADD_SUBTRACT_INSURED.ENDORSE_ITEM_ID).and(CHG_GROUP_ADD_SUBTRACT_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));

        selectJoinStep.where(ENDORSE.ENDORSE_STATUS.in(EndorseTermEnum.GROUP_ENDORSE_STATUS.PAYMENT.name()))
                .and(ENDORSE_ACCEPT.ENDORSE_TYPE.eq(EndorseTermEnum.ENDORSE_TYPE.GROUP_POLICY.name()))
                .and(ENDORSE_ITEM.ENDORSE_FINISH_DATE.lt(DateUtils.addStringDayRT(DateUtils.timeToTimeTop(DateUtils.getCurrentTime()), -8)))
                .and(ENDORSE_FEE.FEE_STATUS.in(paymentStatus))
                .and(ENDORSE_ITEM_FEE.FEE_STATUS.in(paymentStatus))
                .and(CHG_GROUP_ADD_SUBTRACT_INSURED.APPLY_TYPE.eq(EndorseTermEnum.GROUP_ENDORSE_PROJECT.ADD_INSURED.name()))
                .orderBy(ENDORSE.CREATED_DATE)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize());

        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(EndorsePo.class);
    }

    /**
     * 查询保全资料
     *
     * @param basePageRequest 分页
     * @param startDate       开始日期
     * @return ReportEndorseResponses
     */
    @Override
    public List<ReportEndorseBo> listReportEndorseData(BasePageRequest basePageRequest, String startDate) {
        //同步时间取更新时间，更新时间为空则取创建时间
        Field<Long> endorseCreateDate = ENDORSE.UPDATED_DATE.nvl(ENDORSE.CREATED_DATE);

        SelectOnConditionStep<Record> records = this.getDslContext()
                .select(ENDORSE.ENDORSE_ID, ENDORSE.ENDORSE_STATUS, ENDORSE.EFFECTIVE_DATE)
                .select(ENDORSE_ITEM.ENDORSE_ITEM_ID, ENDORSE_ITEM.PROJECT_ID, ENDORSE_ITEM.EFFECTIVE_TYPE, ENDORSE_ITEM.EFFECTIVE_START_DATE,
                        ENDORSE_ITEM.EFFECTIVE_END_DATE, ENDORSE_ITEM.ENDORSE_FINISH_DATE)
                .select(BASE_PROJECT.PROJECT_CODE, BASE_PROJECT.PROJECT_NAME)
                .select(ENDORSE_ACCEPT.ACCEPT_NO, ENDORSE_ACCEPT.ENDORSE_TYPE, ENDORSE_ACCEPT.APPLY_ID, ENDORSE_ACCEPT.APPLY_NO, ENDORSE_ACCEPT.ACCEPT_BRANCH_ID, ENDORSE_ACCEPT.ACCEPT_DATE, ENDORSE_ACCEPT.APPLY_DATE)
                .select(ENDORSE_FEE.TOTAL_FEE, ENDORSE_FEE.FEE_TYPE, ENDORSE_FEE.FEE_STATUS)
                .select(endorseCreateDate.as("endorseCreateDate"))
                .select(ENDORSE_AGENT.AGENT_ID)
                .select(ENDORSE_POLICY.APPROVE_DATE)
                .select(ENDORSE.ENDORSE_ID.countOver().as("totalLine"))
                .from(ENDORSE)
                .leftJoin(ENDORSE_ACCEPT).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ACCEPT.ENDORSE_ID).and(ENDORSE_ACCEPT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID).and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(BASE_PROJECT).on(BASE_PROJECT.PROJECT_ID.eq(ENDORSE_ITEM.PROJECT_ID))
                .leftJoin(ENDORSE_FEE).on(ENDORSE_FEE.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID))
                .leftJoin(ENDORSE_AGENT).on(ENDORSE_AGENT.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID).and(ENDORSE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_POLICY).on(ENDORSE_POLICY.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID).and(ENDORSE_POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));

        records.where(ENDORSE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                .and(endorseCreateDate.between(DateUtils.stringToTime(startDate, DateUtils.FORMATE51), DateUtils.getCurrentTime())));

        records.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        System.out.println(records.toString());
        return records.fetchInto(ReportEndorseBo.class);
    }

    @Override
    public List<ReportEndorseBo> listReportEndorseDataDetail(BasePageRequest basePageRequest, String startDate) {
        List<ReportEndorseBo> reportEndorseBos = new ArrayList<>();
        //同步时间取更新时间，更新时间为空则取创建时间
        Field<Long> endorseCreateDate = ENDORSE.UPDATED_DATE.nvl(ENDORSE.CREATED_DATE);

        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(ENDORSE.ENDORSE_ID, ENDORSE.ENDORSE_STATUS, ENDORSE.EFFECTIVE_DATE)
                .select(ENDORSE_ITEM.ENDORSE_ITEM_ID, ENDORSE_ITEM.PROJECT_ID, ENDORSE_ITEM.EFFECTIVE_TYPE, ENDORSE_ITEM.EFFECTIVE_START_DATE,
                        ENDORSE_ITEM.EFFECTIVE_END_DATE, ENDORSE_ITEM.ENDORSE_FINISH_DATE)
                .select(BASE_PROJECT.PROJECT_CODE, BASE_PROJECT.PROJECT_NAME)
                .select(ENDORSE_ACCEPT.ACCEPT_NO, ENDORSE_ACCEPT.ENDORSE_TYPE, ENDORSE_ACCEPT.APPLY_ID, ENDORSE_ACCEPT.APPLY_NO, ENDORSE_ACCEPT.ACCEPT_BRANCH_ID, ENDORSE_ACCEPT.ACCEPT_DATE, ENDORSE_ACCEPT.APPLY_DATE)
                .select(ENDORSE_FEE.TOTAL_FEE, ENDORSE_FEE.FEE_TYPE, ENDORSE_FEE.FEE_STATUS)
                .select(endorseCreateDate.as("endorseCreateDate"))
                .select(ENDORSE_AGENT.AGENT_ID)
                .select(ENDORSE_POLICY.APPROVE_DATE)
                .select(ENDORSE.ENDORSE_ID.countOver().as("totalLine"))
                .from(ENDORSE)
                .leftJoin(ENDORSE_ACCEPT).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ACCEPT.ENDORSE_ID).and(ENDORSE_ACCEPT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID).and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(BASE_PROJECT).on(BASE_PROJECT.PROJECT_ID.eq(ENDORSE_ITEM.PROJECT_ID))
                .leftJoin(ENDORSE_FEE).on(ENDORSE_FEE.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID))
                .leftJoin(ENDORSE_AGENT).on(ENDORSE_AGENT.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID).and(ENDORSE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_POLICY).on(ENDORSE_POLICY.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID).and(ENDORSE_POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .where(ENDORSE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                        .and(endorseCreateDate.between(DateUtils.stringToTime(startDate, DateUtils.FORMATE51), DateUtils.getCurrentTime()))
                        .and(
                                // 查询条件
                                BASE_PROJECT.PROJECT_CODE.in(
                                                CUSTOMER_CONTACT_CHANGE.name(),
                                                MODIFY_APPLICANT_REPRESENTATIVE.name(),
                                                MODIFY_ESTABLISHMENT_CONTACTS_INFO.name(),
                                                CHANGE_ID.name(), CHANGE_OCCUPATION.name(), BENEFICIARY_CHANGE.name(),
                                                OTHER.name()
                                        )
                                        .or(BASE_PROJECT.PROJECT_CODE.isNull())
                        ));

        SelectSeekStep4<Record, Long, String, String, String> records5 = this.getDslContext()
                .select(ENDORSE.ENDORSE_ID, ENDORSE.ENDORSE_STATUS, ENDORSE.EFFECTIVE_DATE)
                .select(ENDORSE_ITEM.ENDORSE_ITEM_ID, ENDORSE_ITEM.PROJECT_ID, ENDORSE_ITEM.EFFECTIVE_TYPE, ENDORSE_ITEM.EFFECTIVE_START_DATE,
                        ENDORSE_ITEM.EFFECTIVE_END_DATE, ENDORSE_ITEM.ENDORSE_FINISH_DATE)
                .select(BASE_PROJECT.PROJECT_CODE, BASE_PROJECT.PROJECT_NAME)
                .select(ENDORSE_ACCEPT.ACCEPT_NO, ENDORSE_ACCEPT.ENDORSE_TYPE, ENDORSE_ACCEPT.APPLY_ID, ENDORSE_ACCEPT.APPLY_NO, ENDORSE_ACCEPT.ACCEPT_BRANCH_ID, ENDORSE_ACCEPT.ACCEPT_DATE, ENDORSE_ACCEPT.APPLY_DATE)
                .select(ENDORSE_FEE.FEE_STATUS)
                .select(ENDORSE_AGENT.AGENT_ID)
                .select(ENDORSE_POLICY.APPROVE_DATE)
                .select(ENDORSE_ITEM_FEE_DETAIL.FEE.as("totalFee"),
                        ENDORSE_ITEM_FEE_DETAIL.FEE_TYPE,
                        ENDORSE_ITEM_FEE_DETAIL.PRODUCT_ID,
                        ENDORSE_ITEM_FEE_DETAIL.PRODUCT_NAME,
                        ENDORSE_ITEM_FEE_DETAIL.PRIMARY_FLAG,
                        ENDORSE_ITEM_FEE_DETAIL.PREMIUM_FREQUENCY)
                .select(ENDORSE.ENDORSE_ID.countOver().as("totalLine"))
                .from(ENDORSE)
                .leftJoin(ENDORSE_ACCEPT).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ACCEPT.ENDORSE_ID)
                        .and(ENDORSE_ACCEPT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_FEE).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_FEE.ENDORSE_ID)
                        .and(ENDORSE_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID)
                        .and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_POLICY).on(ENDORSE_POLICY.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(BASE_PROJECT).on(BASE_PROJECT.PROJECT_ID.eq(ENDORSE_ITEM.PROJECT_ID)
                        .and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM_FEE).on(ENDORSE_ITEM_FEE.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID)
                        .and(ENDORSE_ITEM_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM_FEE_DETAIL).on(ENDORSE_ITEM_FEE_DETAIL.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM_FEE.ENDORSE_ITEM_ID)
                        .and(ENDORSE_ITEM_FEE_DETAIL.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_AGENT).on(ENDORSE_AGENT.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .where(ENDORSE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                        .and(endorseCreateDate.between(DateUtils.stringToTime(startDate, DateUtils.FORMATE51), DateUtils.getCurrentTime()))
                        .and(
                                // 增减附加项
                                BASE_PROJECT.PROJECT_CODE.in(
                                        HESITATION_REVOKE.name(),
                                        SURRENDER.name(),
                                        REINSTATEMENT.name(),
                                        MODE_OF_PAYMENT_MODIFY.name()

                                )
                        )
                )
                /*.groupBy(
                        ENDORSE.ENDORSE_ID,
                        ENDORSE_ITEM.ENDORSE_ITEM_ID,
                        ENDORSE_ITEM.PROJECT_ID,
                        BASE_PROJECT.PROJECT_CODE,
                        BASE_PROJECT.PROJECT_NAME,
                        ENDORSE_ACCEPT.APPLY_ID,
                        ENDORSE_ACCEPT.APPLY_NO,
                        ENDORSE_ACCEPT.ACCEPT_NO,
                        ENDORSE_ACCEPT.ENDORSE_TYPE,
                        ENDORSE_ACCEPT.ACCEPT_BRANCH_ID,
                        ENDORSE_ACCEPT.ACCEPT_DATE,
                        ENDORSE_ACCEPT.APPLY_DATE,
                        ENDORSE_ITEM_FEE_DETAIL.FEE,
                        ENDORSE_ITEM_FEE_DETAIL.FEE_TYPE,
                        ENDORSE_ITEM_FEE_DETAIL.PRODUCT_ID,
                        ENDORSE_ITEM_FEE_DETAIL.PRODUCT_NAME,
                        ENDORSE_ITEM_FEE_DETAIL.PRIMARY_FLAG,
                        ENDORSE_ITEM_FEE_DETAIL.PREMIUM_FREQUENCY,
                        ENDORSE_AGENT.AGENT_ID,
                        ENDORSE_POLICY.APPROVE_DATE,
                        ENDORSE_FEE.FEE_TYPE,
                        ENDORSE_FEE.FEE_STATUS)*/
                .orderBy(ENDORSE_ACCEPT.ACCEPT_DATE.desc(),
                        ENDORSE_ACCEPT.APPLY_ID.desc(),
                        ENDORSE_ACCEPT.APPLY_NO.desc(),
                        //ENDORSE_COVERAGE.INSURED_ID.desc(),
                        ENDORSE_ITEM_FEE_DETAIL.PRIMARY_FLAG.desc()
                );


        SelectSeekStep5<Record, Long, String, String, String, String> records1 = this.getDslContext()
                .select(ENDORSE.ENDORSE_ID, ENDORSE.ENDORSE_STATUS, ENDORSE.EFFECTIVE_DATE)
                .select(ENDORSE_ITEM.ENDORSE_ITEM_ID, ENDORSE_ITEM.PROJECT_ID, ENDORSE_ITEM.EFFECTIVE_TYPE, ENDORSE_ITEM.EFFECTIVE_START_DATE,
                        ENDORSE_ITEM.EFFECTIVE_END_DATE, ENDORSE_ITEM.ENDORSE_FINISH_DATE)
                .select(BASE_PROJECT.PROJECT_CODE, BASE_PROJECT.PROJECT_NAME)
                .select(ENDORSE_ACCEPT.ACCEPT_NO, ENDORSE_ACCEPT.ENDORSE_TYPE, ENDORSE_ACCEPT.APPLY_ID, ENDORSE_ACCEPT.APPLY_NO, ENDORSE_ACCEPT.ACCEPT_BRANCH_ID, ENDORSE_ACCEPT.ACCEPT_DATE, ENDORSE_ACCEPT.APPLY_DATE)
                .select(ENDORSE_FEE.FEE_STATUS)
                .select(ENDORSE_AGENT.AGENT_ID)
                .select(ENDORSE_POLICY.APPROVE_DATE)
                .select(ENDORSE_ITEM_FEE_DETAIL.FEE.as("totalFee"),ENDORSE_ITEM_FEE_DETAIL.FEE_TYPE,
                        ENDORSE_ITEM_FEE_DETAIL.PRODUCT_ID,
                        ENDORSE_ITEM_FEE_DETAIL.PRODUCT_NAME,
                        ENDORSE_ITEM_FEE_DETAIL.PRIMARY_FLAG,
                        ENDORSE_ITEM_FEE_DETAIL.PREMIUM_FREQUENCY,
                        ENDORSE_ITEM_FEE_DETAIL.INSURED_ID)
               /* .select(ENDORSE_COVERAGE.POLICY_ID,
                        ENDORSE_COVERAGE.PRODUCT_CODE,
                        ENDORSE_COVERAGE.COVERAGE_PERIOD,
                        ENDORSE_COVERAGE.COVERAGE_PERIOD_UNIT,
                        ENDORSE_COVERAGE.PREMIUM_PERIOD,
                        ENDORSE_COVERAGE.PREMIUM_PERIOD_UNIT)*/
                .select(ENDORSE.ENDORSE_ID.countOver().as("totalLine"))
                .from(ENDORSE)
                .leftJoin(ENDORSE_ACCEPT).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ACCEPT.ENDORSE_ID)
                        .and(ENDORSE_ACCEPT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_FEE).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_FEE.ENDORSE_ID)
                        .and(ENDORSE_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID)
                        .and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_POLICY).on(ENDORSE_POLICY.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(BASE_PROJECT).on(BASE_PROJECT.PROJECT_ID.eq(ENDORSE_ITEM.PROJECT_ID)
                        .and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                /*.leftJoin(ENDORSE_COVERAGE)
                .on(ENDORSE_COVERAGE.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(ENDORSE_COVERAGE.CHANGE_FLAG.in(ADD.name(), SUBTRACT.name()))
                        .and(ENDORSE_COVERAGE.INSURED_ID.isNotNull()))
                .leftJoin(ENDORSE_INSURED)
                .on(ENDORSE_COVERAGE.INSURED_ID.eq(ENDORSE_INSURED.INSURED_ID)
                        .and(ENDORSE_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))*/
                .leftJoin(ENDORSE_ITEM_FEE).on(ENDORSE_ITEM_FEE.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID)
                        .and(ENDORSE_ITEM_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM_FEE_DETAIL).on(ENDORSE_ITEM_FEE_DETAIL.ENDORSE_ITEM_FEE_ID.eq(ENDORSE_ITEM_FEE.ENDORSE_ITEM_FEE_ID)
                        .and(ENDORSE_ITEM_FEE_DETAIL.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_AGENT).on(ENDORSE_AGENT.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .where(ENDORSE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                        .and(endorseCreateDate.between(DateUtils.stringToTime(startDate, DateUtils.FORMATE51), DateUtils.getCurrentTime()))
                        .and(
                                // 增减附加项
                                BASE_PROJECT.PROJECT_CODE.in(
                                        ADD_ADDITIONAL.name(),
                                        GROUP_ADD_ADDITIONAL.name()
                                )
                        )
                )
                /*.groupBy(
                        ENDORSE.ENDORSE_ID,
                        ENDORSE_ITEM.ENDORSE_ITEM_ID,
                        ENDORSE_ITEM.PROJECT_ID,
                        BASE_PROJECT.PROJECT_CODE,
                        BASE_PROJECT.PROJECT_NAME,
                        ENDORSE_ACCEPT.APPLY_ID,
                        ENDORSE_ACCEPT.APPLY_NO,
                        ENDORSE_ACCEPT.ACCEPT_NO,
                        ENDORSE_ACCEPT.ENDORSE_TYPE,
                        ENDORSE_ACCEPT.ACCEPT_BRANCH_ID,
                        ENDORSE_ACCEPT.ACCEPT_DATE,
                        ENDORSE_ACCEPT.APPLY_DATE,
                        ENDORSE_AGENT.AGENT_ID,
                        ENDORSE_POLICY.APPROVE_DATE,
                        ENDORSE_COVERAGE.INSURED_ID,
                        ENDORSE_COVERAGE.POLICY_ID,
                        ENDORSE_COVERAGE.PRODUCT_ID,
                        ENDORSE_COVERAGE.PRODUCT_CODE,
                        ENDORSE_COVERAGE.PRODUCT_NAME,
                        ENDORSE_COVERAGE.PREMIUM_FREQUENCY,
                        ENDORSE_COVERAGE.COVERAGE_PERIOD,
                        ENDORSE_COVERAGE.COVERAGE_PERIOD_UNIT,
                        ENDORSE_COVERAGE.PREMIUM_PERIOD,
                        ENDORSE_COVERAGE.PREMIUM_PERIOD_UNIT,
                        ENDORSE_COVERAGE.PRIMARY_FLAG,
                        ENDORSE_ITEM_FEE_DETAIL.FEE,
                        //ENDORSE_COVERAGE.ACTUAL_PREMIUM,
                        ENDORSE_ITEM_FEE_DETAIL.FEE_TYPE,
                        ENDORSE_FEE.FEE_STATUS)*/
                .orderBy(ENDORSE_ACCEPT.ACCEPT_DATE.desc(),
                        ENDORSE_ACCEPT.APPLY_ID.desc(),
                        ENDORSE_ACCEPT.APPLY_NO.desc(),
                        ENDORSE_ITEM_FEE_DETAIL.INSURED_ID.desc(),
                        ENDORSE_ITEM_FEE_DETAIL.PRIMARY_FLAG.desc());


        SelectSeekStep5<Record, Long, String, String, String, String> records = this.getDslContext()
                .select(ENDORSE.ENDORSE_ID, ENDORSE.ENDORSE_STATUS, ENDORSE.EFFECTIVE_DATE)
                .select(ENDORSE_ITEM.ENDORSE_ITEM_ID, ENDORSE_ITEM.PROJECT_ID, ENDORSE_ITEM.EFFECTIVE_TYPE, ENDORSE_ITEM.EFFECTIVE_START_DATE,
                        ENDORSE_ITEM.EFFECTIVE_END_DATE, ENDORSE_ITEM.ENDORSE_FINISH_DATE)
                .select(BASE_PROJECT.PROJECT_CODE, BASE_PROJECT.PROJECT_NAME)
                .select(ENDORSE_ACCEPT.ACCEPT_NO, ENDORSE_ACCEPT.ENDORSE_TYPE, ENDORSE_ACCEPT.APPLY_ID, ENDORSE_ACCEPT.APPLY_NO, ENDORSE_ACCEPT.ACCEPT_BRANCH_ID, ENDORSE_ACCEPT.ACCEPT_DATE, ENDORSE_ACCEPT.APPLY_DATE)
                .select(ENDORSE_FEE.FEE_STATUS)
                .select(ENDORSE_AGENT.AGENT_ID)
                .select(ENDORSE_POLICY.APPROVE_DATE)
                .select(ENDORSE_ITEM_FEE_DETAIL.FEE.as("totalFee"),ENDORSE_ITEM_FEE_DETAIL.FEE_TYPE,
                        ENDORSE_ITEM_FEE_DETAIL.PRODUCT_ID,
                        ENDORSE_ITEM_FEE_DETAIL.PRODUCT_NAME,
                        ENDORSE_ITEM_FEE_DETAIL.PRIMARY_FLAG,
                        ENDORSE_ITEM_FEE_DETAIL.PREMIUM_FREQUENCY,
                        ENDORSE_ITEM_FEE_DETAIL.INSURED_ID)
               /* .select(ENDORSE_COVERAGE.POLICY_ID,
                        ENDORSE_COVERAGE.PRODUCT_CODE,
                        ENDORSE_COVERAGE.COVERAGE_PERIOD,
                        ENDORSE_COVERAGE.COVERAGE_PERIOD_UNIT,
                        ENDORSE_COVERAGE.PREMIUM_PERIOD,
                        ENDORSE_COVERAGE.PREMIUM_PERIOD_UNIT)*/
                .select(ENDORSE.ENDORSE_ID.countOver().as("totalLine"))
                .from(ENDORSE)
                .leftJoin(ENDORSE_ACCEPT).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ACCEPT.ENDORSE_ID)
                        .and(ENDORSE_ACCEPT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_FEE).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_FEE.ENDORSE_ID)
                        .and(ENDORSE_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID)
                        .and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_POLICY).on(ENDORSE_POLICY.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(BASE_PROJECT).on(BASE_PROJECT.PROJECT_ID.eq(ENDORSE_ITEM.PROJECT_ID)
                        .and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                /*.leftJoin(ENDORSE_INSURED)
                .on(ENDORSE_INSURED.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(ENDORSE_INSURED.CHANGE_FLAG.in(ADD.name(), SUBTRACT.name())))
                .leftJoin(ENDORSE_COVERAGE)
                .on(ENDORSE_COVERAGE.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(ENDORSE_COVERAGE.INSURED_ID.eq(ENDORSE_INSURED.INSURED_ID)))*/
                .leftJoin(ENDORSE_ITEM_FEE).on(ENDORSE_ITEM_FEE.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID)
                        .and(ENDORSE_ITEM_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_ITEM_FEE_DETAIL).on(ENDORSE_ITEM_FEE_DETAIL.ENDORSE_ITEM_FEE_ID.eq(ENDORSE_ITEM_FEE.ENDORSE_ITEM_FEE_ID)
                        .and(ENDORSE_ITEM_FEE_DETAIL.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_AGENT).on(ENDORSE_AGENT.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .where(ENDORSE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                        .and(endorseCreateDate.between(DateUtils.stringToTime(startDate, DateUtils.FORMATE51), DateUtils.getCurrentTime()))
                        .and(
                                // 复效缴费周期变更 展示又有险种
                                BASE_PROJECT.PROJECT_CODE.in(
                                        ADD_SUBTRACT_INSURED.name()
                                )
                        )
                )/*.groupBy(
                        ENDORSE.ENDORSE_ID,
                        ENDORSE_ITEM.ENDORSE_ITEM_ID,
                        ENDORSE_ITEM.PROJECT_ID,
                        BASE_PROJECT.PROJECT_CODE,
                        BASE_PROJECT.PROJECT_NAME,
                        ENDORSE_ACCEPT.APPLY_ID,
                        ENDORSE_ACCEPT.APPLY_NO,
                        ENDORSE_ACCEPT.ACCEPT_NO,
                        ENDORSE_ACCEPT.ENDORSE_TYPE,
                        ENDORSE_ACCEPT.ACCEPT_BRANCH_ID,
                        ENDORSE_ACCEPT.ACCEPT_DATE,
                        ENDORSE_ACCEPT.APPLY_DATE,
                        ENDORSE_AGENT.AGENT_ID,
                        ENDORSE_POLICY.APPROVE_DATE,
                        ENDORSE_COVERAGE.INSURED_ID,
                        ENDORSE_COVERAGE.POLICY_ID,
                        ENDORSE_COVERAGE.PRODUCT_ID,
                        ENDORSE_COVERAGE.PRODUCT_CODE,
                        ENDORSE_COVERAGE.PRODUCT_NAME,
                        ENDORSE_COVERAGE.PREMIUM_FREQUENCY,
                        ENDORSE_COVERAGE.COVERAGE_PERIOD,
                        ENDORSE_COVERAGE.COVERAGE_PERIOD_UNIT,
                        ENDORSE_COVERAGE.PREMIUM_PERIOD,
                        ENDORSE_COVERAGE.PREMIUM_PERIOD_UNIT,
                        ENDORSE_COVERAGE.PRIMARY_FLAG,
                        ENDORSE_ITEM_FEE_DETAIL.FEE,
                        //ENDORSE_COVERAGE.ACTUAL_PREMIUM,
                        ENDORSE_ITEM_FEE_DETAIL.FEE_TYPE,
                        ENDORSE_FEE.FEE_STATUS)*/
                .orderBy(ENDORSE_ACCEPT.ACCEPT_DATE.desc(),
                        ENDORSE_ACCEPT.APPLY_ID.desc(),
                        ENDORSE_ACCEPT.APPLY_NO.desc(),
                        ENDORSE_ITEM_FEE_DETAIL.INSURED_ID.desc(),
                        ENDORSE_ITEM_FEE_DETAIL.PRIMARY_FLAG.desc());


        selectConditionStep.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());

        records1.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());

        records.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());

        records5.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());

        System.out.println(selectConditionStep.toString());
        System.out.println(records1.toString());
        System.out.println(records.toString());
        System.out.println(records5.toString());
        List<ReportEndorseBo> reportEndorseBos1 = selectConditionStep.fetchInto(ReportEndorseBo.class);
        List<ReportEndorseBo> reportEndorseBos2 = records1.fetchInto(ReportEndorseBo.class);
        List<ReportEndorseBo> reportEndorseBos3 = records.fetchInto(ReportEndorseBo.class);
        List<ReportEndorseBo> reportEndorseBos4 = records5.fetchInto(ReportEndorseBo.class);

        reportEndorseBos.addAll(reportEndorseBos1);
        reportEndorseBos.addAll(reportEndorseBos2);
        reportEndorseBos.addAll(reportEndorseBos3);
        reportEndorseBos.addAll(reportEndorseBos4);

        return reportEndorseBos;
    }

    /**
     * 查询保全缴费信息
     *
     * @param endorseItemIds
     * @return
     */
    @Override
    public List<ReportEndorsePaymentBo> listReportEndorsePaymentData(List<String> endorseItemIds) {
        SelectConditionStep<Record> where = this.getDslContext().select(ENDORSE_PAYMENT.fields())
                .select(ENDORSE_ITEM.ENDORSE_ID)
                .from(ENDORSE_PAYMENT)
                .leftJoin(ENDORSE_ITEM).on(ENDORSE_ITEM.ENDORSE_ITEM_ID.eq(ENDORSE_PAYMENT.ENDORSE_ITEM_ID).and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .where(ENDORSE_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()).and(ENDORSE_ITEM.ENDORSE_ITEM_ID.in(endorseItemIds)));
        return where.fetchInto(ReportEndorsePaymentBo.class);
    }

    /**
     * 查询所有已超时的复效保全或个险增加附加险项目
     *
     * @param basePageRequest 分页信息
     * @return
     */
    @Override
    public List<EndorsePo> listTimeOutReinstatement(BasePageRequest basePageRequest) {
        SelectOnConditionStep<Record> selectJoinStep = this.getDslContext()
                .select(ENDORSE.fields())
                .from(ENDORSE)
                .innerJoin(ENDORSE_ITEM).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID));
        List<Condition> conditions = new ArrayList<>();

        conditions.add((ENDORSE_ITEM.PROJECT_ID.eq("prj20180830004").and(ENDORSE.ENDORSE_STATUS.eq(EndorseTermEnum.ENDORSE_STATUS.ENDORSE_STATUS_AUDIT_COMPLETE.name())))
                .or(ENDORSE_ITEM.PROJECT_ID.eq("prj20190305006").and(ENDORSE.ENDORSE_STATUS.eq(EndorseTermEnum.ENDORSE_STATUS.ENDORSE_STATUS_UNDERWRITE_PASS.name()))));
        conditions.add(ENDORSE_ITEM.ENDORSE_FINISH_DATE.lt(DateUtils.addStringDayRT(DateUtils.timeToTimeLow(DateUtils.getCurrentTime()), -13)));
        selectJoinStep.where(conditions);
        //先按代缴费排序,再按时间排序。
        selectJoinStep.orderBy(ENDORSE_ITEM.ENDORSE_FINISH_DATE);
        selectJoinStep.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
//        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(EndorsePo.class);
    }

    @Override
    public List<ActualPerformanceReportBo> queryActualPerformance(List<String> businessIdList) {

        Table<Record> policy_coverage_level = this.getDslContext()
                .select(ENDORSE_COVERAGE.ENDORSE_ITEM_ID)
                .select(ENDORSE_COVERAGE_LEVEL.PRODUCT_LEVEL)
                .select(ENDORSE_COVERAGE_LEVEL.MULT)
                .select(ENDORSE_COVERAGE.PRODUCT_ID.decode(
                        "PRO8800000000000G12", new BigDecimal(0), ENDORSE_COVERAGE.PREMIUM).as("coveragePremium"))
                .select(ENDORSE_COVERAGE.PRODUCT_ID.decode(
                        "PRO8800000000000G12", new BigDecimal(0), ENDORSE_COVERAGE_LEVEL.PREMIUM)
                        .as(ENDORSE_COVERAGE_LEVEL.PREMIUM))
                .select(ENDORSE_COVERAGE.TOTAL_PREMIUM)
                .select(ENDORSE_COVERAGE.COVERAGE_ID)
                .select(ENDORSE_COVERAGE_LEVEL.COVERAGE_DUTY_ID)
                .select(ENDORSE_COVERAGE_DUTY.DUTY_ID)
                .select(ENDORSE_COVERAGE_LEVEL.REFUND_AMOUNT)
                .select(ENDORSE_COVERAGE_LEVEL.ACTUAL_PREMIUM)
                .select(ENDORSE_COVERAGE.ORIGINAL_PREMIUM)
                .from(ENDORSE_COVERAGE)
                .leftJoin(ENDORSE_COVERAGE_LEVEL)
                .on(ENDORSE_COVERAGE.COVERAGE_ID.eq(ENDORSE_COVERAGE_LEVEL.COVERAGE_ID)
                        .and(ENDORSE_COVERAGE.ENDORSE_ITEM_ID.eq(ENDORSE_COVERAGE_LEVEL.ENDORSE_ITEM_ID))
                        .and(ENDORSE_COVERAGE_LEVEL.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(ENDORSE_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                )
                .leftJoin(ENDORSE_COVERAGE_DUTY)
                .on(ENDORSE_COVERAGE_DUTY.COVERAGE_DUTY_ID.eq(ENDORSE_COVERAGE_LEVEL.COVERAGE_DUTY_ID)
                        .and(ENDORSE_COVERAGE_DUTY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(ENDORSE_COVERAGE_DUTY.ENDORSE_ITEM_ID.eq(ENDORSE_COVERAGE_LEVEL.ENDORSE_ITEM_ID))
                ).asTable();

        SelectOffsetStep<Record1<String>> APPLY_ID = this.getDslContext().select(ENDORSE_ACCEPT.APPLY_ID).from(ENDORSE_ACCEPT).where(ENDORSE_ACCEPT.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID)).limit(1);

        Table<Record> endorse_item_fee_detail = this.getDslContext()
                .select(ENDORSE_ITEM_FEE.ENDORSE_ITEM_ID)
                .select(ENDORSE_ITEM_FEE_DETAIL.COVERAGE_ID)
                .select(BASE_PROJECT.ENDORSE_TYPE.decode(
                        POLICY.name(), ENDORSE_ITEM_FEE_DETAIL.COVERAGE_ID,
                        ENDORSE_ITEM_FEE.ENDORSE_ITEM_ID)
                        .as("policyCoverageId"))
                .select(ENDORSE_ITEM_FEE_DETAIL.FEE_TYPE.decode(
                        PAY.name(), ENDORSE_ITEM_FEE_DETAIL.FEE, new BigDecimal(0)).sum().as("payFee"))
                .select(ENDORSE_ITEM_FEE_DETAIL.FEE_TYPE.decode(
                        GET.name(), ENDORSE_ITEM_FEE_DETAIL.FEE, new BigDecimal(0)).sum().as("getFee"))
                .from(ENDORSE_ITEM_FEE)
                .leftJoin(ENDORSE_ITEM_FEE_DETAIL)
                .on(ENDORSE_ITEM_FEE_DETAIL.ENDORSE_ITEM_FEE_ID.eq(ENDORSE_ITEM_FEE.ENDORSE_ITEM_FEE_ID)
                        .and(ENDORSE_ITEM_FEE_DETAIL.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(ENDORSE_ITEM_FEE_DETAIL.COVERAGE_ID.isNotNull()))
                .and(ENDORSE_ITEM_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .leftJoin(ENDORSE_ITEM)
                .on(ENDORSE_ITEM_FEE.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID))
                .leftJoin(BASE_PROJECT)
                .on(BASE_PROJECT.PROJECT_ID.eq(ENDORSE_ITEM.PROJECT_ID))
                .groupBy(ENDORSE_ITEM_FEE.ENDORSE_ITEM_ID, ENDORSE_ITEM_FEE_DETAIL.COVERAGE_ID, BASE_PROJECT.ENDORSE_TYPE)
                .asTable();

        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(ENDORSE.ENDORSE_ID)
                .select(ENDORSE.ACCEPT_BRANCH_ID)
                .select(BASE_PROJECT.PROJECT_CODE)
                //.select(ENDORSE_PAYMENT.POLICY_YEAR.as("policyYear"))
                .select(ENDORSE_POLICY_RELATION.OLD_VERSION_NO)
                .select(ENDORSE_POLICY_RELATION.NEW_VERSION_NO)
                .select(BASE_PROJECT.ENDORSE_TYPE)
                .select(APPLY_ID.asField().as(ENDORSE_COVERAGE.POLICY_ID))
                .select(ENDORSE_COVERAGE.PRODUCT_ID.nvl(endorse_item_fee_detail.field("policyCoverageId", String.class)).as(ENDORSE_COVERAGE.PRODUCT_ID))
                .select(ENDORSE_COVERAGE.PRODUCT_CODE)
                .select(ENDORSE_COVERAGE.PRIMARY_FLAG)
                .select(ENDORSE_COVERAGE.PREMIUM_PERIOD)
                .select(ENDORSE_COVERAGE.COVERAGE_PERIOD)
                .select(ENDORSE_COVERAGE.COVERAGE_PERIOD_UNIT)
                .select(BASE_PROJECT.ENDORSE_TYPE.decode(
                        POLICY.name(), ENDORSE_COVERAGE.PRODUCT_LEVEL,
                        GROUP_POLICY.name(), policy_coverage_level.field(ENDORSE_COVERAGE_LEVEL.PRODUCT_LEVEL)
                ).as(ENDORSE_COVERAGE.PRODUCT_LEVEL))
                .select(policy_coverage_level.field(ENDORSE_COVERAGE_DUTY.DUTY_ID))
                .select(ENDORSE.EFFECTIVE_DATE)
                .select(ENDORSE_COVERAGE.AMOUNT.sum().as(ENDORSE_COVERAGE.AMOUNT))
                .select(BASE_PROJECT.ENDORSE_TYPE.decode(
                        POLICY.name(), policy_coverage_level.field("coveragePremium", BigDecimal.class),
                        GROUP_POLICY.name(), policy_coverage_level.field(ENDORSE_COVERAGE_LEVEL.PREMIUM)
                ).sum().as(ENDORSE_COVERAGE.PREMIUM))
                .select(ENDORSE_COVERAGE.TOTAL_AMOUNT.sum().as(ENDORSE_COVERAGE.TOTAL_AMOUNT))
                .select(policy_coverage_level.field(ENDORSE_COVERAGE_LEVEL.MULT).nvl(ENDORSE_COVERAGE.MULT.nvl("1")).sum().as("mult"))
                .select(ENDORSE_COVERAGE.PREMIUM_FREQUENCY)
                .select(ENDORSE_COVERAGE.ORIGINAL_PREMIUM)
                .select(endorse_item_fee_detail.field("policyCoverageId"))
                .select(BASE_PROJECT.ENDORSE_TYPE.decode(
                        POLICY.name(),
                        BASE_PROJECT.PROJECT_CODE.decode(
                                REINSTATEMENT.name(), ENDORSE_COVERAGE_PAYMENT.TOTAL_PREMIUM,
                                endorse_item_fee_detail.field("payFee")),
                        GROUP_POLICY.name(), policy_coverage_level.field(ENDORSE_COVERAGE_LEVEL.ACTUAL_PREMIUM)
                ).sum().as(ENDORSE_COVERAGE.TOTAL_PREMIUM))
                .select(BASE_PROJECT.ENDORSE_TYPE.decode(
                        POLICY.name(),
                        endorse_item_fee_detail.field("getFee"),
                        GROUP_POLICY.name(),
                        policy_coverage_level.field(ENDORSE_COVERAGE_LEVEL.REFUND_AMOUNT)
                ).sum().as(ENDORSE_COVERAGE_LEVEL.REFUND_AMOUNT))
                .select(ENDORSE_INSURED.CHANGE_FLAG.as("insuredChangeFlag"))
                .select(ENDORSE_COVERAGE.CHANGE_FLAG.as("coverageChangeFlag"))
                .from(ENDORSE)
                .leftJoin(ENDORSE_ITEM)
                .on(ENDORSE_ITEM.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID)
                        .and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_POLICY)
                .on(ENDORSE_POLICY.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(BASE_PROJECT)
                .on(BASE_PROJECT.PROJECT_ID.eq(ENDORSE_ITEM.PROJECT_ID)
                        .and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(ENDORSE_COVERAGE)
                .on(ENDORSE_COVERAGE.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(ENDORSE_COVERAGE.INSURED_ID.isNotNull()))
                .leftJoin(endorse_item_fee_detail)
                .on(endorse_item_fee_detail.field(ENDORSE_ITEM_FEE.ENDORSE_ITEM_ID).eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_COVERAGE.COVERAGE_ID.eq(endorse_item_fee_detail.field(ENDORSE_ITEM_FEE_DETAIL.COVERAGE_ID))))
                .leftJoin(ENDORSE_INSURED)
                .on(ENDORSE_INSURED.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(ENDORSE_INSURED.INSURED_ID.eq(ENDORSE_COVERAGE.INSURED_ID)))
                .leftJoin(ENDORSE_COVERAGE_PAYMENT)
                .on(ENDORSE_COVERAGE_PAYMENT.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_COVERAGE_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(ENDORSE_COVERAGE_PAYMENT.COVERAGE_ID.eq(ENDORSE_COVERAGE.COVERAGE_ID)))
                .leftJoin(GROUP_ADD_SUBTRACT_INSURED_DEDUCT)
                .on(GROUP_ADD_SUBTRACT_INSURED_DEDUCT.ENDORSE_ITEM_ID.eq(ENDORSE_COVERAGE.ENDORSE_ITEM_ID)
                        .and(GROUP_ADD_SUBTRACT_INSURED_DEDUCT.ADD_INSURED_ID.eq(ENDORSE_COVERAGE.INSURED_ID)
                                .or(GROUP_ADD_SUBTRACT_INSURED_DEDUCT.SUBTRACT_INSURED_ID.eq(ENDORSE_COVERAGE.INSURED_ID)))
                        .and(GROUP_ADD_SUBTRACT_INSURED_DEDUCT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(policy_coverage_level)
                .on(policy_coverage_level.field(ENDORSE_COVERAGE_LEVEL.ENDORSE_ITEM_ID).eq(ENDORSE_COVERAGE.ENDORSE_ITEM_ID)
                        .and(policy_coverage_level.field(ENDORSE_COVERAGE_LEVEL.COVERAGE_ID).eq(ENDORSE_COVERAGE.COVERAGE_ID)))
                .leftJoin(ENDORSE_POLICY_RELATION)
                .on(ENDORSE_POLICY_RELATION.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID)
                        .and(ENDORSE_POLICY_RELATION.POLICY_ID.eq(ENDORSE_COVERAGE.POLICY_ID))
                        .and(ENDORSE_POLICY_RELATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
                /*.leftJoin(ENDORSE_PAYMENT)
                .on(ENDORSE_COVERAGE_PAYMENT.ENDORSE_ITEM_ID.eq(ENDORSE_PAYMENT.ENDORSE_ITEM_ID));*/

        selectOnConditionStep.where(
                ENDORSE.ENDORSE_ID.in(businessIdList)
                        .and(GROUP_ADD_SUBTRACT_INSURED_DEDUCT.GROUP_ADD_SUBTRACT_INSURED_DEDUCT_ID.isNull())
                        .and(
                                // 复效缴费周期变更 展示又有险种
                                BASE_PROJECT.PROJECT_CODE.in(
                                        HESITATION_REVOKE.name(),
                                        SURRENDER.name(),
                                        REINSTATEMENT.name(),
                                        MODE_OF_PAYMENT_MODIFY.name())
                                        .or(ENDORSE_COVERAGE.CHANGE_FLAG.eq(ADD.name())
                                                .or(ENDORSE_COVERAGE.CHANGE_FLAG.eq(SUBTRACT.name()))
                                                .or(ENDORSE_INSURED.CHANGE_FLAG.eq(ADD.name()))
                                                .or(ENDORSE_INSURED.CHANGE_FLAG.eq(SUBTRACT.name()))
                                        )
                        )
        );

        selectOnConditionStep.groupBy(
                ENDORSE.ENDORSE_ID,
                ENDORSE.ACCEPT_BRANCH_ID,
                BASE_PROJECT.ENDORSE_TYPE,
                BASE_PROJECT.PROJECT_CODE,
                ENDORSE_COVERAGE.POLICY_ID,
                ENDORSE_COVERAGE.PRODUCT_ID,
                ENDORSE_COVERAGE.PRODUCT_CODE,
                ENDORSE_COVERAGE.PRIMARY_FLAG,
                ENDORSE_COVERAGE.PREMIUM_PERIOD,
                ENDORSE_COVERAGE.COVERAGE_PERIOD,
                ENDORSE_COVERAGE.COVERAGE_PERIOD_UNIT,
                policy_coverage_level.field(ENDORSE_COVERAGE_LEVEL.PRODUCT_LEVEL),
                ENDORSE_COVERAGE.PRODUCT_LEVEL,
                policy_coverage_level.field(ENDORSE_COVERAGE_DUTY.DUTY_ID),
                ENDORSE.EFFECTIVE_DATE,
                ENDORSE_COVERAGE.PREMIUM_FREQUENCY,
                ENDORSE_COVERAGE.ORIGINAL_PREMIUM,
                endorse_item_fee_detail.field("policyCoverageId"),
                ENDORSE_POLICY_RELATION.OLD_VERSION_NO,
                ENDORSE_POLICY_RELATION.NEW_VERSION_NO,
                ENDORSE_INSURED.CHANGE_FLAG,
                ENDORSE_COVERAGE.CHANGE_FLAG);
                //ENDORSE_PAYMENT.POLICY_YEAR);

        System.out.println(selectOnConditionStep.toString());

        return selectOnConditionStep.fetchInto(ActualPerformanceReportBo.class);
    }

    /**
     * 根据保单ID查询保全记录
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<EndorsePo> listEndorseByPolicyId(String policyId) {
        return this.getDslContext()
                .select(ENDORSE.fields())
                .from(ENDORSE)
                .innerJoin(ENDORSE_ACCEPT).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ACCEPT.ENDORSE_ID))
                .where(ENDORSE_ACCEPT.APPLY_ID.eq(policyId))
                .and(ENDORSE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(EndorsePo.class);
    }

    @Override
    public List<ReportEndorseBo> queryReportEndorseCoverageList(List<String> coverageIdList) {

        Field<Long> effectiveDateMin = DSL.field("row_number() OVER (PARTITION BY {0} ORDER BY {1} ASC)", SQLDataType.BIGINT,
                ENDORSE_COVERAGE.COVERAGE_ID, ENDORSE.EFFECTIVE_DATE);

        Table recordTable = this.getDslContext()
                .select(ENDORSE.fields())
                .select(ENDORSE_COVERAGE.COVERAGE_ID)
                .select(ENDORSE_COVERAGE.ENDORSE_ITEM_ID)
                .select(ENDORSE_COVERAGE.INSURED_ID)
                .select(ENDORSE_COVERAGE.CHANGE_FLAG)
                .select(ENDORSE_COVERAGE.EFFECTIVE_DATE.as("coverageEffectiveDate"))
                .select(ENDORSE_COVERAGE.REFUND_DATE.as("coverageRefundDate"))
                .select(ENDORSE_INSURED.CHANGE_FLAG.as("insuredChangeFlag"))
                .select(ENDORSE_ITEM.PROJECT_ID)
                .select(effectiveDateMin.as("effectiveDateMin"))
                .from(ENDORSE_COVERAGE)
                .leftJoin(ENDORSE_INSURED).on(ENDORSE_INSURED.ENDORSE_ITEM_ID.eq(ENDORSE_COVERAGE.ENDORSE_ITEM_ID), ENDORSE_INSURED.INSURED_ID.eq(ENDORSE_COVERAGE.INSURED_ID), ENDORSE_INSURED.CHANGE_FLAG.eq(ADD.name()))
                .leftJoin(ENDORSE_ITEM).on(ENDORSE_ITEM.ENDORSE_ITEM_ID.eq(ENDORSE_COVERAGE.ENDORSE_ITEM_ID))
                .leftJoin(ENDORSE).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID))
                .where(ENDORSE_COVERAGE.COVERAGE_ID.in(coverageIdList),
                        ENDORSE_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()),
                        ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()),
                        ENDORSE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .asTable();

        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(recordTable.fields())
                .select(GROUP_ADD_SUBTRACT_INSURED_DEDUCT.ADD_INSURED_ID)
                .from(recordTable)
                .leftJoin(GROUP_ADD_SUBTRACT_INSURED_DEDUCT)
                .on(GROUP_ADD_SUBTRACT_INSURED_DEDUCT.ADD_INSURED_ID.eq(recordTable.field(ENDORSE_COVERAGE.INSURED_ID)),
                        GROUP_ADD_SUBTRACT_INSURED_DEDUCT.ENDORSE_ITEM_ID.eq(recordTable.field(ENDORSE_COVERAGE.ENDORSE_ITEM_ID)),
                        GROUP_ADD_SUBTRACT_INSURED_DEDUCT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .where(recordTable.field("effectiveDateMin").eq(1).and(recordTable.field(ENDORSE_COVERAGE.CHANGE_FLAG).eq(ADD.name()).or(recordTable.field("insuredChangeFlag").eq(ADD.name())).or(GROUP_ADD_SUBTRACT_INSURED_DEDUCT.ADD_INSURED_ID.isNotNull())));
        return selectConditionStep.fetchInto(ReportEndorseBo.class);
    }

    @Override
    public List<EndorseBo> queryEndorseByPolicyIdListAndEndorse(List<String> policyIdList, String projectCode) {
        Field<Long> effectiveDateMax = DSL.field("row_number() OVER (PARTITION BY {0} ORDER BY {1} DESC)", SQLDataType.BIGINT,
                ENDORSE_ACCEPT.APPLY_ID, ENDORSE.EFFECTIVE_DATE);
        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(ENDORSE.fields())
                .select(ENDORSE_ACCEPT.APPLY_ID)
                .select(effectiveDateMax.as("effectiveDateMax"))
                .from(ENDORSE)
                .leftJoin(ENDORSE_ACCEPT).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ACCEPT.ENDORSE_ID))
                .leftJoin(ENDORSE_ITEM).on(ENDORSE_ITEM.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID))
                .leftJoin(BASE_PROJECT).on(BASE_PROJECT.PROJECT_ID.eq(ENDORSE_ITEM.PROJECT_ID));
        List<Condition> conditionList = new ArrayList<>();
        if (AssertUtils.isNotEmpty(projectCode)) {
            conditionList.add(BASE_PROJECT.PROJECT_CODE.eq(projectCode));
        }
        conditionList.add(ENDORSE_ACCEPT.APPLY_ID.in(policyIdList));
        conditionList.add(ENDORSE.ENDORSE_STATUS.in(ENDORSE_STATUS_EFFETIVE.name(), EFFECTIVE.name()));
        conditionList.add(ENDORSE.EFFECTIVE_DATE.isNotNull());

        Table<Record> recordTable = selectOnConditionStep.where(conditionList).asTable();

        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(recordTable.fields())
                .from(recordTable).where(recordTable.field("effectiveDateMax", Integer.class).eq(1));

        return selectConditionStep.fetchInto(EndorseBo.class);
    }

    @Override
    public List<ReportEndorseBo> queryPolicyCoverageDetailList(List<String> policyIdList) {

        SelectHavingStep<Record> recordSelectHavingStep = this.getDslContext()
                .select(ENDORSE_ACCEPT.APPLY_ID)
                .select(ENDORSE_COVERAGE.PRODUCT_ID)
                .select(ENDORSE_INSURED.CHANGE_FLAG.decode(
                        SUBTRACT.name(), ENDORSE_COVERAGE.REFUND_AMOUNT,
                        ENDORSE_COVERAGE.CHANGE_FLAG.decode(SUBTRACT.name(), ENDORSE_COVERAGE.REFUND_AMOUNT)).sum().as(ENDORSE_COVERAGE.REFUND_AMOUNT))
                .select(ENDORSE_INSURED.CHANGE_FLAG.decode(
                        ADD.name(), ENDORSE_COVERAGE.ACTUAL_PREMIUM,
                        ENDORSE_COVERAGE.CHANGE_FLAG.decode(ADD.name(), ENDORSE_COVERAGE.ACTUAL_PREMIUM)).sum().as(ENDORSE_COVERAGE.ACTUAL_PREMIUM))
                .from(ENDORSE)
                .leftJoin(ENDORSE_ACCEPT)
                .on(ENDORSE_ACCEPT.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID))
                .leftJoin(ENDORSE_ITEM)
                .on(ENDORSE_ITEM.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID))
                .leftJoin(ENDORSE_INSURED)
                .on(ENDORSE_INSURED.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID))
                .leftJoin(ENDORSE_COVERAGE)
                .on(ENDORSE_INSURED.ENDORSE_ITEM_ID.eq(ENDORSE_COVERAGE.ENDORSE_ITEM_ID),
                        ENDORSE_INSURED.INSURED_ID.eq(ENDORSE_COVERAGE.INSURED_ID))
                .leftJoin(GROUP_ADD_SUBTRACT_INSURED_DEDUCT)
                .on(GROUP_ADD_SUBTRACT_INSURED_DEDUCT.ADD_INSURED_ID.eq(ENDORSE_INSURED.INSURED_ID)
                                .or(GROUP_ADD_SUBTRACT_INSURED_DEDUCT.SUBTRACT_INSURED_ID.eq(ENDORSE_INSURED.INSURED_ID)),
                        GROUP_ADD_SUBTRACT_INSURED_DEDUCT.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID))
                .where(ENDORSE_INSURED.CHANGE_FLAG.in(SUBTRACT.name(), ADD.name()).or(ENDORSE_COVERAGE.CHANGE_FLAG.in(SUBTRACT.name(), ADD.name())),
                        GROUP_ADD_SUBTRACT_INSURED_DEDUCT.GROUP_ADD_SUBTRACT_INSURED_DEDUCT_ID.isNull(),
                        ENDORSE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()),
                        ENDORSE.ENDORSE_STATUS.eq(EFFECTIVE.name()),
                        ENDORSE_ACCEPT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()),
                        ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()),
                        ENDORSE_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()),
                        ENDORSE_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()),
                        ENDORSE_ACCEPT.APPLY_ID.in(policyIdList))
                .groupBy(ENDORSE_ACCEPT.APPLY_ID,
                        ENDORSE_COVERAGE.PRODUCT_ID);

        return recordSelectHavingStep.fetchInto(ReportEndorseBo.class);
    }


    @Override
    public List<ReportEndorseBo> queryEndorseListByPolicyId(List<String> policyIdList, List<String> projectCodeList) {

        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(ENDORSE.EFFECTIVE_DATE)
                .select(ENDORSE_ACCEPT.APPLY_ID)
                .select(BASE_PROJECT.PROJECT_CODE)
                .from(ENDORSE)
                .leftJoin(ENDORSE_ACCEPT)
                .on(ENDORSE_ACCEPT.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID))
                .leftJoin(ENDORSE_ITEM)
                .on(ENDORSE_ITEM.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID)
                        .and(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(BASE_PROJECT)
                .on(BASE_PROJECT.PROJECT_ID.eq(ENDORSE_ITEM.PROJECT_ID));

        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(ENDORSE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditionList.add(ENDORSE.ENDORSE_STATUS.in(ENDORSE_STATUS_EFFETIVE.name(), EFFECTIVE.name()));
        if (AssertUtils.isNotEmpty(projectCodeList)) {
            conditionList.add(BASE_PROJECT.PROJECT_CODE.in(projectCodeList));
        }
        if (AssertUtils.isNotEmpty(policyIdList)) {
            conditionList.add(ENDORSE_ACCEPT.APPLY_ID.in(policyIdList));
        }

        selectOnConditionStep.where(conditionList);
        return selectOnConditionStep.fetchInto(ReportEndorseBo.class);
    }

    @Override
    public List<EndorseRemarkPo> getRemarkList(String endorseId) {
        return this.getDslContext().selectFrom(ENDORSE_REMARK)
                .where(ENDORSE_REMARK.ENDORSE_ID.eq(endorseId))
                .and(ENDORSE_REMARK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .orderBy(ENDORSE_REMARK.CREATED_DATE.asc())
                .fetchInto(EndorseRemarkPo.class);
    }

    @Override
    public List<EndorseRemarkPo> getRemark(List<String> policyNos) {
        return this.getDslContext().selectFrom(ENDORSE_REMARK)
                .where(ENDORSE_REMARK.POLICY_NO.in(policyNos))
                .and(ENDORSE_REMARK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(EndorseRemarkPo.class);
    }
}
