package com.gclife.policy.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.BasePojo;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAgentPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAllocationPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAllocationRemarkPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAssignAgentPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyAssignRemarkPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCoveragePo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyServiceAgentPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyServiceAgentRemarkPo;
import com.gclife.policy.core.jooq.tables.records.PolicyPaymentRecord;
import com.gclife.policy.core.jooq.tables.records.PolicyRecord;
import com.gclife.policy.dao.PolicyQueryBaseDao;
import com.gclife.policy.model.bo.PolicyAllocationBo;
import com.gclife.policy.model.bo.PolicyCoverageBo;
import com.gclife.policy.model.bo.PolicyCoveragePaymentBo;
import com.gclife.policy.model.bo.PolicyListBo;
import com.gclife.policy.model.bo.PolicyPaymentBo;
import com.gclife.policy.model.bo.PolicyStatusBo;
import com.gclife.policy.model.bo.RenewalAssignPolicyBo;
import com.gclife.policy.model.bo.ServiceStaffAssignBo;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.vo.PolicyListVo;
import com.gclife.policy.model.vo.RenewalAssignPolicyVo;
import com.gclife.policy.model.vo.RenewalPagingVo;
import com.gclife.policy.model.vo.ServiceStaffAssignVo;
import com.gclife.product.model.config.ProductTermEnum;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectOnConditionStep;
import org.jooq.Table;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.gclife.policy.core.jooq.Tables.POLICY_AGENT;
import static com.gclife.policy.core.jooq.Tables.POLICY_ALLOCATION;
import static com.gclife.policy.core.jooq.Tables.POLICY_ALLOCATION_REMARK;
import static com.gclife.policy.core.jooq.Tables.POLICY_APPLICANT;
import static com.gclife.policy.core.jooq.Tables.POLICY_ASSIGN_AGENT;
import static com.gclife.policy.core.jooq.Tables.POLICY_ASSIGN_REMARK;
import static com.gclife.policy.core.jooq.Tables.POLICY_COVERAGE;
import static com.gclife.policy.core.jooq.Tables.POLICY_COVERAGE_PAYMENT;
import static com.gclife.policy.core.jooq.Tables.POLICY_INSURED;
import static com.gclife.policy.core.jooq.Tables.POLICY_PAYMENT;
import static com.gclife.policy.core.jooq.Tables.POLICY_PREMIUM;
import static com.gclife.policy.core.jooq.Tables.POLICY_PRINT_INFO;
import static com.gclife.policy.core.jooq.Tables.POLICY_SERVICE_AGENT;
import static com.gclife.policy.core.jooq.Tables.POLICY_SERVICE_AGENT_REMARK;
import static com.gclife.policy.core.jooq.tables.Policy.POLICY;

/**
 * <AUTHOR>
 * create 18-5-23
 * description:
 */
@Component
public class PolicyQueryBaseDaoImpl extends BaseDaoImpl implements PolicyQueryBaseDao {

    @Override
    public List<PolicyListBo> loadPolicyListByBranchIds(List<String> branchIds, PolicyListVo policyListVo, String policyType) {
        //基表
        SelectJoinStep selectJoinStep = this.getDslContext()
                .selectDistinct(POLICY.fields())
                .select(POLICY_APPLICANT.NAME.as("applicantName"), POLICY_APPLICANT.MOBILE.as("applicantMobile"), POLICY_APPLICANT.ID_NO.as("applicantIdNo"),
                        POLICY_APPLICANT.COMPANY_NAME, POLICY_APPLICANT.COMPANY_ID_TYPE, POLICY_APPLICANT.COMPANY_ID_NO, POLICY_APPLICANT.COMPANY_CONTRACT_NAME, POLICY_APPLICANT.COMPANY_CONTRACT_MOBILE,
                        POLICY_APPLICANT.DELEGATE_NAME, POLICY_APPLICANT.DELEGATE_MOBILE)
                .select(POLICY_AGENT.AGENT_ID, POLICY_AGENT.AGENT_CODE)
                .select(POLICY_PREMIUM.PERIOD_TOTAL_PREMIUM.as("totalPremium"))
                .select(POLICY_PREMIUM.PERIOD_TOTAL_PREMIUM)
                .select(POLICY_PREMIUM.PERIOD_ORIGINAL_PREMIUM)
                .select(POLICY_PREMIUM.TOTAL_ACTUAL_PREMIUM)
                .select(POLICY_PREMIUM.TOTAL_REFUND_AMOUNT)
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                .from(POLICY)
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_PREMIUM).on(POLICY.POLICY_ID.eq(POLICY_PREMIUM.POLICY_ID));

        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.SALES_BRANCH_ID.in(branchIds)
                .and(POLICY.VALID_FLAG.eq(PolicyTermEnum.VALID_FLAG.effective.name()))
        );

        if (AssertUtils.isNotEmpty(policyType)) {
            conditions.add(POLICY.POLICY_TYPE.eq(policyType));
        }
        if (AssertUtils.isNotEmpty(policyListVo.getKeyword())) {
            Condition condition = POLICY.POLICY_NO.like("%" + policyListVo.getKeyword() + "%")
                    .or(POLICY_APPLICANT.COMPANY_NAME.like("%" + policyListVo.getKeyword() + "%"))
                    .or(POLICY_APPLICANT.NAME.like("%" + policyListVo.getKeyword() + "%"))
                    .or(POLICY_APPLICANT.DELEGATE_NAME.like("%" + policyListVo.getKeyword() + "%"))
                    .or(POLICY_AGENT.AGENT_CODE.like("%" + policyListVo.getKeyword() + "%"));
            if (AssertUtils.isNotEmpty(policyListVo.getPolicyIds())) {
                condition = condition.or(POLICY.POLICY_ID.in(policyListVo.getPolicyIds()));
            }
            conditions.add(condition);
            /*conditions.add(
                    POLICY.POLICY_NO.like("%" + policyListVo.getKeyword() + "%")
                            .or(POLICY_APPLICANT.COMPANY_NAME.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_APPLICANT.NAME.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_APPLICANT.DELEGATE_NAME.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_AGENT.AGENT_CODE.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY.POLICY_ID.in(policyListVo.getPolicyIds()))
            );*/
        }

        //生效起期  大于等于
        String effectiveDateStartFormat = policyListVo.getEffectiveDateStart();
        if (AssertUtils.isNotEmpty(effectiveDateStartFormat) && AssertUtils.isDateFormat(effectiveDateStartFormat)) {
            conditions.add(POLICY.EFFECTIVE_DATE.ge(DateUtils.timeToTimeLow(DateUtils.stringToTime(effectiveDateStartFormat))));
        }
        //生效止期  小于等于
        String effectiveDateEndFormat = policyListVo.getEffectiveDateEnd();
        if (AssertUtils.isNotEmpty(effectiveDateEndFormat) && AssertUtils.isDateFormat(effectiveDateEndFormat)) {
            conditions.add(POLICY.EFFECTIVE_DATE.le(DateUtils.timeToTimeTop(DateUtils.stringToTime(effectiveDateEndFormat))));
        }

        //承保起期  大于等于
        String approveDateStartFormat = policyListVo.getApproveDateStart();
        if (AssertUtils.isNotEmpty(approveDateStartFormat) && AssertUtils.isDateFormat(approveDateStartFormat)) {
            conditions.add(POLICY.APPROVE_DATE.ge(DateUtils.timeToTimeLow(DateUtils.stringToTime(approveDateStartFormat))));
        }
        //承保止期  小于等于
        String approveDateEndFormat = policyListVo.getApproveDateEnd();
        if (AssertUtils.isNotEmpty(approveDateEndFormat) && AssertUtils.isDateFormat(approveDateEndFormat)) {
            conditions.add(POLICY.APPROVE_DATE.le(DateUtils.timeToTimeTop(DateUtils.stringToTime(approveDateEndFormat))));
        }

        //投保起期  大于等于
        String applyDateStartFormat = policyListVo.getApplyDateStart();
        if (AssertUtils.isNotEmpty(applyDateStartFormat) && AssertUtils.isDateFormat(applyDateStartFormat)) {
            conditions.add(POLICY.APPLY_DATE.ge(DateUtils.timeToTimeLow(DateUtils.stringToTime(applyDateStartFormat))));
        }
        //投保止期  小于等于
        String applyDateEndFormat = policyListVo.getApplyDateEnd();
        if (AssertUtils.isNotEmpty(applyDateEndFormat) && AssertUtils.isDateFormat(applyDateEndFormat)) {
            conditions.add(POLICY.APPLY_DATE.le(DateUtils.timeToTimeTop(DateUtils.stringToTime(applyDateEndFormat))));
        }

        //新旧单关联查询条件
        if (AssertUtils.isNotEmpty(policyListVo.getPolicyIds())) {
            conditions.add(POLICY.POLICY_ID.in(policyListVo.getPolicyIds()));
        }


        selectJoinStep.where(conditions);
        selectJoinStep.orderBy(POLICY.EFFECTIVE_DATE.desc());
        Table table = selectJoinStep.asTable();

        //查询产品名称
        SelectJoinStep applyCoverage = this.getDslContext()
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .from(POLICY_COVERAGE);

        List<Condition> coverageConditions = new ArrayList<Condition>();
        coverageConditions.add(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        coverageConditions.add(POLICY_COVERAGE.POLICY_ID.eq(table.field(POLICY.POLICY_ID.getName())));

        applyCoverage.where(coverageConditions).limit(1);


        SelectJoinStep select = this.getDslContext()
                .select(table.fields())
                .select(applyCoverage.asField().as("productName"))
                .from(table);

        select.offset(policyListVo.getOffset()).limit(policyListVo.getPageSize());
        System.out.println(select.toString());
        return select.fetchInto(PolicyListBo.class);
    }

    @Override
    public List<PolicyListBo> loadPolicyListByAgentId(String agentId, PolicyListVo policyListVo, String policyType) {
//        Field<Long> stringField = DSL.field("row_number() OVER (PARTITION BY {0} ORDER BY {1} DESC)", SQLDataType.BIGINT,
//                POLICY_PAYMENT.POLICY_ID, POLICY_PAYMENT.CREATED_DATE);
        // 要排除的
        Result<Record1<String>> excludePolicyId = getDslContext()
                .selectDistinct(POLICY_SERVICE_AGENT.POLICY_ID)
                .from(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(PolicyTermEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_SERVICE_AGENT.EFFECTIVE_FLAG.eq(PolicyTermEnum.YES_NO.YES.name()))
                .and(POLICY_SERVICE_AGENT.POLICY_ID.in(getDslContext()
                        .select(POLICY_AGENT.POLICY_ID)
                        .from(POLICY_AGENT)
                        .where(POLICY_AGENT.AGENT_ID.eq(agentId))
                        .fetch()))
                .fetch();
        // 要新增的
        Result<Record1<String>> addPolicyId = getDslContext()
                .selectDistinct(POLICY_SERVICE_AGENT.POLICY_ID)
                .from(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(PolicyTermEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_SERVICE_AGENT.EFFECTIVE_FLAG.eq(PolicyTermEnum.YES_NO.YES.name()))
                .and(POLICY_SERVICE_AGENT.SERVICE_AGENT_ID.eq(agentId))
                .fetch();

        //基表
        SelectJoinStep selectJoinStep = this.getDslContext()
                .selectDistinct(POLICY.fields())
                .select(POLICY_APPLICANT.NAME.as("applicantName"), POLICY_APPLICANT.MOBILE.as("applicantMobile"), POLICY_APPLICANT.ID_NO.as("applicantIdNo"),
                        POLICY_APPLICANT.COMPANY_NAME, POLICY_APPLICANT.COMPANY_ID_TYPE, POLICY_APPLICANT.COMPANY_ID_NO, POLICY_APPLICANT.COMPANY_CONTRACT_NAME, POLICY_APPLICANT.COMPANY_CONTRACT_MOBILE,
                        POLICY_APPLICANT.DELEGATE_NAME, POLICY_APPLICANT.DELEGATE_MOBILE)
                .select(POLICY_AGENT.AGENT_ID, POLICY_AGENT.AGENT_CODE)
                .select(POLICY_PREMIUM.ACTUAL_PREMIUM.as("totalPremium"))
                .select(POLICY_PREMIUM.PERIOD_TOTAL_PREMIUM)
                .select(POLICY_PREMIUM.PERIOD_ORIGINAL_PREMIUM)
                .select(POLICY_PREMIUM.TOTAL_ACTUAL_PREMIUM)
                .select(POLICY_PREMIUM.TOTAL_REFUND_AMOUNT)
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
//                .select(stringField.as("row"))
                .from(POLICY)
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
//                .leftJoin(POLICY_PAYMENT).on(POLICY.POLICY_ID.eq(POLICY_PAYMENT.POLICY_ID).and(POLICY_PAYMENT.VALID_FLAG.eq(PolicyTermEnum.VALID_FLAG.effective.name())))
                .leftJoin(POLICY_PREMIUM).on(POLICY.POLICY_ID.eq(POLICY_PREMIUM.POLICY_ID));

        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.VALID_FLAG.eq(PolicyTermEnum.VALID_FLAG.effective.name())
        );

        if (AssertUtils.isNotEmpty(policyType)) {
            conditions.add(POLICY.POLICY_TYPE.eq(policyType));
        }
        if (AssertUtils.isNotEmpty(policyListVo.getKeyword())) {
            conditions.add(
                    POLICY.POLICY_NO.like("%" + policyListVo.getKeyword() + "%")
                            .or(POLICY_APPLICANT.COMPANY_NAME.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_APPLICANT.MOBILE.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_APPLICANT.NAME.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_APPLICANT.DELEGATE_NAME.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_AGENT.AGENT_CODE.like("%" + policyListVo.getKeyword() + "%"))
            );
        }

        //生效起期  大于等于
        String effectiveDateStartFormat = policyListVo.getEffectiveDateStart();
        if (AssertUtils.isNotEmpty(effectiveDateStartFormat) && AssertUtils.isDateFormat(effectiveDateStartFormat)) {
            conditions.add(POLICY.EFFECTIVE_DATE.ge(DateUtils.timeToTimeLow(DateUtils.stringToTime(effectiveDateStartFormat))));
        }
        //生效止期  小于等于
        String effectiveEndFormat = policyListVo.getEffectiveDateEnd();
        if (AssertUtils.isNotEmpty(effectiveEndFormat) && AssertUtils.isDateFormat(effectiveEndFormat)) {
            conditions.add(POLICY.EFFECTIVE_DATE.le(DateUtils.timeToTimeTop(DateUtils.stringToTime(effectiveEndFormat))));
        }

        //承保起期  大于等于
        String approveDateStartFormat = policyListVo.getApproveDateStart();
        if (AssertUtils.isNotEmpty(approveDateStartFormat) && AssertUtils.isDateFormat(approveDateStartFormat)) {
            conditions.add(POLICY.APPROVE_DATE.ge(DateUtils.timeToTimeLow(DateUtils.stringToTime(approveDateStartFormat))));
        }
        //承保止期  小于等于
        String approveDateEndFormat = policyListVo.getApproveDateEnd();
        if (AssertUtils.isNotEmpty(approveDateEndFormat) && AssertUtils.isDateFormat(approveDateEndFormat)) {
            conditions.add(POLICY.APPROVE_DATE.le(DateUtils.timeToTimeTop(DateUtils.stringToTime(approveDateEndFormat))));
        }

        conditions.add(POLICY_AGENT.AGENT_ID.eq(agentId)
                .and(POLICY.POLICY_ID.notIn(excludePolicyId))// 排除
                .or(POLICY.POLICY_ID.in(addPolicyId)));// 新增

        selectJoinStep.where(conditions);
        Table table = selectJoinStep.asTable();

        // 查询产品名称
        SelectJoinStep applyCoverage = this.getDslContext()
                .select(POLICY_COVERAGE.PRODUCT_NAME.as("productName"))
                .from(POLICY_AGENT)
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY_AGENT.POLICY_ID));

        List<Condition> coverageConditions = new ArrayList<Condition>();
        coverageConditions.add(POLICY_AGENT.AGENT_ID.eq(agentId));
        coverageConditions.add(POLICY_AGENT.POLICY_ID.eq(table.field(0)));
        coverageConditions.add(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        applyCoverage.where(coverageConditions).limit(1);

        //查询被保人人数
        SelectJoinStep applyInsured = this.getDslContext()
                .select(POLICY_INSURED.INSURED_ID.count().as("insuredSum"))
                .from(POLICY_INSURED);

        List<Condition> applyInsuredConditions = new ArrayList<Condition>();
        applyInsuredConditions.add(POLICY_INSURED.POLICY_ID.eq(table.field(0)));
        applyInsured.where(applyInsuredConditions);
        SelectJoinStep select = this.getDslContext()
                .select(table.fields())
                .select(DSL.field("(" + applyCoverage.toString() + ")").as("productName"))
                .select(DSL.field("(" + applyInsured.toString() + ")").as("insuredSum"))
                .from(table);
//        select.where(table.field("row").eq(1));
        select.orderBy(table.field(POLICY.APPROVE_DATE.getName()).desc());
        select.offset(policyListVo.getOffset()).limit(policyListVo.getPageSize());
        System.out.println(select.toString());
        return select.fetchInto(PolicyListBo.class);
    }

    @Override
    public List<PolicyListBo> loadPolicyListByPolicyIds(List<String> applyIds, PolicyListVo policyListVo, String policyType, List<String> salesBranchIds) {
        List<String> policyIds = this.getDslContext().select(POLICY.POLICY_ID).from(POLICY).where(POLICY.APPLY_ID.in(applyIds)).fetch().map(record -> {
            PolicyPo policyPo = BasePojo.getInstance(PolicyPo.class, record.into(PolicyRecord.class));
            return policyPo.getPolicyId();
        });
        SelectJoinStep applyCoverage = this.getDslContext()
                .selectDistinct(POLICY_COVERAGE.PRODUCT_NAME.as("productName"),POLICY_COVERAGE.PRODUCT_ID.as("productId"))
                .select(POLICY_COVERAGE.POLICY_ID.as("policyId"))
                .from(POLICY_COVERAGE);
        List<Condition> coverageConditions = new ArrayList<>();
        coverageConditions.add(POLICY_COVERAGE.POLICY_ID.in(policyIds));
        coverageConditions.add(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        applyCoverage.where(coverageConditions);
        Table applyCoverageTable = applyCoverage.asTable();

        SelectJoinStep selectJoinStep = this.getDslContext()
                .selectDistinct(POLICY_PRINT_INFO.fields())
                .select(POLICY_APPLICANT.NAME.as("applicantName"), POLICY_APPLICANT.MOBILE.as("applicantMobile"), POLICY_APPLICANT.ID_NO.as("applicantIdNo"), POLICY_APPLICANT.COMPANY_CONTRACT_NAME, POLICY_APPLICANT.COMPANY_CONTRACT_MOBILE, POLICY_APPLICANT.COMPANY_NAME,
                        POLICY_APPLICANT.DELEGATE_NAME, POLICY_APPLICANT.DELEGATE_MOBILE)
                .select(applyCoverageTable.fields())
                .select(POLICY_AGENT.fields())
                .select(POLICY.fields())
                .select(POLICY_PAYMENT.TOTAL_PREMIUM)
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                .from(POLICY)
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(applyCoverageTable).on(applyCoverageTable.field("policyId").eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_PRINT_INFO).on(POLICY_PRINT_INFO.POLICY_ID.eq(POLICY.POLICY_ID))
                //只查新契约的支付记录
                .leftJoin(POLICY_PAYMENT).on(POLICY_PAYMENT.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE.eq(PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_NEW_CONTRACT.name())));

        List<Condition> conditions = new ArrayList<>();
        if (AssertUtils.isNotEmpty(salesBranchIds)) {
            conditions.add(POLICY.SALES_BRANCH_ID.in(salesBranchIds));
        }
        conditions.add(POLICY.POLICY_ID.in(policyIds)
                .and(POLICY.VALID_FLAG.eq(PolicyTermEnum.VALID_FLAG.effective.name()))
        );
        if (AssertUtils.isNotEmpty(policyType)) {
            conditions.add(POLICY.POLICY_TYPE.eq(policyType));
        }
        if (AssertUtils.isNotEmpty(policyListVo.getKeyword())) {
            conditions.add(
                    POLICY.POLICY_NO.like("%" + policyListVo.getKeyword() + "%")
                            .or(POLICY.APPLY_NO.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_APPLICANT.COMPANY_NAME.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_APPLICANT.MOBILE.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_APPLICANT.NAME.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_APPLICANT.DELEGATE_NAME.like("%" + policyListVo.getKeyword() + "%"))
                            .or(POLICY_AGENT.AGENT_CODE.like("%" + policyListVo.getKeyword() + "%"))
            );
        }

        //生效起期  大于等于
        String effectiveDateStartFormat = policyListVo.getEffectiveDateStart();
        if (AssertUtils.isNotEmpty(effectiveDateStartFormat) && AssertUtils.isDateFormat(effectiveDateStartFormat)) {
            conditions.add(POLICY.EFFECTIVE_DATE.ge(DateUtils.timeToTimeLow(DateUtils.stringToTime(effectiveDateStartFormat))));
        }
        //生效止期  小于等于
        String effectiveDateEndFormat = policyListVo.getEffectiveDateEnd();
        if (AssertUtils.isNotEmpty(effectiveDateEndFormat) && AssertUtils.isDateFormat(effectiveDateEndFormat)) {
            conditions.add(POLICY.EFFECTIVE_DATE.le(DateUtils.timeToTimeLow(DateUtils.stringToTime(effectiveDateEndFormat))));
        }

        //投保起期  大于等于
        String applyDateStartFormat = policyListVo.getApplyDateStart();
        if (AssertUtils.isNotEmpty(applyDateStartFormat) && AssertUtils.isDateFormat(applyDateStartFormat)) {
            conditions.add(POLICY.APPLY_DATE.ge(DateUtils.timeToTimeLow(DateUtils.stringToTime(applyDateStartFormat))));
        }
        //投保止期  小于等于
        String applyDateEndFormat = policyListVo.getEffectiveDateEnd();
        if (AssertUtils.isNotEmpty(applyDateEndFormat) && AssertUtils.isDateFormat(applyDateEndFormat)) {
            conditions.add(POLICY.APPLY_DATE.le(DateUtils.timeToTimeLow(DateUtils.stringToTime(applyDateEndFormat))));
        }

        selectJoinStep.where(conditions);
        selectJoinStep.offset(policyListVo.getOffset()).limit(policyListVo.getPageSize());
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(PolicyListBo.class);
    }

    @Override
    public List<PolicyListBo> queryPolicyList(List<String> policyIdList, Users currentLoginUsers) {
        SelectJoinStep applyCoverage = this.getDslContext()
                .selectDistinct(POLICY_COVERAGE.PRODUCT_NAME)
                .select(POLICY_COVERAGE.POLICY_ID)
                .from(POLICY_COVERAGE);
        List<Condition> coverageConditions = new ArrayList<>();
        coverageConditions.add(POLICY_COVERAGE.POLICY_ID.in(policyIdList));
        coverageConditions.add(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        applyCoverage.where(coverageConditions);
        Table applyCoverageTable = applyCoverage.asTable();

        SelectJoinStep originalPremiumSum = this.getDslContext()
                .select(POLICY_COVERAGE.ORIGINAL_PREMIUM.nvl(new BigDecimal(0)).sum().as("originalPremiumSum"))
                .from(POLICY_COVERAGE);
        List<Condition> originalPremiumSumConditions = new ArrayList<>();
        originalPremiumSumConditions.add(POLICY_COVERAGE.POLICY_ID.in(POLICY.POLICY_ID));
        originalPremiumSum.where(originalPremiumSumConditions);
        originalPremiumSum.groupBy(POLICY_COVERAGE.POLICY_ID);

        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY_AGENT.AGENT_ID)
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_APPLICANT.MOBILE.as("applicantMobile"))
                .select(applyCoverageTable.fields())
                .select(POLICY_PAYMENT.TOTAL_PREMIUM)
                .select(originalPremiumSum.asField("originalPremiumSum"))
                .from(POLICY)
                .leftJoin(applyCoverageTable).on(applyCoverageTable.field(POLICY_COVERAGE.POLICY_ID.getName()).eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_PAYMENT).on(POLICY_PAYMENT.POLICY_ID.eq(POLICY.POLICY_ID));

        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(POLICY.POLICY_ID.in(policyIdList));
        selectJoinStep.where(conditionList);
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(PolicyListBo.class);
    }

    @Override
    public PolicyListBo queryPolicyListBo(String policyId) {
        SelectJoinStep applyCoverage = this.getDslContext()
                .selectDistinct(POLICY_COVERAGE.PRODUCT_NAME.as("productName"),POLICY_COVERAGE.PRODUCT_ID.as("productId"))
                .select(POLICY_COVERAGE.POLICY_ID.as("policyId"))
                .from(POLICY_COVERAGE);
        List<Condition> coverageConditions = new ArrayList<>();
        coverageConditions.add(POLICY_COVERAGE.POLICY_ID.eq(policyId));
        coverageConditions.add(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        applyCoverage.where(coverageConditions);
        Table applyCoverageTable = applyCoverage.asTable();

        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(POLICY_APPLICANT.NAME.as("applicantName"), POLICY_APPLICANT.MOBILE.as("applicantMobile"), POLICY_APPLICANT.ID_NO.as("applicantIdNo"), POLICY_APPLICANT.COMPANY_CONTRACT_NAME, POLICY_APPLICANT.COMPANY_CONTRACT_MOBILE, POLICY_APPLICANT.COMPANY_NAME,
                        POLICY_APPLICANT.DELEGATE_NAME, POLICY_APPLICANT.DELEGATE_MOBILE)
                .select(applyCoverageTable.fields())
                .select(POLICY_AGENT.fields())
                .select(POLICY.fields())
                .select(POLICY_PAYMENT.TOTAL_PREMIUM)
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                .from(POLICY)
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(applyCoverageTable).on(applyCoverageTable.field("policyId").eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                //只查新契约的支付记录
                .leftJoin(POLICY_PAYMENT).on(POLICY_PAYMENT.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE.eq(PolicyTermEnum.COMMISSION_BUSINESS_TYPE.BUSINESS_TYPE_NEW_CONTRACT.name())))
                .where(POLICY.POLICY_ID.eq(policyId));
        System.out.println(selectConditionStep.toString());
        return selectConditionStep.fetchOneInto(PolicyListBo.class);
    }

    @Override
    public List<PolicyPo> listPolicyPo(BasePageRequest basePageRequest) {
        return this.getDslContext()
                .selectFrom(POLICY)
                .where(POLICY.HESITATION_END_DATE.isNotNull()).and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .orderBy(POLICY.CREATED_DATE)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize()).fetchInto(PolicyPo.class);
    }

    /**
     * 分页查询保单险种数据
     *
     * @param basePageRequest 分页数据
     * @param renewalType     续期续保类型
     * @return
     */
    @Override
    public List<PolicyCoverageBo> listPolicyCoverage(BasePageRequest basePageRequest, String renewalType) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.POLICY_TYPE.eq(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name()));
        conditions.add(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        conditions.add(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        if (PolicyTermEnum.RENEWAL_TYPE.RENEWAL.name().equals(renewalType)) {
            // 续期
            conditions.add(POLICY_COVERAGE.PREMIUM_FREQUENCY.ne(PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name()));
        } else {
            // 续保
            // #5产品不可以续保
            conditions.add(POLICY_COVERAGE.PRODUCT_ID.notIn(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()));
            conditions.add(
                    // 趸交 或者一年期年缴
                    POLICY_COVERAGE.PREMIUM_FREQUENCY.eq(PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name())
                            .or(POLICY_COVERAGE.PREMIUM_FREQUENCY.eq(PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name())
                                    .and(POLICY_COVERAGE.PREMIUM_PERIOD.eq("1"))
                                    .and(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT.eq(PolicyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.YEAR.name()))
                            )
            );
            conditions.add(POLICY.POLICY_STATUS.eq(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name()));
            long currentTime = DateUtils.timeToTimeLow(DateUtils.getCurrentTime());
            conditions.add(POLICY_COVERAGE.COVERAGE_PERIOD_END_DATE.ge(currentTime));
            long after30Day = DateUtils.addStringDayRT(currentTime, 30);
            conditions.add(POLICY_COVERAGE.COVERAGE_PERIOD_END_DATE.lt(after30Day));
        }
        return this.getDslContext()
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.APPROVE_DATE)
                .select(POLICY.EFFECTIVE_DATE.as("policyEffectiveDate"))
                .select(POLICY_COVERAGE.fields())
                .from(POLICY)
                .innerJoin(POLICY_COVERAGE).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID))
                .where(conditions)
                .orderBy(POLICY.CREATED_DATE)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize())
                .fetchInto(PolicyCoverageBo.class);
    }

    /**
     * 查询离职代理人下的状态未终止的保单
     *
     * @param agentId 离职代理人
     * @return PolicyAssignAgentPos
     */
    @Override
    public List<PolicyAssignAgentPo> queryPolicyByDragAgentId(String agentId) {
        /*
        --查询业务员下所有生效的保单
SELECT * FROM policy
INNER JOIN policy_agent ON policy_agent.policy_id  = policy.policy_id
WHERE policy_agent.agent_id = '04b77fda8f76413abf2d4d74cb3300b5' AND policy.policy_status != 'POLICY_STATUS_INVALID';
         */
        /**
         *  POLICY_STATUS_INDEMNITY_TERMINATION("赔付终止"),
         POLICY_STATUS_HESITATION_REVOKE("犹豫期撤单"),
         POLICY_STATUS_INVALID_THOROUGH("永久失效"),
         POLICY_STATUS_EFFECT_TERMINATION("效力终止"),
         POLICY_STATUS_SURRENDER("退保"),
         */
        List<String> invalidPolicyStatus = Arrays.asList(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INDEMNITY_TERMINATION.name(),
                PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_HESITATION_REVOKE.name(),
                PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID_THOROUGH.name(),
                PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECT_TERMINATION.name(),
                PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_SURRENDER.name());
        SelectJoinStep selectOnConditionStep = this.getDslContext()
                .select(POLICY.POLICY_ID, POLICY.POLICY_NO, POLICY.SALES_BRANCH_ID.as("branchId"))
                .select(POLICY_AGENT.AGENT_ID.as("historyAgentId"), POLICY_AGENT.AGENT_CODE.as("historyAgentCode"))
                .from(POLICY)
                .innerJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
        selectOnConditionStep.where(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY.POLICY_STATUS.notIn(invalidPolicyStatus))
                .and(POLICY_AGENT.AGENT_ID.eq(agentId));
        System.out.println(selectOnConditionStep.toString());
        return selectOnConditionStep.fetchInto(PolicyAssignAgentPo.class);
    }

    /**
     * 查询代理人下的所有分单
     *
     * @param agentId 代理人
     * @return PolicyAssignAgentPos
     */
    @Override
    public List<PolicyAssignAgentPo> queryPolicyAssignByAgentId(String agentId, String agentStatus) {
        SelectJoinStep selectOnConditionStep = this.getDslContext()
                .select(POLICY_ASSIGN_AGENT.fields())
                .from(POLICY_ASSIGN_AGENT);
        List<Condition> conditions = new ArrayList<>();
        if (PolicyTermEnum.AGENT_STATUS.DRAG.name().equals(agentStatus)) {
            conditions.add(POLICY_ASSIGN_AGENT.HISTORY_AGENT_ID.eq(agentId));
        } else {
            conditions.add(POLICY_ASSIGN_AGENT.AGENT_ID.eq(agentId));
        }
        conditions.add(POLICY_ASSIGN_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        //20190724 修复bug 产生分单时过滤掉已指派的保单
        conditions.add(POLICY_ASSIGN_AGENT.ASSIGN_STATUS.ne(PolicyTermEnum.ASSIGN_STATUS.ASSIGNED.name()));
        selectOnConditionStep.where(conditions);
        return selectOnConditionStep.fetchInto(PolicyAssignAgentPo.class);
    }

    /**
     * 根据保单号查询分单信息 CHENBO MARK 双表联查典型
     *
     * @param renewalAssignPolicyVo
     * @param agentId
     * @return
     */
    @Override
    public List<RenewalAssignPolicyBo> queryList(RenewalAssignPolicyVo renewalAssignPolicyVo, List<String> agentId) {
        // 联合查询RENEWAL & RENEWAL_COVERAGE_PREMIUM
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(POLICY_ASSIGN_AGENT.ASSIGN_AGENT_ID,
                        POLICY_ASSIGN_AGENT.POLICY_ID,
                        POLICY_ASSIGN_AGENT.POLICY_NO,
                        POLICY_ASSIGN_AGENT.ASSIGN_STATUS,
                        POLICY_ASSIGN_AGENT.HISTORY_AGENT_ID,
                        POLICY_ASSIGN_AGENT.AGENT_CODE,
                        POLICY_ASSIGN_AGENT.AGENT_ID,
                        POLICY_ASSIGN_AGENT.HISTORY_AGENT_CODE)
                .select(POLICY_ASSIGN_AGENT.ASSIGN_AGENT_ID.countOver().as("totalLine"))
                .from(POLICY_ASSIGN_AGENT);

        // 关键字（保单合同号，营销员编号）
        List<Condition> conditions = new ArrayList<>();
        if (AssertUtils.isNotEmpty(renewalAssignPolicyVo.getKeyword())) {
            conditions.add(POLICY_ASSIGN_AGENT.POLICY_NO.like("%" + renewalAssignPolicyVo.getKeyword() + "%")
                    .or(POLICY_ASSIGN_AGENT.HISTORY_AGENT_ID.in(agentId)));
        }
        // 应收月份
//        if (AssertUtils.isNotEmpty(renewalAssignPolicyVo.getYearMonth())) {
//            conditions.add(RENEWAL.RENEWAL_YEAR_MONTH.eq(renewalAssignPolicyVo.getYearMonth()));
//        }
        conditions.add(POLICY_ASSIGN_AGENT.BRANCH_ID.in(renewalAssignPolicyVo.getBranchIdList()));
        // 按照状态筛选
        conditions.add(POLICY_ASSIGN_AGENT.ASSIGN_STATUS.in(renewalAssignPolicyVo.getAssignStatusList()));
        conditions.add(POLICY_ASSIGN_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        selectJoinStep.where(conditions);
        //分页
        selectJoinStep.offset(renewalAssignPolicyVo.getOffset()).limit(renewalAssignPolicyVo.getPageSize());
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(RenewalAssignPolicyBo.class);
    }

    @Override
    public List<RenewalAssignPolicyBo> queryRenewalAssignPolicyDo(RenewalPagingVo renewalPagingVo, List<String> agentId) {

        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(POLICY_ASSIGN_AGENT.ASSIGN_AGENT_ID,
                        POLICY_ASSIGN_AGENT.POLICY_ID,
                        POLICY_ASSIGN_AGENT.POLICY_NO,
                        POLICY_ASSIGN_AGENT.ASSIGN_STATUS,
                        POLICY_ASSIGN_AGENT.HISTORY_AGENT_ID,
                        POLICY_ASSIGN_AGENT.AGENT_CODE,
                        POLICY_ASSIGN_AGENT.AGENT_ID,
                        POLICY_ASSIGN_AGENT.HISTORY_AGENT_CODE)
                .select(POLICY_ASSIGN_AGENT.ASSIGN_AGENT_ID.countOver().as("totalLine"))
                .from(POLICY_ASSIGN_AGENT);
        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(POLICY_ASSIGN_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        // 关键字（保单合同号，营销员编号）
        if (AssertUtils.isNotEmpty(renewalPagingVo.getKeyword())) {
            conditionList.add(POLICY_ASSIGN_AGENT.POLICY_NO.like("%" + renewalPagingVo.getKeyword() + "%")
                    .or(POLICY_ASSIGN_AGENT.HISTORY_AGENT_ID.in(agentId)
                            .or(POLICY_ASSIGN_AGENT.AGENT_ID.in(agentId) )));
        }
        if (AssertUtils.isNotEmpty(renewalPagingVo.getAuditStatus())) {
            conditionList.add(POLICY_ASSIGN_AGENT.ASSIGN_STATUS.in( renewalPagingVo.getAuditStatus() ));
        }
        conditionList.add(POLICY_ASSIGN_AGENT.BRANCH_ID.in(renewalPagingVo.getBranchIdList()));
        selectJoinStep.where(conditionList).orderBy(POLICY_ASSIGN_AGENT.POLICY_NO.asc()).offset(renewalPagingVo.getOffset()).limit(renewalPagingVo.getPageSize());
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(RenewalAssignPolicyBo.class);
    }

    @Override
    public PolicyAssignAgentPo queryPolicyAssignAgentDo(String assignAgentId) {
        return this.getDslContext().select(POLICY_ASSIGN_AGENT.fields()).from(POLICY_ASSIGN_AGENT)
                .where(POLICY_ASSIGN_AGENT.ASSIGN_AGENT_ID.eq(assignAgentId)).and(POLICY_ASSIGN_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(PolicyAssignAgentPo.class);
    }

    @Override
    public PolicyAssignAgentPo queryPolicyAssignAgentDo(String policyId, List<String> assignStatusList) {
        return this.getDslContext().select(POLICY_ASSIGN_AGENT.fields()).from(POLICY_ASSIGN_AGENT)
                .where(POLICY_ASSIGN_AGENT.POLICY_ID.eq(policyId)).and(POLICY_ASSIGN_AGENT.ASSIGN_STATUS.in(assignStatusList))
                .and(POLICY_ASSIGN_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .limit(1)
                .fetchOneInto(PolicyAssignAgentPo.class);
    }

    @Override
    public void updateInvalidPolicyAssignAgent(String policyId) {
        getDslContext()
                .update(POLICY_ASSIGN_AGENT)
                .set(POLICY_ASSIGN_AGENT.VALID_FLAG, TerminologyConfigEnum.VALID_FLAG.invalid.name())
                .set(POLICY_ASSIGN_AGENT.UPDATED_DATE, DateUtils.getCurrentTime())
                .where(POLICY_ASSIGN_AGENT.POLICY_ID.eq(policyId), POLICY_ASSIGN_AGENT.ASSIGN_STATUS.ne(PolicyTermEnum.ASSIGN_STATUS.ASSIGNED.name()), POLICY_ASSIGN_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .execute();
    }

    /**
     * 查询分单信息
     *
     * @param assignAgentIds
     * @return
     */
    @Override
    public List<PolicyAssignAgentPo> listPolicyAssignAgent(List<String> assignAgentIds) {
        return this.getDslContext()
                .select(POLICY_ASSIGN_AGENT.fields())
                .from(POLICY_ASSIGN_AGENT)
                .where(POLICY_ASSIGN_AGENT.ASSIGN_AGENT_ID.in(assignAgentIds))
                .and(POLICY_ASSIGN_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(PolicyAssignAgentPo.class);
    }

    @Override
    public List<PolicyAssignRemarkPo> queryPolicyAssignRemarkDo(String assignAgentId) {
        return this.getDslContext().select(POLICY_ASSIGN_REMARK.fields()).from(POLICY_ASSIGN_REMARK)
                .where(POLICY_ASSIGN_REMARK.ASSIGN_AGENT_ID.eq(assignAgentId)).and(POLICY_ASSIGN_REMARK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .orderBy(POLICY_ASSIGN_REMARK.CREATED_DATE.desc())
                .fetchInto(PolicyAssignRemarkPo.class);
    }

    /**
     * 查询要生成佣金的保单缴费记录
     *
     * @param basePageRequest 分页数据
     * @return
     */
    @Override
    public List<PolicyPaymentBo> listPayment4Commission(BasePageRequest basePageRequest) {
        return this.getDslContext()
                .selectFrom(POLICY_PAYMENT)
                .where(POLICY_PAYMENT.PAYMENT_STATUS_CODE.eq(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name()))
                .and(POLICY_PAYMENT.COMMISSION_GENERATE_FLAG.eq(PolicyTermEnum.COMMISSION_GENERATE_FLAG.UNGENERATED.name()))
                .and(POLICY_PAYMENT.BIZ_DATE.le(DateUtils.timeToTimeTop(System.currentTimeMillis())))
                .orderBy(POLICY_PAYMENT.BIZ_DATE)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize())
                .fetch().map(record -> {
                    PolicyPaymentBo policyPaymentBo = BasePojo.getInstance(PolicyPaymentBo.class, record.into(PolicyPaymentRecord.class));
                    // 查询险种缴费信息
                    List<PolicyCoveragePaymentBo> policyCoveragePaymentBos = listCoveragePayment4Commission(policyPaymentBo.getPolicyPaymentId());
                    policyPaymentBo.setListPolicyCoveragePayment(policyCoveragePaymentBos);

                    return policyPaymentBo;
                });
    }

    @Override
    public List<PolicyPaymentBo> listPayment4Referral(List<String> idList) {
        return this.getDslContext()
                .selectFrom(POLICY_PAYMENT)
                .where(POLICY_PAYMENT.POLICY_ID.in(idList)
                        .and(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE.eq("BUSINESS_TYPE_NEW_CONTRACT")))
                .fetch().map(record -> {
                    PolicyPaymentBo policyPaymentBo = BasePojo.getInstance(PolicyPaymentBo.class, record.into(PolicyPaymentRecord.class));
                    // 查询险种缴费信息
                    List<PolicyCoveragePaymentBo> policyCoveragePaymentBos = listCoveragePayment4Commission(policyPaymentBo.getPolicyPaymentId());
                    policyPaymentBo.setListPolicyCoveragePayment(policyCoveragePaymentBos);

                    return policyPaymentBo;
                });
    }

    /**
     * 查询险种缴费信息
     *
     * @param policyPaymentId 　保单缴费信息ID
     * @return
     */
    @Override
    public List<PolicyCoveragePaymentBo> listCoveragePayment4Commission(String policyPaymentId) {
        return this.getDslContext()
                .select(POLICY_COVERAGE_PAYMENT.fields())
                .select(POLICY_COVERAGE.EFFECTIVE_DATE)
                .from(POLICY_COVERAGE_PAYMENT)
                .innerJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.COVERAGE_ID.eq(POLICY_COVERAGE_PAYMENT.COVERAGE_ID))
                .where(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID.eq(policyPaymentId))
                .and(POLICY_COVERAGE_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(PolicyCoveragePaymentBo.class);
    }

    @Override
    public List<PolicyStatusBo> queryPolicyStatus(List<String> idList) {
        SelectConditionStep<Record> where = this.getDslContext()
                .select(POLICY.POLICY_STATUS)
                .select(POLICY_APPLICANT.DELEGATE_CUSTOMER_ID)
                .select(POLICY_APPLICANT.CUSTOMER_ID)
                .from(POLICY)
                .leftJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .where(POLICY_APPLICANT.CUSTOMER_ID.in(idList).or(POLICY_APPLICANT.DELEGATE_CUSTOMER_ID.in(idList)));
        System.out.println(where.toString());
        return where.fetchInto(PolicyStatusBo.class);
    }

    /**
     * 查询待续保团险保单
     *
     * @param basePageRequest 分页信息
     * @return
     */
    @Override
    public List<PolicyPo> listPendingGroupRenewal(BasePageRequest basePageRequest) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.POLICY_TYPE.eq(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name()));
        conditions.add(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        List<String> policyStatus = new ArrayList<>();
        policyStatus.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name());
        policyStatus.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_IEXPIRE.name());
        conditions.add(POLICY.POLICY_STATUS.in(policyStatus));
        // 提前30天（含当天）产生团险续保数据
        conditions.add(POLICY.MATURITY_DATE.le(DateUtils.addStringDayRT(DateUtils.timeToTimeTop(DateUtils.getCurrentTime()), 29)));
        // 最迟到团险续保申请截止时间（满期+15天）
        conditions.add(POLICY.MATURITY_DATE.ge(DateUtils.addStringDayRT(DateUtils.timeToTimeLow(DateUtils.getCurrentTime()), -15)));
        return this.getDslContext()
                .selectFrom(POLICY)
                .where(conditions)
                .orderBy(POLICY.EFFECTIVE_DATE)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize())
                .fetchInto(PolicyPo.class);
    }

    /**
     * 查询待续保团险保单
     *
     * @param basePageRequest 分页信息
     * @return
     */
    @Override
    public List<PolicyCoverageBo> listPendingGroupInstallment(BasePageRequest basePageRequest) {

        Table<Record> groupMainCoverage = this.getDslContext()
                .selectDistinct(POLICY_COVERAGE.POLICY_ID)
                .select(POLICY_COVERAGE.POLICY_NO)
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_COVERAGE.PRIMARY_FLAG)
                .select(POLICY_COVERAGE.COVERAGE_STATUS)
                .select(POLICY_COVERAGE.EFFECTIVE_DATE)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD)
                .select(POLICY_COVERAGE.MATURITY_DATE)
                .select(POLICY_COVERAGE.PRODUCT_CODE)
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .select(POLICY_COVERAGE.PREMIUM_FREQUENCY)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_START_DATE)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_END_DATE)
                .from(POLICY_COVERAGE)
                .where(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()),
                        POLICY_COVERAGE.INSURED_ID.isNotNull(),
                        POLICY_COVERAGE.PREMIUM_FREQUENCY.notIn(PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name(), PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name()),
                        POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())).asTable();

        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.POLICY_TYPE.eq(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name()));
        conditions.add(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        SelectOnConditionStep<Record> on = this.getDslContext()
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.APPROVE_DATE)
                .select(groupMainCoverage.fields())
                .from(POLICY)
                .innerJoin(groupMainCoverage).on(POLICY.POLICY_ID.eq(groupMainCoverage.field(POLICY_COVERAGE.POLICY_ID)));
        on.where(conditions)
                .orderBy(POLICY.CREATED_DATE)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize());
        System.out.println(on.toString());
        return on.fetchInto(PolicyCoverageBo.class);
    }

    /**
     * 提前两个月产生团险续保提醒消息
     *
     * @param basePageRequest
     * @return
     */
    @Override
    public List<PolicyPo> listPendingGroupRenewalMessage(BasePageRequest basePageRequest) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.POLICY_TYPE.eq(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name()));
        conditions.add(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(POLICY.POLICY_STATUS.eq(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name()));
        // 续保提醒（保单满期前60天，30天，15天） 30天在产生续保数据的时候已发送 v3.7.0
        Condition between60 = POLICY.MATURITY_DATE.between(DateUtils.addStringDayRT(DateUtils.timeToTimeLow(DateUtils.getCurrentTime()), 60),
                DateUtils.addStringDayRT(DateUtils.timeToTimeTop(DateUtils.getCurrentTime()), 60));
        Condition between15 = POLICY.MATURITY_DATE.between(DateUtils.addStringDayRT(DateUtils.timeToTimeLow(DateUtils.getCurrentTime()), 15),
                DateUtils.addStringDayRT(DateUtils.timeToTimeTop(DateUtils.getCurrentTime()), 15));
        conditions.add(between60.or(between15));
        return this.getDslContext()
                .select(POLICY.fields())
                .from(POLICY)
                .where(conditions)
                .orderBy(POLICY.EFFECTIVE_DATE)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize())
                .fetchInto(PolicyPo.class);
    }

    @Override
    public List<ServiceStaffAssignBo> listServiceStaffAssignApplying(ServiceStaffAssignVo serviceStaffAssignVo, List<String> agentId) {
        SelectConditionStep<Record> selectConditionStep = getDslContext()
                .select(POLICY_SERVICE_AGENT.fields())
                .select(POLICY_SERVICE_AGENT.POLICY_SERVICE_AGENT_ID.countOver().as("totalLine"))
                .from(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.BRANCH_ID.in(serviceStaffAssignVo.getBranchIdList()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.in(serviceStaffAssignVo.getAssignStatusList()));

        String keyword = serviceStaffAssignVo.getKeyword();
        if (AssertUtils.isNotEmpty(keyword)) {
            selectConditionStep.and(POLICY_SERVICE_AGENT.POLICY_NO.like("%" + keyword + "%")
                    .or(POLICY_SERVICE_AGENT.SERVICE_AGENT_ID.in(agentId)));
        }

        return selectConditionStep
                .orderBy(POLICY_SERVICE_AGENT.CREATED_DATE.desc())
                .offset(serviceStaffAssignVo.getOffset())
                .limit(serviceStaffAssignVo.getPageSize())
                .fetchInto(ServiceStaffAssignBo.class);
    }

    @Override
    public List<PolicyPo> listPolicyPoByPolicyIds(List<String> policyIds) {
        return getDslContext()
                .selectFrom(POLICY)
                .where(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY.POLICY_ID.in(policyIds))
                .fetchInto(PolicyPo.class);
    }

    @Override
    public List<PolicyCoveragePo> listPolicyCoveragePoByPolicyIds(List<String> policyIds) {
        return getDslContext()
                .selectFrom(POLICY_COVERAGE)
                .where(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_COVERAGE.POLICY_ID.in(policyIds))
                .fetchInto(PolicyCoveragePo.class);
    }

    @Override
    public List<PolicyAgentPo> listPolicyAgentPoByPolicyIds(List<String> policyIds) {
        return getDslContext()
                .selectFrom(POLICY_AGENT)
                .where(POLICY_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_AGENT.POLICY_ID.in(policyIds))
                .fetchInto(PolicyAgentPo.class);
    }

    @Override
    public List<PolicyServiceAgentRemarkPo> listPolicyServiceAgentRemarkByPk(String policyServiceAgentId) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT_REMARK)
                .where(POLICY_SERVICE_AGENT_REMARK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT_REMARK.POLICY_SERVICE_AGENT_ID.eq(policyServiceAgentId))
                .orderBy(POLICY_SERVICE_AGENT_REMARK.CREATED_DATE.desc())
                .fetchInto(PolicyServiceAgentRemarkPo.class);
    }

    @Override
    public PolicyServiceAgentPo getPolicyServiceAgentPo(String policyId, List<String> assignStatusList) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.in(assignStatusList))
                .and(POLICY_SERVICE_AGENT.POLICY_ID.eq(policyId))
                .fetchOneInto(PolicyServiceAgentPo.class);
    }

    @Override
    public List<ServiceStaffAssignBo> listServiceStaffAssignAudit(ServiceStaffAssignVo serviceStaffAssignVo, List<String> agentId) {
        SelectConditionStep<Record> selectConditionStep = getDslContext()
                .select(POLICY_SERVICE_AGENT.fields())
                .select(POLICY_SERVICE_AGENT.POLICY_SERVICE_AGENT_ID.countOver().as("totalLine"))
                .from(POLICY_SERVICE_AGENT)
                .leftJoin(POLICY_AGENT)
                .on(POLICY_SERVICE_AGENT.POLICY_ID.eq(POLICY_AGENT.POLICY_ID))
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.BRANCH_ID.in(serviceStaffAssignVo.getBranchIdList()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.in(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.PENDING_REVIEW.name(),
                        PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.UNDER_AUDIT.name()));

        // 关键字（保单合同号，营销员编号）
        String keyword = serviceStaffAssignVo.getKeyword();
        if (AssertUtils.isNotEmpty(keyword)) {
            selectConditionStep.and(POLICY_SERVICE_AGENT.POLICY_NO.like("%" + keyword + "%")
                    .or(POLICY_SERVICE_AGENT.SERVICE_AGENT_ID.in(agentId)
                            .or(POLICY_AGENT.AGENT_ID.in(agentId))));
        }
        String auditStatus = serviceStaffAssignVo.getAuditStatus();
        if (AssertUtils.isNotEmpty(auditStatus)) {
            selectConditionStep.and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.like("%" + auditStatus + "%"));
        }

        return selectConditionStep
                .orderBy(POLICY_SERVICE_AGENT.CREATED_DATE.desc())
                .offset(serviceStaffAssignVo.getOffset())
                .limit(serviceStaffAssignVo.getPageSize())
                .fetchInto(ServiceStaffAssignBo.class);
    }

    @Override
    public List<PolicyServiceAgentPo> listPolicyServiceAgentPoByPks(List<String> policyServiceAgentIds) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.POLICY_SERVICE_AGENT_ID.in(policyServiceAgentIds))
                .fetchInto(PolicyServiceAgentPo.class);
    }

    @Override
    public void updateEffectiveFlag(List<String> policyServiceAgentIds) {
        getDslContext()
                .update(POLICY_SERVICE_AGENT)
                .set(POLICY_SERVICE_AGENT.EFFECTIVE_FLAG, TerminologyConfigEnum.WHETHER.YES.name())
                .set(POLICY_SERVICE_AGENT.UPDATED_DATE, DateUtils.getCurrentTime())
                .where(POLICY_SERVICE_AGENT.POLICY_SERVICE_AGENT_ID.in(policyServiceAgentIds))
                .execute();
    }

    @Override
    public List<PolicyServiceAgentPo> listPolicyServiceAgentPoByEffectiveDate(Long todayTimeTop, Long todayTimeLow) {
        Table<Record> newPolicyServiceAgent = getDslContext()
                .select(POLICY_SERVICE_AGENT.POLICY_ID)
                .select(DSL.max(POLICY_SERVICE_AGENT.CREATED_DATE).as(POLICY_SERVICE_AGENT.CREATED_DATE))
                .from(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_SERVICE_AGENT.EFFECTIVE_DATE.between(todayTimeLow, todayTimeTop))
                .groupBy(POLICY_SERVICE_AGENT.POLICY_ID)
                .asTable();

        return getDslContext()
                .select(POLICY_SERVICE_AGENT.fields())
                .from(POLICY_SERVICE_AGENT)
                .join(newPolicyServiceAgent)
                .on(POLICY_SERVICE_AGENT.POLICY_ID.eq(newPolicyServiceAgent.field(POLICY_SERVICE_AGENT.POLICY_ID))
                        .and(POLICY_SERVICE_AGENT.CREATED_DATE.eq(newPolicyServiceAgent.field(POLICY_SERVICE_AGENT.CREATED_DATE))))
                .fetchInto(PolicyServiceAgentPo.class);
    }

    @Override
    public PolicyServiceAgentPo getPolicyServiceAgentPoByPolicyId(String policyId) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_SERVICE_AGENT.POLICY_ID.eq(policyId))
                .orderBy(POLICY_SERVICE_AGENT.CREATED_DATE.desc())
                .limit(1)
                .fetchOneInto(PolicyServiceAgentPo.class);
    }

    @Override
    public void updateAuditStatus(Users users, String policyServiceAgentId) {
        getDslContext()
                .update(POLICY_SERVICE_AGENT)
                .set(POLICY_SERVICE_AGENT.UPDATED_DATE, DateUtils.getCurrentTime())
                .set(POLICY_SERVICE_AGENT.UPDATED_USER_ID, users.getUserId())
                .set(POLICY_SERVICE_AGENT.ASSIGN_STATUS, PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.UNDER_AUDIT.name())
                .where(POLICY_SERVICE_AGENT.POLICY_SERVICE_AGENT_ID.eq(policyServiceAgentId))
                .execute();
    }

    @Override
    public List<ServiceStaffAssignBo> listServiceStaffAssignAll(ServiceStaffAssignVo serviceStaffAssignVo, List<String> agentId) {
        SelectConditionStep<Record> selectConditionStep = getDslContext()
                .select(POLICY_SERVICE_AGENT.fields())
                .select(POLICY_SERVICE_AGENT.POLICY_SERVICE_AGENT_ID.countOver().as("totalLine"))
                .from(POLICY_SERVICE_AGENT)
                .leftJoin(POLICY_AGENT)
                .on(POLICY_SERVICE_AGENT.POLICY_ID.eq(POLICY_AGENT.POLICY_ID))
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.BRANCH_ID.in(serviceStaffAssignVo.getBranchIdList()));

        // 关键字（保单合同号，营销员编号）
        String keyword = serviceStaffAssignVo.getKeyword();
        if (AssertUtils.isNotEmpty(keyword)) {
            selectConditionStep.and(POLICY_SERVICE_AGENT.POLICY_NO.like("%" + keyword + "%")
                    .or(POLICY_SERVICE_AGENT.SERVICE_AGENT_ID.in(agentId)
                            .or(POLICY_AGENT.AGENT_ID.in(agentId))));
        }
        String auditStatus = serviceStaffAssignVo.getAuditStatus();
        if (AssertUtils.isNotEmpty(auditStatus)) {
            selectConditionStep.and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.like("%" + auditStatus + "%"));
        }

        return selectConditionStep
                .orderBy(POLICY_SERVICE_AGENT.CREATED_DATE.desc())
                .offset(serviceStaffAssignVo.getOffset())
                .limit(serviceStaffAssignVo.getPageSize())
                .fetchInto(ServiceStaffAssignBo.class);
    }

    @Override
    public List<PolicyServiceAgentPo> listPolicyServiceAgentPoByPolicyId(String policyId) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_SERVICE_AGENT.POLICY_ID.eq(policyId))
                .orderBy(POLICY_SERVICE_AGENT.CREATED_DATE.desc())
                .fetchInto(PolicyServiceAgentPo.class);
    }

    @Override
    public List<PolicyAssignAgentPo> listPolicyAssignAgentPoByPolicyId(String policyId) {
        return getDslContext()
                .selectFrom(POLICY_ASSIGN_AGENT)
                .where(POLICY_ASSIGN_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ASSIGN_AGENT.POLICY_ID.eq(policyId))
                .orderBy(POLICY_ASSIGN_AGENT.CREATED_DATE.desc())
                .fetchInto(PolicyAssignAgentPo.class);
    }

    @Override
    public PolicyServiceAgentPo getPolicyServiceAgentPo(String policyId) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_SERVICE_AGENT.EFFECTIVE_FLAG.eq(PolicyTermEnum.YES_NO.YES.name()))
                .and(POLICY_SERVICE_AGENT.POLICY_ID.eq(policyId))
                .fetchOneInto(PolicyServiceAgentPo.class);
    }

    @Override
    public List<PolicyServiceAgentPo> listNewServiceAgentPoByPolicyIds(List<String> policyIds) {
        Table<Record> newPolicyServiceAgent = getDslContext()
                .select(POLICY_SERVICE_AGENT.POLICY_ID)
                .select(DSL.max(POLICY_SERVICE_AGENT.CREATED_DATE).as(POLICY_SERVICE_AGENT.CREATED_DATE))
                .from(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_SERVICE_AGENT.POLICY_ID.in(policyIds))
                .groupBy(POLICY_SERVICE_AGENT.POLICY_ID)
                .asTable();

        return getDslContext()
                .select(POLICY_SERVICE_AGENT.fields())
                .from(POLICY_SERVICE_AGENT)
                .join(newPolicyServiceAgent)
                .on(POLICY_SERVICE_AGENT.POLICY_ID.eq(newPolicyServiceAgent.field(POLICY_SERVICE_AGENT.POLICY_ID))
                        .and(POLICY_SERVICE_AGENT.CREATED_DATE.eq(newPolicyServiceAgent.field(POLICY_SERVICE_AGENT.CREATED_DATE))))
                .fetchInto(PolicyServiceAgentPo.class);
    }

    @Override
    public List<PolicyServiceAgentPo> listPolicyServiceAgentPoByServiceAgentId(String serviceAgentId) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_SERVICE_AGENT.EFFECTIVE_FLAG.eq(PolicyTermEnum.YES_NO.YES.name()))
                .and(POLICY_SERVICE_AGENT.SERVICE_AGENT_ID.eq(serviceAgentId))
                .fetchInto(PolicyServiceAgentPo.class);
    }

    @Override
    public void updateEffectiveFlagInvalidByPolicyIds(List<String> policyIds) {
        getDslContext()
                .update(POLICY_SERVICE_AGENT)
                .set(POLICY_SERVICE_AGENT.EFFECTIVE_FLAG, PolicyTermEnum.YES_NO.NO.name())
                .set(POLICY_SERVICE_AGENT.UPDATED_DATE, DateUtils.getCurrentTime())
                .where(POLICY_SERVICE_AGENT.POLICY_ID.in(policyIds))
                .execute();
    }

    @Override
    public void updateEffectiveFlagInvalidByPolicyId(String policyId) {
        getDslContext()
                .update(POLICY_SERVICE_AGENT)
                .set(POLICY_SERVICE_AGENT.EFFECTIVE_FLAG, PolicyTermEnum.YES_NO.NO.name())
                .where(POLICY_SERVICE_AGENT.POLICY_ID.eq(policyId))
                .execute();
    }

    @Override
    public List<PolicyServiceAgentPo> listPolicyServiceAgentPoByServiceAgentId(String serviceAgentId, List<String> assignStatusList) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.SERVICE_AGENT_ID.eq(serviceAgentId))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.in(assignStatusList))
                .fetchInto(PolicyServiceAgentPo.class);
    }

    @Override
    public void deletePolicyServiceAgentPoByPks(List<String> policyServiceAgentIds) {
        getDslContext()
                .deleteFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.POLICY_SERVICE_AGENT_ID.in(policyServiceAgentIds))
                .execute();
    }

    @Override
    public PolicyServiceAgentPo getNewestPolicyServiceAgentPo(String policyId) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_SERVICE_AGENT.POLICY_ID.eq(policyId))
                .orderBy(POLICY_SERVICE_AGENT.CREATED_DATE.desc())
                .limit(1)
                .fetchOneInto(PolicyServiceAgentPo.class);
    }

    @Override
    public List<PolicyServiceAgentPo> listAllPolicyServiceAgentPoByPolicyIds(List<String> policyIds) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.POLICY_ID.in(policyIds))
                .fetchInto(PolicyServiceAgentPo.class);
    }

    @Override
    public PolicyServiceAgentPo getNewestPolicyServiceAgentPo(String policyId, String effectiveFlag) {
        return getDslContext()
                .selectFrom(POLICY_SERVICE_AGENT)
                .where(POLICY_SERVICE_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_SERVICE_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_SERVICE_AGENT.EFFECTIVE_FLAG.eq(effectiveFlag))
                .and(POLICY_SERVICE_AGENT.POLICY_ID.eq(policyId))
                .orderBy(POLICY_SERVICE_AGENT.CREATED_DATE.desc())
                .limit(1)
                .fetchOneInto(PolicyServiceAgentPo.class);
    }


    @Override
    public List<PolicyAllocationBo> listPolicyAllocationApplying(ServiceStaffAssignVo serviceStaffAssignVo, List<String> agentId) {
        SelectJoinStep<Record> records = getDslContext()
                .select(POLICY_ALLOCATION.fields())
                .select(POLICY_ALLOCATION.POLICY_ALLOCATION_ID.countOver().as("totalLine"))
                .from(POLICY_ALLOCATION);

        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(POLICY_ALLOCATION.BRANCH_ID.in(serviceStaffAssignVo.getBranchIdList()));
        String keyword = serviceStaffAssignVo.getKeyword();
        if (AssertUtils.isNotEmpty(keyword)) {
            conditions.add(POLICY_ALLOCATION.POLICY_NO.like("%" + keyword + "%").or(POLICY_ALLOCATION.AGENT_ID.in(agentId).or(POLICY_ALLOCATION.HISTORY_AGENT_ID.in(agentId))));
        }
        if (AssertUtils.isNotEmpty(serviceStaffAssignVo.getAssignStatusList())) {
            conditions.add(POLICY_ALLOCATION.ASSIGN_STATUS.in(serviceStaffAssignVo.getAssignStatusList()));
        }
        records.where(conditions);
        return records
                .orderBy(POLICY_ALLOCATION.CREATED_DATE.desc())
                .offset(serviceStaffAssignVo.getOffset())
                .limit(serviceStaffAssignVo.getPageSize())
                .fetchInto(PolicyAllocationBo.class);
    }

    @Override
    public List<PolicyAllocationRemarkPo> listPolicyAllocationRemarkByPk(String policyAllocationId) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION_REMARK)
                .where(POLICY_ALLOCATION_REMARK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION_REMARK.POLICY_ALLOCATION_ID.eq(policyAllocationId))
                .orderBy(POLICY_ALLOCATION_REMARK.CREATED_DATE.desc())
                .fetchInto(PolicyAllocationRemarkPo.class);
    }

    @Override
    public PolicyAllocationPo getPolicyAllocationPo(String policyId, List<String> assignStatusList) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.ASSIGN_STATUS.in(assignStatusList))
                .and(POLICY_ALLOCATION.POLICY_ID.eq(policyId))
                .fetchOneInto(PolicyAllocationPo.class);
    }

    @Override
    public List<PolicyAllocationPo> listPolicyAllocationPoByPks(List<String> policyAllocationIds) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.POLICY_ALLOCATION_ID.in(policyAllocationIds))
                .fetchInto(PolicyAllocationPo.class);
    }

    @Override
    public void updatePolicyAllocationEffectiveFlag(List<String> policyAllocationIds) {
        getDslContext()
                .update(POLICY_ALLOCATION)
                .set(POLICY_ALLOCATION.EFFECTIVE_FLAG, TerminologyConfigEnum.WHETHER.YES.name())
                .set(POLICY_ALLOCATION.UPDATED_DATE, DateUtils.getCurrentTime())
                .where(POLICY_ALLOCATION.POLICY_ALLOCATION_ID.in(policyAllocationIds))
                .execute();
    }

    @Override
    public List<PolicyAllocationPo> listPolicyAllocationPoByEffectiveDate(Long todayTimeTop, Long todayTimeLow) {
        Table<Record> newPolicyServiceAgent = getDslContext()
                .select(POLICY_ALLOCATION.POLICY_ID)
                .select(DSL.max(POLICY_ALLOCATION.CREATED_DATE).as(POLICY_ALLOCATION.CREATED_DATE))
                .from(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_ALLOCATION.EFFECTIVE_DATE.between(todayTimeLow, todayTimeTop))
                .groupBy(POLICY_ALLOCATION.POLICY_ID)
                .asTable();

        return getDslContext()
                .select(POLICY_ALLOCATION.fields())
                .from(POLICY_ALLOCATION)
                .join(newPolicyServiceAgent)
                .on(POLICY_ALLOCATION.POLICY_ID.eq(newPolicyServiceAgent.field(POLICY_ALLOCATION.POLICY_ID))
                        .and(POLICY_ALLOCATION.CREATED_DATE.eq(newPolicyServiceAgent.field(POLICY_ALLOCATION.CREATED_DATE))))
                .fetchInto(PolicyAllocationPo.class);
    }

    @Override
    public PolicyAllocationPo getPolicyAllocationPoByPolicyId(String policyId) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_ALLOCATION.POLICY_ID.eq(policyId))
                .orderBy(POLICY_ALLOCATION.CREATED_DATE.desc())
                .limit(1)
                .fetchOneInto(PolicyAllocationPo.class);
    }

    @Override
    public void updatePolicyAllocationAuditStatus(Users users, String policyAllocationId) {
        getDslContext()
                .update(POLICY_ALLOCATION)
                .set(POLICY_ALLOCATION.UPDATED_DATE, DateUtils.getCurrentTime())
                .set(POLICY_ALLOCATION.UPDATED_USER_ID, users.getUserId())
                .set(POLICY_ALLOCATION.ASSIGN_STATUS, PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.UNDER_AUDIT.name())
                .where(POLICY_ALLOCATION.POLICY_ALLOCATION_ID.eq(policyAllocationId))
                .execute();
    }

    @Override
    public List<PolicyAllocationPo> listPolicyAllocationPoByPolicyId(String policyId) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_ALLOCATION.POLICY_ID.eq(policyId))
                .orderBy(POLICY_ALLOCATION.CREATED_DATE.desc())
                .fetchInto(PolicyAllocationPo.class);
    }

    @Override
    public PolicyAllocationPo getPolicyAllocationPo(String policyId) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_ALLOCATION.EFFECTIVE_FLAG.eq(PolicyTermEnum.YES_NO.YES.name()))
                .and(POLICY_ALLOCATION.POLICY_ID.eq(policyId))
                .fetchOneInto(PolicyAllocationPo.class);
    }

    @Override
    public List<PolicyAllocationPo> listNewPolicyAllocationPoByPolicyIds(List<String> policyIds) {
        Table<Record> newPolicyAllocation = getDslContext()
                .select(POLICY_ALLOCATION.POLICY_ID)
                .select(DSL.max(POLICY_ALLOCATION.CREATED_DATE).as(POLICY_ALLOCATION.CREATED_DATE))
                .from(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_ALLOCATION.POLICY_ID.in(policyIds))
                .groupBy(POLICY_ALLOCATION.POLICY_ID)
                .asTable();

        return getDslContext()
                .select(POLICY_ALLOCATION.fields())
                .from(POLICY_ALLOCATION)
                .join(newPolicyAllocation)
                .on(POLICY_ALLOCATION.POLICY_ID.eq(newPolicyAllocation.field(POLICY_ALLOCATION.POLICY_ID))
                        .and(POLICY_ALLOCATION.CREATED_DATE.eq(newPolicyAllocation.field(POLICY_ALLOCATION.CREATED_DATE))))
                .fetchInto(PolicyAllocationPo.class);
    }

    public List<PolicyAllocationPo> listPolicyAllocationPoById(String agentId) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_ALLOCATION.EFFECTIVE_FLAG.eq(PolicyTermEnum.YES_NO.YES.name()))
                .and(POLICY_ALLOCATION.AGENT_ID.eq(agentId))
                .fetchInto(PolicyAllocationPo.class);
    }

    @Override
    public void updatePolicyAllocationEffectiveFlagInvalidByPolicyIds(List<String> policyIds) {
        getDslContext()
                .update(POLICY_ALLOCATION)
                .set(POLICY_ALLOCATION.EFFECTIVE_FLAG, PolicyTermEnum.YES_NO.NO.name())
                .set(POLICY_ALLOCATION.UPDATED_DATE, DateUtils.getCurrentTime())
                .where(POLICY_ALLOCATION.POLICY_ID.in(policyIds))
                .execute();
    }

    @Override
    public void updatePolicyAllocationEffectiveFlagInvalidByPolicyId(String policyId) {
        getDslContext()
                .update(POLICY_ALLOCATION)
                .set(POLICY_ALLOCATION.EFFECTIVE_FLAG, PolicyTermEnum.YES_NO.NO.name())
                .where(POLICY_ALLOCATION.POLICY_ID.eq(policyId))
                .execute();
    }

    public List<PolicyAllocationPo> listPolicyAllocationPoById(String serviceAgentId, List<String> assignStatusList) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.AGENT_ID.eq(serviceAgentId))
                .and(POLICY_ALLOCATION.ASSIGN_STATUS.in(assignStatusList))
                .fetchInto(PolicyAllocationPo.class);
    }

    @Override
    public void deletePolicyAllocationPoByPks(List<String> policyServiceAgentIds) {
        getDslContext()
                .deleteFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.POLICY_ALLOCATION_ID.in(policyServiceAgentIds))
                .execute();
    }

    @Override
    public PolicyAllocationPo getNewestPolicyAllocationPo(String policyId) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_ALLOCATION.POLICY_ID.eq(policyId))
                .orderBy(POLICY_ALLOCATION.CREATED_DATE.desc())
                .limit(1)
                .fetchOneInto(PolicyAllocationPo.class);
    }

    @Override
    public List<PolicyAllocationPo> listAllPolicyAllocationPoByPolicyIds(List<String> policyIds) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.POLICY_ID.in(policyIds))
                .fetchInto(PolicyAllocationPo.class);
    }

    @Override
    public PolicyAllocationPo getNewestPolicyAllocationPo(String policyId, String effectiveFlag) {
        return getDslContext()
                .selectFrom(POLICY_ALLOCATION)
                .where(POLICY_ALLOCATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_ALLOCATION.ASSIGN_STATUS.eq(PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.ASSIGNED.name()))
                .and(POLICY_ALLOCATION.EFFECTIVE_FLAG.eq(effectiveFlag))
                .and(POLICY_ALLOCATION.POLICY_ID.eq(policyId))
                .orderBy(POLICY_ALLOCATION.CREATED_DATE.desc())
                .limit(1)
                .fetchOneInto(PolicyAllocationPo.class);
    }
}
