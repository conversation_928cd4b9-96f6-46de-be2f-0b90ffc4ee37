package com.gclife.policy.dao.impl;

import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.policy.dao.PolicyApplicantBaseDao;
import com.gclife.policy.model.bo.PolicyApplicantBo;
import com.gclife.policy.model.bo.PolicyApplicantListBo;
import com.gclife.policy.model.config.PolicyTermEnum;
import org.jooq.Condition;
import org.jooq.SelectJoinStep;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.gclife.policy.core.jooq.Tables.POLICY;
import static com.gclife.policy.core.jooq.Tables.POLICY_APPLICANT;
import static com.gclife.policy.core.jooq.Tables.POLICY_COVERAGE;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 15:26 2018/12/7
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Repository
public class PolicyApplicantBaseDaoImpl extends BaseDaoImpl implements PolicyApplicantBaseDao {
    /**
     * 查询保单投保人
     * @param policyId 保单ID
     * @return
     */
    @Override
    public PolicyApplicantBo queryPolicyApplicant(String policyId) {
        return this.getDslContext()
                .selectFrom(POLICY_APPLICANT)
                .where(POLICY_APPLICANT.POLICY_ID.eq(policyId))
                .and(POLICY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(PolicyApplicantBo.class);
    }

    @Override
    public PolicyApplicantBo queryPolicyApplicantByPolicyId(String policyId) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(POLICY_APPLICANT.fields())
                .from(POLICY_APPLICANT);
        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(POLICY_APPLICANT.POLICY_ID.eq(policyId));
        selectJoinStep.where(conditionList);
        return (PolicyApplicantBo) selectJoinStep.fetchOneInto(PolicyApplicantBo.class);
    }

    /**
     * 模糊查询投保人
     * @param keyword 关键字
     * @param applicantType 投保人类型
     * @return
     */
    @Override
    public List<PolicyApplicantBo> listFuzzyPolicyApplicant(String keyword, String applicantType) {
        List<Condition> conditions = new ArrayList<>();
        if (AssertUtils.isNotEmpty(applicantType)) {
            conditions.add(POLICY_APPLICANT.APPLICANT_TYPE.eq(applicantType));
        }
        if (AssertUtils.isNotEmpty(keyword)) {
            conditions.add(POLICY_APPLICANT.NAME.like("%" + keyword + "%"));
        }
        conditions.add(POLICY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        return this.getDslContext()
                .select(POLICY_APPLICANT.fields())
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .from(POLICY_APPLICANT)
                .where(conditions)
                .fetchInto(PolicyApplicantBo.class);
    }

    @Override
    public List<PolicyApplicantListBo> policyApplicantList(List<String> policyNos) {
         SelectJoinStep selectJoinStep = this.getDslContext().select(POLICY_APPLICANT.fields())
                 .select(POLICY.APPLY_DATE)
                 .select(POLICY.APPROVE_DATE)
                 .select(POLICY.POLICY_STATUS)
                 .select(POLICY.POLICY_NO)
                 .select(POLICY_COVERAGE.PRODUCT_NAME)
                 .select(POLICY_COVERAGE.COVERAGE_PERIOD)
                 .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT)
                 .select(POLICY_APPLICANT.COMPANY_LEGAL_PERSON_NAME)
                 .select(POLICY_COVERAGE.PRODUCT_ID)
                 .from(POLICY_APPLICANT)
                 .leftJoin(POLICY).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                 .leftJoin(POLICY_COVERAGE).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID));
         selectJoinStep.where(POLICY.POLICY_NO.in(policyNos)).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
         return selectJoinStep.fetchInto(PolicyApplicantListBo.class);
    }
}
