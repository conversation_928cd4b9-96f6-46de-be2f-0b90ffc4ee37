package com.gclife.policy.dao;

import com.gclife.common.dao.base.BaseDao;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.base.Users;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.vo.PolicyListVo;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.ReserveWithdrawalReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 保单基础Dao
 * @date 18-5-18
 */
public interface PolicyBaseDao extends BaseDao {


    PolicyPo queryPolicyPo(String policyId);
    /**
     * 查询保单代理人信息
     *
     * @param policyId 保单ID
     * @return PolicyAgentPo
     */
    PolicyAgentPo getPolicyAgent(String policyId);

    /**
     * 查询投保人信息
     *
     * @param policyIdOrNo 保单ID
     * @return PolicyApplicantPo
     */
    PolicyApplicantBo getPolicyApplicant(String policyIdOrNo);

    /**
     * 查询保单联系信息
     *
     * @param policyId 保单ID
     * @return PolicyContactInfoPo
     */
    PolicyContactInfoPo getPolicyContactInfo(String policyId);

    /**
     * 查询保单附件列表
     *
     * @param policyId           保单ID
     * @param attachmentTypeCode 保单附件类形编码
     * @return list
     */
    List<PolicyAttachmentPo> getPolicyAttachmentList(String policyId, String attachmentTypeCode);

    /**
     * 查询保单附件列表
     *
     * @param policyId            保单ID
     * @param attachmentTypeCodes 保单附件类形编码集
     * @return list
     */
    List<PolicyAttachmentPo> getApplyAttachmentList(String policyId, List<String> attachmentTypeCodes);

    /**
     * 查询保单特别约定
     *
     * @param policyId 保单ID
     * @return PolicySpecialContractPo
     */
    List<PolicySpecialContractPo> getPolicySpecialContract(String policyId);

    /**
     * 查询保单回执
     *
     * @param policyId 保单ID
     * @return PolicyReceiptInfoPo
     */
    PolicyReceiptInfoPo getPolicyReceiptInfo(String policyId);

    /**
     * 查询保单回执
     *
     * @param policyIds 保单ID
     * @return list
     */
    List<PolicyReceiptInfoPo> listPolicyReceiptInfo(List<String> policyIds);

    /**
     * 查询投保单被保人列表
     *
     * @param policyId 保单ID
     * @param keyword  搜索关键字
     * @return list
     */
    List<PolicyInsuredBo> getPolicyInsuredList(String policyId, String keyword);

    /**
     * 查询投保单被保人列表
     *
     * @param policyId 保单ID
     * @param insuredIds  被保人
     * @return list
     */
    List<PolicyInsuredBo> getPolicyInsuredListForPb(String policyId, List<String> insuredIds);

    /**
     * 批量查询被保人
     * @param policyIds 保单ID
     * @return
     */
    List<PolicyInsuredPo> listPolicyInsured(List<String> policyIds);


    /**
     * 查询投保单被保人列表
     *
     * @param policyId  保单ID
     * @param versionNo 版本号
     * @return list
     */
    List<PolicyInsuredBo> getPolicyInsuredByVersionNo(String policyId, String versionNo);

    /**
     * 查询被保人险种信息列表
     *
     * @param policyId  保单ID
     * @param insuredId 被保人ID
     * @return list
     */
    List<PolicyCoveragePo> getPolicyCoverageList(String policyId, String insuredId);

    /**
     * 查询团险保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    List<PolicyCoveragePo> listGroupPolicyCoverage(String policyId);

    /**
     * 查询团险保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    List<PolicyCoverageLevelBo> listGroupPolicyPaymentCoverage(String policyId);

    /**
     * 查询团险保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    List<PolicyCoveragePo> listAllGroupPolicyCoverage(String policyId);

    /**
     * 查询保单险种
     *
     * @param policyId
     * @return
     */
    List<PolicyCoverageBo> getPolicyCoverageList(String policyId);

    /**
     * 查询保单险种
     *
     * @param policyId
     * @return
     */
    List<PolicyCoverageBo> getPolicyCoverageListForPb(String policyId);


    PolicyCoveragePaymentBo queryPolicyCoveragePayment(String policyPaymentId, String policyId, String coverageId);

    /**
     * 保费
     *
     * @param policyId
     * @return
     */
    PolicyPremiumBo getPolicyPremium(String policyId);

    /**
     * 保费不包括支付信息
     *
     * @param policyId
     * @return
     */
    PolicyPremiumBo getPolicyPremiumExtendPayment(String policyId);


    /**
     * 保单加费信息
     *
     * @param policyId 保单ID
     * @return PolicyAddPremiumPo
     */
    List<PolicyAddPremiumPo> getPolicyAddPremium(String policyId);

    /**
     * 根据保单ID查询被保人统计信息
     *
     * @param policyId 保单ID
     * @return PolicyInsuredCollectPo
     */
    PolicyInsuredCollectPo getPolicyInsuredCollect(String policyId);

    /**
     * 根据投保单ID查询保单信息
     *
     * @param applyId 投保单ID
     * @return PolicyPo
     */
    PolicyPo getPolicyByApplyId(String applyId);

    /**
     * 根据投保单状态查询保单信息
     *
     * @param policyStatus 投保单ID
     * @return PolicyPo
     */
    List<PolicyPo> getPolicyByPolicyStatus(String policyStatus);

    /**
     * 根据保单号查询保单信息
     *
     * @param policyNo 保单号
     * @return
     */
    PolicyPo getPolicyByPolicyNo(String policyNo);

    /**
     * 查询缴费信息
     *
     * @param policyId 保单号
     * @return PolicyPaymentPo
     */
    PolicyPaymentPo getPolicyPayment(String policyId);

    /**
     * 查询最新的缴费信息
     *
     * @param policyId
     * @return
     */
    PolicyPaymentBo getNewPolicyPayment(String policyId);

    /**
     * 查询保单下所有的缴费信息
     *
     * @param policyId      保单ID
     * @param paymentStatus
     * @return
     */
    List<PolicyPaymentBo> getListPolicyPayment(String policyId, String paymentStatus);

    /**
     * 查询保单下所有的缴费成功信息
     *
     * @param policyId 保单ID
     * @return
     */
    List<PolicyPaymentBo> getListPayPolicyPayment(String policyId);

    /**
     * 查询保单下所有的缴费成功信息(排除续期预缴费记录)
     *
     * @param policyId 保单ID
     * @param endTime  结束日期
     * @param paymentBusinessType
     * @return
     */
    List<PolicyPaymentBo> getListPayPolicyPayment(String policyId, long endTime, List<String> paymentBusinessType);

    /**
     * 查询指定的缴费信息
     *
     * @param policyPaymentIds 保单缴费ID
     * @return
     */
    List<PolicyPaymentBo> listPolicyPayment(List<String> policyPaymentIds);

    /**
     * 根据代理人ID集合查询其名下的所有保单数
     *
     * @param agentIds 代理人ID集合
     * @return PolicyPos
     */
    List<PolicyPo> getPolicyByAgentId(List<String> agentIds);

    /**
     * 根据保单ID查询保单历史代理人列表
     *
     * @param policyId 保单ID
     * @return List<PolicyAgentHistoryPo>
     */
    List<PolicyAgentHistoryPo> queryListPolicyAgentHistoryPo(String policyId);

    /**
     * 根据保单ID查询上一个保单历史代理人
     *
     * @param policyId 保单ID
     * @return PolicyAgentHistoryPo
     */
    PolicyAgentHistoryPo queryOnePolicyAgentHistoryPo(String policyId);

    /**
     * 根据保单ID和应收时间查询抢单记录
     *
     * @param policyId       保单ID
     * @param receivableDate 应缴日
     * @return null
     */
    PolicyRenewalGrabPo queryPolicyRenewalGrab(String policyId, Long receivableDate);

    /**
     * 根据保单ID查询抢单记录
     *
     * @param policyId 保单ID
     * @return list
     */
    List<PolicyRenewalGrabPo> listPolicyRenewalGrab(String policyId);

    /**
     * 根据保单ID查询抢单记录
     *
     * @param policyIds 保单ID集
     * @return list
     */
    List<PolicyRenewalGrabPo> listPolicyRenewalGrab(List<String> policyIds);

    /**
     * 查询保单险种缴费表
     *
     * @param policyPaymentId 保单缴费ID
     * @return List<PolicyCoveragePaymentBo>
     */
    List<PolicyCoveragePaymentBo> queryPolicyCoveragePaymentBo(String policyPaymentId);

    List<PolicyCoveragePaymentBo> queryPolicyCoveragePaymentBos(List<String> policyPaymentIds);

    /**
     * 查询首期缴费信息
     *
     * @param policyId
     * @return
     */
    PolicyPaymentBo queryFirstPolicyPayment(String policyId);

    /**
     * 根据保单号，投保人姓名检索保单信息
     *
     * @param policyListVo
     * @return
     */
    List<PolicyApplicantCoverageBo> queryPolicyApplicantCoverageBo(PolicyListVo policyListVo);

    /**
     * 查询客户关联的保单
     *
     * @param customerAgentIds 客户ID
     * @return List<PolicyEndorseBo>
     */
    List<PolicyEndorseBo> queryPolicyByCustomerId(List<String> customerAgentIds);

    /**
     * 查询客户关联的保单
     *
     * @param customerAgentIds 客户ID
     * @return List<PolicyEndorseBo>
     */
    List<PolicyApplicantBo> queryPolicyByCustomerAgentIds(List<String> customerAgentIds);

    /**
     * 查询客户作为投保人关联的保单
     *
     * @param customerAgentIds 客户ID
     * @return List<PolicyEndorseBo>
     */
    List<PolicyEndorseBo> queryPolicyByApplicantCustomerId(List<String> customerAgentIds);

    /**
     * 查询保单详情
     *
     * @param policyId  保单ID
     * @param versionNo 版本号
     * @return PolicyEndorseBo
     */
    PolicyEndorseInfoBo queryPolicyInfoByPolicyId(String policyId, String versionNo);

    /**
     * 查询保单被保人扩展信息
     *
     * @param policyId 保单ID
     * @return List<PolicyInsuredExtendPo>
     */
    List<PolicyInsuredExtendPo> queryPolicyInsuredExtend(String policyId);

    /**
     * 查询险种缴费
     *
     * @param policyId   保单ID
     * @param coverageId 险种ID
     * @return PolicyCoveragePaymentBo
     */
    List<PolicyCoveragePaymentBo> queryPolicyCoveragePayment(String policyId, String coverageId);

    /**
     * 查询险种保费
     *
     * @param policyId   保单ID
     * @param coverageId 险种ID
     * @return PolicyCoveragePremiumBo
     */
    PolicyCoveragePremiumBo queryOnePolicyCoveragePremium(String policyId, String coverageId);

    /**
     * 查询险种责任
     *
     * @param policyId   保单ID
     * @param coverageId 险种ID
     * @return List<PolicyCoverageDutyBo>
     */
    List<PolicyCoverageDutyBo> queryPolicyCoverageDuty(String policyId, String coverageId);

    /**
     * 查询险种红利
     *
     * @param policyId   保单ID
     * @param coverageId 险种ID
     * @return PolicyCoverageBonusBo
     */
    PolicyCoverageBonusBo queryOnePolicyCoverageBonus(String policyId, String coverageId);

    /**
     * 查询被保人统计信息
     *
     * @param policyId 保单ID
     * @return PolicyInsuredCollectPo
     */
    PolicyInsuredCollectPo queryOnePolicyInsuredCollect(String policyId);

    /**
     * 查询保单付费人信息
     *
     * @param policyId 保单ID
     * @return PolicyPayorInfoBo
     */
    PolicyPayorInfoBo queryOnePolicyPayorInfo(String policyId);

    /**
     * 根据保单ID查询指定支付状态缴费信息
     *
     * @param policyId      保单ID
     * @param paymentStatus 支付状态
     * @return
     */
    List<PolicyEndorsePaymentBo> listPolicyPayment(String policyId, String paymentStatus);

    /**
     * 团险被保人下的已缴费信息
     *
     * @param policyId      保单ID
     * @param customerId    客户ID
     * @param paymentStatus
     * @return PolicyEndorsePaymentBos
     */
    List<PolicyCoveragePaymentBo> queryGroupCustomerPayments(String policyId, String customerId, String paymentStatus);

    /**
     * @param policyPaymentIds ids
     * @return List<PolicyPaymentBo>
     */
    public List<PolicyPaymentBo> queryPolicyPayment(List<String> policyPaymentIds);

    /**
     * 查询保单信息跟保人信息（收付费明细报表）
     *
     * @param businessNo payment关联投保单id
     */
    public List<PolicyReportBo> queryPolicyReport(List<String> businessNo);

    /**
     * 查询业务报表（投保人资料）
     */
    public List<PolicyApplicantReportBo> queryPolicyApplicantReport(BasePageRequest basePageRequest, String startDate);

    /**
     * 查询业务报表（被保人资料）
     */
    List<PolicyInsuredReportBo> queryPolicyInsuredsReport(BasePageRequest basePageRequest, String startDate);

    /**
     * 投保人代表报表数据
     *
     * @param basePageRequest
     * @param startDate
     * @return
     */
    List<PolicyApplicantReportBo> queryDelegateApplicantReport(BasePageRequest basePageRequest, String startDate);

    /**
     * 查询承保清单
     */
    public List<PolicyReportUnderwritingBo> queryPolicyReportUnderwritingBo(BasePageRequest basePageRequest, String startDate);

    /**
     * 根据policyId查询policy_agment中数据
     */
    PolicyAgentPo queryOnePolicyAgentPo(String policyId);

    /**
     * 根据policyId查询policy_applicant中数据
     *
     * @param policyIds
     */
    List<PolicyApplicantPo> queryOnePolicyApplicantPo(List<String> policyIds);

    /**
     * 根据policyId查询policy_insured中数据
     */
    PolicyInsuredPo queryOnePolicyInsuredPo(String policyId);

    /**
     * 查询监管报表承保清单所需数据
     *
     * @param basePageRequest
     * @param startDate
     * @return
     */
    List<PolicyReportUnderwritingBo> queryPolicyReportUnderwritingRegulatory(BasePageRequest basePageRequest, String startDate);

    /**
     * 根据policyId查询policy_payement中支付成功数据
     */
    List<PolicyPaymentPo> queryAllPolicyPaymentPo(String policyId);


    /**
     * 根据policyId查询policy_payement中数据
     */
    List<PolicyPaymentPo> queryPolicyPayment(String policyId);

    // TODO: 18-11-19 只查询个险数据，未查询团险

    /**
     * 根据policyId查询policy——coverage中数据
     */
    PolicyCoveragePo queryPolicyCoveragePo(String policyId);

    /**
     * 查询待生效险种扩展信息
     *
     * @param policyId 保单ID
     * @return
     */
    List<PolicyCoverageExtendPo> listPendingCoverageExtend(String policyId);

    /**
     * 查询待生效险种扩展信息
     *
     * @param policyId 保单ID
     * @param dataType 数据类型
     * @return
     */
    List<PolicyCoverageExtendPo> listPendingCoverageExtend(String policyId, String dataType);

    /**
     * 查询待生效险种扩展信息
     *
     * @param policyIds 保单ID集
     * @param dataType  数据类型
     * @return
     */
    List<PolicyCoverageExtendPo> listPendingCoverageExtend(List<String> policyIds, String dataType);

    /**
     * 查询保单操作
     *
     * @param policyId      保单ID
     * @param operationCode 操作编码
     * @return
     */
    PolicyOperationPo queryPolicyOperation(String policyId, String operationCode);

    BaseOperationPo queryBaseOperationPoByOperationCode(String operationCode);

    /**
     * 查询保单险种信息
     *
     * @param policyId
     * @return
     */
    List<PolicyCoveragePo> queryPolicyCoverage(String policyId);

    PolicyCoverageBo queryPolicyPublicMainCoveragePo(String policyId);

    /**
     * 根据险种ID查询险种扩展信息
     *
     * @param coverageIds 险种IDS
     * @param dataType    数据类型
     * @return
     */
    List<PolicyCoverageExtendPo> listCoverageExtendByCoverageId(List<String> coverageIds, String dataType);

    /**
     * 查询当天满期的保单
     *
     * @param basePageRequest 参数
     * @return
     */
    List<PolicyPo> queryMaturityPolicy(BasePageRequest basePageRequest);

    /**
     * 处理(满期不续保)/(保单中止续期附加险满期)的续期附加险数据
     *
     * @param basePageRequest 分页参数
     * @return String
     */
    List<PolicyCoveragePo> queryMaturityRenewalAdditionCoverage(BasePageRequest basePageRequest);

    /**
     * 查询险种保费信息
     *
     * @param policyId 保单ID
     * @return
     */
    List<PolicyCoveragePremiumPo> listPolicyCoveragePremium(String policyId);

    /**
     * 查询保单挂起操作
     *
     * @param policyId     保单ID
     * @param hookObjectId 挂起对象ID
     * @param hookStatus
     * @return PolicyHookPo
     */
    PolicyHookPo getPolicyHook(String policyId, String hookObjectId, String hookStatus);

    /**
     * 查询保单挂起操作
     *
     * @param policyId     保单ID
     * @param hookObjectId 挂起对象ID
     * @param customerId   customerId
     * @param hookStatus
     * @return PolicyHookPo
     */
    PolicyHookPo getGroupPolicyHook(String policyId, String hookObjectId, String customerId, String hookStatus);

    /**
     * 根据保单ID查询回访信息
     *
     * @param policyId 保单ID
     * @return
     */
    PolicyReturnVisitPo queryReturnVisitByBusinessId(String policyId);

    /**
     * 查询客户保单
     *
     * @param customerIds
     * @return
     */
    List<ClaimPolicyBo> queryPolicyByCustomerIds(String... customerIds);

    /**
     * 模糊查询保单信息
     *
     * @param keyword 关键字
     * @return
     */
    List<PolicyApplicantInfoBo> queryListPolicysByKeyword(String keyword);

    /**
     * 查询当前用户的团险保单信息
     *
     * @param userId  当前用户
     * @param keyword 关键字
     * @return
     */
    List<PolicyApplicantInfoBo> queryAgentListPolicysByKeyword(String userId, String keyword);

    /**
     * 查询所有团险保单信息
     *
     * @param userId  当前用户
     * @param keyword 关键字
     * @return
     */
    List<PolicyApplicantInfoBo> queryAgentListPolicysByKeywordNew(String userId, String keyword);

    List<PolicyApplicantPo> queryPolicyApplicantByCustomerId(String customerId);

    List<PolicyInsuredPo> queryPolicyInsuredByCustomerId(String customerId);

    List<PolicyCoveragePaymentBo> queryPolicyCoveragePaymentByPaymentId(String policyPaymentId);

    PolicyBo queryPolicyBoByJoin(String policyId);

    List<PolicyGroupReportUnderwritingBo> queryPolicyGroupReportUnderwritingBo(BasePageRequest basePageRequest, String startDate);

    List<GroupSdfReportBo> queryListPolicyGroupSdfReportBo(BasePageRequest basePageRequest, String startDate);

    List<PolicyCoverageDutyBo> queryPolicyCoverageDutyBoByCoverageIds(List<String> coverageIds);

    List<PolicyCoverageDutyBo> getPolicyCoverageDutyList(String policyId);

    /**
     * 查询个险和团险保单
     *
     * @param keyword 　关键字
     * @return
     */
    List<PolicyApplicantInfoBo> queryReturnVisitPolicyByKeyword(String keyword);

    /**
     * 团险根据保单id查询被保人信息
     *
     * @param policyId
     * @return
     */
    List<GroupInsuredInfoBo> queryPolicyInsuredInfoBo(String policyId);

    /**
     * 根据客户ID查询保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimPolicyBo> listSimplePolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据客户ID查询团险保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimPolicyBo> listSimpleGroupPolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据客户ID查询历史保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimPolicyBo> listSimplePolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据客户ID查询历史团险保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimPolicyBo> listSimpleGroupPolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据保单ID查询保单基础信息列表
     *
     * @param policyId          保单ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    ClaimPolicyBo querySimplePolicyById(String policyId, Long dataEffectiveDate);

    /**
     * 根据客户ID查询历史保单基础信息列表
     *
     * @param policyId          保单ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimPolicyBo> listSimplePolicyHistoryByPolicyId(String policyId, Long dataEffectiveDate);

    /**
     * 根据客户ID查询保单详细信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimDetailPolicyBo> listDetailPolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据客户ID查询历史保单详细信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimDetailPolicyBo> listDetailPolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据保单ID查询保单详细信息列表
     *
     * @param policyId          保单ID
     * @param customerId
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    ClaimDetailPolicyBo queryDetailPolicyByPolicyId(String policyId, String customerId, Long dataEffectiveDate);

    /**
     * 根据保单ID查询历史保单详细信息列表
     *
     * @param policyId          保单ID
     * @param customerId
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimDetailPolicyBo> listDetailPolicyHistoryByPolicyId(String policyId, String customerId, Long dataEffectiveDate);

    /**
     * 根据保单ID查询历史保单详细信息列表
     *
     * @param policyId          保单ID
     * @param customerId
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimDetailPolicyBo> listDetailPolicyHistoryByPolicyId1(String policyId, String customerId, Long dataEffectiveDate);

    List<PolicyBeneficiaryInfoBo> queryPolicyBeneficiaryInfoBo(String policyId);

    /**
     * 需要设置的投保人代表数据
     *
     * @return
     */
    List<PolicyGroupReportSyncApplicantBo> querySyncApplicantCustomer();

    /**
     * 需要设置的投保人代表Po数据
     *
     * @return
     */
    List<PolicyApplicantPo> queryApplicantCustomer();

    Integer countNewApplicantByCustomerIds(List<String> customerIds);

    Integer countNewInsuredByCustomerIds(List<String> customerIds);

    /**
     * 查询业绩报表统计数据
     *
     * @param policyIdList
     * @return
     */
    List<ActualPerformanceReportBo> queryActualPerformance(List<String> policyIdList);

    /**
     * 实收历史版业绩明细表统计
     *
     * @param versionNo
     * @return
     */
    List<ActualPerformanceReportBo> queryPolicyVersionActualPerformance(List<String> versionNo);

    /**
     * 保单ID查询受益人信息
     *
     * @param policyId
     * @param modifyFlag
     * @return
     */
    List<PolicyBeneficiaryInfoBo> queryPolicyLoanBeneficiary(String policyId, String modifyFlag);

    /**
     * 查询保单信息
     *
     * @param policyId 保单ID
     * @return
     */
    PolicyBo queryPolicy(String policyId);

    /**
     * 查询全部被保人数据
     *
     * @param policyId 保单ID
     * @return list
     */
    List<PolicyInsuredBo> getPolicyAllInsuredList(String policyId);

    /**
     * 查询包括已失效的险种责任
     *
     * @param coverageIds
     * @return
     */
    List<PolicyCoverageDutyBo> queryPolicyCoverageDutyValidFlagByCoverageIds(List<String> coverageIds);

    /**
     * 季度准备金提取报表统计
     *
     *
     * @param quarterDate
     * @param basePageRequest
     * @return
     */
    List<ReserveWithdrawalReportBo> quarterlyStatisticsReserveWithdrawalReport(String quarterDate, BasePageRequest basePageRequest);

    /**
     * 执行同步保单银保渠道手续费费用明细表
     *
     * @param basePageRequest
     * @param syncDate
     * @return
     */
    List<ServiceChargeBankChannelBo> syncPolicyServiceChargeBankChannel(BasePageRequest basePageRequest, String syncDate);

    /**
     * 执行同步保单银保渠道手续费费用明细表-支付明细
     * @param policyPaymentIdList
     * @return
     */
    List<ServiceChargeBankChannelBo> syncPolicyServiceChargeBankChannelPayment(List<String> policyPaymentIdList);

    List<PolicyInsuredPo> getGroupPolicyCustomer(String policyId, List<String> policyCustomerIds);

    /**
     * 查询保额 根据保单ID
     *
     * @param policyIdList
     * @return
     */
    List<PolicyAmountBo> queryPolicyAmount(List<String> policyIdList);

    /**
     * 季度准备金提取报表统计
     *
     *
     * @param quarterDate
     * @param basePageRequest
     * @return
     */
    List<ReserveWithdrawalReportBo> quarterlyHistoryStatisticsReserveWithdrawalReport(String quarterDate, BasePageRequest basePageRequest);

    /**
     * 查询被保人id的缴费频次
     * @param insuredIdList
     * @return
     */
    List<ReserveWithdrawalReportBo> quarterlyStatisticsReserveWithdrawalPaymentReport(List<String> insuredIdList);

    /**
     * 获取满期保单加费信息
     *
     * @return PolicyAddPremiumPo
     */
    List<PolicyAddPremiumPo> listMaturityPolicyAddPremium();

    /**
     *
     * @param policyId
     * @param gainedDate
     * @return
     */
    PolicyAssignAgentPo queryAssignAgent(String policyId, Long gainedDate);

    /**
     *
     * @param policyId
     * @param agentId
     * @param gainedDate
     * @return
     */
    PolicyAssignAgentPo queryAssignDate(String policyId, String agentId, Long gainedDate);

    /**
     * 同步保单销售报表
     * 包含：保单、保单缴费信息表、保单投保人、保单被保人、保单代理人、保单回执
     * @param basePageRequest
     * @param syncDate
     * @return
     */
    List<SaleApplyPolicyBo> syncSaleReportPolicyDetail(BasePageRequest basePageRequest, String syncDate);

    /**
     * 保单覆盖范围、保单承保费、保单支付、保单承保支付、记录表
     * @param policyIdList
     * @return
     */
    List<SaleApplyPolicyBo> syncSaleReportCoverage(List<String> policyIdList);

    /**
     *
     * @param coverageListId
     * @param ratingsNameList
     * @param addPremiumObjectCode
     * @return
     */
    List<PolicyAddPremiumPo> listPolicyAddPremiumByCoverageIdList(List<String> coverageListId, List<String> ratingsNameList, List<String> addPremiumObjectCode);

    /**
     * 查询支付记录的被保人人数
     * @return
     */
    List<PolicyPaymentBo> queryPolicyPaymentInsuredSum(BasePageRequest basePageRequest, String syncDate);

    /**
     * 查询指定加费信息
     *
     * @param policyId   保单Id
     * @param insuredId  被保人Id
     * @param coverageId 险种Id
     * @return PolicyAddPremiumPo
     */
    List<PolicyAddPremiumPo> getPolicyCoverageAddPremium(String policyId, String insuredId, String coverageId);

    /**
     * 分单指派更新审核状态
     *
     * @param users
     * @param assignAgentId
     */
    void updateAuditStatus(Users users, String assignAgentId);

    /**
     * 根据主键获取 PolicyPo
     *
     * @param policyId
     * @return
     */
    PolicyPo getPolicyPoByPk(String policyId);

    /**
     * 根据保单ID集合 获取PolicyAgentPo集合
     *
     * @param policyIds
     * @return
     */
    List<PolicyAgentPo> listPolicyAgentPoByPolicyIds(List<String> policyIds);

    /**
     * 查询客户投保的保单
     * @param customerIds 客户ID
     * @param policyStatusList 保单状态
     * @return
     */
    List<ClientPolicyBo> listCustomerPolicy(List<String> customerIds, List<String> policyStatusList);

    /**
     * 查询保障中客户
     * @param agentId 业务员ID
     * @param policyStatusList 保单状态
     * @return
     */
    List<String> listEffectiveCustomer(String agentId, List<String> policyStatusList);

    List<PolicyInsuredBo> pagePolicyInsuredBo(String policyId, PolicyListVo policyListVo);

    /**
     * 针对34好产品的保单：如果犹豫期撤单，可以允许继续投保
     * @param idNo
     * @return
     */
    List<PolicyAndInsuredBo> querySokSanPolicyInsured(String idNo);
}
