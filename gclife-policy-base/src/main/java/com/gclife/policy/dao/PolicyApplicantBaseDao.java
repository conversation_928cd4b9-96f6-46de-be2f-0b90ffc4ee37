package com.gclife.policy.dao;

import com.gclife.policy.model.bo.PolicyApplicantBo;
import com.gclife.policy.model.bo.PolicyApplicantListBo;

import java.util.List;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 15:26 2018/12/7
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
public interface PolicyApplicantBaseDao {
    /**
     * 查询保单投保人
     * @param policyId 保单ID
     * @return
     */
    PolicyApplicantBo queryPolicyApplicant(String policyId);

    /**
     * 查询保单投保人
     *
     * @param policyId
     * @return
     */
    PolicyApplicantBo queryPolicyApplicantByPolicyId(String policyId);

    /**
     * 模糊查询投保人
     * @param keyword 关键字
     * @param applicantType 投保人类型
     * @return
     */
    List<PolicyApplicantBo> listFuzzyPolicyApplicant(String keyword, String applicantType);

    List<PolicyApplicantListBo> policyApplicantList(List<String> policyNos);
}
