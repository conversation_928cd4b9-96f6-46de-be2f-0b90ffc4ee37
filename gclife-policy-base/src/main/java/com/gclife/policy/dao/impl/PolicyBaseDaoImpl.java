package com.gclife.policy.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.BasePojo;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.policy.core.jooq.tables.PolicyCoverage;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.core.jooq.tables.records.*;
import com.gclife.policy.dao.PolicyBaseDao;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.vo.PolicyListVo;
import com.gclife.policy.transform.PolicyBaseEndorseTransfer;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.ReserveWithdrawalReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.policy.core.jooq.Tables.*;
import static com.gclife.policy.core.jooq.tables.Policy.POLICY;
import static com.gclife.policy.core.jooq.tables.PremiumCheck.PREMIUM_CHECK;
import static com.gclife.policy.model.config.PolicyTermEnum.BUSINESS_TYPE.GROUP_RENEWAL;
import static com.gclife.policy.model.config.PolicyTermEnum.COMMISSION_BUSINESS_TYPE.*;
import static com.gclife.policy.model.config.PolicyTermEnum.COMMISSION_GENERATE_FLAG.GENERATED;
import static com.gclife.policy.model.config.PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED;
import static com.gclife.policy.model.config.PolicyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_PENDING_EFFECT;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_TYPE.STATUTORY_TRAVEL_ACCIDENT_INSURANCE;
import static com.gclife.policy.model.config.PolicyTermEnum.VALID_FLAG.effective;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 保单基础Dao
 * @date 18-5-18
 */
@Slf4j
@Component
public class PolicyBaseDaoImpl extends BaseDaoImpl implements PolicyBaseDao {

    @Autowired
    private PolicyBaseEndorseTransfer policyBaseEndorseTransfer;

    @Override
    public PolicyPo queryPolicyPo(String policyId) {
        return this.getDslContext()
                .selectFrom(POLICY)
                .where(POLICY.POLICY_ID.eq(policyId).or(POLICY.POLICY_NO.eq(policyId)))
                .and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .limit(1)
                .fetchOneInto(PolicyPo.class);
    }

    /**
     * 查询保单代理人信息
     *
     * @param policyId 保单ID
     * @return PolicyAgentPo
     */
    @Override
    public PolicyAgentPo getPolicyAgent(String policyId) {
        return this.getDslContext()
                .select(POLICY_AGENT.fields())
                .from(POLICY_AGENT)
                .where(POLICY_AGENT.POLICY_ID.eq(policyId))
                .and(POLICY_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(PolicyAgentPo.class);
    }

    /**
     * 查询投保人信息
     *
     * @param policyIdOrNo 保单ID
     * @return PolicyApplicantPo
     */
    @Override
    public PolicyApplicantBo getPolicyApplicant(String policyIdOrNo) {
        PolicyApplicantBo policyApplicantPo;
        try {
            policyApplicantPo = this.getDslContext()
                    .select(POLICY_APPLICANT.fields())
                    .select(POLICY.POLICY_STATUS)
                    .select(POLICY.POLICY_NO)
                    .from(POLICY_APPLICANT)
                    .leftJoin(POLICY)
                    .on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                    .where(POLICY_APPLICANT.POLICY_ID.eq(policyIdOrNo).or(POLICY.POLICY_NO.eq(policyIdOrNo)))
                    .fetchOneInto(PolicyApplicantBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_APPLICANT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_APPLICANT_ERROR);
        }
        return policyApplicantPo;
    }

    /**
     * 查询保单联系信息
     *
     * @param policyId 保单ID
     * @return PolicyContactInfoPo
     */
    @Override
    public PolicyContactInfoPo getPolicyContactInfo(String policyId) {
        PolicyContactInfoPo policyContactInfoPo;
        try {
            policyContactInfoPo = this.getDslContext()
                    .select(POLICY_CONTACT_INFO.fields())
                    .from(POLICY_CONTACT_INFO)
                    .where(POLICY_CONTACT_INFO.POLICY_ID.eq(policyId))
                    .and(POLICY_CONTACT_INFO.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .fetchOneInto(PolicyContactInfoPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_CONTACT_INFO_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_CONTACT_INFO_ERROR);
        }
        return policyContactInfoPo;
    }

    /**
     * 查询保单附件列表
     *
     * @param policyId           保单ID
     * @param attachmentTypeCode 保单附件类形编码
     * @return list
     */
    @Override
    public List<PolicyAttachmentPo> getPolicyAttachmentList(String policyId, String attachmentTypeCode) {
        List<PolicyAttachmentPo> policyAttachmentPos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(POLICY_ATTACHMENT.POLICY_ID.eq(policyId));
            if (AssertUtils.isNotEmpty(attachmentTypeCode)) {
                conditions.add(POLICY_ATTACHMENT.ATTACHMENT_TYPE_CODE.eq(attachmentTypeCode));
            }
            policyAttachmentPos = this.getDslContext()
                    .select(POLICY_ATTACHMENT.fields())
                    .from(POLICY_ATTACHMENT)
                    .where(conditions)
                    .fetchInto(PolicyAttachmentPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ATTACHMENT_ERROR);
        }
        return policyAttachmentPos;
    }

    /**
     * 查询保单附件列表
     *
     * @param policyId            保单ID
     * @param attachmentTypeCodes 保单附件类形编码集
     * @return list
     */
    @Override
    public List<PolicyAttachmentPo> getApplyAttachmentList(String policyId, List<String> attachmentTypeCodes) {
        List<PolicyAttachmentPo> policyAttachmentPos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(POLICY_ATTACHMENT.POLICY_ID.eq(policyId));
            if (AssertUtils.isNotEmpty(attachmentTypeCodes)) {
                conditions.add(POLICY_ATTACHMENT.ATTACHMENT_TYPE_CODE.in(attachmentTypeCodes));
            }
            policyAttachmentPos = this.getDslContext()
                    .select(POLICY_ATTACHMENT.fields())
                    .from(POLICY_ATTACHMENT)
                    .where(conditions)
                    .fetchInto(PolicyAttachmentPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ATTACHMENT_ERROR);
        }
        return policyAttachmentPos;
    }

    /**
     * 查询保单特别约定
     *
     * @param policyId 保单ID
     * @return PolicySpecialContractPo
     */
    @Override
    public List<PolicySpecialContractPo> getPolicySpecialContract(String policyId) {
        List<PolicySpecialContractPo> policySpecialContractPos;
        try {
            policySpecialContractPos = this.getDslContext()
                    .selectFrom(POLICY_SPECIAL_CONTRACT)
                    .where(POLICY_SPECIAL_CONTRACT.POLICY_ID.eq(policyId))
                    .and(POLICY_SPECIAL_CONTRACT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .fetchInto(PolicySpecialContractPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_SPECIAL_CONTRACT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_SPECIAL_CONTRACT_ERROR);
        }
        return policySpecialContractPos;
    }

    @Override
    public PolicyReceiptInfoPo getPolicyReceiptInfo(String policyId) {
        PolicyReceiptInfoPo policyReceiptInfoPo;
        try {
            policyReceiptInfoPo = this.getDslContext()
                    .selectFrom(POLICY_RECEIPT_INFO)
                    .where(POLICY_RECEIPT_INFO.POLICY_ID.eq(policyId)).and(POLICY_RECEIPT_INFO.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())).limit(1)
                    .fetchOneInto(PolicyReceiptInfoPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_RECEIPT_INFO_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_RECEIPT_INFO_ERROR);
        }
        return policyReceiptInfoPo;
    }

    /**
     * 查询保单回执
     *
     * @param policyIds 保单ID
     * @return list
     */
    @Override
    public List<PolicyReceiptInfoPo> listPolicyReceiptInfo(List<String> policyIds) {
        return this.getDslContext()
                .selectFrom(POLICY_RECEIPT_INFO)
                .where(POLICY_RECEIPT_INFO.POLICY_ID.in(policyIds))
                .and(POLICY_RECEIPT_INFO.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(PolicyReceiptInfoPo.class);
    }

    @Override
    public List<PolicyInsuredBo> getPolicyInsuredList(String policyId, String keyword) {
        List<PolicyInsuredBo> policyInsuredBos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(POLICY_INSURED.POLICY_ID.eq(policyId));
            conditions.add(POLICY_INSURED.VALID_FLAG.eq(effective.name()));
            if (AssertUtils.isNotEmpty(keyword)) {
                conditions.add(POLICY_INSURED.NAME.like("%" + keyword + "%"));
            }
            policyInsuredBos = this.getDslContext()
                    .select(POLICY.MATURITY_DATE)
                    .select(POLICY_INSURED.fields())
                    .select(POLICY_INSURED_EXTEND.INSURED_STATUS)
                    .select(POLICY_INSURED_EXTEND.ADD_DATE)
                    .select(POLICY_INSURED_EXTEND.EFFECTIVE_DATE)
                    .select(POLICY_INSURED_EXTEND.INVALID_DATE)
                    .from(POLICY_INSURED)
                    .leftJoin(POLICY).on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID))
                    .leftJoin(POLICY_INSURED_EXTEND).on(POLICY_INSURED.POLICY_ID.eq(POLICY_INSURED_EXTEND.POLICY_ID)
                            .and(POLICY_INSURED.INSURED_ID.eq(POLICY_INSURED_EXTEND.INSURED_ID)).and(POLICY_INSURED_EXTEND.VALID_FLAG.eq(effective.name())))
                    .where(conditions)
                    .orderBy(POLICY_INSURED.CREATED_DATE.asc(), POLICY_INSURED.NAME.asc())
                    .fetchInto(PolicyInsuredBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_ERROR);
        }
        return policyInsuredBos;
    }

    @Override
    public List<PolicyInsuredBo> getPolicyInsuredListForPb(String policyId, List<String> insuredIds) {
        List<PolicyInsuredBo> policyInsuredBos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(POLICY_INSURED.POLICY_ID.eq(policyId));
            //conditions.add(POLICY_INSURED.VALID_FLAG.eq(effective.name()));
            if (AssertUtils.isNotEmpty(insuredIds)) {
                conditions.add(POLICY_INSURED.INSURED_ID.in(insuredIds));
            }
            policyInsuredBos = this.getDslContext()
                    .select(POLICY_INSURED.fields())
                    .select(POLICY_INSURED_EXTEND.INSURED_STATUS)
                    .select(POLICY_INSURED_EXTEND.ADD_DATE)
                    .select(POLICY_INSURED_EXTEND.EFFECTIVE_DATE)
                    .select(POLICY_INSURED_EXTEND.INVALID_DATE)
                    .from(POLICY_INSURED)
                    .leftJoin(POLICY_INSURED_EXTEND).on(POLICY_INSURED.POLICY_ID.eq(POLICY_INSURED_EXTEND.POLICY_ID)
                            .and(POLICY_INSURED.INSURED_ID.eq(POLICY_INSURED_EXTEND.INSURED_ID)).and(POLICY_INSURED_EXTEND.VALID_FLAG.eq(effective.name())))
                    .where(conditions)
                    .orderBy(POLICY_INSURED.CREATED_DATE.asc(), POLICY_INSURED.NAME.asc())
                    .fetchInto(PolicyInsuredBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_ERROR);
        }
        return policyInsuredBos;
    }

    /**
     * 批量查询被保人
     * @param policyIds 保单ID
     * @return
     */
    @Override
    public List<PolicyInsuredPo> listPolicyInsured(List<String> policyIds) {
        return this.getDslContext()
                .selectFrom(POLICY_INSURED)
                .where(POLICY_INSURED.POLICY_ID.in(policyIds))
                .and(POLICY_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(PolicyInsuredPo.class);
    }

    /**
     * 查询投保单被保人列表
     *
     * @param policyId  保单ID
     * @param versionNo 版本号
     * @return list
     */
    @Override
    public List<PolicyInsuredBo> getPolicyInsuredByVersionNo(String policyId, String versionNo) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.POLICY_ID.eq(policyId));
        if (AssertUtils.isNotEmpty(versionNo)) {
            conditions.add(POLICY.VERSION_NO.eq(versionNo));
        }
        return this.getDslContext()
                .select(POLICY_INSURED.fields())
                .from(POLICY)
                .leftJoin(POLICY_INSURED).on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID))
                .where(conditions)
                .orderBy(POLICY_INSURED.CREATED_DATE.asc(), POLICY_INSURED.NAME.asc())
                .fetchInto(PolicyInsuredBo.class);
    }

    @Override
    public List<PolicyCoveragePo> getPolicyCoverageList(String policyIdOrPolicyNo, String insuredId) {
        List<PolicyCoveragePo> applyCoveragePos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(POLICY_COVERAGE.POLICY_ID.eq(policyIdOrPolicyNo).or(POLICY_COVERAGE.POLICY_NO.eq(policyIdOrPolicyNo)));
            if (AssertUtils.isNotEmpty(insuredId)) {
                conditions.add(POLICY_COVERAGE.INSURED_ID.eq(insuredId));
            } else {
                conditions.add(POLICY_COVERAGE.INSURED_ID.isNotNull());
            }
            applyCoveragePos = this.getDslContext()
                    .select(POLICY_COVERAGE.fields())
                    .from(POLICY_COVERAGE)
                    .where(conditions)
                    .and(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .orderBy(POLICY_COVERAGE.PRIMARY_FLAG.sortAsc(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                    .fetchInto(PolicyCoveragePo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_ERROR);
        }
        return applyCoveragePos;
    }

    /**
     * 查询团险保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoveragePo> listGroupPolicyCoverage(String policyId) {
        return this.getDslContext()
                .select(POLICY_COVERAGE.fields())
                .from(POLICY_COVERAGE)
                .where(POLICY_COVERAGE.POLICY_ID.eq(policyId))
                .and(POLICY_COVERAGE.INSURED_ID.isNull())
                .and(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .orderBy(POLICY_COVERAGE.PRIMARY_FLAG.sortAsc(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                .fetchInto(PolicyCoveragePo.class);
    }

    @Override
    public List<PolicyCoverageLevelBo> listGroupPolicyPaymentCoverage(String policyId) {
        /*select pcl.*,pc.insured_id
         from policy_payment
         left join policy_coverage_payment pcp on policy_payment.policy_payment_id = pcp.policy_payment_id
         left join policy_coverage pc on pcp.coverage_id = pc.coverage_id
         left join policy_coverage_level pcl on pcl.coverage_id = pc.coverage_id
         where policy_payment.policy_id = '2380a963f3ed4f3faf7b9720497da358'
         and payment_business_type = 'BUSINESS_TYPE_NEW_CONTRACT'*/
        return this.getDslContext()
                .select(POLICY_COVERAGE_LEVEL.fields())
                .select(POLICY_COVERAGE.INSURED_ID)
                .from(POLICY_PAYMENT)
                .leftJoin(POLICY_COVERAGE_PAYMENT).on(POLICY_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID))
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE_PAYMENT.COVERAGE_ID.eq(POLICY_COVERAGE.COVERAGE_ID))
                .leftJoin(POLICY_COVERAGE_LEVEL).on(POLICY_COVERAGE_LEVEL.COVERAGE_ID.eq(POLICY_COVERAGE.COVERAGE_ID))
                .where(POLICY_PAYMENT.POLICY_ID.eq(policyId))
                .and(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE.eq("BUSINESS_TYPE_NEW_CONTRACT"))
                .fetchInto(PolicyCoverageLevelBo.class);
    }

    /**
     * 查询团险保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoveragePo> listAllGroupPolicyCoverage(String policyId) {
        return this.getDslContext()
                .select(POLICY_COVERAGE.fields())
                .from(POLICY_COVERAGE)
                .where(POLICY_COVERAGE.POLICY_ID.eq(policyId))
                .and(POLICY_COVERAGE.INSURED_ID.isNull())
                .orderBy(POLICY_COVERAGE.PRIMARY_FLAG.sortAsc(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                .fetchInto(PolicyCoveragePo.class);
    }

    @Override
    public List<PolicyCoverageBo> getPolicyCoverageList(String policyId) {
        List<PolicyCoverageBo> policyCoverageBos = null;
        try {
            SelectConditionStep selectConditionStep = this.getDslContext()
                    .select(POLICY_COVERAGE.fields())
                    .select(POLICY_COVERAGE_PREMIUM.fields())
                    .from(POLICY_COVERAGE)
                    .leftJoin(POLICY_COVERAGE_PREMIUM).on(POLICY_COVERAGE.COVERAGE_ID.eq(POLICY_COVERAGE_PREMIUM.COVERAGE_ID))
                    .and(POLICY_COVERAGE_PREMIUM.VALID_FLAG.eq(effective.name()))
                    .where(POLICY_COVERAGE.POLICY_ID.eq(policyId))
                    .and(POLICY_COVERAGE.VALID_FLAG.eq(effective.name()));
            policyCoverageBos = selectConditionStep.fetch().map(record -> {
                PolicyCoveragePremiumBo policyCoveragePremiumBo = BasePojo.getInstance(PolicyCoveragePremiumBo.class, record.into(PolicyCoveragePremiumRecord.class));
                PolicyCoverageBo policyCoverageBo = BasePojo.getInstance(PolicyCoverageBo.class, record.into(PolicyCoverageRecord.class));
                Optional.ofNullable(policyCoveragePremiumBo.getPolicyCoveragePremiumId()).ifPresent(r -> policyCoverageBo.setPolicyCoveragePremium(policyCoveragePremiumBo));
                return policyCoverageBo;
            });
            return policyCoverageBos;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_ERROR);
        }
    }

    @Override
    public List<PolicyCoverageBo> getPolicyCoverageListForPb(String policyId) {
        List<PolicyCoverageBo> policyCoverageBos = null;
        try {
            SelectConditionStep selectConditionStep = this.getDslContext()
                    .select(POLICY_COVERAGE.fields())
                    .select(POLICY_COVERAGE_PREMIUM.fields())
                    .from(POLICY_COVERAGE)
                    .leftJoin(POLICY_COVERAGE_PREMIUM).on(POLICY_COVERAGE.COVERAGE_ID.eq(POLICY_COVERAGE_PREMIUM.COVERAGE_ID))
                    //.and(POLICY_COVERAGE_PREMIUM.VALID_FLAG.eq(effective.name()))
                    .where(POLICY_COVERAGE.POLICY_ID.eq(policyId));
                    //.and(POLICY_COVERAGE.VALID_FLAG.eq(effective.name()));
            policyCoverageBos = selectConditionStep.fetch().map(record -> {
                PolicyCoveragePremiumBo policyCoveragePremiumBo = BasePojo.getInstance(PolicyCoveragePremiumBo.class, record.into(PolicyCoveragePremiumRecord.class));
                PolicyCoverageBo policyCoverageBo = BasePojo.getInstance(PolicyCoverageBo.class, record.into(PolicyCoverageRecord.class));
                Optional.ofNullable(policyCoveragePremiumBo.getPolicyCoveragePremiumId()).ifPresent(r -> policyCoverageBo.setPolicyCoveragePremium(policyCoveragePremiumBo));
                return policyCoverageBo;
            });
            return policyCoverageBos;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_ERROR);
        }
    }

    @Override
    public PolicyCoveragePaymentBo queryPolicyCoveragePayment(String policyPaymentId, String policyId, String coverageId) {
        PolicyCoveragePaymentBo policyCoveragePaymentBo;
        try {
            SelectConditionStep selectConditionStep = this.getDslContext()
                    .select(POLICY_COVERAGE_PAYMENT.fields())
                    .from(POLICY_COVERAGE_PAYMENT)
                    .where(POLICY_COVERAGE_PAYMENT.POLICY_ID.eq(policyId)
                            .and(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID.eq(policyPaymentId))
                            .and(POLICY_COVERAGE_PAYMENT.COVERAGE_ID.eq(coverageId))
                            .and(POLICY_COVERAGE_PAYMENT.VALID_FLAG.eq(effective.name())));
            System.out.println(selectConditionStep.toString());
            policyCoveragePaymentBo = (PolicyCoveragePaymentBo) selectConditionStep.fetchOneInto(PolicyCoveragePaymentBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PREMIUM_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PREMIUM_ERROR);
        }
        return policyCoveragePaymentBo;
    }

    @Override
    public PolicyPremiumBo getPolicyPremium(String policyId) {
        PolicyPremiumBo policyPremiumBo;
        try {
            policyPremiumBo = this.getDslContext()
                    .select(POLICY_PREMIUM.fields())
                    .from(POLICY_PREMIUM)
                    .where(POLICY_PREMIUM.POLICY_ID.eq(policyId).and(POLICY_PREMIUM.VALID_FLAG.eq(effective.name())))
                    .fetchOneInto(PolicyPremiumBo.class);
            /*查询保单付费信息*/
            PolicyPaymentBo policyPaymentBo = this.getNewPolicyPayment(policyId);
            policyPremiumBo.setPolicyPayment(policyPaymentBo);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PREMIUM_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PREMIUM_ERROR);
        }
        return policyPremiumBo;
    }

    @Override
    public PolicyPremiumBo getPolicyPremiumExtendPayment(String policyId) {
        PolicyPremiumBo policyPremiumBo;
        try {
            policyPremiumBo = this.getDslContext()
                    .select(POLICY_PREMIUM.fields())
                    .from(POLICY_PREMIUM)
                    .where(POLICY_PREMIUM.POLICY_ID.eq(policyId).and(POLICY_PREMIUM.VALID_FLAG.eq(effective.name())))
                    .fetchOneInto(PolicyPremiumBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PREMIUM_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PREMIUM_ERROR);
        }
        return policyPremiumBo;
    }

    @Override
    public List<PolicyAddPremiumPo> getPolicyAddPremium(String policyId) {
        List<PolicyAddPremiumPo> policyAddPremiumPos;
        try {
            policyAddPremiumPos = this.getDslContext()
                    .select(POLICY_ADD_PREMIUM.fields())
                    .from(POLICY_ADD_PREMIUM)
                    .where(POLICY_ADD_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(POLICY_ADD_PREMIUM.POLICY_ID.eq(policyId))
                    .fetchInto(PolicyAddPremiumPo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ADD_PREMIUM_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ADD_PREMIUM_ERROR);
        }
        return policyAddPremiumPos;
    }

    @Override
    public PolicyInsuredCollectPo getPolicyInsuredCollect(String policyId) {
        PolicyInsuredCollectPo policyInsuredCollectPo;
        try {
            policyInsuredCollectPo = this.getDslContext()
                    .select(POLICY_INSURED_COLLECT.fields())
                    .from(POLICY_INSURED_COLLECT)
                    .where(POLICY_INSURED_COLLECT.POLICY_ID.eq(policyId))
                    .and(POLICY_INSURED_COLLECT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .fetchOneInto(PolicyInsuredCollectPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_COLLECT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_COLLECT_ERROR);
        }
        return policyInsuredCollectPo;
    }

    /**
     * 根据投保单ID查询保单信息
     *
     * @param applyId 投保单ID
     * @return PolicyPo
     */
    @Override
    public PolicyPo getPolicyByApplyId(String applyId) {
        PolicyPo policyPo;
        try {
            policyPo = this.getDslContext()
                    .select(POLICY.fields())
                    .from(POLICY)
                    .where(POLICY.APPLY_ID.eq(applyId).and(POLICY.VALID_FLAG.eq(effective.name())))
                    .fetchOneInto(PolicyPo.class);
            return policyPo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
        }
    }

    /**
     * 根据投保单状态查询保单信息
     *
     * @param policyStatus 投保单ID
     * @return PolicyPo
     */
    @Override
    public List<PolicyPo> getPolicyByPolicyStatus(String policyStatus) {
        List<PolicyPo> policyPos;
        try {
            policyPos = this.getDslContext()
                    .select(POLICY.fields())
                    .from(POLICY)
                    .where(POLICY.POLICY_STATUS.eq(policyStatus))
                    .fetchInto(PolicyPo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
        }
        return policyPos;
    }

    /**
     * 根据保单号查询保单信息
     *
     * @param policyNo 保单号
     * @return
     */
    @Override
    public PolicyPo getPolicyByPolicyNo(String policyNo) {
        PolicyPo policyPo;
        try {
            policyPo = this.getDslContext()
                    .select(POLICY.fields())
                    .from(POLICY)
                    .where(POLICY.POLICY_NO.eq(policyNo).and(POLICY.VALID_FLAG.eq(effective.name())))
                    .fetchOneInto(PolicyPo.class);
            return policyPo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
        }
    }

    @Override
    public PolicyPaymentPo getPolicyPayment(String policyId) {
        PolicyPaymentPo policyPaymentPo;
        try {
            policyPaymentPo = this.getDslContext()
                    .selectFrom(POLICY_PAYMENT)
                    .where(POLICY_PAYMENT.POLICY_ID.eq(policyId).and(POLICY_PAYMENT.VALID_FLAG.eq(effective.name())))
                    .orderBy(POLICY_PAYMENT.CREATED_DATE.desc()).limit(1)
                    .fetchOneInto(PolicyPaymentPo.class);
            return policyPaymentPo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR);
        }
    }

    @Override
    public PolicyPaymentBo getNewPolicyPayment(String policyId) {
        PolicyPaymentBo policyPaymentBo;
        try {
            policyPaymentBo = this.getDslContext()
                    .selectFrom(POLICY_PAYMENT)
                    .where(POLICY_PAYMENT.POLICY_ID.eq(policyId).and(POLICY_PAYMENT.VALID_FLAG.eq(effective.name())))
                    .orderBy(POLICY_PAYMENT.RECEIVABLE_DATE.desc(), POLICY_PAYMENT.BIZ_DATE.asc()).limit(1)
                    .fetchOneInto(PolicyPaymentBo.class);
            return policyPaymentBo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR);
        }
    }

    @Override
    public List<PolicyPaymentBo> getListPolicyPayment(String policyId, String paymentStatus) {
        List<PolicyPaymentBo> policyPaymentBos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(POLICY_PAYMENT.POLICY_ID.eq(policyId));
            conditions.add(POLICY_PAYMENT.VALID_FLAG.eq(effective.name()));
            if (AssertUtils.isNotEmpty(paymentStatus)) {
                conditions.add(POLICY_PAYMENT.PAYMENT_STATUS_CODE.eq(paymentStatus));
            }
            policyPaymentBos = this.getDslContext()
                    .selectFrom(POLICY_PAYMENT)
                    .where(conditions)
                    .orderBy(POLICY_PAYMENT.RECEIVABLE_DATE.desc(), POLICY_PAYMENT.BIZ_DATE.asc())
                    .fetchInto(PolicyPaymentBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR);
        }
        return policyPaymentBos;
    }

    @Override
    public List<PolicyPaymentBo> getListPayPolicyPayment(String policyId) {
        List<PolicyPaymentBo> policyPaymentBos;
        try {
            policyPaymentBos = this.getDslContext()
                    .selectFrom(POLICY_PAYMENT)
                    .where(POLICY_PAYMENT.POLICY_ID.eq(policyId).and(POLICY_PAYMENT.PAYMENT_STATUS_CODE.eq(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name())).and(POLICY_PAYMENT.VALID_FLAG.eq(effective.name())))
                    .orderBy(POLICY_PAYMENT.CREATED_DATE.desc())
                    .fetchInto(PolicyPaymentBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR);
        }
        return policyPaymentBos;
    }

    @Override
    public List<PolicyPaymentBo> getListPayPolicyPayment(String policyId, long endTime, List<String> paymentBusinessType) {
        List<Condition> conditions = new ArrayList<>();
        List<String> paymentStatusList = Arrays.asList(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name(), PolicyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name());
        conditions.add(POLICY_PAYMENT.POLICY_ID.eq(policyId));
        conditions.add(POLICY_PAYMENT.PAYMENT_STATUS_CODE.in(paymentStatusList));
        conditions.add(POLICY_PAYMENT.VALID_FLAG.eq(effective.name()));
        conditions.add(POLICY_PAYMENT.RECEIVABLE_DATE.le(DateUtils.timeToTimeTop(endTime)));
        if (AssertUtils.isNotEmpty(paymentBusinessType)) {
            conditions.add(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE.in(paymentBusinessType));
        }
        conditions.add(POLICY_PAYMENT.RECEIVABLE_DATE.le(DateUtils.timeToTimeTop(endTime)));
        return this.getDslContext()
                .selectFrom(POLICY_PAYMENT)
                .where(conditions)
                .orderBy(POLICY_PAYMENT.RECEIVABLE_DATE.desc())
                .fetchInto(PolicyPaymentBo.class);
    }

    @Override
    public List<PolicyPaymentBo> listPolicyPayment(List<String> policyPaymentIds) {
        return this.getDslContext()
                .selectFrom(POLICY_PAYMENT)
                .where(POLICY_PAYMENT.POLICY_PAYMENT_ID.in(policyPaymentIds)
                        .and(POLICY_PAYMENT.VALID_FLAG.eq(effective.name())))
                .orderBy(POLICY_PAYMENT.CREATED_DATE.desc())
                .fetchInto(PolicyPaymentBo.class);
    }

    @Override
    public List<PolicyPo> getPolicyByAgentId(List<String> agentIds) {
        List<PolicyPo> policyPos;
        try {
            policyPos = this.getDslContext()
                    .select(POLICY.fields())
                    .from(POLICY)
                    .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                    .where(POLICY_AGENT.AGENT_ID.in(agentIds)).and(POLICY.VALID_FLAG.eq(effective.name()))
                    .fetchInto(PolicyPo.class);
            return policyPos;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
        }
    }

    @Override
    public List<PolicyAgentHistoryPo> queryListPolicyAgentHistoryPo(String policyId) {
        List<PolicyAgentHistoryPo> policyAgentHistoryPos;
        try {
            policyAgentHistoryPos = this.getDslContext()
                    .select(POLICY_AGENT_HISTORY.fields())
                    .from(POLICY_AGENT_HISTORY)
                    .leftJoin(POLICY).on(POLICY.POLICY_ID.eq(POLICY_AGENT_HISTORY.POLICY_ID))
                    .where(POLICY.POLICY_ID.eq(policyId)).and(POLICY.VALID_FLAG.eq(effective.name()))
                    .fetchInto(PolicyAgentHistoryPo.class);
            return policyAgentHistoryPos;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_AGENT_HISTORY_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_AGENT_HISTORY_ERROR);
        }
    }

    @Override
    public PolicyAgentHistoryPo queryOnePolicyAgentHistoryPo(String policyId) {
        PolicyAgentHistoryPo policyAgentHistoryPo;
        try {
            policyAgentHistoryPo = this.getDslContext()
                    .select(POLICY_AGENT_HISTORY.fields())
                    .from(POLICY_AGENT_HISTORY)
                    .leftJoin(POLICY).on(POLICY.POLICY_ID.eq(POLICY_AGENT_HISTORY.POLICY_ID))
                    .where(POLICY.POLICY_ID.eq(policyId)).and(POLICY.VALID_FLAG.eq(effective.name()))
                    .orderBy(POLICY_AGENT_HISTORY.CREATED_DATE.desc()).limit(1)
                    .fetchOneInto(PolicyAgentHistoryPo.class);
            return policyAgentHistoryPo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_AGENT_HISTORY_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_AGENT_HISTORY_ERROR);
        }
    }

    @Override
    public PolicyRenewalGrabPo queryPolicyRenewalGrab(String policyId, Long receivableDate) {
        return this.getDslContext().selectFrom(POLICY_RENEWAL_GRAB)
                .where(POLICY_RENEWAL_GRAB.POLICY_ID.eq(policyId)
//                        .and(POLICY_RENEWAL_GRAB.RECEIVABLE_DATE.eq(receivableDate))
                                // todo 由于业务规则调整，历史数据应收日期可能不完全匹配，故暂时改为区间过滤，后期历史数据都处理完后可改回来
                                .and(POLICY_RENEWAL_GRAB.RECEIVABLE_DATE.ge(DateUtils.timeToTimeLow(DateUtils.addStringDayRT(receivableDate, -1))))
                                .and(POLICY_RENEWAL_GRAB.RECEIVABLE_DATE.le(DateUtils.timeToTimeTop(DateUtils.addStringDayRT(receivableDate, 1))))
                )
                .fetchOneInto(PolicyRenewalGrabPo.class);
    }

    /**
     * 根据保单ID查询抢单记录
     *
     * @param policyId 保单ID
     * @return list
     */
    @Override
    public List<PolicyRenewalGrabPo> listPolicyRenewalGrab(String policyId) {
        return this.getDslContext().selectFrom(POLICY_RENEWAL_GRAB)
                .where(POLICY_RENEWAL_GRAB.POLICY_ID.eq(policyId))
                .fetchInto(PolicyRenewalGrabPo.class);
    }

    /**
     * 根据保单ID查询抢单记录
     *
     * @param policyIds 保单ID集
     * @return list
     */
    @Override
    public List<PolicyRenewalGrabPo> listPolicyRenewalGrab(List<String> policyIds) {
        return this.getDslContext().selectFrom(POLICY_RENEWAL_GRAB)
                .where(POLICY_RENEWAL_GRAB.POLICY_ID.in(policyIds))
                .orderBy(POLICY_RENEWAL_GRAB.POLICY_ID, POLICY_RENEWAL_GRAB.RECEIVABLE_DATE.desc())
                .fetchInto(PolicyRenewalGrabPo.class);
    }

    @Override
    public List<PolicyCoveragePaymentBo> queryPolicyCoveragePaymentBo(String policyPaymentId) {
        List<PolicyCoveragePaymentBo> policyCoveragePaymentBos;
        try {
            policyCoveragePaymentBos = this.getDslContext()
                    .select(POLICY_COVERAGE_PAYMENT.fields())
                    .select(POLICY_PAYMENT.RECEIVABLE_DATE)
                    .from(POLICY_COVERAGE_PAYMENT)
                    .leftJoin(POLICY_PAYMENT).on(POLICY_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID))
                    .where(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID.eq(policyPaymentId).and(POLICY_COVERAGE_PAYMENT.VALID_FLAG.eq(effective.name())))
                    .fetchInto(PolicyCoveragePaymentBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_PAYMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_PAYMENT_ERROR);
        }
        return policyCoveragePaymentBos;
    }

    @Override
    public List<PolicyCoveragePaymentBo> queryPolicyCoveragePaymentBos(List<String> policyPaymentIds) {
        return this.getDslContext()
                .select(POLICY_COVERAGE_PAYMENT.fields())
                .select(POLICY_PAYMENT.RECEIVABLE_DATE)
                .from(POLICY_COVERAGE_PAYMENT)
                .leftJoin(POLICY_PAYMENT).on(POLICY_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID))
                .where(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID.in(policyPaymentIds).and(POLICY_COVERAGE_PAYMENT.VALID_FLAG.eq(effective.name())))
                .fetchInto(PolicyCoveragePaymentBo.class);
    }

    @Override
    public PolicyPaymentBo queryFirstPolicyPayment(String policyId) {
        PolicyPaymentBo policyPaymentBo;
        try {
            policyPaymentBo = this.getDslContext()
                    .selectFrom(POLICY_PAYMENT)
                    .where(POLICY_PAYMENT.POLICY_ID.eq(policyId).and(POLICY_PAYMENT.VALID_FLAG.eq(effective.name())))
                    .orderBy(POLICY_PAYMENT.CREATED_DATE.asc()).limit(1)
                    .fetchOneInto(PolicyPaymentBo.class);
            return policyPaymentBo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR);
        }
    }

    @Override
    public List<PolicyApplicantCoverageBo> queryPolicyApplicantCoverageBo(PolicyListVo policyListVo) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .selectDistinct(POLICY.fields())
                .select(POLICY_APPLICANT.NAME)
                .select(POLICY_COVERAGE.PRODUCT_CODE, POLICY_COVERAGE.PRODUCT_NAME,
                        POLICY_COVERAGE.PRODUCT_ID, POLICY_COVERAGE.PRIMARY_FLAG)
                .select(POLICY.POLICY_ID.countOver().as("√"))
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                .from(POLICY).leftJoin(POLICY_APPLICANT).on(POLICY.APPLICANT_ID.eq(POLICY_APPLICANT.APPLICANT_ID))
                .leftJoin(POLICY_COVERAGE).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID));
        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        // 只查个险
        conditionList.add(POLICY.POLICY_TYPE.eq(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL.name()));
        conditionList.add(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        if (AssertUtils.isNotNull(policyListVo)) {
            if (AssertUtils.isNotEmpty(policyListVo.getKeyword())) {
                conditionList.add(POLICY.POLICY_NO.like("%" + policyListVo.getKeyword() + "%").or(POLICY_APPLICANT.NAME.like("%" + policyListVo.getKeyword() + "%")));
            }
        }
        //分页查询
        selectJoinStep.offset(policyListVo.getOffset()).limit(policyListVo.getPageSize());
        selectJoinStep.orderBy(POLICY.POLICY_NO);
        selectJoinStep.where(conditionList);
        System.out.print(selectJoinStep.toString());
        return selectJoinStep.fetchInto(PolicyApplicantCoverageBo.class);
    }

    @Override
    public List<PolicyEndorseBo> queryPolicyByCustomerId(List<String> customerAgentIds) {
        List<PolicyEndorseBo> policyEndorseBos = null;
        try {

            SelectJoinStep selectOnStep = this.getDslContext()
                    .selectDistinct(POLICY.fields())
                    .select(POLICY_APPLICANT.fields())
                    .select(POLICY_AGENT.fields())
                    .from(POLICY)
                    // 投保人信息表
                    .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.APPLICANT_ID.eq(POLICY.APPLICANT_ID))
                    .innerJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                    .leftJoin(POLICY_INSURED).on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID));

            List<Condition> conditions = new ArrayList<Condition>();

            conditions.add(POLICY.VALID_FLAG.eq(effective.name()));
            conditions.add((POLICY_APPLICANT.CUSTOMER_ID.in(customerAgentIds).and(POLICY_APPLICANT.VALID_FLAG.eq(effective.name()))
                    .or(POLICY_INSURED.CUSTOMER_ID.in(customerAgentIds).and(POLICY_INSURED.VALID_FLAG.eq(effective.name())))));
            //条件
            selectOnStep.where(conditions);
            //打印sql
            System.err.println(selectOnStep.toString());
            policyEndorseBos = policyBaseEndorseTransfer.fetchEndorseData(policyEndorseBos, selectOnStep);
            if (AssertUtils.isNotEmpty(policyEndorseBos)) {
                policyEndorseBos.forEach(policyEndorseBo -> {
                    policyEndorseBo.setPolicyCoverage(this.queryPolicyMainCoveragePo(policyEndorseBo.getPolicyId()));
                });
            }
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_CUSTOMER_RELATION_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_CUSTOMER_RELATION_ERROR);
        }
        return policyEndorseBos;
    }

    @Override
    public List<PolicyApplicantBo> queryPolicyByCustomerAgentIds(List<String> customerAgentIds) {
        List<PolicyApplicantBo> PolicyApplicantBos = null;
        try {

            SelectJoinStep selectOnStep = this.getDslContext()
                    .select(POLICY_APPLICANT.fields())
                    .from(POLICY_APPLICANT);

            List<Condition> conditions = new ArrayList<Condition>();
            conditions.add((POLICY_APPLICANT.CUSTOMER_ID.in(customerAgentIds).and(POLICY_APPLICANT.VALID_FLAG.eq(effective.name()))));
            //条件
            selectOnStep.where(conditions);
            //打印sql
            System.err.println(selectOnStep.toString());
            PolicyApplicantBos = selectOnStep.fetchInto(PolicyApplicantBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_CUSTOMER_RELATION_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_CUSTOMER_RELATION_ERROR);
        }
        return PolicyApplicantBos;
    }

    @Override
    public List<PolicyEndorseBo> queryPolicyByApplicantCustomerId(List<String> customerAgentIds) {
        List<PolicyEndorseBo> policyEndorseBos = null;
        try {
            SelectJoinStep selectOnStep = this.getDslContext()
                    .selectDistinct(POLICY.fields())
                    .select(POLICY_APPLICANT.fields())
                    .select(POLICY_AGENT.fields())
                    .from(POLICY)
                    // 投保人信息表
                    .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.APPLICANT_ID.eq(POLICY.APPLICANT_ID))
                    .innerJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID));

            List<Condition> conditions = new ArrayList<Condition>();

            conditions.add(POLICY.VALID_FLAG.eq(effective.name()));
            conditions.add((POLICY_APPLICANT.CUSTOMER_ID.in(customerAgentIds).and(POLICY_APPLICANT.VALID_FLAG.eq(effective.name()))));
            //条件
            selectOnStep.where(conditions);
            //打印sql
            System.err.println(selectOnStep.toString());


            policyEndorseBos = policyBaseEndorseTransfer.fetchEndorseData(policyEndorseBos, selectOnStep);
            if (AssertUtils.isNotEmpty(policyEndorseBos)) {
                policyEndorseBos.forEach(policyEndorseBo -> {
                    policyEndorseBo.setPolicyCoverage(this.queryPolicyMainCoveragePo(policyEndorseBo.getPolicyId()));
                });
            }
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_CUSTOMER_APPLICANT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_CUSTOMER_APPLICANT_ERROR);
        }
        return policyEndorseBos;
    }

    @Override
    public PolicyEndorseInfoBo queryPolicyInfoByPolicyId(String policyId, String versionNo) {
        PolicyEndorseInfoBo policyEndorseInfoBo;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(POLICY.POLICY_ID.eq(policyId));
            if (AssertUtils.isNotEmpty(versionNo)) {
                conditions.add(POLICY.VERSION_NO.eq(versionNo));
            }
            conditions.add(POLICY.VALID_FLAG.eq(effective.name()));
            //查询保单基础信息
            policyEndorseInfoBo = this.getDslContext().select(POLICY.fields()).from(POLICY)
                    .where(conditions).fetchOneInto(PolicyEndorseInfoBo.class);

            if (!AssertUtils.isNotNull(policyEndorseInfoBo)) {
                return null;
            }

            /*查询保单所属代理人*/
            PolicyAgentPo policyAgentPo = getPolicyAgent(policyId);
            policyEndorseInfoBo.setPolicyAgent(policyAgentPo);

            /*查询保单险种*/
            List<PolicyCoveragePo> listPolicyCoveragePo = getPolicyCoverageList(policyId, null);
            List<PolicyCoverageBo> listPolicyCoverageBo = new ArrayList<>();
            listPolicyCoveragePo.forEach(policyCoveragePo -> {
                PolicyCoverageBo policyCoverageBo = new PolicyCoverageBo();
                ClazzUtils.copyPropertiesIgnoreNull(policyCoveragePo, policyCoverageBo);

                /*查询险种保费信息*/
                PolicyCoveragePremiumBo policyCoveragePremiumBo = queryOnePolicyCoveragePremium(policyCoveragePo.getPolicyId(), policyCoveragePo.getCoverageId());
                policyCoverageBo.setPolicyCoveragePremium(policyCoveragePremiumBo);

                listPolicyCoverageBo.add(policyCoverageBo);
            });

            policyEndorseInfoBo.setListPolicyCoverage(listPolicyCoverageBo);

            /*查询保单投保人*/
            PolicyApplicantPo policyApplicantPo = getPolicyApplicant(policyId);
            policyEndorseInfoBo.setPolicyApplicant(policyApplicantPo);

            // 查询保单缴费信息
            List<PolicyPaymentBo> listPolicyPayment = getListPolicyPayment(policyId, null);

            // 查询险种缴费信息
            listPolicyPayment.forEach(policyPaymentBo -> {
                List<PolicyCoveragePaymentBo> policyCoveragePaymentBos = queryPolicyCoveragePaymentBo(policyPaymentBo.getPolicyPaymentId());
                policyPaymentBo.setListPolicyCoveragePayment(policyCoveragePaymentBos);
            });
            policyEndorseInfoBo.setListPolicyPayment(listPolicyPayment);

        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_ERROR);
        }
        return policyEndorseInfoBo;
    }

    @Override
    public List<PolicyInsuredExtendPo> queryPolicyInsuredExtend(String policyId) {
        return this.getDslContext().select(POLICY_INSURED_EXTEND.fields()).from(POLICY_INSURED_EXTEND).where(POLICY_INSURED_EXTEND.POLICY_ID.eq(policyId).and(POLICY_INSURED_EXTEND.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchInto(PolicyInsuredExtendPo.class);
    }

    @Override
    public List<PolicyCoveragePaymentBo> queryPolicyCoveragePayment(String policyId, String coverageId) {
        return this.getDslContext().select(POLICY_COVERAGE_PAYMENT.fields()).from(POLICY_COVERAGE_PAYMENT)
                .where(POLICY_COVERAGE_PAYMENT.POLICY_ID.eq(policyId).and(POLICY_COVERAGE_PAYMENT.COVERAGE_ID.eq(coverageId))
                        .and(POLICY_COVERAGE_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchInto(PolicyCoveragePaymentBo.class);
    }

    @Override
    public PolicyCoveragePremiumBo queryOnePolicyCoveragePremium(String policyId, String coverageId) {
        return this.getDslContext().select(POLICY_COVERAGE_PREMIUM.fields()).from(POLICY_COVERAGE_PREMIUM)
                .where(POLICY_COVERAGE_PREMIUM.POLICY_ID.eq(policyId).and(POLICY_COVERAGE_PREMIUM.COVERAGE_ID.eq(coverageId))
                        .and(POLICY_COVERAGE_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchOneInto(PolicyCoveragePremiumBo.class);
    }

    @Override
    public List<PolicyCoverageDutyBo> queryPolicyCoverageDuty(String policyId, String coverageId) {
        return this.getDslContext().select(POLICY_COVERAGE_DUTY.fields()).from(POLICY_COVERAGE_DUTY)
                .where(POLICY_COVERAGE_DUTY.POLICY_ID.eq(policyId).and(POLICY_COVERAGE_DUTY.COVERAGE_ID.eq(coverageId))
                        .and(POLICY_COVERAGE_DUTY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchInto(PolicyCoverageDutyBo.class);
    }

    @Override
    public PolicyCoverageBonusBo queryOnePolicyCoverageBonus(String policyId, String coverageId) {
        return this.getDslContext().select(POLICY_COVERAGE_BONUS.fields()).from(POLICY_COVERAGE_BONUS)
                .where(POLICY_COVERAGE_BONUS.POLICY_ID.eq(policyId).and(POLICY_COVERAGE_BONUS.COVERAGE_ID.eq(coverageId))
                        .and(POLICY_COVERAGE_BONUS.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchOneInto(PolicyCoverageBonusBo.class);
    }

    @Override
    public PolicyInsuredCollectPo queryOnePolicyInsuredCollect(String policyId) {
        return this.getDslContext().select(POLICY_INSURED_COLLECT.fields()).from(POLICY_INSURED_COLLECT)
                .where(POLICY_INSURED_COLLECT.POLICY_ID.eq(policyId)
                        .and(POLICY_INSURED_COLLECT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchOneInto(PolicyInsuredCollectPo.class);
    }

    @Override
    public PolicyPayorInfoBo queryOnePolicyPayorInfo(String policyId) {
        return this.getDslContext().select(POLICY_PAYOR_INFO.fields()).from(POLICY_PAYOR_INFO)
                .where(POLICY_PAYOR_INFO.POLICY_ID.eq(policyId)
                        .and(POLICY_PAYOR_INFO.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchOneInto(PolicyPayorInfoBo.class);
    }

    /**
     * 根据保单ID查询指定支付状态缴费信息
     *
     * @param policyId      保单ID
     * @param paymentStatus 支付状态
     * @return
     */
    @Override
    public List<PolicyEndorsePaymentBo> listPolicyPayment(String policyId, String paymentStatus) {
        List<PolicyEndorsePaymentBo> listPolicyEndorsePaymentBo;
        try {
            listPolicyEndorsePaymentBo = this.getDslContext()
                    .select(POLICY_PAYMENT.fields())
                    .select(POLICY.fields())
                    .from(POLICY_PAYMENT)
                    .leftJoin(POLICY).on(POLICY.POLICY_ID.eq(POLICY_PAYMENT.POLICY_ID))
                    .where(POLICY_PAYMENT.POLICY_ID.eq(policyId))
                    .and(POLICY_PAYMENT.PAYMENT_STATUS_CODE.eq(paymentStatus))
                    .and(POLICY_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .orderBy(POLICY_PAYMENT.FREQUENCY)
                    .fetch().map(record -> {
                        PolicyEndorsePaymentBo policyEndorsePaymentBo = BasePojo.getInstance(PolicyEndorsePaymentBo.class, record.into(PolicyPaymentRecord.class));
                        PolicyPo policyPo = BasePojo.getInstance(PolicyPo.class, record.into(PolicyRecord.class));
                        policyEndorsePaymentBo.setPolicyNo(policyPo.getPolicyNo());
                        //查询险种缴费信息
                        List<PolicyCoveragePaymentBo> policyCoveragePaymentBoList = queryPolicyCoveragePaymentBo(policyEndorsePaymentBo.getPolicyPaymentId());
                        policyEndorsePaymentBo.setListPolicyCoveragePayment(policyCoveragePaymentBoList);
                        return policyEndorsePaymentBo;
                    });
            return listPolicyEndorsePaymentBo;
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_PAYMENT_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_PAYMENT_ERROR);
        }
    }

    /**
     * 团险被保人下的已缴费信息
     *
     * @param policyId      保单ID
     * @param customerId    客户ID
     * @param paymentStatus
     * @return PolicyEndorsePaymentBos
     */
    @Override
    public List<PolicyCoveragePaymentBo> queryGroupCustomerPayments(String policyId, String customerId, String paymentStatus) {
        return this.getDslContext()
                .selectDistinct(POLICY_PAYMENT.FREQUENCY)
                .select(POLICY_COVERAGE_PAYMENT.fields())
                .from(POLICY_INSURED)
                .innerJoin(POLICY_PAYMENT).on(POLICY_PAYMENT.POLICY_ID.eq(POLICY_INSURED.POLICY_ID))
                .innerJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY_INSURED.POLICY_ID).and(POLICY_COVERAGE.INSURED_ID.eq(POLICY_INSURED.INSURED_ID)))
                .leftJoin(POLICY_COVERAGE_HISTORY).on(POLICY_COVERAGE_HISTORY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID).and(POLICY_COVERAGE_HISTORY.INSURED_ID.eq(POLICY_INSURED.INSURED_ID)))
                .innerJoin(POLICY_COVERAGE_PAYMENT)
                .on(POLICY_COVERAGE_PAYMENT.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID)
                        .and(POLICY_COVERAGE_PAYMENT.COVERAGE_ID.eq(POLICY_COVERAGE.COVERAGE_ID).or(POLICY_COVERAGE_PAYMENT.COVERAGE_ID.eq(POLICY_COVERAGE_HISTORY.COVERAGE_ID)))
                        .and(POLICY_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID))
                )
                .where(POLICY_INSURED.POLICY_ID.eq(policyId).and(POLICY_INSURED.CUSTOMER_ID.eq(customerId)).and(POLICY_PAYMENT.PAYMENT_STATUS_CODE.eq(paymentStatus)))
                .fetchInto(PolicyCoveragePaymentBo.class);
    }

    @Override
    public List<PolicyPaymentBo> queryPolicyPayment(List<String> policyPaymentIds) {
        return this.getDslContext().selectFrom(POLICY_PAYMENT).where(POLICY_PAYMENT.POLICY_PAYMENT_ID.in(policyPaymentIds)).fetchInto(PolicyPaymentBo.class);
    }

    @Override
    public List<PolicyReportBo> queryPolicyReport(List<String> businessNo) {

        Field<Long> stringField = DSL.field("row_number() OVER (PARTITION BY {0} ORDER BY {1} DESC)", SQLDataType.BIGINT,
                POLICY_INSURED.POLICY_ID, POLICY_INSURED.CREATED_DATE);

        Table recordTable = this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY_COVERAGE.PRODUCT_ID, POLICY_COVERAGE.PRODUCT_NAME, POLICY_COVERAGE.PRODUCT_CODE, POLICY_COVERAGE.PRODUCT_LEVEL,
                        POLICY_COVERAGE.PREMIUM_FREQUENCY, POLICY_COVERAGE.PREMIUM_PERIOD, POLICY_COVERAGE.PREMIUM_PERIOD_UNIT, POLICY_COVERAGE.ORIGINAL_PREMIUM)
                .select(POLICY_PREMIUM.SPECIAL_DISCOUNT, POLICY_PREMIUM.PROMOTION_TYPE, POLICY_PREMIUM.DISCOUNT_TYPE, POLICY_PREMIUM.DISCOUNT_MODEL)
                .select(POLICY_APPLICANT.NAME.as("applicantName"), POLICY_APPLICANT.MOBILE.as("applicantMobile"),
                        POLICY_APPLICANT.CUSTOMER_ID, POLICY_APPLICANT.DELEGATE_CUSTOMER_ID, POLICY_APPLICANT.APPLICANT_TYPE)

                .select(stringField.as("rowNum"))
                .select(POLICY_INSURED.NAME.as("insuredName"), POLICY_INSURED.BIRTHDAY.as("insuredBirthday"))
                .select(POLICY_PAYMENT.POLICY_YEAR, POLICY_PAYMENT.FREQUENCY)
                .from(POLICY)
                .join(POLICY_COVERAGE).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID).and(POLICY_COVERAGE.INSURED_ID.isNotNull()))
                .join(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .join(POLICY_INSURED).on(POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID))
                .join(POLICY_PREMIUM).on(POLICY.POLICY_ID.eq(POLICY_PREMIUM.POLICY_ID))
                .join(POLICY_PAYMENT).on(POLICY.POLICY_ID.eq(POLICY_PAYMENT.POLICY_ID))
                .where(POLICY.POLICY_NO.in(businessNo).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                        .and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                                .and(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                                        .and(POLICY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                                                .and(POLICY_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                                                .and(POLICY_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))))).asTable();

        SelectConditionStep rowNum = this.getDslContext().select(recordTable.fields()).from(recordTable).where(recordTable.field("rowNum").eq(1));
        return rowNum.fetchInto(PolicyReportBo.class);
    }


    @Override
    public List<PolicyApplicantReportBo> queryPolicyApplicantReport(BasePageRequest basePageRequest, String startDate) {
        Field<Long> stringField = DSL.field("row_number() OVER (PARTITION BY {0} ORDER BY {1} DESC)", SQLDataType.BIGINT,
                POLICY_APPLICANT.CUSTOMER_ID, POLICY_APPLICANT.CREATED_DATE);

        //同步时间取更新时间，更新时间为空则取创建时间
        Field<Long> applicantCreateDate = POLICY_APPLICANT.UPDATED_DATE.nvl(POLICY_APPLICANT.CREATED_DATE).as("applicantCreateDate");

        Table recordTable = this.getDslContext()
                .select(POLICY_APPLICANT.fields())
                .select(POLICY_APPLICANT.APPLICANT_TYPE.as("customerType"))
                .select(applicantCreateDate)
                .select(stringField.as("rowNum"))
                .from(POLICY_APPLICANT)
                .asTable();

        SelectConditionStep rowNum = this.getDslContext().select(recordTable.fields()).from(recordTable).where(recordTable.field("rowNum").eq(1)
                .and(recordTable.field("applicantCreateDate").between(
                        DateUtils.stringToTime(startDate, DateUtils.FORMATE51)
                        , DateUtils.getCurrentTime())));
        rowNum.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        System.out.println(rowNum.toString());

        return rowNum.fetchInto(PolicyApplicantReportBo.class);
    }

    /**
     * 查询业务报表（被保人资料）
     *
     * @param basePageRequest
     * @param startDate
     */
    @Override
    public List<PolicyInsuredReportBo> queryPolicyInsuredsReport(BasePageRequest basePageRequest, String startDate) {
        Field<Long> stringField = DSL.field("row_number() OVER (PARTITION BY {0} ORDER BY {1} DESC)", SQLDataType.BIGINT,
                POLICY_INSURED.CUSTOMER_ID, POLICY_INSURED.CREATED_DATE);

        //同步时间取更新时间，更新时间为空则取创建时间
        Field<Long> applicantCreateDate = POLICY_INSURED.UPDATED_DATE.nvl(POLICY_INSURED.CREATED_DATE).as("insuredCreateDate");

        Table recordTable = this.getDslContext()
                .select(POLICY_INSURED.fields())
                .select(applicantCreateDate)
                .select(stringField.as("rowNum"))
                .from(POLICY_INSURED)
                .asTable();

        SelectConditionStep rowNum = this.getDslContext().select(recordTable.fields()).from(recordTable).where(recordTable.field("rowNum").eq(1)
                .and(recordTable.field("insuredCreateDate").between(
                        DateUtils.stringToTime(startDate, DateUtils.FORMATE51)
                        , DateUtils.getCurrentTime())));
        rowNum.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        System.out.println(rowNum.toString());

        return rowNum.fetchInto(PolicyInsuredReportBo.class);
    }

    @Override
    public List<PolicyApplicantReportBo> queryDelegateApplicantReport(BasePageRequest basePageRequest, String startDate) {
        Field<Long> stringField = DSL.field("row_number() OVER (PARTITION BY {0} ORDER BY {1} DESC)", SQLDataType.BIGINT,
                POLICY_APPLICANT.DELEGATE_CUSTOMER_ID, POLICY_APPLICANT.CREATED_DATE);

        //同步时间取更新时间，更新时间为空则取创建时间
        Field<Long> applicantCreateDate = POLICY_APPLICANT.UPDATED_DATE.nvl(POLICY_APPLICANT.CREATED_DATE).as("applicantCreateDate");

        Table recordTable = this.getDslContext()
                .select(POLICY_APPLICANT.CREATED_DATE, POLICY_APPLICANT.CREATED_USER_ID, POLICY_APPLICANT.UPDATED_DATE, POLICY_APPLICANT.UPDATED_USER_ID, POLICY_APPLICANT.VALID_FLAG,
                        POLICY_APPLICANT.DELEGATE_NAME.as("name"),
                        POLICY_APPLICANT.DELEGATE_ID_NO.as("idNo"),
                        POLICY_APPLICANT.DELEGATE_ID_TYPE.as("idType"),
                        POLICY_APPLICANT.DELEGATE_MOBILE.as("mobile"),
                        POLICY_APPLICANT.DELEGATE_BIRTHDAY.as("birthday"),
                        POLICY_APPLICANT.DELEGATE_CUSTOMER_ID.as("customerId"))
                .select(DSL.field("'GROUP_DELEGATE'").as("customerType"))
                .select(applicantCreateDate)
                .select(stringField.as("rowNum"))
                .from(POLICY_APPLICANT)
                .where(POLICY_APPLICANT.APPLICANT_TYPE.eq(PolicyTermEnum.APPLICANT_TYPE.GROUP.name())
                        .and(POLICY_APPLICANT.DELEGATE_CUSTOMER_ID.isNotNull()))
                .asTable();

        SelectConditionStep rowNum = this.getDslContext().select(recordTable.fields()).from(recordTable).where(recordTable.field("rowNum").eq(1)
                .and(recordTable.field("applicantCreateDate").between(
                        DateUtils.stringToTime(startDate, DateUtils.FORMATE51)
                        , DateUtils.getCurrentTime())));
        rowNum.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        System.out.println(rowNum.toString());

        return rowNum.fetchInto(PolicyApplicantReportBo.class);
    }

    @Override
    public List<PolicyReportUnderwritingBo> queryPolicyReportUnderwritingBo(BasePageRequest basePageRequest, String startDate) {

        String effective = TerminologyConfigEnum.VALID_FLAG.effective.name();
        List<PolicyReportUnderwritingBo> policyReportUnderwritingBos;

        //同步时间取更新时间，更新时间为空则取创建时间
        Field<Long> policyCreateDate = POLICY.UPDATED_DATE.nvl(POLICY.CREATED_DATE).as("policyCreateDate");

        Table recordTable = this.getDslContext()
                //不能用fields
                .select(POLICY.APPLY_ID, POLICY.APPLY_NO, POLICY.POLICY_STATUS, POLICY.APPROVE_DATE, POLICY.SALES_BRANCH_ID, POLICY.CHANNEL_TYPE_CODE,
                        POLICY.APPLY_DATE, POLICY.SELF_INSURANCE_FLAG, POLICY.HESITATION_END_DATE,
                        POLICY.THOROUGH_INVALID_DATE.as("surrenderDate"), POLICY.CREATED_DATE.as("policyCreatedDate"), POLICY.UPDATED_DATE.as("policyUpdatedDate"), POLICY.RISK_COMMENCEMENT_DATE.as("riskCommencementDate"))
                .select(POLICY.POLICY_PERIOD)
                .select(POLICY_PREMIUM.ACTUAL_PREMIUM.as("premiumActualPremium"))
                .select(policyCreateDate)
                .select(POLICY_AGENT.AGENT_ID, POLICY_AGENT.AGENT_CODE,POLICY_AGENT.REFERRAL_NAME,POLICY_AGENT.ABA_ACCOUNT)
                .select(POLICY_COVERAGE.fields())
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT.as("mainCoveragePeriodUnit"), POLICY_COVERAGE.COVERAGE_PERIOD.as("mainCoveragePeriod"))
                .select(POLICY.INVALID_DATE, POLICY.THOROUGH_INVALID_DATE)
                .from(POLICY)
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_AGENT.VALID_FLAG.eq(effective)))
                .leftJoin(POLICY_COVERAGE).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID).and(POLICY.VALID_FLAG.eq(effective)).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())))
                .leftJoin(POLICY_PREMIUM).on(POLICY_PREMIUM.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_PREMIUM.VALID_FLAG.eq(effective)))
                .where(POLICY.VALID_FLAG.eq(effective).and(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name())))
                .orderBy(POLICY.CREATED_DATE.asc())
                .asTable();

        SelectConditionStep rowNum = this.getDslContext().select(recordTable.fields()).from(recordTable).where(recordTable.field("policyCreateDate").between(
                DateUtils.stringToTime(startDate, DateUtils.FORMATE51), DateUtils.getCurrentTime()));
        rowNum.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        System.out.println(rowNum.toString());
        policyReportUnderwritingBos = rowNum.fetchInto(PolicyReportUnderwritingBo.class);
        return policyReportUnderwritingBos;
    }

    /**
     * 查询监管报表承保清单所需数据
     *
     * @param basePageRequest
     * @param startDate
     * @return
     */
    @Override
    public List<PolicyReportUnderwritingBo> queryPolicyReportUnderwritingRegulatory(BasePageRequest basePageRequest, String startDate) {

        String effective = TerminologyConfigEnum.VALID_FLAG.effective.name();
        Table policyCoverageRecordTable = this.getDslContext().selectFrom(POLICY_COVERAGE).where(POLICY_COVERAGE.PRIMARY_FLAG.eq("MAIN")).asTable();
        List<PolicyReportUnderwritingBo> policyReportUnderwritingBos;

        //同步时间取更新时间，更新时间为空则取创建时间
        Field<Long> policyCreateDate = POLICY.UPDATED_DATE.nvl(POLICY.CREATED_DATE).as("policyCreateDate");

        Table recordTable = this.getDslContext()
                .select(POLICY_COVERAGE.fields())
                //不能用fields
                .select(POLICY.APPLY_ID, POLICY.APPLY_NO, POLICY.POLICY_STATUS, POLICY.APPROVE_DATE, POLICY.SALES_BRANCH_ID, POLICY.CHANNEL_TYPE_CODE,
                        POLICY.APPLY_DATE)
                .select(policyCreateDate)
                .select(POLICY_AGENT.AGENT_ID, POLICY_AGENT.AGENT_CODE)
                .select(policyCoverageRecordTable.field(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT).as("mainCoveragePeriodUnit"), policyCoverageRecordTable.field(POLICY_COVERAGE.COVERAGE_PERIOD).as("mainCoveragePeriod"))
                .from(POLICY_COVERAGE)
                .leftJoin(POLICY).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID).and(POLICY.VALID_FLAG.eq(effective)))
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_AGENT.VALID_FLAG.eq(effective)))
                .innerJoin(policyCoverageRecordTable).on(policyCoverageRecordTable.field(POLICY_COVERAGE.POLICY_ID).eq(POLICY_COVERAGE.POLICY_ID))
                .where(POLICY_COVERAGE.VALID_FLAG.eq(effective).and(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name())))
                .groupBy(POLICY_COVERAGE.POLICY_ID, POLICY_COVERAGE.COVERAGE_ID, POLICY.POLICY_ID, POLICY_AGENT.AGENT_ID, POLICY_AGENT.AGENT_CODE
                        , policyCoverageRecordTable.field(POLICY_COVERAGE.COVERAGE_ID),
                        policyCoverageRecordTable.field(POLICY_COVERAGE.POLICY_ID),
                        policyCoverageRecordTable.field(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT),
                        policyCoverageRecordTable.field(POLICY_COVERAGE.COVERAGE_PERIOD))
                .orderBy(POLICY_COVERAGE.POLICY_ID.asc(), POLICY_COVERAGE.PRIMARY_FLAG.desc())
                .asTable();

        SelectConditionStep rowNum = this.getDslContext().select(recordTable.fields()).from(recordTable).where(recordTable.field("policyCreateDate").between(
                DateUtils.stringToTime(startDate, DateUtils.FORMATE51), DateUtils.getCurrentTime()));
        rowNum.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        System.out.println(rowNum.toString());
        policyReportUnderwritingBos = rowNum.fetchInto(PolicyReportUnderwritingBo.class);
        return policyReportUnderwritingBos;
    }

    @Override
    public List<PolicyPaymentPo> queryAllPolicyPaymentPo(String policyId) {
        return this.getDslContext()
                .select(POLICY_PAYMENT.fields())
                .from(POLICY_PAYMENT)
                .where(POLICY_PAYMENT.POLICY_ID.eq(policyId))
                .and(POLICY_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_PAYMENT.PAYMENT_STATUS_CODE.eq(PolicyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name()))
                .orderBy(POLICY_PAYMENT.CREATED_DATE.asc())
                .fetchInto(PolicyPaymentPo.class);
    }

    @Override
    public List<PolicyPaymentPo> queryPolicyPayment(String policyId) {
        return this.getDslContext()
                .select(POLICY_PAYMENT.fields())
                .from(POLICY_PAYMENT)
                .where(POLICY_PAYMENT.POLICY_ID.eq(policyId))
                .and(POLICY_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .orderBy(POLICY_PAYMENT.CREATED_DATE.asc())
                .fetchInto(PolicyPaymentPo.class);
    }

    @Override
    public PolicyAgentPo queryOnePolicyAgentPo(String policyId) {
        PolicyAgentPo policyAgentPo = this.getDslContext()
                .select(POLICY_AGENT.fields())
                .from(POLICY_AGENT)
                .where(POLICY_AGENT.POLICY_ID.eq(policyId))
                .and(POLICY_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(PolicyAgentPo.class);
        return policyAgentPo;
    }

    @Override
    public List<PolicyApplicantPo> queryOnePolicyApplicantPo(List<String> policyIds) {
        return this.getDslContext()
                .select(POLICY_APPLICANT.fields())
                .from(POLICY_APPLICANT)
                .where(POLICY_APPLICANT.POLICY_ID.in(policyIds))
                .and(POLICY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(PolicyApplicantPo.class);
    }

    @Override
    public PolicyInsuredPo queryOnePolicyInsuredPo(String policyId) {
        PolicyInsuredPo policyInsuredPo = this.getDslContext()
                .select(POLICY_INSURED.fields())
                .from(POLICY_INSURED)
                .where(POLICY_INSURED.POLICY_ID.eq(policyId))
                .and(POLICY_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .orderBy(POLICY_INSURED.CREATED_DATE.desc()).limit(1)
                .fetchOneInto(PolicyInsuredPo.class);
        return policyInsuredPo;
    }

    @Override
    public PolicyCoveragePo queryPolicyCoveragePo(String policyId) {
        // TODO: 18-11-19 只查询个险数据，未查询团险
        PolicyCoveragePo policyCoveragePo = this.getDslContext()
                .select(POLICY_COVERAGE.fields())
                .from(POLICY_COVERAGE)
                .innerJoin(POLICY).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()))
                        .and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                )
                .where(POLICY_COVERAGE.POLICY_ID.eq(policyId)
                        .and(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                )
                .limit(1)
                .fetchOneInto(PolicyCoveragePo.class);
        return policyCoveragePo;
    }

    public PolicyCoverageBo queryPolicyMainCoveragePo(String policyId) {
        return this.getDslContext()
                .select(POLICY_COVERAGE.fields())
                .from(POLICY_COVERAGE)
                .innerJoin(POLICY).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID))
                .and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                )
                .where(POLICY_COVERAGE.POLICY_ID.eq(policyId)
                        .and(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                        .and(POLICY_COVERAGE.INSURED_ID.isNotNull())
                )
                .limit(1)
                .fetchOneInto(PolicyCoverageBo.class);
    }

    /**
     * 查询待生效险种扩展信息
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoverageExtendPo> listPendingCoverageExtend(String policyId) {
        return this.getDslContext()
                .select(POLICY_COVERAGE_EXTEND.fields())
                .from(POLICY_COVERAGE_EXTEND)
                .where(POLICY_COVERAGE_EXTEND.POLICY_ID.eq(policyId))
                .and(POLICY_COVERAGE_EXTEND.COVERAGE_STATUS.eq(PolicyTermEnum.COVERAGE_STATUS.PENDING_EFFECT.name()))
                .fetchInto(PolicyCoverageExtendPo.class);
    }

    /**
     * 查询待生效险种扩展信息
     *
     * @param policyId 保单ID
     * @param dataType 数据类型
     * @return
     */
    @Override
    public List<PolicyCoverageExtendPo> listPendingCoverageExtend(String policyId, String dataType) {
        return this.getDslContext()
                .select(POLICY_COVERAGE_EXTEND.fields())
                .from(POLICY_COVERAGE_EXTEND)
                .where(POLICY_COVERAGE_EXTEND.POLICY_ID.eq(policyId))
                .and(POLICY_COVERAGE_EXTEND.COVERAGE_STATUS.eq(PolicyTermEnum.COVERAGE_STATUS.PENDING_EFFECT.name()))
                .and(POLICY_COVERAGE_EXTEND.DATA_TYPE.eq(dataType))
                .fetchInto(PolicyCoverageExtendPo.class);
    }

    /**
     * 查询待生效险种扩展信息
     *
     * @param policyIds 保单ID集
     * @param dataType  数据类型
     * @return
     */
    @Override
    public List<PolicyCoverageExtendPo> listPendingCoverageExtend(List<String> policyIds, String dataType) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_COVERAGE_EXTEND.COVERAGE_STATUS.eq(PolicyTermEnum.COVERAGE_STATUS.PENDING_EFFECT.name()));
        conditions.add(POLICY_COVERAGE_EXTEND.POLICY_ID.in(policyIds));
        if (AssertUtils.isNotEmpty(dataType)) {
            conditions.add(POLICY_COVERAGE_EXTEND.DATA_TYPE.eq(dataType));
        }
        return this.getDslContext()
                .selectFrom(POLICY_COVERAGE_EXTEND)
                .where(conditions)
                .fetchInto(PolicyCoverageExtendPo.class);
    }

    /**
     * 查询保单操作
     *
     * @param policyId      保单ID
     * @param operationCode 操作编码
     * @return
     */
    @Override
    public PolicyOperationPo queryPolicyOperation(String policyId, String operationCode) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_OPERATION.POLICY_ID.eq(policyId));
        conditions.add(POLICY_OPERATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        if (AssertUtils.isNotEmpty(operationCode)) {
            conditions.add(POLICY_OPERATION.OPERATION_CODE.eq(operationCode));
        }
        return this.getDslContext()
                .selectFrom(POLICY_OPERATION)
                .where(conditions)
                .orderBy(POLICY_OPERATION.CREATED_DATE.desc())
                .limit(1)
                .fetchOneInto(PolicyOperationPo.class);
    }

    @Override
    public BaseOperationPo queryBaseOperationPoByOperationCode(String operationCode) {
        return this.getDslContext()
                .select(BASE_OPERATION.fields())
                .from(BASE_OPERATION)
                .where(BASE_OPERATION.OPERATION_CODE.eq(operationCode))
                .and(BASE_OPERATION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(BaseOperationPo.class);
    }

    @Override
    public List<PolicyCoveragePo> queryPolicyCoverage(String policyId) {
        return this.getDslContext()
                .select(POLICY_COVERAGE.fields())
                .from(POLICY_COVERAGE)
                .where(POLICY_COVERAGE.POLICY_ID.eq(policyId))
                .and(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(PolicyCoveragePo.class);
    }

    @Override
    public PolicyCoverageBo queryPolicyPublicMainCoveragePo(String policyId) {
        return this.getDslContext()
                .select(POLICY_COVERAGE.fields())
                .from(POLICY_COVERAGE)
                .innerJoin(POLICY).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID))
                .and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                )
                .where(POLICY_COVERAGE.POLICY_ID.eq(policyId)
                        .and(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                        .and(POLICY_COVERAGE.INSURED_ID.isNull())
                )
                .limit(1)
                .fetchOneInto(PolicyCoverageBo.class);
    }

    /**
     * 根据险种ID查询险种扩展信息
     *
     * @param coverageIds 险种IDS
     * @param dataType    数据类型
     * @return
     */
    @Override
    public List<PolicyCoverageExtendPo> listCoverageExtendByCoverageId(List<String> coverageIds, String dataType) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_COVERAGE_EXTEND.COVERAGE_ID.in(coverageIds));
        if (AssertUtils.isNotEmpty(dataType)) {
            conditions.add(POLICY_COVERAGE_EXTEND.DATA_TYPE.eq(dataType));
        }
        conditions.add(POLICY_COVERAGE_EXTEND.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));

        return this.getDslContext()
                .select(POLICY_COVERAGE_EXTEND.fields())
                .from(POLICY_COVERAGE_EXTEND)
                .where(conditions)
                .orderBy(POLICY_COVERAGE_EXTEND.PRIMARY_FLAG.desc())
                .fetchInto(PolicyCoverageExtendPo.class);
    }

    /**
     * 查询当天满期的保单
     *
     * @param basePageRequest 参数
     * @return
     */
    @Override
    public List<PolicyPo> queryMaturityPolicy(BasePageRequest basePageRequest) {
        List<String> policyStatusList = new ArrayList<>();
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INDEMNITY_TERMINATION.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_HESITATION_REVOKE.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID_THOROUGH.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECT_TERMINATION.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_SURRENDER.name());
        policyStatusList.add(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_IEXPIRE.name());
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.POLICY_STATUS.notIn(policyStatusList));
        conditions.add(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(POLICY.POLICY_TYPE.ne(PolicyTermEnum.POLICY_TYPE.STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        // 满期日期小于等于当天最大时间
        conditions.add(POLICY.MATURITY_DATE.lt(DateUtils.timeToTimeTop(DateUtils.getCurrentTime())));

        return this.getDslContext()
                .selectFrom(POLICY)
                .where(conditions)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize())
                .fetchInto(PolicyPo.class);
    }

    /**
     * 查询当天满期的保单
     *
     * @param basePageRequest
     * @return
     */
    @Override
    public List<PolicyCoveragePo> queryMaturityRenewalAdditionCoverage(BasePageRequest basePageRequest) {

        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_COVERAGE.MATURITY_DATE.lessThan(DateUtils.getCurrentTime()));
        conditions.add(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name()));
        conditions.add(POLICY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(POLICY_COVERAGE.PREMIUM_FREQUENCY.eq(PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name())
                .or(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT.eq("YEAR").and(POLICY_COVERAGE.PREMIUM_PERIOD.eq("1"))));
        List<String> coverageStatus = Arrays.asList(
                PolicyTermEnum.COVERAGE_STATUS.EXPIRED.name(),
                PolicyTermEnum.COVERAGE_STATUS.INDEMNITY_TERMINATION.name()
        );
        conditions.add(POLICY_COVERAGE.COVERAGE_STATUS.notIn(coverageStatus));
        List<String> policyStatus = Arrays.asList(
                PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID.name(),
                PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name(),
                PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_REINSTATEMENT.name()
        );
        conditions.add(POLICY.POLICY_STATUS.in(policyStatus));

        PolicyCoverage c1 = POLICY_COVERAGE.as("c1");

        SelectOrderByStep<Record> union = this.getDslContext().select(POLICY_COVERAGE.fields())
                .from(POLICY_COVERAGE)
                .innerJoin(c1).on(c1.field(POLICY_COVERAGE.POLICY_ID).eq(POLICY_COVERAGE.POLICY_ID)
                        .and(c1.field(POLICY_COVERAGE.PRIMARY_FLAG).eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())
                                .and(c1.field(POLICY_COVERAGE.PREMIUM_FREQUENCY).ne(PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name())
                                        .or(c1.field(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT).ne("YEAR").and(c1.field(POLICY_COVERAGE.PREMIUM_PERIOD).ne("1")))
                                )))
                .innerJoin(POLICY).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID)
                        .and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name())))
                .where(conditions).and(POLICY_COVERAGE.RENEWAL_PERMIT_FLAG.eq(TerminologyConfigEnum.WHETHER.NO.name()))
                .union(
                        this.getDslContext().select(POLICY_COVERAGE.fields())
                                .from(POLICY_COVERAGE)
                                .innerJoin(c1).on(c1.field(POLICY_COVERAGE.POLICY_ID).eq(POLICY_COVERAGE.POLICY_ID)
                                .and(c1.field(POLICY_COVERAGE.PRIMARY_FLAG).eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())
                                        .and(c1.field(POLICY_COVERAGE.PREMIUM_FREQUENCY).ne(PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name())
                                                .or(c1.field(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT).ne("YEAR").and(c1.field(POLICY_COVERAGE.PREMIUM_PERIOD).ne("1")))
                                        )))
                                .innerJoin(POLICY).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID)
                                .and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                                .and(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()))
                                .and(POLICY.POLICY_STATUS.eq(PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID.name()))
                        )
                                .where(conditions)
                );
        union.offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize());
        System.out.println(union.toString());
        return union.fetchInto(PolicyCoveragePo.class);
    }

    /**
     * 查询险种保费信息
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoveragePremiumPo> listPolicyCoveragePremium(String policyId) {
        return this.getDslContext()
                .select(POLICY_COVERAGE_PREMIUM.fields())
                .from(POLICY_COVERAGE_PREMIUM)
                .where(POLICY_COVERAGE_PREMIUM.POLICY_ID.eq(policyId))
                .and(POLICY_COVERAGE_PREMIUM.VALID_FLAG.eq(effective.name()))
                .fetchInto(PolicyCoveragePremiumPo.class);
    }

    /**
     * 查询保单挂起操作
     *
     * @param policyId     保单ID
     * @param hookObjectId 挂起对象ID
     * @param hookStatus
     * @return PolicyHookPo
     */
    @Override
    public PolicyHookPo getPolicyHook(String policyId, String hookObjectId, String hookStatus) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_HOOK.POLICY_ID.eq(policyId));
        conditions.add(POLICY_HOOK.VALID_FLAG.eq(effective.name()));
        conditions.add(POLICY_HOOK.CUSTOMER_ID.isNull());
        if (AssertUtils.isNotEmpty(hookObjectId)) {
            conditions.add(POLICY_HOOK.HOOK_OBJECT_ID.eq(hookObjectId));
        }
        if (AssertUtils.isNotEmpty(hookStatus)) {
            conditions.add(POLICY_HOOK.HOOK_STATUS.eq(hookStatus));
        }
        return this.getDslContext().selectFrom(POLICY_HOOK).where(conditions)
                .limit(1).fetchOneInto(PolicyHookPo.class);
    }

    /**
     * 查询团险保单挂起操作
     *
     * @param policyId     保单ID
     * @param hookObjectId 挂起对象ID
     * @param customerId   customerId
     * @param hookStatus
     * @return PolicyHookPo
     */
    @Override
    public PolicyHookPo getGroupPolicyHook(String policyId, String hookObjectId, String customerId, String hookStatus) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_HOOK.POLICY_ID.eq(policyId));
        conditions.add(POLICY_HOOK.VALID_FLAG.eq(effective.name()));
        if (AssertUtils.isNotEmpty(hookObjectId)) {
            conditions.add(POLICY_HOOK.HOOK_OBJECT_ID.eq(hookObjectId));
        }
        if (AssertUtils.isNotEmpty(hookStatus)) {
            conditions.add(POLICY_HOOK.HOOK_STATUS.eq(hookStatus));
        }
        conditions.add(AssertUtils.isNotEmpty(customerId) ? POLICY_HOOK.CUSTOMER_ID.eq(customerId) : POLICY_HOOK.CUSTOMER_ID.isNotNull());
        return this.getDslContext().selectFrom(POLICY_HOOK).where(conditions)
                .limit(1).fetchOneInto(PolicyHookPo.class);
    }

    /**
     * 根据保单ID查询回访信息
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public PolicyReturnVisitPo queryReturnVisitByBusinessId(String policyId) {
        return this.getDslContext()
                .selectFrom(POLICY_RETURN_VISIT)
                .where(POLICY_RETURN_VISIT.BUSINESS_ID.eq(policyId))
                .and(POLICY_RETURN_VISIT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .limit(1)
                .fetchOneInto(PolicyReturnVisitPo.class);
    }

    @Override
    public List<ClaimPolicyBo> queryPolicyByCustomerIds(String... customerIds) {

        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(POLICY.POLICY_ID)
                .select(POLICY.POLICY_NO)
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_INSURED.NAME.as("insuredName"))
                .select(POLICY.EFFECTIVE_DATE)
                .select(POLICY.POLICY_TYPE)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.VERSION_NO)
                .from(POLICY)
                .leftJoin(POLICY_COVERAGE)
                .on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).and(
                        POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name())
                                .or(POLICY.POLICY_TYPE.eq(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name()).and(POLICY_COVERAGE.INSURED_ID.isNull()))
                        )
                )
                .leftJoin(POLICY_APPLICANT)
                .on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .leftJoin(POLICY_INSURED)
                .on(POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID));

        selectOnConditionStep.where((POLICY_APPLICANT.CUSTOMER_ID.in(customerIds).or(POLICY_INSURED.CUSTOMER_ID.in(customerIds))).and(POLICY.POLICY_TYPE.ne(LIFE_INSURANCE_PERSONAL.name())));

        System.out.println(selectOnConditionStep.toString());

        return selectOnConditionStep.fetchInto(ClaimPolicyBo.class);
    }

    /**
     * 模糊查询保单信息
     *
     * @param keyword 关键字
     * @return
     */
    @Override
    public List<PolicyApplicantInfoBo> queryListPolicysByKeyword(String keyword) {
        List<PolicyApplicantInfoBo> policyPos = null;
        SelectConditionStep selectConditionStep = this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY_APPLICANT.fields())
                .from(POLICY)
                .leftJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .where(POLICY.POLICY_TYPE.eq(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name()).and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
        if (AssertUtils.isNotEmpty(keyword)) {
            selectConditionStep.and(POLICY.POLICY_NO.like("%" + keyword.trim() + "%").or(POLICY_APPLICANT.COMPANY_NAME.like("%" + keyword.trim() + "%")));
        }
        selectConditionStep.orderBy(POLICY.CREATED_DATE.desc());
        System.out.println("queryPolicysByKeyword :" + selectConditionStep.toString());
        policyPos = selectConditionStep.limit(20).fetchInto(PolicyApplicantInfoBo.class);
        return policyPos;
    }

    @Override
    public List<PolicyApplicantInfoBo> queryAgentListPolicysByKeyword(String userId, String keyword) {
        List<PolicyApplicantInfoBo> policyPos = null;
        SelectConditionStep selectConditionStep = this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY_APPLICANT.fields())
                .select(POLICY_AGENT.AGENT_ID)
                .from(POLICY)
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .where(POLICY.POLICY_TYPE.eq(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name()).and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
        if (AssertUtils.isNotEmpty(userId)) {
            selectConditionStep.and(POLICY_AGENT.AGENT_ID.eq(userId));
        }
        if (AssertUtils.isNotEmpty(keyword)) {
            selectConditionStep.and(POLICY.POLICY_NO.like("%" + keyword.trim() + "%").or(POLICY_APPLICANT.COMPANY_NAME.like("%" + keyword.trim() + "%")));
        }
        selectConditionStep.orderBy(POLICY.CREATED_DATE.desc());
        System.out.println("queryPolicysByKeyword :" + selectConditionStep.toString());
        policyPos = selectConditionStep.limit(20).fetchInto(PolicyApplicantInfoBo.class);
        return policyPos;
    }

    @Override
    public List<PolicyApplicantInfoBo> queryAgentListPolicysByKeywordNew(String userId, String keyword) {
        List<PolicyApplicantInfoBo> policyPos = null;
        SelectConditionStep selectConditionStep = this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY_APPLICANT.fields())
                .select(POLICY_AGENT.AGENT_ID)
                .from(POLICY)
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .where(POLICY.POLICY_TYPE.eq(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name()).and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
        if (AssertUtils.isNotEmpty(keyword)) {
            selectConditionStep.and(POLICY.POLICY_NO.like("%" + keyword.trim() + "%").or(POLICY_APPLICANT.COMPANY_NAME.like("%" + keyword.trim() + "%")));
        }
        selectConditionStep.orderBy(POLICY.CREATED_DATE.desc());
        System.out.println("queryPolicysByKeyword :" + selectConditionStep.toString());
        policyPos = selectConditionStep.limit(20).fetchInto(PolicyApplicantInfoBo.class);
        return policyPos;
    }

    @Override
    public List<PolicyApplicantPo> queryPolicyApplicantByCustomerId(String customerId) {
        return this.getDslContext()
                .selectFrom(POLICY_APPLICANT)
                .where(POLICY_APPLICANT.CUSTOMER_ID.eq(customerId))
                .and(POLICY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(PolicyApplicantPo.class);
    }

    @Override
    public List<PolicyInsuredPo> queryPolicyInsuredByCustomerId(String customerId) {
        return this.getDslContext()
                .selectFrom(POLICY_INSURED)
                .where(POLICY_INSURED.CUSTOMER_ID.eq(customerId))
                .and(POLICY_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(PolicyInsuredPo.class);
    }

    @Override
    public List<PolicyCoveragePaymentBo> queryPolicyCoveragePaymentByPaymentId(String policyPaymentId) {
        return this.getDslContext()
                .selectFrom(POLICY_COVERAGE_PAYMENT)
                .where(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID.eq(policyPaymentId))
                .and(POLICY_COVERAGE_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(PolicyCoveragePaymentBo.class);
    }

    @Override
    public PolicyBo queryPolicyBoByJoin(String policyId) {
        PolicyBo policyBo2 = null;
        List<PolicyBo> policyBos = this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY_AGENT.fields())
                .select(POLICY_APPLICANT.fields())
                .select(POLICY_CONTACT_INFO.fields())
                .select(POLICY_RECEIPT_INFO.fields())
                .select(POLICY_PREMIUM.fields())
                .from(POLICY)
                .innerJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .innerJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_CONTACT_INFO).on(POLICY_CONTACT_INFO.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_RECEIPT_INFO).on(POLICY_RECEIPT_INFO.POLICY_ID.eq(POLICY.POLICY_ID))
                .innerJoin(POLICY_PREMIUM).on(POLICY_PREMIUM.POLICY_ID.eq(POLICY.POLICY_ID))
                .where(POLICY.POLICY_ID.eq(policyId).and(POLICY.VALID_FLAG.eq(effective.name())))
                .fetch().map(record -> {
                    PolicyBo policyBo = BasePojo.getInstance(PolicyBo.class, record.into(PolicyRecord.class));
                    PolicyAgentBo policyAgentBo = BasePojo.getInstance(PolicyAgentBo.class, record.into(PolicyAgentRecord.class));
                    PolicyApplicantBo policyApplicantBo = BasePojo.getInstance(PolicyApplicantBo.class, record.into(PolicyApplicantRecord.class));
                    PolicyContactInfoBo policyContactInfoBo = BasePojo.getInstance(PolicyContactInfoBo.class, record.into(PolicyContactInfoRecord.class));
                    PolicyReceiptInfoBo policyReceiptInfoBo = BasePojo.getInstance(PolicyReceiptInfoBo.class, record.into(PolicyReceiptInfoRecord.class));
                    PolicyPremiumBo policyPremiumBo = BasePojo.getInstance(PolicyPremiumBo.class, record.into(PolicyPremiumRecord.class));

                    Optional.ofNullable(policyAgentBo.getPolicyAgentId()).ifPresent(r -> policyBo.setPolicyAgent(policyAgentBo));
                    Optional.ofNullable(policyApplicantBo.getApplicantId()).ifPresent(r -> policyBo.setPolicyApplicant(policyApplicantBo));
                    Optional.ofNullable(policyContactInfoBo.getPolicyContactId()).ifPresent(r -> policyBo.setPolicyContactInfo(policyContactInfoBo));
                    Optional.ofNullable(policyReceiptInfoBo.getReceiptInfoId()).ifPresent(r -> policyBo.setPolicyReceiptInfo(policyReceiptInfoBo));
                    Optional.ofNullable(policyPremiumBo.getPolicyPremiumId()).ifPresent(r -> policyBo.setPolicyPremium(policyPremiumBo));
                    return policyBo;
                });
        if (AssertUtils.isNotEmpty(policyBos)) {
            policyBo2 = policyBos.get(0);
        }
        return policyBo2;
    }

    @Override
    public List<PolicyGroupReportUnderwritingBo> queryPolicyGroupReportUnderwritingBo(BasePageRequest basePageRequest, String startDate) {
        List<PolicyGroupReportUnderwritingBo> policyGroupReportUnderwritingBos;
        String effective = TerminologyConfigEnum.VALID_FLAG.effective.name();

        //同步时间取更新时间，更新时间为空则取创建时间
        Field<Long> policyCreateDate = POLICY.UPDATED_DATE.nvl(POLICY.CREATED_DATE).as("policyCreateDate");

        Table recordTable = this.getDslContext()
                //不能用fields
                .select(POLICY.APPLY_ID, POLICY.APPLY_NO, POLICY.POLICY_ID, POLICY.POLICY_NO, POLICY.APPLY_DATE, POLICY.POLICY_STATUS, POLICY.APPROVE_DATE, POLICY.EFFECTIVE_DATE,
                        POLICY.MATURITY_DATE,
                        POLICY.CHANNEL_TYPE_CODE,
                        POLICY_PREMIUM.SPECIAL_DISCOUNT,
                        POLICY_PREMIUM.COMPANY_DISCOUNT,
                        POLICY_PREMIUM.AGENT_DISCOUNT,
                        POLICY_PREMIUM.DISCOUNT_TYPE,
                        POLICY_PREMIUM.PERIOD_ORIGINAL_PREMIUM,
                        POLICY_PREMIUM.ACTUAL_PREMIUM,
                        POLICY_PREMIUM.ACTUAL_PREMIUM.as("premiumActualPremium"),
                        POLICY.SALES_BRANCH_ID, POLICY.THOROUGH_INVALID_DATE.as("surrenderDate"),
                        POLICY.CREATED_DATE.as("createdDate"), POLICY.UPDATED_DATE.as("updatedDate"),
                        POLICY.RISK_COMMENCEMENT_DATE.as("riskCommencementDate"))
                .select(POLICY.POLICY_PERIOD)
                .select(policyCreateDate)
                .select(POLICY_AGENT.AGENT_ID, POLICY_AGENT.AGENT_CODE,POLICY_AGENT.ABA_ACCOUNT,POLICY_AGENT.REFERRAL_NAME)
                .select(POLICY.INVALID_DATE, POLICY.THOROUGH_INVALID_DATE)
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .from(POLICY)
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_AGENT.VALID_FLAG.eq(effective)))
                .leftJoin(POLICY_PREMIUM).on(POLICY_PREMIUM.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_PREMIUM.VALID_FLAG.eq(effective)))
                .leftJoin(POLICY_COVERAGE).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID).and(POLICY_COVERAGE.INSURED_ID.isNull()).and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())))
                .where(POLICY.VALID_FLAG.eq(effective).and(POLICY.POLICY_TYPE.eq(PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name())))
                .orderBy(POLICY.CREATED_DATE.asc())
                .asTable();

        SelectConditionStep rowNum = this.getDslContext().select(recordTable.fields()).from(recordTable).where(recordTable.field("policyCreateDate").between(
                DateUtils.stringToTime(startDate, DateUtils.FORMATE51), DateUtils.getCurrentTime()));
        rowNum.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        System.out.println(rowNum.toString());
        policyGroupReportUnderwritingBos = rowNum.fetchInto(PolicyGroupReportUnderwritingBo.class);
        return policyGroupReportUnderwritingBos;
    }

    @Override
    public List<GroupSdfReportBo> queryListPolicyGroupSdfReportBo(BasePageRequest basePageRequest, String startDate) {
        List<GroupSdfReportBo> groupSdfReportBos;
        String effective = TerminologyConfigEnum.VALID_FLAG.effective.name();

        //同步时间取更新时间，更新时间为空则取创建时间
        Field<Long> policyCreateDate = POLICY_PAYMENT.UPDATED_DATE.nvl(POLICY_PAYMENT.CREATED_DATE).as("policyCreateDate");

        Table recordTable = this.getDslContext()
                .select(POLICY.APPLY_ID, POLICY.APPLY_NO, POLICY.POLICY_ID, POLICY.POLICY_NO, POLICY.HESITATION_END_DATE,POLICY.EFFECTIVE_DATE.as("policyEffectiveDate"))
                .select(policyCreateDate)
                .select(POLICY_PAYMENT.BUSINESS_ID, POLICY_PAYMENT.ACTUAL_PREMIUM,POLICY_PAYMENT.PERIOD_ORIGINAL_PREMIUM
                        ,POLICY_PAYMENT.PERIOD_ACTUAL_PREMIUM,POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE
                ,POLICY_PAYMENT.BIZ_DATE,POLICY_PAYMENT.CREATED_DATE.as("transactionDate"))
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_RECEIPT_INFO.RECEIPT_DATE)
                .from(POLICY_PAYMENT)
                .leftJoin(POLICY).on(POLICY_PAYMENT.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY.VALID_FLAG.eq(effective)))
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_APPLICANT.VALID_FLAG.eq(effective)))
                .leftJoin(POLICY_RECEIPT_INFO).on(POLICY.POLICY_ID.eq(POLICY_RECEIPT_INFO.POLICY_ID))
                .leftJoin(POLICY_COVERAGE).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID))
                .where(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE.in("BUSINESS_TYPE_NEW_CONTRACT", "BUSINESS_TYPE_ENDORSE").and(POLICY_COVERAGE.INSURED_ID.isNull()).and(POLICY_COVERAGE.PRODUCT_ID.eq("PRO880000000000029")))
                .orderBy(POLICY_PAYMENT.CREATED_DATE.asc())
                .asTable();

        SelectConditionStep rowNum = this.getDslContext().select(recordTable.fields()).from(recordTable).where(recordTable.field("policyCreateDate").between(
                DateUtils.stringToTime(startDate, DateUtils.FORMATE51), DateUtils.getCurrentTime()));
        rowNum.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        System.out.println(rowNum.toString());
        groupSdfReportBos = rowNum.fetchInto(GroupSdfReportBo.class);
        return groupSdfReportBos;
    }

    @Override
    public List<PolicyCoverageDutyBo> queryPolicyCoverageDutyBoByCoverageIds(List<String> coverageIds) {
        return this.getDslContext().select(POLICY_COVERAGE_DUTY.fields()).from(POLICY_COVERAGE_DUTY)
                .where(POLICY_COVERAGE_DUTY.COVERAGE_ID.in(coverageIds)
                        .and(POLICY_COVERAGE_DUTY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchInto(PolicyCoverageDutyBo.class);
    }

    @Override
    public List<PolicyCoverageDutyBo> getPolicyCoverageDutyList(String policyId) {
        return this.getDslContext()
                .select(POLICY_COVERAGE_DUTY.fields())
                .from(POLICY_COVERAGE_DUTY)
                .where(POLICY_COVERAGE_DUTY.POLICY_ID.eq(policyId))
                .and(POLICY_COVERAGE_DUTY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())).fetchInto(PolicyCoverageDutyBo.class);
    }

    @Override
    public List<PolicyApplicantInfoBo> queryReturnVisitPolicyByKeyword(String keyword) {
        List<PolicyApplicantInfoBo> policyPos = null;
        SelectConditionStep selectConditionStep = this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY_APPLICANT.fields())
                .from(POLICY)
                .leftJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .where(POLICY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name())
                        .and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
        if (AssertUtils.isNotEmpty(keyword)) {
            selectConditionStep.and(POLICY.POLICY_NO.like("%" + keyword.trim() + "%")
                    .or(POLICY_APPLICANT.COMPANY_NAME.like("%" + keyword.trim() + "%"))
                    .or(POLICY_APPLICANT.NAME.like("%" + keyword.trim() + "%"))
            );
        }
        selectConditionStep.orderBy(POLICY.CREATED_DATE.desc());
        System.out.println("queryPolicysByKeyword :" + selectConditionStep.toString());
        policyPos = selectConditionStep.limit(20).fetchInto(PolicyApplicantInfoBo.class);
        return policyPos;
    }

    @Override
    public List<GroupInsuredInfoBo> queryPolicyInsuredInfoBo(String policyId) {
        List<GroupInsuredInfoBo> groupInsuredInfoBoList = this.getDslContext()
                .select(POLICY_INSURED.NAME, POLICY_INSURED.BIRTHDAY, POLICY_INSURED.SEX, POLICY_INSURED.ID_TYPE, POLICY_INSURED.ID_NO, POLICY_INSURED.OCCUPATION_CODE)
                .select(POLICY_COVERAGE.PRODUCT_CODE, POLICY_COVERAGE.PRODUCT_NAME, POLICY_COVERAGE.PRIMARY_FLAG, POLICY_COVERAGE.DUTY_CHOOSE_FLAG)
                .select(POLICY_COVERAGE_LEVEL.PRODUCT_LEVEL, POLICY_COVERAGE_LEVEL.MULT, POLICY_COVERAGE_LEVEL.TOTAL_PREMIUM)
                .from(POLICY)
                .leftJoin(POLICY_INSURED).on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_COVERAGE.INSURED_ID.eq(POLICY_INSURED.INSURED_ID)))
                .leftJoin(POLICY_COVERAGE_LEVEL).on(POLICY_COVERAGE_LEVEL.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_COVERAGE_LEVEL.COVERAGE_ID.eq(POLICY_COVERAGE.COVERAGE_ID)))
                .where(POLICY.VALID_FLAG.eq(effective.name()).and(POLICY.POLICY_ID.eq(policyId))
                        .and(POLICY_INSURED.VALID_FLAG.eq(effective.name()))
                        .and(POLICY_COVERAGE.VALID_FLAG.eq(effective.name()))
                        .and(POLICY_COVERAGE_LEVEL.VALID_FLAG.eq(effective.name())))
                .orderBy(POLICY_INSURED.CREATED_DATE.asc(), POLICY_INSURED.NAME.asc(), POLICY_COVERAGE.PRIMARY_FLAG.desc(), POLICY_COVERAGE_LEVEL.PRODUCT_LEVEL.asc()).fetchInto(GroupInsuredInfoBo.class);

        List<GroupInsuredInfoBo> groupInsuredInfoBos = this.getDslContext()
                .select(POLICY_INSURED.NAME, POLICY_INSURED.BIRTHDAY, POLICY_INSURED.SEX, POLICY_INSURED.ID_TYPE, POLICY_INSURED.ID_NO, POLICY_INSURED.OCCUPATION_CODE)
                .select(POLICY_COVERAGE.PRODUCT_CODE, POLICY_COVERAGE.PRODUCT_NAME, POLICY_COVERAGE.PRIMARY_FLAG, POLICY_COVERAGE.DUTY_CHOOSE_FLAG)
                .select(POLICY_COVERAGE_DUTY.DUTY_NAME, POLICY_COVERAGE_DUTY.DUTY_ID)
                .select(POLICY_COVERAGE_LEVEL.PRODUCT_LEVEL, POLICY_COVERAGE_LEVEL.MULT, POLICY_COVERAGE_LEVEL.TOTAL_PREMIUM)
                .from(POLICY)
                .leftJoin(POLICY_INSURED).on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_COVERAGE.INSURED_ID.eq(POLICY_INSURED.INSURED_ID)))
                .leftJoin(POLICY_COVERAGE_DUTY).on(POLICY_COVERAGE_DUTY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID).and(POLICY_COVERAGE_DUTY.COVERAGE_ID.eq(POLICY_COVERAGE.COVERAGE_ID)))
                .leftJoin(POLICY_COVERAGE_LEVEL).on(POLICY_COVERAGE_LEVEL.COVERAGE_DUTY_ID.eq(POLICY_COVERAGE_DUTY.COVERAGE_DUTY_ID).and(POLICY_COVERAGE_LEVEL.COVERAGE_ID.eq(POLICY_COVERAGE_DUTY.COVERAGE_ID)))
                .where(POLICY.VALID_FLAG.eq(effective.name()).and(POLICY.POLICY_ID.eq(policyId))
                        .and(POLICY_INSURED.VALID_FLAG.eq(effective.name()))
                        .and(POLICY_COVERAGE.VALID_FLAG.eq(effective.name()))
                        .and(POLICY_COVERAGE.DUTY_CHOOSE_FLAG.eq(TerminologyConfigEnum.WHETHER.YES.name()))
                        .and(POLICY_COVERAGE_LEVEL.VALID_FLAG.eq(effective.name())))
                .orderBy(POLICY_INSURED.CREATED_DATE.asc(), POLICY_INSURED.NAME.asc(), POLICY_COVERAGE.PRIMARY_FLAG.desc(), POLICY_COVERAGE_LEVEL.PRODUCT_LEVEL.asc()).fetchInto(GroupInsuredInfoBo.class);
        if (AssertUtils.isNotEmpty(groupInsuredInfoBos)) {
            groupInsuredInfoBoList.addAll(groupInsuredInfoBos);
        }
        return groupInsuredInfoBoList;
    }

    /**
     * 根据客户ID查询保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimPolicyBo> listSimplePolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_APPLICANT.CUSTOMER_ID.in(customerAgentIds));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        conditions.add(POLICY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getSimplePolicySql();
        selectOnConditionStep.where(conditions);

        List<Condition> conditions2 = new ArrayList<>();
        conditions2.add(POLICY_INSURED.CUSTOMER_ID.in(customerAgentIds));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions2.add(POLICY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        conditions2.add(POLICY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep2 = getSimplePolicySql();
        selectOnConditionStep2.where(conditions2);

        return selectOnConditionStep.union(selectOnConditionStep2).fetchInto(ClaimPolicyBo.class);
    }

    /**
     * 根据客户ID查询团险保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimPolicyBo> listSimpleGroupPolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_INSURED.CUSTOMER_ID.in(customerAgentIds));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查团险
        conditions.add(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_GROUP.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getSimpleGroupPolicySql();
        selectOnConditionStep.where(conditions);

        return selectOnConditionStep.fetchInto(ClaimPolicyBo.class);
    }

    /**
     * 根据保单ID查询保单基础信息列表
     *
     * @param policyId          保单ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public ClaimPolicyBo querySimplePolicyById(String policyId, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.POLICY_ID.eq(policyId));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        conditions.add(POLICY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getSimplePolicySql();
        selectOnConditionStep.where(conditions);

        return selectOnConditionStep.fetchOneInto(ClaimPolicyBo.class);
    }

    /**
     * 根据客户ID查询历史保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimPolicyBo> listSimplePolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_APPLICANT_HISTORY.CUSTOMER_ID.in(customerAgentIds));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY_HISTORY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        conditions.add(POLICY_HISTORY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getSimplePolicyHistorySql();
        selectOnConditionStep.where(conditions);

        List<Condition> conditions2 = new ArrayList<>();
        conditions2.add(POLICY_INSURED_HISTORY.CUSTOMER_ID.in(customerAgentIds));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions2.add(POLICY_HISTORY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        conditions2.add(POLICY_HISTORY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep2 = getSimplePolicyHistorySql();
        selectOnConditionStep2.where(conditions2);

        return selectOnConditionStep.union(selectOnConditionStep2).fetchInto(ClaimPolicyBo.class);
    }

    /**
     * 根据客户ID查询历史团险保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimPolicyBo> listSimpleGroupPolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_INSURED_HISTORY.CUSTOMER_ID.in(customerAgentIds));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY_HISTORY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查团险
        conditions.add(POLICY_HISTORY.POLICY_TYPE.eq(LIFE_INSURANCE_GROUP.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getSimpleGroupPolicyHistorySql();
        selectOnConditionStep.where(conditions);
        return selectOnConditionStep.fetchInto(ClaimPolicyBo.class);
    }

    /**
     * 根据客户ID查询历史保单基础信息列表
     *
     * @param policyId          保单ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimPolicyBo> listSimplePolicyHistoryByPolicyId(String policyId, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_HISTORY.POLICY_ID.eq(policyId));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY_HISTORY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        conditions.add(POLICY_HISTORY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getSimplePolicyHistorySql();
        selectOnConditionStep.where(conditions);

        return selectOnConditionStep.fetchInto(ClaimPolicyBo.class);
    }

    /**
     * 根据客户ID查询保单详细信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimDetailPolicyBo> listDetailPolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_APPLICANT.CUSTOMER_ID.in(customerAgentIds).or(POLICY_INSURED.CUSTOMER_ID.in(customerAgentIds)));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        conditions.add(POLICY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getDetailPolicySql();
        selectOnConditionStep.where(conditions);

        List<Condition> conditions2 = new ArrayList<>();
        conditions2.add(POLICY_APPLICANT.CUSTOMER_ID.in(customerAgentIds).or(POLICY_INSURED.CUSTOMER_ID.in(customerAgentIds)));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions2.add(POLICY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        conditions2.add(POLICY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep2 = getDetailPolicySql();
        selectOnConditionStep2.where(conditions2);
        selectOnConditionStep.union(selectOnConditionStep2);

        Map<String, ClaimDetailPolicyBo> policyMap = new HashMap<>();
        Map<String, PolicyApplicantBo> policyApplicantMap = new HashMap<>();
        Map<String, PolicyInsuredBo> policyInsuredMap = new HashMap<>();
        Map<String, PolicyCoverageBo> policyCoverageMap = new HashMap<>();
        // 执行sql
        fetchDetailPolicySql(selectOnConditionStep, policyMap, policyApplicantMap, policyInsuredMap, policyCoverageMap);

        return transClaimDetailPolicyBos(policyMap, policyApplicantMap, policyInsuredMap, policyCoverageMap);
    }

    /**
     * 根据客户ID查询历史保单详细信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimDetailPolicyBo> listDetailPolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_APPLICANT_HISTORY.CUSTOMER_ID.in(customerAgentIds));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY_HISTORY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        conditions.add(POLICY_HISTORY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getDetailPolicyHistorySql();
        selectOnConditionStep.where(conditions);

        List<Condition> conditions2 = new ArrayList<>();
        conditions2.add(POLICY_INSURED_HISTORY.CUSTOMER_ID.in(customerAgentIds));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions2.add(POLICY_HISTORY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        conditions2.add(POLICY_HISTORY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()));
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep2 = getDetailPolicyHistorySql();
        selectOnConditionStep2.where(conditions2);
        selectOnConditionStep.union(selectOnConditionStep2);

        Map<String, ClaimDetailPolicyBo> policyMap = new HashMap<>();
        Map<String, PolicyApplicantBo> policyApplicantMap = new HashMap<>();
        Map<String, PolicyInsuredBo> policyInsuredMap = new HashMap<>();
        Map<String, PolicyCoverageBo> policyCoverageMap = new HashMap<>();
        // 执行sql
        fetchDetailPolicyHistorySql(selectOnConditionStep, policyMap, policyApplicantMap, policyInsuredMap, policyCoverageMap);

        return transClaimDetailPolicyBos(policyMap, policyApplicantMap, policyInsuredMap, policyCoverageMap);
    }

    /**
     * 根据保单ID查询保单详细信息列表
     *
     * @param policyId          保单ID
     * @param customerId
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public ClaimDetailPolicyBo queryDetailPolicyByPolicyId(String policyId, String customerId, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.POLICY_ID.eq(policyId));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险   兼容旧版本
        if (!AssertUtils.isNotEmpty(customerId)) {
            conditions.add(POLICY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        }
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getDetailPolicySqlById(customerId);
        selectOnConditionStep.where(conditions);

        Map<String, ClaimDetailPolicyBo> policyMap = new HashMap<>();
        Map<String, PolicyApplicantBo> policyApplicantMap = new HashMap<>();
        Map<String, PolicyInsuredBo> policyInsuredMap = new HashMap<>();
        Map<String, PolicyCoverageBo> policyCoverageMap = new HashMap<>();
        // 执行sql
        fetchDetailPolicySql(selectOnConditionStep, policyMap, policyApplicantMap, policyInsuredMap, policyCoverageMap);

        List<ClaimDetailPolicyBo> claimDetailPolicyBos = transClaimDetailPolicyBos(policyMap, policyApplicantMap, policyInsuredMap, policyCoverageMap);
        if (AssertUtils.isNotEmpty(claimDetailPolicyBos)) {
            return claimDetailPolicyBos.get(0);
        }
        return null;
    }

    /**
     * 根据保单ID查询历史保单详细信息列表
     *
     * @param policyId          保单ID
     * @param customerId
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    @Override
    public List<ClaimDetailPolicyBo> listDetailPolicyHistoryByPolicyId(String policyId, String customerId, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_HISTORY.POLICY_ID.eq(policyId));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY_HISTORY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        if (!AssertUtils.isNotEmpty(customerId)) {
            conditions.add(POLICY_HISTORY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        } else {
            conditions.add(POLICY_INSURED_HISTORY.CUSTOMER_ID.eq(customerId));
        }
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getDetailPolicyHistorySqlById(customerId);
        selectOnConditionStep.where(conditions);

        System.out.println(selectOnConditionStep.toString());

        Map<String, ClaimDetailPolicyBo> policyMap = new HashMap<>();
        Map<String, PolicyApplicantBo> policyApplicantMap = new HashMap<>();
        Map<String, PolicyInsuredBo> policyInsuredMap = new HashMap<>();
        Map<String, PolicyCoverageBo> policyCoverageMap = new HashMap<>();
        // 执行sql
        fetchDetailPolicyHistorySql(selectOnConditionStep, policyMap, policyApplicantMap, policyInsuredMap, policyCoverageMap);

        return transClaimDetailPolicyBos(policyMap, policyApplicantMap, policyInsuredMap, policyCoverageMap);
    }

    public List<ClaimDetailPolicyBo> listDetailPolicyHistoryByPolicyId1(String policyId, String customerId, Long dataEffectiveDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_HISTORY.POLICY_ID.eq(policyId));
        if (AssertUtils.isNotNull(dataEffectiveDate)) {
            conditions.add(POLICY_HISTORY.DATA_EFFECTIVE_DATE.le(dataEffectiveDate));
        }
        // 只查个险
        if (!AssertUtils.isNotEmpty(customerId)) {
            conditions.add(POLICY_HISTORY.POLICY_TYPE.in(LIFE_INSURANCE_PERSONAL.name(), STATUTORY_TRAVEL_ACCIDENT_INSURANCE.name()));
        } else {
            conditions.add(POLICY_INSURED_HISTORY.CUSTOMER_ID.eq(customerId));
        }
        // 获取sql
        SelectOnConditionStep<Record> selectOnConditionStep = getDetailPolicyHistorySqlById1(customerId);
        selectOnConditionStep.where(conditions);

        System.out.println(selectOnConditionStep.toString());

        Map<String, ClaimDetailPolicyBo> policyMap = new HashMap<>();
        Map<String, PolicyCoverageBo> policyCoverageMap = new HashMap<>();
        // 执行sql
        selectOnConditionStep.fetch().map(record -> {
            // 保单信息
            ClaimDetailPolicyBo claimDetailPolicyBo = BasePojo.getInstance(ClaimDetailPolicyBo.class, record.into(PolicyHistoryRecord.class));
            // 险种
            PolicyCoverageBo policyCoverageBo = BasePojo.getInstance(PolicyCoverageBo.class, record.into(PolicyCoverageHistoryRecord.class));

            String historyPolicyStatus = (String) record.getValue("historyPolicyStatus");
            claimDetailPolicyBo.setHistoryPolicyStatus(historyPolicyStatus);

            String historyCoverageStatus = (String) record.getValue("historyCoverageStatus");
            policyCoverageBo.setHistoryCoverageStatus(historyCoverageStatus);

            // 保单
            if (!AssertUtils.isNotNull(policyMap.get(claimDetailPolicyBo.getPolicyId()))) {
                policyMap.put(claimDetailPolicyBo.getPolicyId(), claimDetailPolicyBo);
            }
            // 险种
            if (!AssertUtils.isNotNull(policyCoverageMap.get(policyCoverageBo.getCoverageId()))) {
                policyCoverageMap.put(policyCoverageBo.getCoverageId(), policyCoverageBo);
            }
            return null;
        });
        List<ClaimDetailPolicyBo> claimDetailPolicyBos = new ArrayList<>();
        for (String s : policyMap.keySet()) {
            ClaimDetailPolicyBo claimDetailPolicyBo = policyMap.get(s);
            // 险种
            List<PolicyCoverageBo> policyCoverageBos = new ArrayList<>();
            policyCoverageMap.keySet().forEach(coverageId -> {
                if (s.equals(policyCoverageMap.get(coverageId).getPolicyId())) {
                    policyCoverageBos.add(policyCoverageMap.get(coverageId));
                }
            });
            claimDetailPolicyBo.setListCoverage(policyCoverageBos);
            claimDetailPolicyBos.add(claimDetailPolicyBo);
        }
        return claimDetailPolicyBos;
    }


    /**
     * 查询保单基础信息sql
     *
     * @return
     */
    private SelectOnConditionStep<Record> getSimplePolicySql() {
        return this.getDslContext()
                .select(POLICY.POLICY_ID)
                .select(POLICY.POLICY_NO)
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_INSURED.NAME.as("insuredName"))
                .select(POLICY.EFFECTIVE_DATE)
                .select(POLICY.POLICY_TYPE)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.POLICY_STATUS.as("historyPolicyStatus"))
                .select(POLICY.VERSION_NO)
                .from(POLICY)
                .leftJoin(POLICY_COVERAGE)
                .on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID)
                        .and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())))
                .leftJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .leftJoin(POLICY_INSURED).on(POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID));
    }

    /**
     * 查询保单基础信息sql
     *
     * @return
     */
    private SelectOnConditionStep<Record> getSimpleGroupPolicySql() {
        return this.getDslContext()
                .select(POLICY.POLICY_ID)
                .select(POLICY.POLICY_NO)
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .select(POLICY_APPLICANT.COMPANY_NAME.as("applicantName"))
                .select(POLICY_INSURED.NAME.as("insuredName"))
                .select(POLICY_INSURED_EXTEND.EFFECTIVE_DATE.as("effectiveDate"))
                .select(POLICY_INSURED_EXTEND.INSURED_STATUS)
                .select(POLICY_INSURED_EXTEND.INSURED_STATUS.as("historyInsuredStatus"))
                .select(POLICY.POLICY_TYPE)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.POLICY_STATUS.as("historyPolicyStatus"))
                .select(POLICY.VERSION_NO)
                .from(POLICY)
                .leftJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .leftJoin(POLICY_INSURED).on(POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID))
                .leftJoin(POLICY_INSURED_EXTEND).on(POLICY.POLICY_ID.eq(POLICY_INSURED_EXTEND.POLICY_ID).and(POLICY_INSURED_EXTEND.INSURED_ID.eq(POLICY_INSURED.INSURED_ID)))
                .leftJoin(POLICY_COVERAGE).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID)
                        .and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).and(POLICY_COVERAGE.INSURED_ID.eq(POLICY_INSURED.INSURED_ID)));
    }

    /**
     * 查询历史保单基础信息
     *
     * @return
     */
    private SelectOnConditionStep<Record> getSimplePolicyHistorySql() {
        return this.getDslContext()
                .select(POLICY_APPLICANT_HISTORY.POLICY_ID)
                .select(POLICY_HISTORY.POLICY_NO)
                .select(POLICY_COVERAGE_HISTORY.PRODUCT_NAME)
                .select(POLICY_APPLICANT_HISTORY.NAME.as("applicantName"))
                .select(POLICY_INSURED_HISTORY.NAME.as("insuredName"))
                .select(POLICY_HISTORY.EFFECTIVE_DATE)
                .select(POLICY_HISTORY.POLICY_TYPE)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY_HISTORY.POLICY_STATUS.as("historyPolicyStatus"))
                .select(POLICY_HISTORY.VERSION_NO)
                .from(POLICY_HISTORY)
                .leftJoin(POLICY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_COVERAGE_HISTORY.POLICY_ID)
                        .and(POLICY_COVERAGE_HISTORY.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_COVERAGE_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_APPLICANT_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_APPLICANT_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_APPLICANT_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_INSURED_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_INSURED_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_INSURED_HISTORY.VERSION_NO)));
    }

    /**
     * 查询历史保单基础信息
     *
     * @return
     */
    private SelectOnConditionStep<Record> getSimpleGroupPolicyHistorySql() {
        return this.getDslContext()
                .selectDistinct(POLICY_APPLICANT_HISTORY.POLICY_ID)
                .select(POLICY_HISTORY.POLICY_NO)
                .select(POLICY_HISTORY.DATA_EFFECTIVE_DATE)
                .select(POLICY_COVERAGE_HISTORY.PRODUCT_NAME)
                .select(POLICY_APPLICANT_HISTORY.COMPANY_NAME.as("applicantName"))
                .select(POLICY_INSURED_HISTORY.NAME.as("insuredName"))
                .select(POLICY_INSURED_EXTEND_HISTORY.EFFECTIVE_DATE)
                .select(POLICY_INSURED_EXTEND.INSURED_STATUS)
                .select(POLICY_INSURED_EXTEND_HISTORY.INSURED_STATUS.as("historyInsuredStatus"))
                .select(POLICY_HISTORY.POLICY_TYPE)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY_HISTORY.POLICY_STATUS.as("historyPolicyStatus"))
                .select(POLICY_HISTORY.VERSION_NO)
                .from(POLICY_HISTORY)
                .leftJoin(POLICY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_APPLICANT_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_APPLICANT_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_APPLICANT_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_INSURED_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_INSURED_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_INSURED_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_INSURED_EXTEND_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_INSURED_EXTEND_HISTORY.POLICY_ID)
                        .and(POLICY_INSURED_EXTEND_HISTORY.INSURED_ID.eq(POLICY_INSURED_HISTORY.INSURED_ID)).and(POLICY_HISTORY.VERSION_NO.eq(POLICY_INSURED_EXTEND_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_INSURED).on(POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID), POLICY_INSURED.INSURED_ID.eq(POLICY_INSURED_HISTORY.INSURED_ID))
                .leftJoin(POLICY_INSURED_EXTEND).on(POLICY.POLICY_ID.eq(POLICY_INSURED_EXTEND.POLICY_ID)
                        .and(POLICY_INSURED_EXTEND.INSURED_ID.eq(POLICY_INSURED.INSURED_ID)))
                .leftJoin(POLICY_COVERAGE_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_COVERAGE_HISTORY.POLICY_ID)
                        .and(POLICY_COVERAGE_HISTORY.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_COVERAGE_HISTORY.VERSION_NO)).and(POLICY_COVERAGE_HISTORY.INSURED_ID.eq(POLICY_INSURED_HISTORY.INSURED_ID)));
    }

    /**
     * 查询保单详细信息sql
     */
    private SelectOnConditionStep<Record> getDetailPolicySql() {
        return this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY.POLICY_STATUS.as("historyPolicyStatus"))
                .select(POLICY_APPLICANT.fields())
                .select(POLICY_INSURED.fields())
                .select(POLICY_COVERAGE.fields())
                .select(POLICY_COVERAGE.COVERAGE_STATUS.as("historyCoverageStatus"))
                .from(POLICY)
                .leftJoin(POLICY_COVERAGE)
                .on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID))
                .leftJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .leftJoin(POLICY_INSURED).on(POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID));
    }

    /**
     * 查询保单详细信息sql
     */
    private SelectOnConditionStep<Record> getDetailPolicySqlById(String customerId) {
        Condition eq = AssertUtils.isNotEmpty(customerId) ?
                POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID).and(POLICY_INSURED.CUSTOMER_ID.eq(customerId)) :
                POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID);
        return this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY.POLICY_STATUS.as("historyPolicyStatus"))
                .select(POLICY_APPLICANT.fields())
                .select(POLICY_INSURED.fields())
                .select(POLICY_COVERAGE.fields())
                .select(POLICY_COVERAGE.COVERAGE_STATUS.as("historyCoverageStatus"))
                .from(POLICY)
                .leftJoin(POLICY_APPLICANT).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .leftJoin(POLICY_INSURED).on(eq)
                .leftJoin(POLICY_COVERAGE).on(POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID).and(POLICY_COVERAGE.INSURED_ID.eq(POLICY_INSURED.INSURED_ID)));
    }

    /**
     * 执行sql
     */
    private void fetchDetailPolicySql(SelectOnConditionStep<Record> selectOnConditionStep, Map<String, ClaimDetailPolicyBo> policyMap, Map<String, PolicyApplicantBo> policyApplicantMap, Map<String, PolicyInsuredBo> policyInsuredMap, Map<String, PolicyCoverageBo> policyCoverageMap) {
        selectOnConditionStep.fetch().map(record -> {
            // 保单信息
            ClaimDetailPolicyBo claimDetailPolicyBo = BasePojo.getInstance(ClaimDetailPolicyBo.class, record.into(PolicyRecord.class));
            // 投保人
            PolicyApplicantBo policyApplicantBo = BasePojo.getInstance(PolicyApplicantBo.class, record.into(PolicyApplicantRecord.class));
            // 被保人
            PolicyInsuredBo policyInsuredBo = BasePojo.getInstance(PolicyInsuredBo.class, record.into(PolicyInsuredRecord.class));
            // 险种
            PolicyCoverageBo policyCoverageBo = BasePojo.getInstance(PolicyCoverageBo.class, record.into(PolicyCoverageRecord.class));

            String historyPolicyStatus = (String) record.getValue("historyPolicyStatus");
            claimDetailPolicyBo.setHistoryPolicyStatus(historyPolicyStatus);
            String historyCoverageStatus = (String) record.getValue("historyCoverageStatus");
            policyCoverageBo.setHistoryCoverageStatus(historyCoverageStatus);

            // 保单
            if (!AssertUtils.isNotNull(policyMap.get(claimDetailPolicyBo.getPolicyId()))) {
                policyMap.put(claimDetailPolicyBo.getPolicyId(), claimDetailPolicyBo);
            }
            // 投保人
            if (!AssertUtils.isNotNull(policyApplicantMap.get(policyApplicantBo.getPolicyId()))) {
                policyApplicantMap.put(policyApplicantBo.getPolicyId(), policyApplicantBo);
            }
            // 被保人
            if (!AssertUtils.isNotNull(policyInsuredMap.get(policyInsuredBo.getInsuredId()))) {
                policyInsuredMap.put(policyInsuredBo.getInsuredId(), policyInsuredBo);
            }
            // 险种
            if (!AssertUtils.isNotNull(policyCoverageMap.get(policyCoverageBo.getCoverageId()))) {
                policyCoverageMap.put(policyCoverageBo.getCoverageId(), policyCoverageBo);
            }

            return null;
        });
    }

    /**
     * 查询历史保单详细信息sql
     */
    private SelectOnConditionStep<Record> getDetailPolicyHistorySql() {
        return this.getDslContext()
                .select(POLICY_HISTORY.fields())
                .select(POLICY_HISTORY.POLICY_STATUS.as("historyPolicyStatus"))
                .select(POLICY.POLICY_STATUS.as("currPolicyStatus"))
                .select(POLICY_APPLICANT_HISTORY.fields())
                .select(POLICY_INSURED_HISTORY.fields())
                .select(POLICY_COVERAGE_HISTORY.fields())
                .select(POLICY_COVERAGE_HISTORY.COVERAGE_STATUS.as("historyCoverageStatus"))
                .select(POLICY_COVERAGE.COVERAGE_STATUS.nvl(PolicyTermEnum.COVERAGE_STATUS.INVALID.name()).as("currCoverageStatus"))
                .from(POLICY_HISTORY)
                .leftJoin(POLICY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_COVERAGE_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_COVERAGE_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_APPLICANT_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_APPLICANT_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_APPLICANT_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_INSURED_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_INSURED_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_INSURED_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID)
                        .and(POLICY_COVERAGE.PRODUCT_ID.eq(POLICY_COVERAGE_HISTORY.PRODUCT_ID)));
    }

    /**
     * 查询历史保单详细信息sql
     */
    private SelectOnConditionStep<Record> getDetailPolicyHistorySqlById(String customerId) {
        Condition condition = AssertUtils.isNotEmpty(customerId) ?
                POLICY_HISTORY.POLICY_ID.eq(POLICY_INSURED_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_INSURED_HISTORY.VERSION_NO)).and(POLICY_INSURED_HISTORY.CUSTOMER_ID.eq(customerId)) :
                POLICY_HISTORY.POLICY_ID.eq(POLICY_INSURED_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_INSURED_HISTORY.VERSION_NO));

        return this.getDslContext()
                .selectDistinct(POLICY_HISTORY.fields())
                .select(POLICY_HISTORY.POLICY_STATUS.as("historyPolicyStatus"))
                .select(POLICY.POLICY_STATUS.as("currPolicyStatus"))
                .select(POLICY_APPLICANT_HISTORY.fields())
                .select(POLICY_INSURED_HISTORY.fields())
                .select(POLICY_COVERAGE_HISTORY.fields())
                .select(POLICY_COVERAGE_HISTORY.COVERAGE_STATUS.nvl(PolicyTermEnum.COVERAGE_STATUS.EFFECTIVE.name()).as("historyCoverageStatus"))
                .select(POLICY_COVERAGE.COVERAGE_STATUS.nvl(PolicyTermEnum.COVERAGE_STATUS.INVALID.name()).as("currCoverageStatus"))
                .from(POLICY_HISTORY)
                .leftJoin(POLICY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_APPLICANT_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_APPLICANT_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_APPLICANT_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_INSURED_HISTORY).on(condition)
                .leftJoin(POLICY_INSURED).on(POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID), POLICY_INSURED_HISTORY.INSURED_ID.eq(POLICY_INSURED.INSURED_ID))
                .leftJoin(POLICY_COVERAGE_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_COVERAGE_HISTORY.POLICY_ID)
                        .and(POLICY_INSURED_HISTORY.INSURED_ID.eq(POLICY_COVERAGE_HISTORY.INSURED_ID))
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_COVERAGE_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID)
                        .and(POLICY_INSURED.INSURED_ID.eq(POLICY_COVERAGE.INSURED_ID))
                        .and(POLICY_COVERAGE.COVERAGE_ID.eq(POLICY_COVERAGE_HISTORY.COVERAGE_ID)));
    }

    /**
     * 查询历史保单详细信息sql
     */
    private SelectOnConditionStep<Record> getDetailPolicyHistorySqlById1(String customerId) {
        Condition condition = AssertUtils.isNotEmpty(customerId) ?
                POLICY_HISTORY.POLICY_ID.eq(POLICY_INSURED_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_INSURED_HISTORY.VERSION_NO)).and(POLICY_INSURED_HISTORY.CUSTOMER_ID.eq(customerId)) :
                POLICY_HISTORY.POLICY_ID.eq(POLICY_INSURED_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_INSURED_HISTORY.VERSION_NO));

        return this.getDslContext()
                .select(POLICY_HISTORY.fields())
                .select(POLICY_HISTORY.POLICY_STATUS.as("historyPolicyStatus"))
                .select(POLICY.POLICY_STATUS.as("currPolicyStatus"))
                .select(POLICY_COVERAGE_HISTORY.fields())
                .select(POLICY_COVERAGE_HISTORY.COVERAGE_STATUS.nvl(PolicyTermEnum.COVERAGE_STATUS.EFFECTIVE.name()).as("historyCoverageStatus"))
                .from(POLICY_HISTORY)
                .leftJoin(POLICY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_INSURED_HISTORY).on(condition)
                .leftJoin(POLICY_COVERAGE_HISTORY).on(POLICY_HISTORY.POLICY_ID.eq(POLICY_COVERAGE_HISTORY.POLICY_ID)
                        .and(POLICY_INSURED_HISTORY.INSURED_ID.eq(POLICY_COVERAGE_HISTORY.INSURED_ID))
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_COVERAGE_HISTORY.VERSION_NO)));
    }

    /**
     * 执行sql
     */
    private void fetchDetailPolicyHistorySql(SelectOnConditionStep<Record> selectOnConditionStep, Map<String, ClaimDetailPolicyBo> policyMap, Map<String, PolicyApplicantBo> policyApplicantMap, Map<String, PolicyInsuredBo> policyInsuredMap, Map<String, PolicyCoverageBo> policyCoverageMap) {
        selectOnConditionStep.fetch().map(record -> {
            // 保单信息
            ClaimDetailPolicyBo claimDetailPolicyBo = BasePojo.getInstance(ClaimDetailPolicyBo.class, record.into(PolicyHistoryRecord.class));
            // 投保人
            PolicyApplicantBo policyApplicantBo = BasePojo.getInstance(PolicyApplicantBo.class, record.into(PolicyApplicantHistoryRecord.class));
            // 被保人
            PolicyInsuredBo policyInsuredBo = BasePojo.getInstance(PolicyInsuredBo.class, record.into(PolicyInsuredHistoryRecord.class));
            // 险种
            PolicyCoverageBo policyCoverageBo = BasePojo.getInstance(PolicyCoverageBo.class, record.into(PolicyCoverageHistoryRecord.class));

            String historyPolicyStatus = (String) record.getValue("historyPolicyStatus");
            String currPolicyStatus = (String) record.getValue("currPolicyStatus");
            claimDetailPolicyBo.setHistoryPolicyStatus(historyPolicyStatus);
            claimDetailPolicyBo.setPolicyStatus(currPolicyStatus);

            String historyCoverageStatus = (String) record.getValue("historyCoverageStatus");
            String currCoverageStatus = (String) record.getValue("currCoverageStatus");
            policyCoverageBo.setHistoryCoverageStatus(historyCoverageStatus);
            policyCoverageBo.setCoverageStatus(currCoverageStatus);

            // 保单
            if (!AssertUtils.isNotNull(policyMap.get(claimDetailPolicyBo.getPolicyId()))) {
                policyMap.put(claimDetailPolicyBo.getPolicyId(), claimDetailPolicyBo);
            }
            // 投保人
            if (!AssertUtils.isNotNull(policyApplicantMap.get(policyApplicantBo.getPolicyId()))) {
                policyApplicantMap.put(policyApplicantBo.getPolicyId(), policyApplicantBo);
            }
            // 被保人
            if (!AssertUtils.isNotNull(policyInsuredMap.get(policyInsuredBo.getInsuredId()))) {
                policyInsuredMap.put(policyInsuredBo.getInsuredId(), policyInsuredBo);
            }
            // 险种
            if (!AssertUtils.isNotNull(policyCoverageMap.get(policyCoverageBo.getCoverageId()))) {
                policyCoverageMap.put(policyCoverageBo.getCoverageId(), policyCoverageBo);
            }

            return null;
        });
    }

    /**
     * 组装理赔保单数据
     */
    private List<ClaimDetailPolicyBo> transClaimDetailPolicyBos(Map<String, ClaimDetailPolicyBo> policyMap, Map<String, PolicyApplicantBo> policyApplicantMap, Map<String, PolicyInsuredBo> policyInsuredMap, Map<String, PolicyCoverageBo> policyCoverageMap) {
        List<ClaimDetailPolicyBo> claimDetailPolicyBos = new ArrayList<>();
        for (String policyId : policyMap.keySet()) {
            ClaimDetailPolicyBo claimDetailPolicyBo = policyMap.get(policyId);
            claimDetailPolicyBo.setApplicant(policyApplicantMap.get(policyId));
            // 被保人
            List<PolicyInsuredBo> policyInsuredBos = new ArrayList<>();
            policyInsuredMap.keySet().forEach(insuredId -> {
                if (policyId.equals(policyInsuredMap.get(insuredId).getPolicyId())) {
                    policyInsuredBos.add(policyInsuredMap.get(insuredId));
                }
            });
            claimDetailPolicyBo.setListInsured(policyInsuredBos);
            // 险种
            List<PolicyCoverageBo> policyCoverageBos = new ArrayList<>();
            policyCoverageMap.keySet().forEach(coverageId -> {
                if (policyId.equals(policyCoverageMap.get(coverageId).getPolicyId())) {
                    policyCoverageBos.add(policyCoverageMap.get(coverageId));
                }
            });
            claimDetailPolicyBo.setListCoverage(policyCoverageBos);
            claimDetailPolicyBos.add(claimDetailPolicyBo);
        }
        return claimDetailPolicyBos;
    }

    @Override
    public List<PolicyBeneficiaryInfoBo> queryPolicyBeneficiaryInfoBo(String policyId) {
        List<PolicyBeneficiaryInfoBo> policyBeneficiaryInfoBos;
        policyBeneficiaryInfoBos = this.getDslContext()
                .select(POLICY_BENEFICIARY_INFO.fields())
                .select(POLICY_BENEFICIARY.fields())
                .from(POLICY_BENEFICIARY)
                .innerJoin(POLICY_BENEFICIARY_INFO).on(POLICY_BENEFICIARY_INFO.BENEFICIARY_ID.eq(POLICY_BENEFICIARY.BENEFICIARY_ID))
                .where(POLICY_BENEFICIARY.POLICY_ID.eq(policyId))
                .and(POLICY_BENEFICIARY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .orderBy(POLICY_BENEFICIARY_INFO.BENEFICIARY_NO_ORDER.sortAsc(Arrays.stream(PolicyTermEnum.BENEFICIARY_NO.values()).map(PolicyTermEnum.BENEFICIARY_NO::name).collect(Collectors.toList())))
                .fetch().map(record -> {
                    PolicyBeneficiaryInfoBo policyBeneficiaryInfoBo = BasePojo.getInstance(PolicyBeneficiaryInfoBo.class, record.into(PolicyBeneficiaryInfoRecord.class));
                    PolicyBeneficiaryBo policyBeneficiaryBo = BasePojo.getInstance(PolicyBeneficiaryBo.class, record.into(PolicyBeneficiaryRecord.class));
                    if (AssertUtils.isNotEmpty(policyBeneficiaryInfoBo.getPolicyBeneficiaryId())) {
                        policyBeneficiaryInfoBo.setPolicyBeneficiary(policyBeneficiaryBo);
                    }
                    return policyBeneficiaryInfoBo;
                });
        return policyBeneficiaryInfoBos;
    }

    /**
     * 需要设置的投保人代表数据
     *
     * @return
     */
    @Override
    public List<PolicyGroupReportSyncApplicantBo> querySyncApplicantCustomer() {
        return this.getDslContext().select(POLICY_AGENT.AGENT_ID)
                .select(POLICY_APPLICANT.DELEGATE_NAME,
                        POLICY_APPLICANT.DELEGATE_ID_NO,
                        POLICY_APPLICANT.DELEGATE_ID_TYPE,
                        POLICY_APPLICANT.DELEGATE_MOBILE,
                        POLICY_APPLICANT.DELEGATE_BIRTHDAY)
                .from(POLICY_APPLICANT)
                .leftJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID).and(POLICY_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .where(POLICY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                        .and(POLICY_APPLICANT.APPLICANT_TYPE.eq(PolicyTermEnum.APPLICANT_TYPE.GROUP.name()))
                        .and(POLICY_AGENT.AGENT_ID.isNotNull())
                        .and(POLICY_APPLICANT.DELEGATE_ID_TYPE.isNotNull())
                        .and(POLICY_APPLICANT.DELEGATE_ID_NO.isNotNull()))
                .groupBy(POLICY_AGENT.AGENT_ID,
                        POLICY_APPLICANT.DELEGATE_NAME,
                        POLICY_APPLICANT.DELEGATE_ID_NO,
                        POLICY_APPLICANT.DELEGATE_ID_TYPE,
                        POLICY_APPLICANT.DELEGATE_MOBILE,
                        POLICY_APPLICANT.DELEGATE_BIRTHDAY).fetchInto(PolicyGroupReportSyncApplicantBo.class);
    }

    /**
     * 需要设置的投保人代表Po数据
     *
     * @return
     */
    @Override
    public List<PolicyApplicantPo> queryApplicantCustomer() {
        return this.getDslContext().selectFrom(POLICY_APPLICANT)
                .where(POLICY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                        .and(POLICY_APPLICANT.APPLICANT_TYPE.eq(PolicyTermEnum.APPLICANT_TYPE.GROUP.name()))
                        .and(POLICY_APPLICANT.DELEGATE_ID_TYPE.isNotNull())
                        .and(POLICY_APPLICANT.DELEGATE_ID_NO.isNotNull())
                        .and(POLICY_APPLICANT.DELEGATE_CUSTOMER_ID.isNull()))
                .fetchInto(PolicyApplicantPo.class);
    }

    @Override
    public Integer countNewApplicantByCustomerIds(List<String> customerIds) {
        return this.getDslContext().selectCount().from(POLICY_APPLICANT).where(POLICY_APPLICANT.CUSTOMER_ID.notIn(customerIds)
                .or(POLICY_APPLICANT.DELEGATE_CUSTOMER_ID.notIn(customerIds))).fetchOneInto(Integer.class);
    }

    @Override
    public Integer countNewInsuredByCustomerIds(List<String> customerIds) {
        return null;
    }

    @Override
    public List<ActualPerformanceReportBo> queryActualPerformance(List<String> policyIdList) {
        Table<Record> policy_coverage_payment = this.getDslContext()
                .select(POLICY_COVERAGE_PAYMENT.ACTUAL_PREMIUM.as(POLICY_COVERAGE_PAYMENT.ACTUAL_PREMIUM))
                .select(POLICY_COVERAGE_PAYMENT.COVERAGE_ID)
                .select(POLICY_COVERAGE_PAYMENT.FREQUENCY)
                .select(POLICY_PAYMENT.POLICY_YEAR)
                .from(POLICY_COVERAGE_PAYMENT)
                .leftJoin(POLICY_PAYMENT).on(POLICY_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID))
                .where(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID.in(policyIdList)).asTable();

        Field<BigDecimal> totalAmount = DSL.field("to_number({0}, '999999999999999999999999999999.99')", SQLDataType.NUMERIC, POLICY_COVERAGE.TOTAL_AMOUNT.decode("", "0", null, "0", POLICY_COVERAGE.TOTAL_AMOUNT));
        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(POLICY.APPLY_ID)
                .select(POLICY.POLICY_ID)
                .select(POLICY.APPLY_NO)
                .select(POLICY.POLICY_NO)
                .select(POLICY.EFFECTIVE_DATE)
                .select(POLICY_INSURED.BIRTHDAY.as("insuredBirthday"))
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_COVERAGE.PREMIUM_FREQUENCY)
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_COVERAGE.PRODUCT_CODE)
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .select(POLICY_COVERAGE.PRIMARY_FLAG)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT)
                .select(POLICY_COVERAGE.AMOUNT)
                .select(POLICY_COVERAGE.PREMIUM)
                .select(totalAmount.as(POLICY_COVERAGE.TOTAL_AMOUNT))
                .select(POLICY_COVERAGE.PRODUCT_LEVEL)
                .select(POLICY_COVERAGE.MULT.nvl("1").as(POLICY_COVERAGE.MULT))
                .select(policy_coverage_payment.field(POLICY_COVERAGE_PAYMENT.ACTUAL_PREMIUM).as(POLICY_COVERAGE.TOTAL_PREMIUM),
                        policy_coverage_payment.field(POLICY_COVERAGE_PAYMENT.FREQUENCY).as("frequency"),
                        policy_coverage_payment.field(POLICY_PAYMENT.POLICY_YEAR).as("policyYear"))
                .select(DSL.field("'RESOURCE_APPLY_INDIVIDUAL'").as("coverageType"))
                .from(POLICY)
                .leftJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_INSURED).on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name())))
                .leftJoin(POLICY_COVERAGE).on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID).and(POLICY_COVERAGE.INSURED_ID.isNotNull()))
                .leftJoin(policy_coverage_payment).on(POLICY_COVERAGE.COVERAGE_ID.eq(policy_coverage_payment.field(POLICY_COVERAGE.COVERAGE_ID)));

        selectOnConditionStep.where(POLICY.POLICY_ID.in(policyIdList));
        selectOnConditionStep.orderBy(policy_coverage_payment.field(POLICY_COVERAGE_PAYMENT.FREQUENCY));
        selectOnConditionStep.orderBy(POLICY_COVERAGE.PRIMARY_FLAG.desc());
        selectOnConditionStep.orderBy(POLICY_COVERAGE.PRODUCT_ID);
        return selectOnConditionStep.fetchInto(ActualPerformanceReportBo.class);
    }

    @Override
    public List<ActualPerformanceReportBo> queryPolicyVersionActualPerformance(List<String> versionNo) {
        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(POLICY_HISTORY.POLICY_ID)
                .select(POLICY_HISTORY.VERSION_NO.as("oldVersionNo"))
                .select(POLICY_COVERAGE_HISTORY.PRODUCT_ID)
                .select(POLICY_COVERAGE_HISTORY.PREMIUM)
                .select(POLICY_COVERAGE_HISTORY.AMOUNT)
                .select(POLICY_COVERAGE_HISTORY.TOTAL_AMOUNT)
                .select(POLICY_COVERAGE_HISTORY.PREMIUM_FREQUENCY)
                .select(DSL.field("'1'").as(POLICY_COVERAGE_HISTORY.MULT))
                .from(POLICY_HISTORY)
                .leftJoin(POLICY_COVERAGE_HISTORY)
                .on(POLICY_HISTORY.POLICY_ID.eq(POLICY_COVERAGE_HISTORY.POLICY_ID)
                        .and(POLICY_HISTORY.VERSION_NO.eq(POLICY_COVERAGE_HISTORY.VERSION_NO)));
        selectOnConditionStep.where(POLICY_HISTORY.VERSION_NO.in(versionNo));
        return selectOnConditionStep.fetchInto(ActualPerformanceReportBo.class);
    }

    /**
     * 保单ID查询受益人信息
     *
     * @param policyId
     * @param modifyFlag
     * @return
     */
    @Override
    public List<PolicyBeneficiaryInfoBo> queryPolicyLoanBeneficiary(String policyId, String modifyFlag) {
        return this.getDslContext()
                .select(POLICY_BENEFICIARY.fields())
                .select(POLICY_BENEFICIARY_INFO.fields())
                .from(POLICY_BENEFICIARY)
                .innerJoin(POLICY_BENEFICIARY_INFO).on(POLICY_BENEFICIARY.BENEFICIARY_ID.eq(POLICY_BENEFICIARY_INFO.BENEFICIARY_ID))
                .where(POLICY_BENEFICIARY.POLICY_ID.eq(policyId).and(POLICY_BENEFICIARY_INFO.MODIFY_FLAG.eq(modifyFlag)))
                .fetch().stream().map(record -> {
                    //受益人
                    PolicyBeneficiaryBo policyBeneficiaryBo = BasePojo.getInstance(PolicyBeneficiaryBo.class, record.into(PolicyBeneficiaryRecord.class));
                    PolicyBeneficiaryInfoBo policyBeneficiaryInfoBo = BasePojo.getInstance(PolicyBeneficiaryInfoBo.class, record.into(PolicyBeneficiaryInfoRecord.class));
                    policyBeneficiaryInfoBo.setPolicyBeneficiary(policyBeneficiaryBo);
                    return policyBeneficiaryInfoBo;
                }).collect(Collectors.toList());
    }

    /**
     * 查询保单信息
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public PolicyBo queryPolicy(String policyId) {
        return this.getDslContext()
                .selectFrom(POLICY)
                .where(POLICY.POLICY_ID.eq(policyId))
                .and(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(PolicyBo.class);
    }


    @Override
    public List<PolicyInsuredBo> getPolicyAllInsuredList(String policyId) {
        List<PolicyInsuredBo> policyInsuredBos;
        try {
            policyInsuredBos = this.getDslContext()
                    .select(POLICY_INSURED.fields())
                    .select(POLICY_INSURED_EXTEND.INSURED_STATUS)
                    .select(POLICY_INSURED_EXTEND.ADD_DATE)
                    .select(POLICY_INSURED_EXTEND.EFFECTIVE_DATE)
                    .select(POLICY_INSURED_EXTEND.INVALID_DATE)
                    .from(POLICY_INSURED)
                    .leftJoin(POLICY_INSURED_EXTEND).on(POLICY_INSURED.POLICY_ID.eq(POLICY_INSURED_EXTEND.POLICY_ID)
                            .and(POLICY_INSURED.INSURED_ID.eq(POLICY_INSURED_EXTEND.INSURED_ID)))
                    .where(POLICY_INSURED.POLICY_ID.eq(policyId))
                    .orderBy(POLICY_INSURED.CREATED_DATE.asc(), POLICY_INSURED.NAME.asc())
                    .fetchInto(PolicyInsuredBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_ERROR);
        }
        return policyInsuredBos;
    }

    @Override
    public List<PolicyCoverageDutyBo> queryPolicyCoverageDutyValidFlagByCoverageIds(List<String> coverageIds) {
        return this.getDslContext().select(POLICY_COVERAGE_DUTY.fields()).from(POLICY_COVERAGE_DUTY)
                .where(POLICY_COVERAGE_DUTY.COVERAGE_ID.in(coverageIds)).fetchInto(PolicyCoverageDutyBo.class);
    }

    @Override
    public List<ReserveWithdrawalReportBo> quarterlyStatisticsReserveWithdrawalReport(String quarterDate, BasePageRequest basePageRequest) {
        List<Condition> conditionList = new ArrayList<>();

        List<Condition> policyPaymentConditionList = new ArrayList<>();

        if (AssertUtils.isNotEmpty(quarterDate)) {
            policyPaymentConditionList.add(POLICY_PAYMENT.POLICY_ID.eq(POLICY.POLICY_ID));
            policyPaymentConditionList.add(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE.in(
                    GROUP_RENEWAL.name(),
                    BUSINESS_TYPE_RENEWAL_INSURANCE.name(),
                    BUSINESS_TYPE_NEW_CONTRACT.name()));
            policyPaymentConditionList.add(POLICY_PAYMENT.PAYMENT_STATUS_CODE.in(PAYMENT_SUCCESS.name(), PAYMENT_FINISHED.name()));
            policyPaymentConditionList.add(POLICY.APPROVE_DATE.between(DateUtils.getThisMonthFirstDay(quarterDate, DateUtils.FORMATE2), DateUtils.getThisMonthLastDay(quarterDate, DateUtils.FORMATE2)));
            quarterDate = DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE2);
            policyPaymentConditionList.add(POLICY_PAYMENT.CREATED_DATE.between(DateUtils.getThisMonthFirstDay(quarterDate, DateUtils.FORMATE2), DateUtils.getThisMonthLastDay(quarterDate, DateUtils.FORMATE2)));
            conditionList.add(POLICY_PAYMENT.POLICY_PAYMENT_ID.isNotNull());
        } else {
            policyPaymentConditionList.add(DSL.condition(" 1=2 "));
        }

        Field<String> levelJson = DSL.field("string_agg ( {0}, ',' ORDER BY {0} ASC )", SQLDataType.VARCHAR, POLICY_COVERAGE_LEVEL.PRODUCT_LEVEL);
        Field<String> multJson = DSL.field("string_agg ( {0}, ',' ORDER BY {1} ASC )", SQLDataType.VARCHAR, POLICY_COVERAGE_LEVEL.MULT, POLICY_COVERAGE_LEVEL.PRODUCT_LEVEL);
        Table<Record> coverageLevelTable = this.getDslContext()
                .select(POLICY_COVERAGE_LEVEL.COVERAGE_ID)
                .select(POLICY_COVERAGE_LEVEL.COVERAGE_DUTY_ID)
                .select(POLICY_COVERAGE_LEVEL.ACTUAL_PREMIUM.sum().as(POLICY_COVERAGE_LEVEL.ACTUAL_PREMIUM))
                .select(levelJson.as(POLICY_COVERAGE_LEVEL.PRODUCT_LEVEL))
                .select(multJson.as(POLICY_COVERAGE_LEVEL.MULT))
                .select(POLICY_COVERAGE_LEVEL.ORIGINAL_PREMIUM.sum().as(POLICY_COVERAGE_LEVEL.ORIGINAL_PREMIUM))
                .from(POLICY_COVERAGE_LEVEL)
                .groupBy(POLICY_COVERAGE_LEVEL.COVERAGE_ID, POLICY_COVERAGE_LEVEL.COVERAGE_DUTY_ID)
                .asTable();

        Field<String> insuredIdNo = DSL.field("trim(regexp_replace({0}, E'[\\\\n\\\\r]+', '', 'g' ))", SQLDataType.VARCHAR, POLICY_INSURED.ID_NO);

        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(POLICY.POLICY_ID)
                .select(POLICY.POLICY_NO)
                .select(POLICY.APPLY_NO)
                .select(POLICY.SALES_BRANCH_ID)
                .select(POLICY.APPLY_ID)
                .select(POLICY.POLICY_TYPE)
                .select(POLICY_COVERAGE.COVERAGE_ID)
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_COVERAGE.PRIMARY_FLAG)
                .select(POLICY.EFFECTIVE_DATE)
                .select(POLICY.APPLY_DATE.as("applyDate"))
                .select(POLICY_COVERAGE.EFFECTIVE_DATE.as(POLICY_COVERAGE.COVERAGE_PERIOD_START_DATE))
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_END_DATE)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT)
                .select(POLICY_COVERAGE.PREMIUM_FREQUENCY)
                .select(POLICY.CHANNEL_TYPE_CODE)
                .select(POLICY.APPROVE_DATE)
                .select(POLICY_COVERAGE.DUTY_CHOOSE_FLAG)
                .select(coverageLevelTable.field(POLICY_COVERAGE_LEVEL.ORIGINAL_PREMIUM).nvl(POLICY_COVERAGE.ORIGINAL_PREMIUM).as("originalPremium"))
                .select(coverageLevelTable.field(POLICY_COVERAGE_LEVEL.ACTUAL_PREMIUM).nvl(POLICY_COVERAGE.ACTUAL_PREMIUM).as("paymentActualPremium"))
                .select(POLICY_APPLICANT.COMPANY_NAME)
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_APPLICANT.ID_TYPE.as("applicantIdType"))
                .select(POLICY_APPLICANT.ID_NO.as("applicantIdNo"))
                .select(POLICY_APPLICANT.BIRTHDAY.as("applicantBirthday"))
                .select(POLICY_APPLICANT.SEX.as("applicantSex"))
                .select(POLICY_APPLICANT.COMPANY_ID_TYPE)
                .select(POLICY_APPLICANT.COMPANY_ID_NO)
                .select(POLICY.MATURITY_DATE)
                .select(POLICY.INVALID_DATE.nvl(POLICY.THOROUGH_INVALID_DATE).as("policyInvalidDate"))
                .select(POLICY_INSURED.NAME.as("insuredName"))
                .select(POLICY_INSURED.ID_TYPE.as("insuredIdType"))
                .select(insuredIdNo.as("insuredIdNo"))
                .select(POLICY_INSURED.BIRTHDAY.as("insuredBirthday"))
                .select(POLICY_INSURED.MOBILE.as("insuredMobile"))
                .select(POLICY_INSURED.SEX.as("insuredSex"))
                .select(POLICY_INSURED.CUSTOMER_ID.as("insuredCustomerId"))
                .select(POLICY_AGENT.AGENT_ID)
                .select(POLICY_AGENT.AGENT_CODE)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.RISK_COMMENCEMENT_DATE)
                .select(coverageLevelTable.field(POLICY_COVERAGE.PRODUCT_LEVEL).nvl(
                        // 34产品特殊处理  产品档次 = JUNIOR_PACKAGES/5
                        DSL.choose().when(POLICY_COVERAGE.PRODUCT_ID.eq(ProductTermEnum.PRODUCT.PRODUCT_34.id()),
                                        POLICY_COVERAGE.PACKAGE_CODE.concat("/").concat(POLICY_COVERAGE.ACC_SI_MULTIPLE))
                                .otherwise(POLICY_COVERAGE.PRODUCT_LEVEL))
                        .as(POLICY_COVERAGE.PRODUCT_LEVEL))
                .select(coverageLevelTable.field(POLICY_COVERAGE_LEVEL.MULT).nvl(POLICY_COVERAGE.MULT).as(POLICY_COVERAGE.MULT))
                .select(POLICY_COVERAGE.AMOUNT.as(POLICY_COVERAGE.TOTAL_AMOUNT))
                .select(POLICY_COVERAGE_DUTY.DUTY_ID)
                .select(POLICY_INSURED.INSURED_ID)
                .select(POLICY_INSURED_EXTEND.INSURED_STATUS)
                .select(POLICY_INSURED_EXTEND.EFFECTIVE_DATE.nvl(POLICY.EFFECTIVE_DATE).as("insuredEffectiveDate"))
                .select(POLICY_INSURED_EXTEND.INVALID_DATE)
                .select(POLICY_COVERAGE.COVERAGE_STATUS)

                .select(POLICY_LOAN.LOAN_INTEREST_RATE)
                .select(POLICY_LOAN.PAYMENT_WAY)
                .select(POLICY_LOAN.LOAN_AMOUNT)
                .select(POLICY_LOAN.NOT_CONVERTED_LOAN_AMOUNT)
                .select(POLICY_APPLICANT.INCOME.as("applicantIncome"))
                .select(POLICY_INSURED.OCCUPATION_CODE.as("insuredOccupationCode"))
                .select(POLICY_INSURED.OCCUPATION_TYPE.as("insuredOccupationType"))
                .select(POLICY_INSURED.INSURED_TYPE)
                .select(POLICY.POLICY_PERIOD.as("renewalInsuranceFrequency"))
                .select(POLICY_INSURED.INSURED_ID.countOver().as("totalLine"))
                .from(POLICY)
                .leftJoin(POLICY_INSURED)
                .on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE)
                .on(POLICY_COVERAGE.INSURED_ID.eq(POLICY_INSURED.INSURED_ID))
                .leftJoin(coverageLevelTable)
                .on(coverageLevelTable.field(POLICY_COVERAGE_LEVEL.COVERAGE_ID).eq(POLICY_COVERAGE.COVERAGE_ID))
                .leftJoin(POLICY_COVERAGE_DUTY)
                .on(POLICY_COVERAGE_DUTY.COVERAGE_DUTY_ID.eq(coverageLevelTable.field(POLICY_COVERAGE_LEVEL.COVERAGE_DUTY_ID)))
                .leftJoin(POLICY_APPLICANT)
                .on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_AGENT)
                .on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_INSURED_EXTEND)
                .on(POLICY_INSURED_EXTEND.INSURED_ID.eq(POLICY_INSURED.INSURED_ID))
                .leftJoin(POLICY_LOAN)
                .on(POLICY_LOAN.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_PAYMENT)
                .on(policyPaymentConditionList.toArray(new Condition[policyPaymentConditionList.size()]));

        conditionList.add(POLICY.POLICY_TYPE.in(LIFE_INSURANCE_GROUP.name(), LIFE_INSURANCE_PERSONAL.name()));
        conditionList.add(POLICY_COVERAGE.INSURED_ID.isNotNull());
        conditionList.add(POLICY.POLICY_STATUS.ne(POLICY_STATUS_PENDING_EFFECT.name()));
        selectOnConditionStep.where(conditionList);

        selectOnConditionStep.orderBy(POLICY_INSURED.INSURED_ID, POLICY_INSURED.CREATED_DATE, POLICY_COVERAGE.COVERAGE_ID, coverageLevelTable.field(POLICY_COVERAGE_LEVEL.COVERAGE_DUTY_ID), POLICY_COVERAGE_DUTY.COVERAGE_DUTY_ID);

        selectOnConditionStep.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());

        log.info(selectOnConditionStep.toString());

        return selectOnConditionStep.fetchInto(ReserveWithdrawalReportBo.class);
    }

    @Override
    public List<ReserveWithdrawalReportBo> quarterlyHistoryStatisticsReserveWithdrawalReport(String quarterDate, BasePageRequest basePageRequest) {
        List<Condition> conditionList = new ArrayList<>();

        List<Condition> policyPaymentConditionList = new ArrayList<>();

        if (AssertUtils.isNotEmpty(quarterDate)) {
            policyPaymentConditionList.add(POLICY_PAYMENT.POLICY_ID.eq(POLICY.POLICY_ID));
            policyPaymentConditionList.add(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE.in(
                    GROUP_RENEWAL.name(),
                    BUSINESS_TYPE_RENEWAL_INSURANCE.name(),
                    BUSINESS_TYPE_NEW_CONTRACT.name()));
            policyPaymentConditionList.add(POLICY_PAYMENT.PAYMENT_STATUS_CODE.in(PAYMENT_SUCCESS.name(), PAYMENT_FINISHED.name()));
            policyPaymentConditionList.add(POLICY.APPROVE_DATE.between(DateUtils.getThisMonthFirstDay(quarterDate, DateUtils.FORMATE2), DateUtils.getThisMonthLastDay(quarterDate, DateUtils.FORMATE2)));
            quarterDate = DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE2);
            policyPaymentConditionList.add(POLICY_PAYMENT.CREATED_DATE.between(DateUtils.getThisMonthFirstDay(quarterDate, DateUtils.FORMATE2), DateUtils.getThisMonthLastDay(quarterDate, DateUtils.FORMATE2)));
            conditionList.add(POLICY_PAYMENT.POLICY_PAYMENT_ID.isNotNull());
        } else {
            policyPaymentConditionList.add(DSL.condition(" 1=2 "));
        }

        Field<String> levelJson = DSL.field("string_agg ( {0}, ',' ORDER BY {0} ASC )", SQLDataType.VARCHAR, POLICY_COVERAGE_LEVEL_HISTORY.PRODUCT_LEVEL);
        Field<String> multJson = DSL.field("string_agg ( {0}, ',' ORDER BY {1} ASC )", SQLDataType.VARCHAR, POLICY_COVERAGE_LEVEL_HISTORY.MULT, POLICY_COVERAGE_LEVEL_HISTORY.PRODUCT_LEVEL);
        Table<Record> coverageLevelTable = this.getDslContext()
                .select(POLICY_COVERAGE_LEVEL_HISTORY.COVERAGE_ID)
                .select(POLICY_COVERAGE_LEVEL_HISTORY.VERSION_NO)
                .select(POLICY_COVERAGE_LEVEL_HISTORY.COVERAGE_DUTY_ID)
                .select(POLICY_COVERAGE_LEVEL_HISTORY.ACTUAL_PREMIUM.sum().as(POLICY_COVERAGE_LEVEL_HISTORY.ACTUAL_PREMIUM))
                .select(levelJson.as(POLICY_COVERAGE_LEVEL_HISTORY.PRODUCT_LEVEL))
                .select(multJson.as(POLICY_COVERAGE_LEVEL_HISTORY.MULT))
                .select(POLICY_COVERAGE_LEVEL_HISTORY.ORIGINAL_PREMIUM.sum().as(POLICY_COVERAGE_LEVEL_HISTORY.ORIGINAL_PREMIUM))
                .from(POLICY_COVERAGE_LEVEL_HISTORY)
                .groupBy(POLICY_COVERAGE_LEVEL_HISTORY.COVERAGE_ID, POLICY_COVERAGE_LEVEL_HISTORY.VERSION_NO, POLICY_COVERAGE_LEVEL_HISTORY.COVERAGE_DUTY_ID)
                .asTable();

        Table<Record> insuredHistoryTable = this.getDslContext()
                .select(POLICY_INSURED_HISTORY.POLICY_ID)
                .select(POLICY_INSURED_HISTORY.INSURED_ID)
                .select(POLICY_INSURED_HISTORY.VERSION_NO.max().as(POLICY_INSURED_HISTORY.VERSION_NO))
                .select(POLICY_INSURED.CUSTOMER_ID)
                .from(POLICY_INSURED_HISTORY)
                .leftJoin(POLICY_INSURED)
                .on(POLICY_INSURED.POLICY_ID.eq(POLICY_INSURED_HISTORY.POLICY_ID),
                        POLICY_INSURED.ID_NO.eq(POLICY_INSURED_HISTORY.ID_NO),
                        POLICY_INSURED.ID_TYPE.eq(POLICY_INSURED_HISTORY.ID_TYPE))
                .where(POLICY_INSURED.INSURED_ID.isNull())
                .groupBy(POLICY_INSURED_HISTORY.POLICY_ID, POLICY_INSURED_HISTORY.INSURED_ID,POLICY_INSURED.CUSTOMER_ID).asTable();

        Field<String> historyInsuredIdNo = DSL.field("trim(regexp_replace({0}, E'[\\\\n\\\\r]+', '', 'g' ))", SQLDataType.VARCHAR, POLICY_INSURED_HISTORY.ID_NO);

        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(POLICY.POLICY_ID)
                .select(POLICY.POLICY_NO)
                .select(POLICY.APPLY_NO)
                .select(POLICY.SALES_BRANCH_ID)
                .select(POLICY.APPLY_ID)
                .select(POLICY.POLICY_TYPE)
                .select(POLICY_COVERAGE_HISTORY.COVERAGE_ID)
                .select(POLICY_COVERAGE_HISTORY.PRODUCT_ID)
                .select(POLICY_COVERAGE_HISTORY.PRIMARY_FLAG)
                .select(POLICY.EFFECTIVE_DATE)
                .select(POLICY.APPLY_DATE.as("applyDate"))
                .select(POLICY_COVERAGE_HISTORY.EFFECTIVE_DATE.as(POLICY_COVERAGE_HISTORY.COVERAGE_PERIOD_START_DATE))
                .select(POLICY_COVERAGE_HISTORY.COVERAGE_PERIOD_END_DATE)
                .select(POLICY_COVERAGE_HISTORY.PREMIUM_PERIOD)
                .select(POLICY_COVERAGE_HISTORY.PREMIUM_PERIOD_UNIT)
                .select(POLICY_COVERAGE_HISTORY.COVERAGE_PERIOD)
                .select(POLICY_COVERAGE_HISTORY.COVERAGE_PERIOD_UNIT)
                .select(POLICY_COVERAGE_HISTORY.PREMIUM_FREQUENCY)
                .select(POLICY.CHANNEL_TYPE_CODE)
                .select(POLICY.APPROVE_DATE)
                .select(POLICY.RISK_COMMENCEMENT_DATE)
                .select(POLICY_COVERAGE_HISTORY.DUTY_CHOOSE_FLAG)
                .select(coverageLevelTable.field(POLICY_COVERAGE_LEVEL_HISTORY.ORIGINAL_PREMIUM).nvl(POLICY_COVERAGE_HISTORY.ORIGINAL_PREMIUM).as("originalPremium"))
                .select(coverageLevelTable.field(POLICY_COVERAGE_LEVEL_HISTORY.ACTUAL_PREMIUM).nvl(POLICY_COVERAGE_HISTORY.ACTUAL_PREMIUM).as("paymentActualPremium"))
                .select(POLICY_APPLICANT.COMPANY_NAME)
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_APPLICANT.ID_TYPE.as("applicantIdType"))
                .select(POLICY_APPLICANT.ID_NO.as("applicantIdNo"))
                .select(POLICY_APPLICANT.BIRTHDAY.as("applicantBirthday"))
                .select(POLICY_APPLICANT.SEX.as("applicantSex"))
                .select(POLICY_APPLICANT.COMPANY_ID_TYPE)
                .select(POLICY_APPLICANT.COMPANY_ID_NO)
                .select(POLICY.MATURITY_DATE)
                .select(POLICY.INVALID_DATE.nvl(POLICY.THOROUGH_INVALID_DATE).as("policyInvalidDate"))
                .select(POLICY_INSURED_HISTORY.NAME.as("insuredName"))
                .select(POLICY_INSURED_HISTORY.ID_TYPE.as("insuredIdType"))
                .select(historyInsuredIdNo.as("insuredIdNo"))
                .select(POLICY_INSURED_HISTORY.BIRTHDAY.as("insuredBirthday"))
                .select(POLICY_INSURED_HISTORY.MOBILE.as("insuredMobile"))
                .select(POLICY_INSURED_HISTORY.SEX.as("insuredSex"))
                .select(insuredHistoryTable.field(POLICY_INSURED.CUSTOMER_ID).as("insuredCustomerId"))
                .select(POLICY_AGENT.AGENT_ID)
                .select(POLICY_AGENT.AGENT_CODE)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.POLICY_PERIOD.as("renewalInsuranceFrequency"))
                .select(coverageLevelTable.field(POLICY_COVERAGE_LEVEL_HISTORY.PRODUCT_LEVEL).nvl(POLICY_COVERAGE_HISTORY.PRODUCT_LEVEL).as(POLICY_COVERAGE_HISTORY.PRODUCT_LEVEL))
                .select(coverageLevelTable.field(POLICY_COVERAGE_LEVEL_HISTORY.MULT).nvl(POLICY_COVERAGE_HISTORY.MULT).as(POLICY_COVERAGE_HISTORY.MULT))
                .select(POLICY_COVERAGE_HISTORY.AMOUNT.as(POLICY_COVERAGE_HISTORY.TOTAL_AMOUNT))
                .select(POLICY_COVERAGE_DUTY_HISTORY.DUTY_ID)
                .select(POLICY_INSURED_HISTORY.INSURED_ID)
                .select(POLICY_INSURED_HISTORY.OCCUPATION_TYPE.as("insuredOccupationType"))
                .select(POLICY_INSURED_HISTORY.OCCUPATION_CODE.as("insuredOccupationCode"))
                .select(POLICY_INSURED_HISTORY.INSURED_TYPE)
                .select(POLICY_INSURED_EXTEND_HISTORY.INSURED_STATUS)
                .select(POLICY_INSURED_EXTEND_HISTORY.EFFECTIVE_DATE.nvl(POLICY.EFFECTIVE_DATE).as("insuredEffectiveDate"))
                .select(POLICY_INSURED_EXTEND_HISTORY.INVALID_DATE)
                .select(POLICY_COVERAGE_HISTORY.COVERAGE_STATUS)
                .select(POLICY_INSURED_HISTORY.INSURED_ID.countOver().as("totalLine"))
                .select(POLICY_APPLICANT.INCOME.as("applicantIncome"))
                .select(POLICY_INSURED_HISTORY.INSURED_ID.countOver().as("totalLine"))
                .from(insuredHistoryTable)
                .leftJoin(POLICY)
                .on(insuredHistoryTable.field(POLICY_INSURED_HISTORY.POLICY_ID).eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_INSURED_HISTORY)
                .on(POLICY_INSURED_HISTORY.POLICY_ID.eq(POLICY.POLICY_ID), insuredHistoryTable.field(POLICY_INSURED_HISTORY.VERSION_NO).eq(POLICY_INSURED_HISTORY.VERSION_NO), insuredHistoryTable.field(POLICY_INSURED_HISTORY.INSURED_ID).eq(POLICY_INSURED_HISTORY.INSURED_ID))
                .leftJoin(POLICY_COVERAGE_HISTORY)
                .on(POLICY_COVERAGE_HISTORY.INSURED_ID.eq(POLICY_INSURED_HISTORY.INSURED_ID), POLICY_COVERAGE_HISTORY.VERSION_NO.eq(POLICY_INSURED_HISTORY.VERSION_NO))
                .leftJoin(coverageLevelTable)
                .on(coverageLevelTable.field(POLICY_COVERAGE_LEVEL_HISTORY.COVERAGE_ID).eq(POLICY_COVERAGE_HISTORY.COVERAGE_ID), coverageLevelTable.field(POLICY_COVERAGE_LEVEL_HISTORY.VERSION_NO).eq(POLICY_COVERAGE_HISTORY.VERSION_NO))
                .leftJoin(POLICY_COVERAGE_DUTY_HISTORY)
                .on(POLICY_COVERAGE_DUTY_HISTORY.COVERAGE_DUTY_ID.eq(coverageLevelTable.field(POLICY_COVERAGE_LEVEL_HISTORY.COVERAGE_DUTY_ID)), POLICY_COVERAGE_DUTY_HISTORY.VERSION_NO.eq(coverageLevelTable.field(POLICY_COVERAGE_LEVEL_HISTORY.VERSION_NO)))
                .leftJoin(POLICY_APPLICANT)
                .on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_AGENT)
                .on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_INSURED_EXTEND_HISTORY)
                .on(POLICY_INSURED_EXTEND_HISTORY.INSURED_ID.eq(POLICY_INSURED_HISTORY.INSURED_ID), POLICY_INSURED_EXTEND_HISTORY.VERSION_NO.eq(POLICY_INSURED_HISTORY.VERSION_NO))
                .leftJoin(POLICY_PAYMENT)
                .on(policyPaymentConditionList.toArray(new Condition[policyPaymentConditionList.size()]));

        conditionList.add(POLICY.POLICY_TYPE.in(LIFE_INSURANCE_GROUP.name(), LIFE_INSURANCE_PERSONAL.name()));
        conditionList.add(POLICY_COVERAGE_HISTORY.INSURED_ID.isNotNull());
        conditionList.add(POLICY.POLICY_STATUS.ne(POLICY_STATUS_PENDING_EFFECT.name()));
        selectOnConditionStep.where(conditionList);

        selectOnConditionStep.orderBy(POLICY_INSURED_HISTORY.INSURED_ID, POLICY_INSURED_HISTORY.CREATED_DATE, POLICY_COVERAGE_HISTORY.COVERAGE_ID, coverageLevelTable.field(POLICY_COVERAGE_LEVEL_HISTORY.COVERAGE_DUTY_ID), POLICY_COVERAGE_DUTY_HISTORY.COVERAGE_DUTY_ID,insuredHistoryTable.field(POLICY_INSURED.CUSTOMER_ID));


        selectOnConditionStep.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());

        log.info(selectOnConditionStep.toString());

        return selectOnConditionStep.fetchInto(ReserveWithdrawalReportBo.class);
    }

    @Override
    public List<ReserveWithdrawalReportBo> quarterlyStatisticsReserveWithdrawalPaymentReport(List<String> insuredIdList) {

        SelectHavingStep<Record> selectHavingStep = this.getDslContext()
                .select(POLICY_COVERAGE_PAYMENT.INSURED_ID)
                .select(POLICY_PAYMENT.RECEIVABLE_DATE.max().as(POLICY_PAYMENT.RECEIVABLE_DATE))
                .select(POLICY_COVERAGE_PAYMENT.POLICY_ID)
                .select(POLICY_COVERAGE_PAYMENT.PRODUCT_ID)
                .select(POLICY_COVERAGE_PAYMENT.ACTUAL_PREMIUM.sum().as("totalActualPremium"))
                // 被保人的险种缴费频次，与现有的缴费频次统计的不一样
                .select(POLICY_COVERAGE_PAYMENT.POLICY_COVERAGE_PAYMENT_ID.count().as(POLICY_COVERAGE_PAYMENT.FREQUENCY))
                .from(POLICY_COVERAGE_PAYMENT)
                .leftJoin(POLICY_PAYMENT)
                .on(POLICY_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID))
                .where(POLICY_PAYMENT.PAYMENT_STATUS_CODE.in(PAYMENT_SUCCESS.name(), PAYMENT_FINISHED.name()),
                        POLICY_PAYMENT.RECEIVABLE_DATE.le(DateUtils.getCurrentTime()),
                        POLICY_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()),
                        POLICY_COVERAGE_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()),
                        POLICY_COVERAGE_PAYMENT.INSURED_ID.in(insuredIdList))
                .groupBy(
                        POLICY_COVERAGE_PAYMENT.INSURED_ID,
                        POLICY_COVERAGE_PAYMENT.POLICY_ID,
                        POLICY_COVERAGE_PAYMENT.PRODUCT_ID
                );
        System.out.println(selectHavingStep.toString());
        return selectHavingStep.fetchInto(ReserveWithdrawalReportBo.class);
    }

    /**
     * 获取满期保单加费信息
     *
     * @return PolicyAddPremiumPo
     */
    @Override
    public List<PolicyAddPremiumPo> listMaturityPolicyAddPremium() {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_ADD_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(POLICY_ADD_PREMIUM.ADD_PREMIUM_STATUS.eq(PolicyTermEnum.ADD_PREMIUM_STATUS.EFFECTIVE.name()));
        conditions.add(POLICY_ADD_PREMIUM.ADD_PREMIUM_END_DATE.isNotNull());
        conditions.add(DSL.field(DateUtils.getCurrentTime() + "").cast(Long.class).gt(POLICY_ADD_PREMIUM.ADD_PREMIUM_END_DATE));
        conditions.add(POLICY_ADD_PREMIUM.POLICY_ID.isNotNull().and(POLICY_ADD_PREMIUM.COVERAGE_ID.isNotNull()));
        return this.getDslContext()
                .select(POLICY_ADD_PREMIUM.fields())
                .from(POLICY_ADD_PREMIUM)
                .where(conditions)
                .fetchInto(PolicyAddPremiumPo.class);
    }

    @Override
    public List<ServiceChargeBankChannelBo> syncPolicyServiceChargeBankChannel(BasePageRequest basePageRequest, String syncDate) {
        long monthLastDay = DateUtils.getThisMonthLastDay(syncDate, DateUtils.FORMATE2);

        // eMoney的保单
        Condition eMoney = POLICY_AGENT.AGENT_ID.eq("INIT_AGENT_ONLINE003").and(POLICY.SALES_BRANCH_ID.eq("ONLINE"));

        SelectForUpdateStep<Record> recordSelectForUpdateStep = this.getDslContext()
                .select(POLICY.APPLY_NO)
                .select(POLICY.POLICY_NO)
                .select(POLICY.POLICY_TYPE)
                .select(POLICY.APPROVE_DATE)
                .select(POLICY.THOROUGH_INVALID_DATE)
                .select(POLICY_AGENT.AGENT_ID)
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_APPLICANT.COMPANY_NAME)
                .select(POLICY_AGENT.AGENT_CODE)
                .select(POLICY_INSURED.NAME.as("insuredName"))
                .select(POLICY.SALES_BRANCH_ID)
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .select(POLICY_COVERAGE.PRODUCT_LEVEL)
                .select(POLICY_COVERAGE.PREMIUM_FREQUENCY)
                .select(POLICY_COVERAGE.PRIMARY_FLAG)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT)
                .select(POLICY_COVERAGE.COVERAGE_STATUS)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.CHANNEL_TYPE_CODE)
                .select(POLICY_REFERRAL_INFO.REFERRAL_SOURCES)
                .select(POLICY_REFERRAL_INFO.INTRODUCER_NAME)
                .select(POLICY_REFERRAL_INFO.INTRODUCER_POSITION)
                .select(POLICY_COVERAGE.COVERAGE_ID.countOver().as("totalLine"))
                .from(POLICY)
                .leftJoin(POLICY_REFERRAL_INFO)
                .on(POLICY_REFERRAL_INFO.POLICY_ID.eq(POLICY.POLICY_ID), POLICY_REFERRAL_INFO.VALID_FLAG.eq(effective.name()))
                .leftJoin(POLICY_AGENT)
                .on(POLICY.POLICY_ID.eq(POLICY_AGENT.POLICY_ID), POLICY_AGENT.VALID_FLAG.eq(effective.name()))
                .leftJoin(POLICY_APPLICANT)
                .on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID), POLICY_APPLICANT.VALID_FLAG.eq(effective.name()))
                .leftJoin(POLICY_INSURED)
                .on(
                        POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID),
                        POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()),
                        POLICY_INSURED.VALID_FLAG.eq(effective.name())
                )
                .leftJoin(POLICY_COVERAGE)
                .on(
                        POLICY_COVERAGE.VALID_FLAG.eq(effective.name()),
                        POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID),
                        (POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()).and(POLICY_COVERAGE.INSURED_ID.isNotNull()))
                                .or(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_GROUP.name()).and(POLICY_COVERAGE.INSURED_ID.isNull()))
                )
                .where(POLICY.APPROVE_DATE.le(monthLastDay), POLICY.CHANNEL_TYPE_CODE.in(PolicyTermEnum.CHANNEL_TYPE.BANK.name(), PolicyTermEnum.CHANNEL_TYPE.BROKER.name()).or(eMoney))
                .orderBy(POLICY_COVERAGE.COVERAGE_ID)
                .offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());

        System.out.println("执行同步保单银保/中介/员工团队/eMoney渠道手续费费用明细表sql: " + recordSelectForUpdateStep.toString());

        return recordSelectForUpdateStep.fetchInto(ServiceChargeBankChannelBo.class);
    }

    @Override
    public List<ServiceChargeBankChannelBo> syncPolicyServiceChargeBankChannelPayment(List<String> policyPaymentIdList) {

        // eMoney的保单
        Condition eMoney = POLICY_AGENT.AGENT_ID.eq("INIT_AGENT_ONLINE003").and(POLICY.SALES_BRANCH_ID.eq("ONLINE"));

        Table<Record> paymentTable = this.getDslContext()
                .select(POLICY.POLICY_ID)
                .select(POLICY.REFERRAL_CODE)
                .select(POLICY_PAYMENT.POLICY_PAYMENT_ID)
                .select(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE)
                .select(POLICY_PAYMENT.GAINED_DATE)
                .select(POLICY_PAYMENT.PAYMENT_MODE_CODE)
                .select(POLICY_PAYMENT.RECEIVABLE_DATE)
                .select(POLICY_PAYMENT.PAYMENT_STATUS_CODE)
                .select(POLICY_COVERAGE_PAYMENT.PRODUCT_ID)
                .select(POLICY_COVERAGE_PAYMENT.FREQUENCY.max().as(POLICY_COVERAGE_PAYMENT.FREQUENCY))
                .select(POLICY_COVERAGE_PAYMENT.SERVICE_CHARGE_FEE.sum().as(POLICY_COVERAGE_PAYMENT.SERVICE_CHARGE_FEE))
                .select(POLICY_COVERAGE_PAYMENT.SERVICE_CHARGE_RATE)
                .select(POLICY_COVERAGE_PAYMENT.ACTUAL_PREMIUM.sum().as(POLICY_COVERAGE_PAYMENT.ACTUAL_PREMIUM))
                .from(POLICY)
                .leftJoin(POLICY_PAYMENT)
                .on(POLICY_PAYMENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE_PAYMENT)
                .on(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_PAYMENT.POLICY_PAYMENT_ID))
                .leftJoin(POLICY_AGENT)
                .on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .where(
                        POLICY_PAYMENT.POLICY_PAYMENT_ID.in(policyPaymentIdList),
                        POLICY_COVERAGE_PAYMENT.SERVICE_CHARGE_FEE.gt(new BigDecimal(0)).or(eMoney),
                        POLICY_PAYMENT.PAYMENT_STATUS_CODE.in(PAYMENT_SUCCESS.name(), PAYMENT_FINISHED.name()),
                        POLICY_PAYMENT.VALID_FLAG.eq(effective.name()),
                        POLICY_COVERAGE_PAYMENT.VALID_FLAG.eq(effective.name())
                )
                .groupBy(POLICY.POLICY_ID, POLICY_PAYMENT.POLICY_PAYMENT_ID, POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE, POLICY_PAYMENT.GAINED_DATE, POLICY_PAYMENT.RECEIVABLE_DATE, POLICY_COVERAGE_PAYMENT.PRODUCT_ID, POLICY_COVERAGE_PAYMENT.SERVICE_CHARGE_RATE, POLICY_PAYMENT.PAYMENT_MODE_CODE)
                .asTable();

        SelectForUpdateStep<Record> recordSelectForUpdateStep = this.getDslContext()
                .select(paymentTable.fields())
                .select(POLICY.APPLY_NO)
                .select(POLICY.POLICY_ID)
                .select(POLICY.POLICY_NO)
                .select(POLICY.POLICY_TYPE)
                .select(POLICY.APPROVE_DATE)
                .select(POLICY.THOROUGH_INVALID_DATE)
                .select(POLICY_AGENT.AGENT_ID)
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_APPLICANT.COMPANY_NAME)
                .select(POLICY_AGENT.AGENT_CODE)
                .select(POLICY_INSURED.NAME.as("insuredName"))
                .select(POLICY.SALES_BRANCH_ID)
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_COVERAGE.PRODUCT_NAME)
                .select(POLICY_COVERAGE.PRODUCT_LEVEL)
                .select(POLICY_COVERAGE.PRIMARY_FLAG)
                .select(POLICY_COVERAGE.PREMIUM_FREQUENCY)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT)
                .select(POLICY_COVERAGE.COVERAGE_STATUS)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.CHANNEL_TYPE_CODE)
                .select(POLICY.HESITATION_END_DATE)
                .select(POLICY_REFERRAL_INFO.REFERRAL_SOURCES)
                .select(POLICY_REFERRAL_INFO.INTRODUCER_NAME)
                .select(POLICY_REFERRAL_INFO.INTRODUCER_POSITION)
                .from(paymentTable)
                .leftJoin(POLICY)
                .on(paymentTable.field(POLICY.POLICY_ID).eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_REFERRAL_INFO)
                .on(POLICY_REFERRAL_INFO.POLICY_ID.eq(POLICY.POLICY_ID), POLICY_REFERRAL_INFO.VALID_FLAG.eq(effective.name()))
                .leftJoin(POLICY_AGENT)
                .on(POLICY.POLICY_ID.eq(POLICY_AGENT.POLICY_ID), POLICY_AGENT.VALID_FLAG.eq(effective.name()))
                .leftJoin(POLICY_APPLICANT)
                .on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID), POLICY_APPLICANT.VALID_FLAG.eq(effective.name()))
                .leftJoin(POLICY_INSURED)
                .on(
                        POLICY.POLICY_ID.eq(POLICY_INSURED.POLICY_ID),
                        POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()),
                        POLICY_INSURED.VALID_FLAG.eq(effective.name())
                )
                .leftJoin(POLICY_COVERAGE)
                .on(
                        POLICY_COVERAGE.VALID_FLAG.eq(effective.name()),
                        POLICY.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID),
                        paymentTable.field(POLICY_COVERAGE_PAYMENT.PRODUCT_ID).eq(POLICY_COVERAGE.PRODUCT_ID),
                        (POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()).and(POLICY_COVERAGE.INSURED_ID.isNotNull()))
                                .or(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_GROUP.name()).and(POLICY_COVERAGE.INSURED_ID.isNull()))
                );

        System.out.println(recordSelectForUpdateStep.toString());


        return recordSelectForUpdateStep.fetchInto(ServiceChargeBankChannelBo.class);
    }

    public List<PolicyInsuredPo> getGroupPolicyCustomer(String policyId, List<String> policyCustomerIds) {
        return this.getDslContext().select(POLICY_INSURED.fields())
                .from(POLICY)
                .leftJoin(POLICY_INSURED).on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID))
                .where(POLICY_INSURED.CUSTOMER_ID.in(policyCustomerIds)
                        .and(POLICY.POLICY_ID.eq(policyId))
                        .and(POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_GROUP.name())))
                .fetchInto(PolicyInsuredPo.class);
    }

    @Override
    public List<SaleApplyPolicyBo> syncSaleReportCoverage(List<String> policyIdList) {

        Table<Record> recordTable = this.getDslContext()
                .select(POLICY_COVERAGE_PAYMENT.POLICY_ID)
                .select(POLICY_COVERAGE_PAYMENT.PRODUCT_ID)
                .select(POLICY_COVERAGE_PAYMENT.POLICY_COVERAGE_PAYMENT_ID.count().as(POLICY_COVERAGE_PAYMENT.FREQUENCY))
                .from(POLICY_COVERAGE_PAYMENT)
                .leftJoin(POLICY_PAYMENT)
                .on(POLICY_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID))
                .where(POLICY_PAYMENT.PAYMENT_STATUS_CODE.in(PAYMENT_SUCCESS.name(), PAYMENT_FINISHED.name()),
                        POLICY_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()),
                        POLICY_COVERAGE_PAYMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .groupBy(
                        POLICY_COVERAGE_PAYMENT.POLICY_ID,
                        POLICY_COVERAGE_PAYMENT.PRODUCT_ID
                ).asTable();

        SelectForUpdateStep<Record> selectForUpdateStep = this.getDslContext()
                .select(POLICY_COVERAGE.POLICY_ID)
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_COVERAGE.PRODUCT_CODE)
                .select(POLICY_COVERAGE.PREMIUM_FREQUENCY)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD)
                .select(POLICY_COVERAGE.COVERAGE_PERIOD_UNIT)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT)
                .select(POLICY_COVERAGE.PRIMARY_FLAG)
                .select(recordTable.field(POLICY_COVERAGE_PAYMENT.FREQUENCY))
                .select(POLICY_COVERAGE_PAYMENT.ACTUAL_PREMIUM.sum().as(POLICY_COVERAGE_PREMIUM.ACTUAL_PREMIUM))
                .select(POLICY_COVERAGE_PREMIUM.TOTAL_ACTUAL_PREMIUM.sum().as(POLICY_COVERAGE_PREMIUM.TOTAL_ACTUAL_PREMIUM))
                .select(POLICY_COVERAGE.TOTAL_AMOUNT.sum().as(POLICY_COVERAGE.AMOUNT))
                .from(POLICY_COVERAGE)
                .leftJoin(POLICY)
                .on(POLICY_COVERAGE.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE_PREMIUM)
                .on(POLICY_COVERAGE_PREMIUM.COVERAGE_ID.eq(POLICY_COVERAGE.COVERAGE_ID))
                .leftJoin(POLICY_PAYMENT).on(POLICY_PAYMENT.POLICY_ID.eq(POLICY.POLICY_ID)
                        .and(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE.in(BUSINESS_TYPE_GROUP_RENEWAL.name()
                                ,BUSINESS_TYPE_RENEWAL_INSURANCE.name(),BUSINESS_TYPE_NEW_CONTRACT.name())))
                .leftJoin(POLICY_COVERAGE_PAYMENT).on(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_PAYMENT.POLICY_PAYMENT_ID)
                        .and(POLICY_COVERAGE_PAYMENT.COVERAGE_ID.eq(POLICY_COVERAGE.COVERAGE_ID)))
                .leftJoin(recordTable)
                .on(recordTable.field(POLICY_COVERAGE_PAYMENT.POLICY_ID).eq(POLICY_COVERAGE_PREMIUM.POLICY_ID),
                        recordTable.field(POLICY_COVERAGE_PAYMENT.PRODUCT_ID).eq(POLICY_COVERAGE.PRODUCT_ID))
                .where(POLICY.POLICY_ID.in(policyIdList), POLICY_COVERAGE.INSURED_ID.isNotNull())
                .groupBy(POLICY_COVERAGE.POLICY_ID,
                        POLICY_COVERAGE.PRODUCT_ID,
                        POLICY_COVERAGE.PRODUCT_CODE,
                        POLICY_COVERAGE.PREMIUM_FREQUENCY,
                        POLICY_COVERAGE.COVERAGE_PERIOD,
                        POLICY_COVERAGE.COVERAGE_PERIOD_UNIT,
                        POLICY_COVERAGE.PREMIUM_PERIOD,
                        POLICY_COVERAGE.PREMIUM_PERIOD_UNIT,
                        POLICY_COVERAGE.PRIMARY_FLAG,
                        recordTable.field(POLICY_COVERAGE_PAYMENT.FREQUENCY));

        System.out.println(selectForUpdateStep.toString());
        return selectForUpdateStep.fetchInto(SaleApplyPolicyBo.class);
    }

    @Override
    public List<SaleApplyPolicyBo> syncSaleReportPolicyDetail(BasePageRequest basePageRequest, String syncDate) {
        long hostLastDay = DateUtils.getCurrentTime();
        long hostFirstDay = DateUtils.stringToTime(syncDate, DateUtils.FORMATE51);

        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(POLICY.APPLY_ID)
                .select(POLICY.POLICY_ID)
                .select(POLICY.APPLY_NO)
                .select(POLICY.POLICY_NO)
                .select(POLICY.POLICY_TYPE.as("applyType"))
                .select(POLICY_APPLICANT.NAME.as("applicantName"))
                .select(POLICY_APPLICANT.ID_TYPE.as("applicantIdType"))
                .select(POLICY_APPLICANT.ID_NO.as("applicantIdNo"))
                .select(POLICY_APPLICANT.MOBILE.as("applicantMobile"))
                .select(POLICY_APPLICANT.COMPANY_NAME)
                .select(POLICY_APPLICANT.COMPANY_ID_TYPE)
                .select(POLICY_APPLICANT.COMPANY_ID_NO)
                .select(POLICY_APPLICANT.COMPANY_PHONE)
                .select(POLICY_INSURED.NAME.as("insuredName"))
                .select(POLICY_INSURED.BIRTHDAY.as("insuredBirthday"))
                .select(POLICY.SALES_BRANCH_ID)
                .select(POLICY.CHANNEL_TYPE_CODE)
                .select(POLICY_AGENT.AGENT_ID,POLICY_AGENT.REFERRAL_NAME,POLICY_AGENT.ABA_ACCOUNT)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.APPROVE_DATE)
                .select(POLICY.RISK_COMMENCEMENT_DATE.as("riskCommencementDate"))
                .select(POLICY.THOROUGH_INVALID_DATE.nvl(POLICY.INVALID_DATE).as(POLICY.THOROUGH_INVALID_DATE))
                .select(POLICY_RECEIPT_INFO.RECEIPT_RETURN_DATE)
                .select(POLICY_RECEIPT_INFO.RECEIPT_DATE)
                .select(POLICY_RECEIPT_INFO.RECEIPT_SUBMIT_DATE)
                .select(POLICY.EFFECTIVE_DATE)
                .select(POLICY.POLICY_TYPE.as("applyType"))
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                .select(POLICY_PREMIUM.ACTUAL_PREMIUM.as("premiumActualPremium"))
                .select(POLICY.POLICY_ID.countOver().as("totalLine"))
                .from(POLICY)
                .leftJoin(POLICY_PREMIUM)
                .on(POLICY_PREMIUM.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_APPLICANT)
                .on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_INSURED)
                .on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID), POLICY.POLICY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()))
                .leftJoin(POLICY_AGENT)
                .on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_RECEIPT_INFO)
                .on(POLICY_RECEIPT_INFO.POLICY_ID.eq(POLICY.POLICY_ID))
                .where(POLICY.CREATED_DATE.between(hostFirstDay, hostLastDay).or(POLICY.UPDATED_DATE.between(hostFirstDay, hostLastDay)));
        selectConditionStep.orderBy(POLICY.POLICY_ID);
        selectConditionStep.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        this.getLogger().info("同步销售报表保单明细 syncSaleReportPolicyDetail \n {}", selectConditionStep.toString());
        return selectConditionStep.fetchInto(SaleApplyPolicyBo.class);
    }

    @Override
    public List<PolicyAmountBo> queryPolicyAmount(List<String> policyIdList) {

        Table<Record> coverageFrequencyTable = this.getDslContext()
                .select(POLICY_PAYMENT.RECEIVABLE_DATE.max().as(POLICY_PAYMENT.RECEIVABLE_DATE))
                .select(POLICY_COVERAGE_PAYMENT.POLICY_ID)
                .select(POLICY_COVERAGE_PAYMENT.PRODUCT_ID)
                .select(POLICY_COVERAGE_PAYMENT.INSURED_ID)
                .select(POLICY_COVERAGE_PAYMENT.POLICY_COVERAGE_PAYMENT_ID.count().as(POLICY_COVERAGE_PAYMENT.FREQUENCY))
                .select(POLICY_PAYMENT.PAYMENT_BUSINESS_TYPE.decode(
                        BUSINESS_TYPE_RENEWAL_INSURANCE.name(), 1,
                        0).sum().as("renewalInsuranceFrequency"))
                .from(POLICY_PAYMENT)
                .leftJoin(POLICY_COVERAGE_PAYMENT)
                .on(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_PAYMENT.POLICY_PAYMENT_ID))
                .where(POLICY_PAYMENT.PAYMENT_STATUS_CODE.in(PAYMENT_SUCCESS.name(), PAYMENT_FINISHED.name()), POLICY_PAYMENT.RECEIVABLE_DATE.le(DateUtils.getCurrentTime()))
                .groupBy(POLICY_COVERAGE_PAYMENT.POLICY_ID, POLICY_COVERAGE_PAYMENT.PRODUCT_ID, POLICY_COVERAGE_PAYMENT.INSURED_ID)
                .asTable();

        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(POLICY.POLICY_ID)
                .select(POLICY.EFFECTIVE_DATE)
                .select(POLICY.POLICY_TYPE)
                .select(POLICY.APPROVE_DATE)
                .select(POLICY_COVERAGE.AMOUNT)
                .select(POLICY_COVERAGE.COVERAGE_STATUS)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT)
                .select(POLICY_INSURED_EXTEND.INSURED_STATUS)
                .select(POLICY_COVERAGE.PRODUCT_ID)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD)
                .select(POLICY_COVERAGE.PREMIUM_PERIOD_UNIT)
                .select(POLICY_COVERAGE_LEVEL.PRODUCT_LEVEL.nvl(POLICY_COVERAGE.PRODUCT_LEVEL).as(POLICY_COVERAGE.PRODUCT_LEVEL))
                .select(POLICY_COVERAGE_LEVEL.MULT.nvl(POLICY_COVERAGE.MULT).as(POLICY_COVERAGE.MULT))
                .select(POLICY_COVERAGE_DUTY.DUTY_ID)
                .select(POLICY_INSURED.BIRTHDAY.as("insuredBirthday"))
                .select(coverageFrequencyTable.field(POLICY_PAYMENT.RECEIVABLE_DATE))
                .select(coverageFrequencyTable.field("renewalInsuranceFrequency"))
                .from(POLICY)
                .leftJoin(POLICY_INSURED)
                .on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE)
                .on(POLICY_COVERAGE.INSURED_ID.eq(POLICY_INSURED.INSURED_ID))
                .leftJoin(POLICY_COVERAGE_LEVEL)
                .on(POLICY_COVERAGE_LEVEL.COVERAGE_ID.eq(POLICY_COVERAGE.COVERAGE_ID))
                .leftJoin(POLICY_COVERAGE_DUTY)
                .on(POLICY_COVERAGE_DUTY.COVERAGE_DUTY_ID.eq(POLICY_COVERAGE_LEVEL.COVERAGE_DUTY_ID))
                .leftJoin(POLICY_INSURED_EXTEND).on(POLICY.POLICY_ID.eq(POLICY_INSURED_EXTEND.POLICY_ID)
                        .and(POLICY_INSURED_EXTEND.INSURED_ID.eq(POLICY_INSURED.INSURED_ID)))
                .leftJoin(coverageFrequencyTable)
                .on(coverageFrequencyTable.field(POLICY_COVERAGE_PAYMENT.POLICY_ID).eq(POLICY_COVERAGE.POLICY_ID),
                        coverageFrequencyTable.field(POLICY_COVERAGE_PAYMENT.PRODUCT_ID).eq(POLICY_COVERAGE.PRODUCT_ID),
                        coverageFrequencyTable.field(POLICY_COVERAGE_PAYMENT.INSURED_ID).eq(POLICY_COVERAGE.INSURED_ID))
                .where(POLICY.POLICY_ID.in(policyIdList));

        System.out.println(selectConditionStep.toString());
        return selectConditionStep.fetchInto(PolicyAmountBo.class);
    }

    @Override
    public PolicyAssignAgentPo queryAssignAgent(String policyId, Long gainedDate) {
        Field<Integer> row_number = DSL.rowNumber().over().partitionBy(POLICY_ASSIGN_AGENT.POLICY_ID).orderBy(POLICY_ASSIGN_AGENT.UPDATED_DATE.asc()).as("row_number");
        Table<Record> table = this.getDslContext()
                .select(POLICY_ASSIGN_AGENT.fields())
                .select(row_number)
                .from(POLICY_ASSIGN_AGENT)
                .where(POLICY_ASSIGN_AGENT.POLICY_ID.eq(policyId), POLICY_ASSIGN_AGENT.UPDATED_DATE.gt(gainedDate), POLICY_ASSIGN_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.ASSIGN_STATUS.ASSIGNED.name()))
                .asTable();
        SelectConditionStep<Record> selectConditionStep = this.getDslContext().select(table.fields()).from(table).where(table.field("row_number", Integer.class).eq(1));
        return selectConditionStep.fetchOneInto(PolicyAssignAgentPo.class);
    }

    @Override
    public PolicyAssignAgentPo queryAssignDate(String policyId, String agentId, Long gainedDate) {
        Field<Integer> row_number = DSL.rowNumber().over().partitionBy(POLICY_ASSIGN_AGENT.POLICY_ID).orderBy(POLICY_ASSIGN_AGENT.UPDATED_DATE.desc()).as("row_number");
        Table<Record> table = this.getDslContext()
                .select(POLICY_ASSIGN_AGENT.fields())
                .select(row_number)
                .from(POLICY_ASSIGN_AGENT)
                .where(POLICY_ASSIGN_AGENT.POLICY_ID.eq(policyId),
                        POLICY_ASSIGN_AGENT.UPDATED_DATE.le(gainedDate),
                        POLICY_ASSIGN_AGENT.AGENT_ID.gt(agentId),
                        POLICY_ASSIGN_AGENT.ASSIGN_STATUS.eq(PolicyTermEnum.ASSIGN_STATUS.ASSIGNED.name()))
                .asTable();
        SelectConditionStep<Record> selectConditionStep = this.getDslContext().select(table.fields()).from(table).where(table.field("row_number", Integer.class).eq(1));
        return selectConditionStep.fetchOneInto(PolicyAssignAgentPo.class);
    }

    @Override
    public List<PolicyAddPremiumPo> listPolicyAddPremiumByCoverageIdList(List<String> coverageListId, List<String> ratingsNameList, List<String> addPremiumObjectCode) {
        SelectJoinStep<Record> selectJoinStep = this.getDslContext()
                .select(POLICY_ADD_PREMIUM.fields())
                .from(POLICY_ADD_PREMIUM);
        List<Condition> conditionList = new ArrayList<>();
        if (AssertUtils.isNotEmpty(coverageListId)) {
            conditionList.add(POLICY_ADD_PREMIUM.COVERAGE_ID.in(coverageListId));
        }
        if (AssertUtils.isNotEmpty(ratingsNameList)) {
            conditionList.add(POLICY_ADD_PREMIUM.RATINGS_NAME.in(ratingsNameList));
        }
        if (AssertUtils.isNotEmpty(addPremiumObjectCode)) {
            conditionList.add(POLICY_ADD_PREMIUM.ADD_PREMIUM_OBJECT_CODE.in(addPremiumObjectCode));
        }
        conditionList.add(POLICY_ADD_PREMIUM.ADD_PREMIUM_STATUS.eq(PolicyTermEnum.ADD_PREMIUM_STATUS.EFFECTIVE.name()));
        conditionList.add(POLICY_ADD_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        selectJoinStep.where(conditionList);

        return selectJoinStep.fetchInto(PolicyAddPremiumPo.class);
    }

    @Override
    public List<PolicyPaymentBo> queryPolicyPaymentInsuredSum(BasePageRequest basePageRequest, String syncDate) {
        long hostLastDay = DateUtils.getCurrentTime();
        long hostFirstDay = DateUtils.stringToTime(syncDate, DateUtils.FORMATE51);

        // eMoney的保单
        Condition eMoney = POLICY_AGENT.AGENT_ID.eq("INIT_AGENT_ONLINE003").and(POLICY.SALES_BRANCH_ID.eq("ONLINE"));
        SelectForUpdateStep<Record> selectForUpdateStep = this.getDslContext()
                .select(POLICY_PAYMENT.POLICY_PAYMENT_ID)
                .select(POLICY_COVERAGE_PAYMENT.INSURED_ID.countDistinct().as("insuredSum"))
                .select(POLICY_PAYMENT.POLICY_PAYMENT_ID.countOver().as("totalLine"))
                .from(POLICY)
                .leftJoin(POLICY_PAYMENT)
                .on(POLICY_PAYMENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .leftJoin(POLICY_COVERAGE_PAYMENT)
                .on(POLICY_COVERAGE_PAYMENT.POLICY_PAYMENT_ID.eq(POLICY_PAYMENT.POLICY_PAYMENT_ID))
                .leftJoin(POLICY_AGENT)
                .on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .where(
                        POLICY_PAYMENT.PAYMENT_STATUS_CODE.in(PAYMENT_SUCCESS.name(), PAYMENT_FINISHED.name()),
                        POLICY_PAYMENT.SERVICE_CHARGE_FEE.gt(new BigDecimal(0)).or(eMoney),
                        POLICY_PAYMENT.CREATED_DATE.between(hostFirstDay, hostLastDay).or(POLICY_PAYMENT.UPDATED_DATE.between(hostFirstDay, hostLastDay)),
                        POLICY_PAYMENT.VALID_FLAG.eq(effective.name()),
                        POLICY_COVERAGE_PAYMENT.VALID_FLAG.eq(effective.name()),
                        POLICY_PAYMENT.COMMISSION_GENERATE_FLAG.eq(GENERATED.name()),
                        POLICY.CHANNEL_TYPE_CODE.in(PolicyTermEnum.CHANNEL_TYPE.BANK.name(), PolicyTermEnum.CHANNEL_TYPE.BROKER.name())
                                .or(POLICY.SALES_BRANCH_ID.eq("GMA101109"))
                                .or(eMoney)
                )
                .groupBy(POLICY_PAYMENT.POLICY_PAYMENT_ID)
                .offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        System.out.println("执行同步保单银保/中介/员工团队/eMoney渠道手续费费用明细表-支付明细sql: " + selectForUpdateStep.toString());
        return selectForUpdateStep.fetchInto(PolicyPaymentBo.class);
    }

    /**
     * 查询指定加费信息
     *
     * @param policyId   保单Id
     * @param insuredId  被保人Id
     * @param coverageId 险种Id
     * @return PolicyAddPremiumPo
     */
    @Override
    public List<PolicyAddPremiumPo> getPolicyCoverageAddPremium(String policyId, String insuredId, String coverageId) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY_ADD_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(POLICY_ADD_PREMIUM.POLICY_ID.eq(policyId));

        if (AssertUtils.isNotEmpty(insuredId)) {
            conditions.add(POLICY_ADD_PREMIUM.INSURED_ID.eq(insuredId));
        } else {
            conditions.add(POLICY_ADD_PREMIUM.INSURED_ID.isNull());
        }

        if (AssertUtils.isNotEmpty(coverageId)) {
            conditions.add(POLICY_ADD_PREMIUM.COVERAGE_ID.eq(coverageId));
        } else {
            conditions.add(POLICY_ADD_PREMIUM.COVERAGE_ID.isNull());
        }
        return this.getDslContext()
                .select(POLICY_ADD_PREMIUM.fields())
                .from(POLICY_ADD_PREMIUM)
                .where(conditions)
                .fetchInto(PolicyAddPremiumPo.class);
    }

    @Override
    public void updateAuditStatus(Users users, String assignAgentId) {
        getDslContext()
                .update(POLICY_ASSIGN_AGENT)
                .set(POLICY_ASSIGN_AGENT.UPDATED_DATE, DateUtils.getCurrentTime())
                .set(POLICY_ASSIGN_AGENT.UPDATED_USER_ID, users.getUserId())
                .set(POLICY_ASSIGN_AGENT.ASSIGN_STATUS, PolicyTermEnum.SERVICE_STAFF_ASSIGN_STATUS.UNDER_AUDIT.name())
                .where(POLICY_ASSIGN_AGENT.ASSIGN_AGENT_ID.eq(assignAgentId))
                .execute();
    }

    @Override
    public PolicyPo getPolicyPoByPk(String policyId) {
        return getDslContext()
                .selectFrom(POLICY)
                .where(POLICY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY.POLICY_ID.eq(policyId))
                .fetchOneInto(PolicyPo.class);
    }

    @Override
    public List<PolicyAgentPo> listPolicyAgentPoByPolicyIds(List<String> policyIds) {
        return getDslContext()
                .selectFrom(POLICY_AGENT)
                .where(POLICY_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(POLICY_AGENT.POLICY_ID.in(policyIds))
                .fetchInto(PolicyAgentPo.class);
    }

    /**
     * 查询客户投保的保单
     * @param customerIds 客户ID
     * @param policyStatusList 保单状态
     * @return
     */
    @Override
    public List<ClientPolicyBo> listCustomerPolicy(List<String> customerIds, List<String> policyStatusList) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(POLICY.VALID_FLAG.eq(PolicyTermEnum.VALID_FLAG.effective.name()));
        conditions.add(POLICY_APPLICANT.VALID_FLAG.eq(PolicyTermEnum.VALID_FLAG.effective.name()));
        conditions.add(POLICY_APPLICANT.CUSTOMER_ID.in(customerIds));
        if (AssertUtils.isNotEmpty(policyStatusList)) {
            conditions.add(POLICY.POLICY_STATUS.in(policyStatusList));
        }
        return this.getDslContext()
                .select(POLICY.fields())
                .select(POLICY_APPLICANT.CUSTOMER_ID)
                .from(POLICY)
                .innerJoin(POLICY_APPLICANT).on(POLICY_APPLICANT.POLICY_ID.eq(POLICY.POLICY_ID))
                .where(conditions)
                .fetchInto(ClientPolicyBo.class);
    }

    /**
     * 查询保障中客户
     * @param agentId 业务员ID
     * @param policyStatusList 保单状态
     * @return
     */
    @Override
    public List<String> listEffectiveCustomer(String agentId, List<String> policyStatusList) {
        return this.getDslContext()
                .select(POLICY_APPLICANT.CUSTOMER_ID)
                .from(POLICY_APPLICANT)
                .innerJoin(POLICY).on(POLICY.POLICY_ID.eq(POLICY_APPLICANT.POLICY_ID))
                .innerJoin(POLICY_AGENT).on(POLICY_AGENT.POLICY_ID.eq(POLICY.POLICY_ID))
                .where(POLICY_AGENT.AGENT_ID.eq(agentId))
                .and(POLICY.POLICY_STATUS.in(policyStatusList))
                .fetchInto(String.class);
    }

    @Override
    public List<PolicyInsuredBo> pagePolicyInsuredBo(String policyId, PolicyListVo policyListVo) {
        List<PolicyInsuredBo> policyInsuredBos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(POLICY_INSURED.POLICY_ID.eq(policyId));
            conditions.add(POLICY_INSURED.VALID_FLAG.eq(effective.name()));
            policyInsuredBos = this.getDslContext()
                    .select(POLICY_INSURED.fields())
                    .select(POLICY.MATURITY_DATE)
                    .select(POLICY_INSURED.POLICY_ID.countOver().as("totalLine"))
                    .select(POLICY_INSURED_EXTEND.INSURED_STATUS)
                    .select(POLICY_INSURED_EXTEND.ADD_DATE)
                    .select(POLICY_INSURED_EXTEND.EFFECTIVE_DATE)
                    .select(POLICY_INSURED_EXTEND.INVALID_DATE)
                    .from(POLICY_INSURED)
                    .leftJoin(POLICY).on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID))
                    .leftJoin(POLICY_INSURED_EXTEND).on(POLICY_INSURED.POLICY_ID.eq(POLICY_INSURED_EXTEND.POLICY_ID)
                            .and(POLICY_INSURED.INSURED_ID.eq(POLICY_INSURED_EXTEND.INSURED_ID)).and(POLICY_INSURED_EXTEND.VALID_FLAG.eq(effective.name())))
                    .where(conditions)
                    .orderBy(POLICY_INSURED.CREATED_DATE.asc(), POLICY_INSURED.NAME.asc())
                    .offset(policyListVo.getOffset()).limit(policyListVo.getPageSize())
                    .fetchInto(PolicyInsuredBo.class);
        } catch (Exception e) {
            this.getLogger().error(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_ERROR.getValue());
            throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_INSURED_ERROR);
        }
        return policyInsuredBos;
    }

    @Override
    public List<PolicyAndInsuredBo> querySokSanPolicyInsured(String idNo) {

        SelectConditionStep<Record> query = this.getDslContext()
                .select(POLICY_INSURED.INSURED_ID)
                .select(POLICY_INSURED.VALID_FLAG)
                .select(POLICY_INSURED.CUSTOMER_ID)
                .select(POLICY_INSURED.NAME)
                .select(POLICY_INSURED.SEX)
                .select(POLICY_INSURED.BIRTHDAY)
                .select(POLICY_INSURED.ID_NO)
                .select(POLICY.POLICY_ID)
                .select(POLICY.POLICY_NO)
                .select(POLICY.APPLY_ID)
                .select(POLICY.APPLY_NO)
                .select(POLICY.POLICY_STATUS)
                .select(POLICY.APPLICANT_ID)
                .from(POLICY_INSURED)
                .innerJoin(POLICY).on(POLICY_INSURED.POLICY_ID.eq(POLICY.POLICY_ID),
                        POLICY.VALID_FLAG.eq(effective.name())
                ).leftJoin(POLICY_COVERAGE).on(POLICY_INSURED.POLICY_ID.eq(POLICY_COVERAGE.POLICY_ID))
                .where(POLICY_INSURED.ID_NO.eq(idNo)
                        .and(POLICY_COVERAGE.PRIMARY_FLAG.eq(PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                        .and(POLICY_COVERAGE.PRODUCT_ID.eq(ProductTermEnum.PRODUCT.PRODUCT_34.id()))
                );

        // 执行查询并返回结果
        return query.fetchInto(PolicyAndInsuredBo.class);
    }

}
