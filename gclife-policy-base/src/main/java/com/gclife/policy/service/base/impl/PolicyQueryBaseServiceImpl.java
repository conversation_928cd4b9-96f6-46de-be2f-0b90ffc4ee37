package com.gclife.policy.service.base.impl;

import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.response.AgentSimpleBaseResponse;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.policy.core.jooq.tables.pojos.PolicyPo;
import com.gclife.policy.dao.PolicyQueryBaseDao;
import com.gclife.policy.model.bo.PolicyCoverageBo;
import com.gclife.policy.model.bo.PolicyListBo;
import com.gclife.policy.model.bo.PolicyPaymentBo;
import com.gclife.policy.model.bo.PolicyStatusBo;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.vo.PolicyListVo;
import com.gclife.policy.service.base.PolicyQueryBaseService;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.request.WaitingTaskRequest;
import com.gclife.workflow.model.response.WorkItemResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 18-5-23
 * description:
 */
@Service
public class PolicyQueryBaseServiceImpl extends BaseBusinessServiceImpl implements PolicyQueryBaseService {

    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private PolicyQueryBaseDao policyQueryBaseDao;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;

    @Override
    public List<PolicyListBo> loadPolicyListByUserId(String userId, PolicyListVo policyListVo, String policyType) {
        ResultObject<AgentSimpleBaseResponse> simpleBaseRespFcResultObject = agentBaseAgentApi.getUserAgents(userId);
        AssertUtils.isResultObjectError(this.getLogger(), simpleBaseRespFcResultObject, PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_AGENT_ERROR);
        return policyQueryBaseDao.loadPolicyListByAgentId(simpleBaseRespFcResultObject.getData().getAgentId(), policyListVo, policyType);
    }

    @Override
    public List<PolicyListBo> loadPolicyListByBranch(String userId, PolicyListVo policyListVo, String policyType) {
        ResultObject<List<BranchResponse>> branchBaseRespFcs = platformBranchBaseApi.queryUserOptionBranchTreeLeaf(userId);
        if (AssertUtils.isResultObjectListDataNull(branchBaseRespFcs)) {
            return new ArrayList<>();
        }
        List<String> branchIds = branchBaseRespFcs.getData().stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());
        getLogger().info("团险保单查询3", DateUtils.timeStrToString(System.currentTimeMillis()));
        return policyQueryBaseDao.loadPolicyListByBranchIds(branchIds, policyListVo, policyType);
    }

    @Override
    public List<PolicyListBo> loadPolicyListByWorkFlow(WaitingTaskRequest tasksRequest, PolicyListVo policyListVo, String policyType) {
        List<PolicyListBo> newPolicyList = new ArrayList<>();
        //获取当前用户的流程中的待办任务
        ResultObject<List<WorkItemResponse>> resultObject = workFlowApi.queryWaitingTasks(tasksRequest);
        AssertUtils.isResultObjectError(this.getLogger(), resultObject);
        List<WorkItemResponse> listWorkflowTaskResponse = resultObject.getData();
        List<String> applyIds = new ArrayList<>();
        if (AssertUtils.isNotEmpty(listWorkflowTaskResponse)) {
            applyIds = listWorkflowTaskResponse.stream().map(WorkItemResponse::getBusinessId).distinct().collect(Collectors.toList());
        }
        if (!AssertUtils.isNotEmpty(applyIds)) {
            return null;
        }
        List<String> salesBranchIds = new ArrayList<>();
        if (AssertUtils.isNotEmpty(policyListVo.getSalesBranchId())) {
            ResultObject<List<BranchResponse>> branchBaseData = platformBranchBaseApi.queryBranchTreeLeafListById(policyListVo.getSalesBranchId());
            if (!AssertUtils.isResultObjectListDataNull(branchBaseData)) {
                salesBranchIds = branchBaseData.getData().stream().map(BranchResponse::getBranchId).collect(Collectors.toList());
            }
        }
        List<PolicyListBo> applyListBos = policyQueryBaseDao.loadPolicyListByPolicyIds(applyIds, policyListVo, policyType, salesBranchIds);

        for (WorkItemResponse workflowTaskRespFc : listWorkflowTaskResponse) {
            for (PolicyListBo applyListBo : applyListBos) {
                if (workflowTaskRespFc.getBusinessId().equals(applyListBo.getApplyId())) {
                    if (PolicyTermEnum.WORKFLOW_ITEM_STATUS.NEW_TASK.name().equals(workflowTaskRespFc.getWorkItemStatus())) {
                        applyListBo.setOrderIndex(3);
                    } else if (PolicyTermEnum.WORKFLOW_ITEM_STATUS.PROCESSING.name().equals(workflowTaskRespFc.getWorkItemStatus())) {
                        applyListBo.setOrderIndex(2);
                    } else if (PolicyTermEnum.WORKFLOW_ITEM_STATUS.GO_BACK.name().equals(workflowTaskRespFc.getWorkItemStatus())) {
                        applyListBo.setOrderIndex(1);
                    }
                    applyListBo.setWorkflowStatus(workflowTaskRespFc.getWorkItemStatus());
                    newPolicyList.add(applyListBo);
                }
            }
        }
        return newPolicyList;
    }

    @Override
    public List<PolicyListBo> queryPolicyList(List<String> policyIdList, Users currentLoginUsers) {

        return policyQueryBaseDao.queryPolicyList(policyIdList, currentLoginUsers);
    }

    @Override
    public List<PolicyPo> listPolicyPo(BasePageRequest basePageRequest) {
        return policyQueryBaseDao.listPolicyPo(basePageRequest);
    }

    /**
     * 分页查询保单险种数据
     * @param basePageRequest 分页数据
     * @param renewalType 续期续保类型
     * @return
     */
    @Override
    public List<PolicyCoverageBo> listPolicyCoverage(BasePageRequest basePageRequest, String renewalType) {
        return policyQueryBaseDao.listPolicyCoverage(basePageRequest, renewalType);
    }

    /**
     * 查询要生成佣金的保单缴费记录
     * @param basePageRequest 分页数据
     * @return
     */
    @Override
    public List<PolicyPaymentBo> listPayment4Commission(BasePageRequest basePageRequest) {
        return policyQueryBaseDao.listPayment4Commission(basePageRequest);
    }

    @Override
    public List<PolicyPaymentBo> listPayment4Referral(List<String> idList) {
        return policyQueryBaseDao.listPayment4Referral(idList);
    }

    @Override
    public List<PolicyStatusBo> queryPolicyStatus(List<String> idList) {

        return policyQueryBaseDao.queryPolicyStatus(idList);
    }

    /**
     * 查询待续保团险保单
     * @param basePageRequest 分页信息
     * @return
     */
    @Override
    public List<PolicyPo> listPendingGroupRenewal(BasePageRequest basePageRequest) {
        return policyQueryBaseDao.listPendingGroupRenewal(basePageRequest);
    }

    /**
     * 分页查询保单险种数据
     * @param basePageRequest 分页数据
     * @return
     */
    @Override
    public List<PolicyCoverageBo> listPendingGroupInstallment(BasePageRequest basePageRequest) {
        return policyQueryBaseDao.listPendingGroupInstallment(basePageRequest);
    }

    /**
     * 提前两个月产生团险续保提醒消息
     *
     * @param basePageRequest
     * @return
     */
    @Override
    public List<PolicyPo> listPendingGroupRenewalMessage(BasePageRequest basePageRequest) {
        return policyQueryBaseDao.listPendingGroupRenewalMessage(basePageRequest);
    }
}
