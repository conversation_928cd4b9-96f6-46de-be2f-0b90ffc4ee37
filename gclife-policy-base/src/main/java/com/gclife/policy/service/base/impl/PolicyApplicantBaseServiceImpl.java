package com.gclife.policy.service.base.impl;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.policy.core.jooq.tables.daos.PolicyApplicantDao;
import com.gclife.policy.core.jooq.tables.pojos.PolicyApplicantPo;
import com.gclife.policy.dao.PolicyApplicantBaseDao;
import com.gclife.policy.model.bo.PolicyApplicantBo;
import com.gclife.policy.model.bo.PolicyApplicantListBo;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.service.base.PolicyApplicantBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2020/9/18
 */
@Service
public class PolicyApplicantBaseServiceImpl extends BaseBusinessServiceImpl implements PolicyApplicantBaseService {
    @Autowired
    private PolicyApplicantBaseDao policyApplicantBaseDao;
    @Autowired
    private PolicyApplicantDao policyApplicantDao;

    /**
     * 模糊查询投保人
     * @param keyword 关键字
     * @param applicantType 投保人类型
     * @return
     */
    @Override
    public List<PolicyApplicantBo> listFuzzyPolicyApplicant(String keyword, String applicantType) {
        return policyApplicantBaseDao.listFuzzyPolicyApplicant(keyword, applicantType);
    }

    /**
     * 保存保单投保人信息
     * @param policyApplicantPo 保单投保人信息
     * @param userId            用户ID
     */
    @Override
    public void savePolicyApplicant(PolicyApplicantPo policyApplicantPo, String userId) {
        if (AssertUtils.isNotEmpty(policyApplicantPo.getApplicantId())) {
            // 执行修改
            policyApplicantPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyApplicantDao.update(policyApplicantPo);
        } else {
            // 执行新增
            policyApplicantPo.setApplicantId(UUIDUtils.getUUIDShort());
            policyApplicantPo.setCreatedDate(DateUtils.getCurrentTime());
            policyApplicantPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
            policyApplicantDao.insert(policyApplicantPo);
        }
    }

    @Override
    public List<PolicyApplicantListBo> policyApplicantList(List<String> policyNos) {
        return policyApplicantBaseDao.policyApplicantList(policyNos);
    }
}
