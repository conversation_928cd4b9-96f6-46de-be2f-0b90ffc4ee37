package com.gclife.policy.service.base;

import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.vo.PolicyListVo;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.ReserveWithdrawalReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-5-16
 * description:保单基础服务
 */
public interface PolicyBaseService extends BaseBusinessService {
    /**
     * 根据保单ID查询保单基本数据
     *
     * @param policyId 保单ID
     * @return PolicyPo
     */
    PolicyPo queryPolicyPo(String policyId);

    /**
     * 根据保单ID查询保单详情数据
     *
     * @param policyId 保单ID
     * @return PolicyBo
     */
    PolicyBo queryPolicyBo(String policyId);

    /**
     * 查询保单代理人数据
     *
     * @param policyId 保单ID
     * @return
     */
    PolicyAgentPo queryPolicyAgent(String policyId);

    /**
     * 查询投保人信息
     *
     * @param policyIdOrNo 保单ID
     * @return PolicyApplicantBo
     */
    PolicyApplicantBo queryPolicyApplicant(String policyIdOrNo);

    /**
     * 根据投保单ID查询被保人清单
     *
     * @param policyId 保单ID
     * @return list
     */
    List<PolicyInsuredBo> listPolicyInsured(String policyId);

    /**
     * 根据投保单ID查询被保人清单(用于保险证取数据）
     *
     * @param policyId 保单ID
     * @return list
     */
    List<PolicyInsuredBo> listPolicyInsuredForPb(String policyId);

    /**
     * 批量查询被保人
     * @param policyIds 保单ID
     * @return
     */
    List<PolicyInsuredPo> listPolicyInsured(List<String> policyIds);

    /**
     * 根据保单ID查询已完成的被保人统计集合信息
     *
     * @param policyId 保单ID
     * @return PolicyInsuredCollectPo
     */
    PolicyInsuredCollectPo queryPolicyInsuredCollect(String policyId);

    /**
     * 根据投保单ID查询被保人清单
     *
     * @param userId  保单ID
     * @param keyword 搜索关键字
     * @return list
     */
    List<PolicyInsuredBo> listPolicyInsured(String userId, String keyword);

    /**
     * 根据投保单ID查询被保人清单,不查险种
     *
     * @param policyId 保单ID
     * @return list
     */
    List<PolicyInsuredBo> listPolicyInsuredBo(String policyId);


    /**
     * 查询投保单被保人列表
     *
     * @param policyId  保单ID
     * @param versionNo 版本号
     * @return list
     */
    List<PolicyInsuredBo> getPolicyInsuredByVersionNo(String policyId, String versionNo);

    /**
     * 查询保单联系信息
     *
     * @param policyId 保单ID
     * @return PolicyContactInfoPo
     */
    PolicyContactInfoPo queryPolicyContactInfo(String policyId);

    /**
     * 根据保单ID查询保单附件列表
     *
     * @param policyId 保单ID
     * @return list
     */
    List<PolicyAttachmentPo> listPolicyAttachment(String policyId);

    /**
     * 根据保单ID、附件类型编码查询保单附件列表
     *
     * @param policyId           保单ID
     * @param attachmentTypeCode 保单附件类形编码
     * @return list
     */
    List<PolicyAttachmentPo> listPolicyAttachment(String policyId, String attachmentTypeCode);

    /**
     * 根据保单ID、附件类型编码集查询保单附件列表
     *
     * @param policyId            保单ID
     * @param attachmentTypeCodes 保单附件类形编码集
     * @return list
     */
    List<PolicyAttachmentPo> listPolicyAttachment(String policyId, List<String> attachmentTypeCodes);

    /**
     * 根据投保单ID、附件类型编码删除投保单附件
     *
     * @param policyId           保单ID
     * @param attachmentTypeCode 投保单附件类形编码
     */
    void deletePolicyAttachment(String policyId, String attachmentTypeCode);

    /**
     * 根据保单ID查询投保单特别约定信息
     *
     * @param policyId 保单ID
     * @return List<PolicySpecialContractPo>
     */
    List<PolicySpecialContractPo> listPolicySpecialContract(String policyId);

    /**
     * 查询保单回执
     *
     * @param policyId 保单ID
     * @return PolicyReceiptInfoPo
     */
    PolicyReceiptInfoPo queryPolicyReceiptInfo(String policyId);

    /**
     * 查询保单回执
     *
     * @param policyIds 保单ID
     * @return list
     */
    List<PolicyReceiptInfoPo> listPolicyReceiptInfo(List<String> policyIds);

    /**
     * 保存保单回执
     *
     * @param userId              用户ID
     * @param policyReceiptInfoPo 保单回执
     */
    void savePolicyReceiptInfo(String userId, PolicyReceiptInfoPo policyReceiptInfoPo);

    /**
     * 保存回执行信息
     *
     * @param policyReceiptInfoPo 保单回执
     */
    void savePolicyReceiptInfo(PolicyReceiptInfoPo policyReceiptInfoPo);

    /**
     * 批量更新回执行信息
     *
     * @param policyReceiptInfoPos 保单回执
     * @param userId               用户ID
     */
    void updatePolicyReceiptInfo(List<PolicyReceiptInfoPo> policyReceiptInfoPos, String userId);

    /**
     * <<<<<<< HEAD
     * 根据保单ID查询所有被保人险种列表
     *
     * @param policyId 保单ID
     * @return PolicyCoveragePo
     */
    List<PolicyCoveragePo> listPolicyCoverageOfInsured(String policyId);

    /**
     * /**
     * 根据保单ID、被保人ID查询被保人险种列表
     *
     * @param policyId  保单ID
     * @param insuredId 被保人ID
     * @return list
     */
    List<PolicyCoveragePo> listPolicyCoverageOfInsured(String policyId, String insuredId);

    /**
     * 查询团险保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    List<PolicyCoveragePo> listGroupPolicyCoverage(String policyId);

    /**
     * 查询保单ID对应的险种责任
     *
     * @param policyId 保单ID
     * @return list
     */
    List<PolicyCoverageDutyBo> getPolicyCoverageDutyList(String policyId);

    /**
     * 查询险种档次信息
     *
     * @param policyId       保单ID
     * @param coverageDutyId 责任ID
     * @return PolicyCoverageLevelPos
     */
    List<PolicyCoverageLevelPo> listPolicyCoverageLevel(String policyId, String coverageDutyId);

    /**
     * 根据投保单ID查询保单信息
     *
     * @param applyId 投保单ID
     * @return PolicyPo
     */
    PolicyPo queryPolicyByApplyId(String applyId);

    /**
     * 根据保单号查询保单信息
     *
     * @param policyNo 保单号
     * @return PolicyPo
     */
    PolicyPo queryPolicyByPolicyNo(String policyNo);

    /**
     * 保存保单基础信息
     *
     * @param policyPo 保单基础信息
     */
    void savePolicyPo(PolicyPo policyPo);

    /**
     * 保存保单基础信息
     *
     * @param policyPo 保单基础信息
     * @param userId   用户ID
     */
    void savePolicy(PolicyPo policyPo, String userId);

    /**
     * 保存保单基础信息
     *
     * @param policyBo 保单基础信息
     */
    void savePolicyBo(PolicyBo policyBo);

    /**
     * 保存保单账户信息
     *
     * @param policyAccountPo 保单账户信息
     */
    void savePolicyAccount(PolicyAccountPo policyAccountPo);

    /**
     * 保存保单代理人信息
     *
     * @param policyAgentPo 保单代理人信息
     */
    void savePolicyAgent(PolicyAgentPo policyAgentPo);

    /**
     * 保存保单投保人信息
     *
     * @param policyApplicantPo 保单投保人信息
     */
    void savePolicyApplicant(PolicyApplicantPo policyApplicantPo);

    /**
     * 保存保单联系人信息
     *
     * @param policyContactInfoPo 保单联系人信息
     */
    void savePolicyContactInfo(PolicyContactInfoPo policyContactInfoPo);

    /**
     * 新增或修改投保单附件信息
     *
     * @param policyAttachmentPo 投保单附件信息
     */
    void savePolicyAttachment(String userId, PolicyAttachmentPo policyAttachmentPo);

    /**
     * 新增投保单附件信息(批量)
     *
     * @param policyAttachmentPos 投保单附件信息列表
     */
    void savePolicyAttachment(String userId, List<PolicyAttachmentPo> policyAttachmentPos);

    /**
     * 保存特别约定信息
     *
     * @param userId                  用户ID
     * @param policySpecialContractPo 特约信息
     */
    void savePolicySpecialContract(String userId, PolicySpecialContractPo policySpecialContractPo);

    /**
     * 批量新增保单特约信息
     *
     * @param userId                   用户ID
     * @param policySpecialContractPos 特约信息集
     */
    void savePolicySpecialContract(String userId, List<PolicySpecialContractPo> policySpecialContractPos);

    /**
     * 保存保单缴费信息
     *
     * @param policyPaymentPo 保单缴费信息
     */
    void savePolicyPayment(PolicyPaymentPo policyPaymentPo);

    /**
     * 查询缴费信息
     *
     * @param policyId 保单ID
     * @return PolicyPaymentPo
     */
    PolicyPaymentPo queryPolicyPayment(String policyId);

    /**
     * 根据主键查询缴费信息
     *
     * @param policyPaymentId 主键
     * @return PolicyPaymentPo
     */
    PolicyPaymentPo queryPolicyPaymentById(String policyPaymentId);

    /**
     * 查询最新一期缴费信息
     *
     * @param policyId 保单ID
     * @return PolicyPaymentPo
     */
    PolicyPaymentBo queryNewPolicyPayment(String policyId);

    /**
     * 查询保单下所有的缴费信息
     *
     * @param policyId 保单ID
     * @return PolicyPaymentBos
     */
    List<PolicyPaymentBo> listPolicyPayment(String policyId);

    /**
     * 查询保单下所有的缴费成功信息
     *
     * @param policyId 保单ID
     * @return PolicyPaymentBos
     */
    List<PolicyPaymentBo> listPayPolicyPayment(String policyId);


    /**
     * 查询保单下所有的缴费成功信息(排除续期预缴费记录)
     *
     * @param policyId 保单ID
     * @param endTime  结束日期
     * @param paymentBusinessType
     * @return
     */
    List<PolicyPaymentBo> listPayPolicyPayment(String policyId, long endTime, List<String> paymentBusinessType);

    /**
     * 查询指定缴费信息列表
     *
     * @param policyPaymentIds 保单缴费ID集
     * @return List
     */
    List<PolicyPaymentBo> listPolicyPayment(List<String> policyPaymentIds);

    /**
     * 查询首期缴费信息
     *
     * @param policyId
     * @return PolicyPaymentBo
     */
    PolicyPaymentBo queryFirstPolicyPayment(String policyId);

    /**
     * 保存保单打印信息
     *
     * @param policyPrintInfoPo 保单打印信息
     */
    void savePolicyPrintInfo(PolicyPrintInfoPo policyPrintInfoPo);

    /**
     * 保存被保人信息
     *
     * @param policyInsuredPo 被保人信息
     */
    void savePolicyInsured(PolicyInsuredPo policyInsuredPo);

    /**
     * 保存被保人拓展新
     *
     * @param policyInsuredExtendPo 被保人拓展信息
     */
    void savePolicyInsuredExtend(PolicyInsuredExtendPo policyInsuredExtendPo);

    /**
     * 保存保单被保人统计信息
     *
     * @param policyInsuredCollect 保单被保人统计信息
     */
    void savePolicyInsuredColect(PolicyInsuredCollectPo policyInsuredCollect);

    void savePolicyAssignRemarkPo(PolicyAssignRemarkPo policyAssignRemarkPo, String userId);

    /**
     * 批量新增分单备注信息
     *
     * @param policyAssignRemarkPos 分单备注信息
     * @param userId                用户ID
     */
    void addPolicyAssignRemark(List<PolicyAssignRemarkPo> policyAssignRemarkPos, String userId);

    /**
     * 根据代理人ID集合查询其名下的所有保单数
     *
     * @param agentIds 代理人ID集合
     * @return PolicyPos
     */
    List<PolicyPo> listPolicyByAgentId(List<String> agentIds);

    /**
     * 保存保单续期订单数据
     *
     * @param policyRenewalGrabPo 保单续期订单数据
     */
    void savePolicyRenewalGrab(PolicyRenewalGrabPo policyRenewalGrabPo);

    /**
     * 根据保单ID和应收时间查询续期订单数据
     *
     * @param policyId       保单ID
     * @param receivableDate 应收时间
     * @return
     */
    PolicyRenewalGrabPo queryPolicyRenewalGrab(String policyId, Long receivableDate);

    /**
     * 根据保单ID查询抢单记录
     *
     * @param policyId 保单ID
     * @return list
     */
    List<PolicyRenewalGrabPo> listPolicyRenewalGrab(String policyId);

    /**
     * 根据保单ID查询抢单记录
     *
     * @param policyIds 保单ID集
     * @return list
     */
    List<PolicyRenewalGrabPo> listPolicyRenewalGrab(List<String> policyIds);

    /**
     * 根据保单ID和应收时间删除抢单记录
     */
    void deletePolicyRenewalGrab(PolicyRenewalGrabPo policyRenewalGrabPo);

    /**
     * 批量删除抢单记录
     *
     * @param policyRenewalGrabPos 抢单记录
     */
    void deletePolicyRenewalGrab(List<PolicyRenewalGrabPo> policyRenewalGrabPos);

    /**
     * 根据保单ID查询保单历史代理人列表
     *
     * @param policyId 保单ID
     * @return List<PolicyAgentHistoryPo>
     */
    List<PolicyAgentHistoryPo> listPolicyAgentHistoryByPolicyId(String policyId);

    /**
     * 根据保单ID查询保单历史最新的代理人
     *
     * @param policyId 保单ID
     * @return List<PolicyAgentHistoryPo>
     */
    PolicyAgentHistoryPo queryPolicyAgentHistoryByPolicyId(AppRequestHeads appRequestHeads, String policyId);

    /**
     * 保存保单历史代理人
     *
     * @param policyAgentHistoryPo 历史代理人
     */
    void savePolicyAgentHistory(PolicyAgentHistoryPo policyAgentHistoryPo);

    /**
     * 批量更新保单状态
     *
     * @param userId       用户ID
     * @param policyIds    　保单ID集
     * @param policyStatus 保单状态
     */
    void updatePolicy(String userId, List<String> policyIds, String policyStatus);

    /**
     * 批量更新保单缴费状态
     *
     * @param userId           用户ID
     * @param policyPaymentPos 　保单缴费数据
     */
    void updatePolicyPayment(String userId, List<PolicyPaymentPo> policyPaymentPos);

    /**
     * 根据保单号，投保人姓名查询保单信息
     *
     * @param policyListVo
     * @return
     */
    List<PolicyApplicantCoverageBo> queryPolicyApplicantCoverageBo(PolicyListVo policyListVo);

    /**
     * 客户关联的保单
     *
     * @param customerAgentIds 关联客户集合
     * @return List<PolicyEndorseBo>
     */
    List<PolicyEndorseBo> queryPolicyByCustomerId(List<String> customerAgentIds);

    /**
     * 客户关联的保单
     *
     * @param customerAgentIds 关联客户集合
     * @return List<PolicyEndorseBo>
     */
    List<PolicyApplicantBo> queryPolicyByCustomerAgentIds(List<String> customerAgentIds);

    /**
     * 客户名下的保单
     *
     * @param customerAgentIds 客户ID
     * @return List<PolicyEndorseBo>
     */
    List<PolicyEndorseBo> queryPolicyByApplicantCustomerId(List<String> customerAgentIds);

    /**
     * 查询保单详情
     *
     * @param policyId  保单ID
     * @param versionNo 版本号
     * @return PolicyEndorseBo
     */
    PolicyEndorseInfoBo queryPolicyInfoByPolicyId(String policyId, String versionNo);

    /**
     * 根据保单ID查询指定支付状态缴费信息
     *
     * @param policyId      保单ID
     * @param paymentStatus 支付状态
     * @return
     */
    List<PolicyEndorsePaymentBo> listPolicyPayment(String policyId, String paymentStatus);

    /**
     * 团险被保人下的已缴费信息
     *
     * @param policyId   保单ID
     * @param customerId 客户ID
     * @return PolicyEndorsePaymentBos
     */
    List<PolicyCoveragePaymentBo> queryGroupCustomerPayments(String policyId, String customerId, String paymentStatus);

    /**
     * 根据保单ID查询指定支付状态缴费信息
     *
     * @param policyId      保单ID
     * @param paymentStatus 支付状态
     * @return
     */
    List<PolicyPaymentBo> queryPolicyPayments(String policyId, String paymentStatus);

    /**
     * 查询保单下所有的缴费信息
     *
     * @param policyPaymentIds 保单ID
     * @return PolicyPaymentBos
     */
    List<PolicyPaymentBo> queryPolicyPayment(List<String> policyPaymentIds);

    /**
     * 查询保单信息跟保人信息（收付费明细报表）
     *
     * @param businessNo payment关联投保单id
     */
    List<PolicyReportBo> queryPolicyReport(List<String> businessNo);

    /**
     * 查询业务报表（投保人资料）
     */
    List<PolicyApplicantReportBo> queryPolicyApplicantReport(BasePageRequest basePageRequest, String startDate);

    /**
     * 查询业务报表（被保人资料）
     */
    List<PolicyInsuredReportBo> queryPolicyInsuredsReport(BasePageRequest basePageRequest, String startDate);

    /**
     * 投保人代表报表数据
     *
     * @param basePageRequest
     * @param startDate
     * @return
     */
    List<PolicyApplicantReportBo> queryDelegateApplicantReport(BasePageRequest basePageRequest, String startDate);

    /**
     * 查询承保清单
     */
    List<PolicyReportUnderwritingBo> queryPolicyReportUnderwritingBo(BasePageRequest basePageRequest, String startDate, String reportType);

    /**
     * 根据policyId查询policy_payement中支付成功的数据
     */
    List<PolicyPaymentPo> queryAllPolicyPaymentPo(String policyId);

    /**
     * 根据policyId查询policy_payement中数据
     */
    List<PolicyPaymentPo> queryPolicyPaymentPo(String policyId);

    /**
     * 根据policyId查询policy_agment中数据
     */
    PolicyAgentPo queryOnePolicyAgentPo(String policyId);

    /**
     * 根据policyId查询policy_applicant中数据
     *
     * @param policyIds
     */
    List<PolicyApplicantPo> queryAllPolicyApplicantPo(List<String> policyIds);

    /**
     * 根据policyId查询policy_insured中数据
     */
    PolicyInsuredPo queryOnePolicyInsuredPo(String policyId);

    /**
     * 保存保单操作
     *
     * @param policyOperationPo 保单操作数据
     */
    void savePolicyOperation(PolicyOperationPo policyOperationPo);

    /**
     * 保存保单操作
     *
     * @param policyOperationPo 保单操作数据
     * @param userId            用户ID
     */
    void savePolicyOperation(PolicyOperationPo policyOperationPo, String userId);

    /**
     * 查询保单操作
     *
     * @param policyId 保单ID
     * @return
     */
    PolicyOperationPo queryPolicyOperation(String policyId);

    /**
     * 查询保单特定操作
     *
     * @param policyId      保单ID
     * @param operationCode 操作编码
     * @return
     */
    PolicyOperationPo queryPolicyOperation(String policyId, String operationCode);

    /**
     * @param operationCode
     * @return
     */
    BaseOperationPo queryBaseOperationPoByOperationCode(String operationCode);

    /**
     * 查询当天满期的保单
     *
     * @param basePageRequest
     * @return
     */
    List<PolicyPo> queryMaturityPolicy(BasePageRequest basePageRequest);

    /**
     * 查询当天满期的保单
     *
     * @param basePageRequest
     * @return
     */
    List<PolicyCoveragePo> queryMaturityRenewalAdditionCoverage(BasePageRequest basePageRequest);

    /**
     * 保单分单指派表
     *
     * @param userId              用户ID
     * @param policyAssignAgentPo 分单数据
     */
    void savePolicyAssignAgentPo(String userId, PolicyAssignAgentPo policyAssignAgentPo);

    /**
     * 批量保存保单分单指派表
     *
     * @param userId               用户ID
     * @param policyAssignAgentPos 分单数据
     */
    void savePolicyAssignAgentPoBatch(String userId, List<PolicyAssignAgentPo> policyAssignAgentPos);

    /**
     * 批量新增保单缴费信息
     *
     * @param policyPaymentBos 保单缴费信息
     * @param userId           用户ID
     */
    void addPolicyPayment(List<PolicyPaymentBo> policyPaymentBos, String userId);

    /**
     * 删除保单缴费信息
     *
     * @param policyPaymentPo 保单缴费信息
     */
    void deletePolicyPayment(PolicyPaymentPo policyPaymentPo);

    /**
     * 删除保单缴费信息(含险种缴费)
     *
     * @param policyPaymentBos 保单缴费信息
     */
    void deletePolicyPayment(List<PolicyPaymentBo> policyPaymentBos);

    /**
     * 删除保单操作数据
     *
     * @param policyOperationPo 保单操作数据
     */
    void deletePolicyOperation(PolicyOperationPo policyOperationPo);

    /**
     * 保存保单挂起表
     *
     * @param policyHookPo 保单挂起
     * @param userId       用户ID
     */
    void savePolicyHookPo(PolicyHookPo policyHookPo, String userId);

    /**
     * 查询保单挂起操作
     *
     * @param policyId     保单ID
     * @param hookObjectId 挂起对象ID
     * @param hookStatus
     * @return PolicyHookPo
     */
    PolicyHookPo getPolicyHook(String policyId, String hookObjectId, String hookStatus);

    /**
     * 查询保单挂起操作
     *
     * @param policyId     保单ID
     * @param hookObjectId 挂起对象ID
     * @param customerId   customerId
     * @param hookStatus
     * @return PolicyHookPo
     */
    PolicyHookPo getGroupPolicyHook(String policyId, String hookObjectId, String customerId, String hookStatus);

    List<PolicyInsuredPo> getGroupPolicyCustomer(String policyId, List<String> policyCustomerIds);

    /**
     * 根据保单ID查询回访信息
     *
     * @param policyId 保单ID
     * @return
     */
    PolicyReturnVisitPo queryReturnVisitByBusinessId(String policyId);

    /**
     * 保存保单回访信息
     *
     * @param policyReturnVisitPo 保单回访信息
     * @param userId              用户ID
     * @return
     */
    void savePolicyReturnVisit(PolicyReturnVisitPo policyReturnVisitPo, String userId);

    /**
     * 查询客户保单信息
     *
     * @param customerIds
     * @return
     */
    List<ClaimPolicyBo> queryPolicyByCustomerIds(String... customerIds);

    /**
     * 模糊查询保单信息
     *
     * @param keyword 关键字
     * @return
     */
    List<PolicyApplicantInfoBo> queryListPolicysByKeyword(String keyword);

    /**
     * 查询当前用户的团险保单信息
     *
     * @param userId  当前用户
     * @param keyword 关键字
     * @return
     */
    List<PolicyApplicantInfoBo> queryAgentListPolicysByKeyword(String userId, String keyword);

    /**
     * 查询所有团险保单信息
     *
     * @param userId  当前用户
     * @param keyword 关键字
     * @return
     */
    List<PolicyApplicantInfoBo> queryAgentListPolicysByKeywordNew(String userId, String keyword);

    List<PolicyApplicantPo> queryPolicyApplicantByCustomerId(String customerId);

    Integer countNewApplicantByCustomerIds(List<String> customerIds);

    Integer countNewInsuredByCustomerIds(List<String> customerIds);

    List<PolicyInsuredPo> queryPolicyInsuredByCustomerId(String customerId);

    /**
     * 团险根据保单id查询被保人信息
     *
     * @param policyId
     * @return
     */
    List<GroupInsuredInfoBo> queryPolicyInsuredInfoBo(String policyId);

    /**
     * 查询团险承保保单
     *
     * @param basePageRequest
     * @param startDate
     * @return
     */
    List<PolicyGroupReportUnderwritingBo> queryPolicyGroupReportUnderwritingBo(BasePageRequest basePageRequest, String startDate);

    /**
     * 查询学校发展基金报表
     *
     * @param basePageRequest
     * @param startDate
     * @return
     */
    List<GroupSdfReportBo> queryListPolicyGroupSdfReportBo(BasePageRequest basePageRequest, String startDate);

    /**
     * 查询个险和团险下的保单信息
     *
     * @param keyword
     * @return
     */
    List<PolicyApplicantInfoBo> queryReturnVisitPolicyByKeyword(String keyword);

    /**
     * 根据客户ID查询保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimPolicyBo> listSimplePolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据客户ID查询团险保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimPolicyBo> listSimpleGroupPolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据客户ID查询历史保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimPolicyBo> listSimplePolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据客户ID查询历史保单基础信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimPolicyBo> listSimpleGroupPolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据保单ID查询保单基础信息列表
     *
     * @param policyId          保单ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    ClaimPolicyBo querySimplePolicyById(String policyId, Long dataEffectiveDate);

    /**
     * 根据客户ID查询历史保单基础信息列表
     *
     * @param policyId          保单ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimPolicyBo> listSimplePolicyHistoryByPolicyId(String policyId, Long dataEffectiveDate);

    /**
     * 根据客户ID查询保单详细信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimDetailPolicyBo> listDetailPolicyByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据客户ID查询历史保单详细信息列表
     *
     * @param customerAgentIds  客户ID
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimDetailPolicyBo> listDetailPolicyHistoryByCustomerId(List<String> customerAgentIds, Long dataEffectiveDate);

    /**
     * 根据保单ID查询保单详细信息列表
     *
     * @param policyId          保单ID
     * @param customerId
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    ClaimDetailPolicyBo queryDetailPolicyByPolicyId(String policyId, String customerId, Long dataEffectiveDate);

    /**
     * 根据保单ID查询历史保单详细信息列表
     *
     * @param policyId          保单ID
     * @param customerId
     * @param dataEffectiveDate 数据生效日期
     * @return
     */
    List<ClaimDetailPolicyBo> listDetailPolicyHistoryByPolicyId(String policyId, String customerId, Long dataEffectiveDate);

    /**
     * 需要设置的投保人代表数据
     *
     * @return
     */
    List<PolicyGroupReportSyncApplicantBo> querySyncApplicantCustomer();

    /**
     * 需要设置的投保人代表Po数据
     *
     * @return
     */
    List<PolicyApplicantPo> queryApplicantCustomer();

    /**
     * 保单显示
     *
     * @param policyIdList
     * @return
     */
    List<ActualPerformanceReportBo> queryActualPerformance(List<String> policyIdList);

    /**
     * @param versionNo
     * @return
     */
    List<ActualPerformanceReportBo> queryPolicyVersionActualPerformance(List<String> versionNo);

    /**
     * 保单ID查询受益人信息
     *
     * @param policyId
     * @return
     */
    List<PolicyBeneficiaryInfoBo> queryPolicyLoanBeneficiary(String policyId, String modifyFlag);

    /**
     * 根据保单ID查询团险保单详细信息
     *
     * @param policyId 团险保单ID
     * @return
     */
    PolicyBo queryGroupPolicyDetail(String policyId);

    /**
     * 查询全部被保人数据
     *
     * @param policyId 保单ID
     * @return list
     */
    List<PolicyInsuredBo> getPolicyAllInsuredList(String policyId);

    /**
     * 季度准备金提取报表统计
     *
     * @param quarterDate
     * @param basePageRequest
     * @return
     */
    List<ReserveWithdrawalReportBo> quarterlyStatisticsReserveWithdrawalReport(String quarterDate, BasePageRequest basePageRequest);

    /**
     * 执行同步保单银保渠道手续费费用明细表
     *
     * @param basePageRequest
     * @param syncDate
     * @return
     */
    List<ServiceChargeBankChannelBo> syncPolicyServiceChargeBankChannel(BasePageRequest basePageRequest, String syncDate);

    /**
     * 执行同步保单银保渠道手续费费用明细表-支付明细
     *
     * @param basePageRequest
     * @param syncDate
     * @return
     */
    List<ServiceChargeBankChannelBo> syncPolicyServiceChargeBankChannelPayment(BasePageRequest basePageRequest, String syncDate);

    /**
     * 销售报表查询
     *
     * @param policyIdList
     * @return
     */
    List<SaleApplyPolicyBo> syncSaleReportCoverage(List<String> policyIdList);

    /**
     * 查询每个保单，险种，责任，档次 保险金额
     *
     * @param policyIdList
     * @return
     */
    List<PolicyAmountBo> queryPolicyAmount(List<String> policyIdList);

    /**
     * 季度准备金提取报表统计
     *
     * @param quarterDate
     * @param basePageRequest
     * @return
     */
    List<ReserveWithdrawalReportBo> quarterlyHistoryStatisticsReserveWithdrawalReport(String quarterDate, BasePageRequest basePageRequest);


    /**
     * 查询
     *
     * @param insuredIdList
     * @return
     */
    List<ReserveWithdrawalReportBo> quarterlyStatisticsReserveWithdrawalPaymentReport(List<String> insuredIdList);

    /**
     * @param policyId
     * @param gainedDate
     */
    PolicyAssignAgentPo queryAssignAgent(String policyId, Long gainedDate);

    /**
     * 查询分单数据
     *
     * @param policyId
     * @param agentId
     * @param gainedDate
     * @return
     */
    PolicyAssignAgentPo queryAssignDate(String policyId, String agentId, Long gainedDate);


    /**
     * 同步保单销售报表
     * 包含：保单、保单缴费信息表、保单投保人、保单被保人、保单代理人、保单回执
     * @param basePageRequest
     * @param syncDate
     * @return
     */
    List<SaleApplyPolicyBo> syncSaleReportPolicyDetail(BasePageRequest basePageRequest, String syncDate);

    /**
     * 保存 policyServiceAgentPo
     *
     * @param userId
     * @param policyServiceAgentPo
     */
    void savePolicyServiceAgentPo(String userId, PolicyServiceAgentPo policyServiceAgentPo);

    /**
     * 保存 policyServiceAgentRemarkPo
     *
     * @param policyServiceAgentRemarkPo
     * @param userId
     */
    void savePolicyServiceAgentRemarkPo(PolicyServiceAgentRemarkPo policyServiceAgentRemarkPo, String userId);

    /**
     * 批量保存 PolicyServiceAgentPo
     *
     * @param userId
     * @param policyServiceAgentPos
     */
    void batchSavePolicyServiceAgentPo(String userId, List<PolicyServiceAgentPo> policyServiceAgentPos);

    /**
     * 批量保存 PolicyServiceAgentRemarkPo
     *
     * @param policyServiceAgentRemarkPos
     * @param userId
     */
    void batchSavePolicyServiceAgentRemarkPo(List<PolicyServiceAgentRemarkPo> policyServiceAgentRemarkPos, String userId);

    /**
     * 分单指派更新审核状态
     *
     * @param users
     * @param assignAgentId
     */
    void updateAuditStatus(Users users, String assignAgentId);

    /**
     * 根据主键获取 PolicyPo
     *
     * @param policyId
     * @return
     */
    PolicyPo getPolicyPoByPk(String policyId);

    /**
     * 根据保单ID集合 获取PolicyAgentPo集合
     *
     * @param policyIds
     * @return
     */
    List<PolicyAgentPo> listPolicyAgentPoByPolicyIds(List<String> policyIds);

    /**
     * 查询客户投保的保单
     * @param customerIds 客户ID
     * @param policyStatusList 保单状态
     * @return
     */
    List<ClientPolicyBo> listCustomerPolicy(List<String> customerIds, List<String> policyStatusList);

    /**
     * 查询保障中客户
     * @param agentId 业务员ID
     * @param policyStatusList 保单状态
     * @return
     */
    List<String> listEffectiveCustomer(String agentId, List<String> policyStatusList);

    void savePolicyAllocationPo(String userId, PolicyAllocationPo policyAllocationPo);

    void savePolicyAllocationRemarkPo(PolicyAllocationRemarkPo policyAllocationRemarkPo, String userId);

    void batchSavePolicyAllocationPo(String userId, List<PolicyAllocationPo> policyAllocationPos);

    void batchSavePolicyAllocationRemarkPo(List<PolicyAllocationRemarkPo> policyAllocationRemarkPos, String userId);

    List<PolicyInsuredBo> pagePolicyInsuredBo(String policyId, PolicyListVo policyListVo);

    List<PolicyAndInsuredBo> querySokSanPolicyInsured(String idNo);
}
