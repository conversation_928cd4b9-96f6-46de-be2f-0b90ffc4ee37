package com.gclife.policy.service.base;

import com.gclife.policy.core.jooq.tables.pojos.PolicyApplicantPo;
import com.gclife.policy.model.bo.PolicyApplicantBo;
import com.gclife.policy.model.bo.PolicyApplicantListBo;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2020/9/18
 */
public interface PolicyApplicantBaseService {

    /**
     * 模糊查询投保人
     * @param keyword 关键字
     * @param applicantType 投保人类型
     * @return
     */
    List<PolicyApplicantBo> listFuzzyPolicyApplicant(String keyword, String applicantType);

    /**
     * 保存保单投保人信息
     * @param policyApplicantPo 保单投保人信息
     * @param userId 用户ID
     */
    void savePolicyApplicant(PolicyApplicantPo policyApplicantPo, String userId);

    List<PolicyApplicantListBo> policyApplicantList(List<String> policyIds);
}
