package com.gclife.policy.service.base.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.policy.core.jooq.tables.daos.*;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.dao.PolicyBaseDao;
import com.gclife.policy.dao.PolicyCoverageBaseDao;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.service.base.PolicyCoverageBaseService;
import com.gclife.policy.service.base.PolicyPremiumBaseService;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 19-8-28
 */
@Service
public class PolicyCoverageBaseServiceImpl extends BaseBusinessServiceImpl implements PolicyCoverageBaseService {
    @Autowired
    private PolicyCoverageBaseDao policyCoverageBaseDao;
    @Autowired
    private PolicyCoverageLevelDao policyCoverageLevelDao;
    @Autowired
    private PolicyCoverageDao policyCoverageDao;
    @Autowired
    private PolicyCoverageDutyDao policyCoverageDutyDao;
    @Autowired
    private PolicyBaseDao policyBaseDao;
    @Autowired
    private PolicyCoverageExtendDao policyCoverageExtendDao;
    @Autowired
    private PolicyCoveragePaymentDao policyCoveragePaymentDao;
    @Autowired
    private PolicyPremiumBaseService policyPremiumBaseService;

    @Override
    public List<PolicyCoverageLevelPo> listPolicyCoverageLevel(String policyId) {
        return policyCoverageBaseDao.listPolicyCoverageLevel(policyId, null);
    }

    /**
     * 查询险种档次
     *
     * @param policyId       保单ID
     * @param coverageDutyId 险种责任ID
     * @return PolicyCoverageLevelPos
     */
    @Override
    public List<PolicyCoverageLevelPo> listPolicyCoverageLevel(String policyId, String coverageDutyId) {
        return policyCoverageBaseDao.listPolicyCoverageLevel(policyId, coverageDutyId);
    }

    @Override
    public List<PolicyCoverageLevelPo> getPolicyCoverageLevel(String coverageId) {
        return policyCoverageLevelDao.fetchByCoverageId(coverageId);
    }

    @Override
    public void addPolicyCoverageLevel(List<PolicyCoverageLevelPo> policyCoverageLevelPos, String userId) {
        policyCoverageLevelPos.forEach(coverageLevelPo -> {
            if (!coverageLevelPo.isForceSave()) {
                coverageLevelPo.setCoverageLevelId(UUIDUtils.getUUIDShort());
                coverageLevelPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            }
            coverageLevelPo.setCreatedUserId(userId);
            coverageLevelPo.setCreatedDate(System.currentTimeMillis());
            coverageLevelPo.setUpdatedUserId(userId);
            coverageLevelPo.setUpdatedDate(System.currentTimeMillis());
        });
        policyCoverageLevelDao.insert(policyCoverageLevelPos);
    }

    /**
     * 批量更新险种档次信息
     *
     * @param policyCoverageLevelPos 险种档次信息
     * @param userId                 用户id
     */
    @Override
    public void updatePolicyCoverageLevel(List<PolicyCoverageLevelPo> policyCoverageLevelPos, String userId) {
        policyCoverageLevelPos.forEach(coverageLevelPo -> {
            coverageLevelPo.setUpdatedUserId(userId);
            coverageLevelPo.setUpdatedDate(System.currentTimeMillis());
        });
        policyCoverageLevelDao.update(policyCoverageLevelPos);
    }

    /**
     * 批量新增险种信息
     *
     * @param policyCoveragePos 险种信息
     * @param userId            用户ID
     */
    @Override
    public void addPolicyCoverage(List<PolicyCoveragePo> policyCoveragePos, String userId) {
        policyCoveragePos.forEach(policyCoveragePo -> {
            if (!policyCoveragePo.isForceSave()) {
                policyCoveragePo.setCoverageId(UUIDUtils.getUUIDShort());
                policyCoveragePo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            }
            policyCoveragePo.setCreatedUserId(userId);
            policyCoveragePo.setCreatedDate(System.currentTimeMillis());
            policyCoveragePo.setUpdatedUserId(userId);
            policyCoveragePo.setUpdatedDate(System.currentTimeMillis());
        });
        policyCoverageDao.insert(policyCoveragePos);
    }

    /**
     * 批量更新险种信息
     *
     * @param policyCoveragePos 险种信息
     * @param userId            用户ID
     */
    @Override
    public void updatePolicyCoverage(List<PolicyCoveragePo> policyCoveragePos, String userId) {
        policyCoveragePos.forEach(policyCoveragePo -> {
            policyCoveragePo.setUpdatedUserId(userId);
            policyCoveragePo.setUpdatedDate(System.currentTimeMillis());
        });
        policyCoverageDao.update(policyCoveragePos);
    }

    /**
     * 批量新增险种责任信息
     *
     * @param policyCoverageDutyPos 险种责任
     * @param userId                用户ID
     */
    @Override
    public void addPolicyCoverageDuty(List<PolicyCoverageDutyPo> policyCoverageDutyPos, String userId) {
        policyCoverageDutyPos.forEach(coverageDutyPo -> {
            if (!coverageDutyPo.isForceSave()) {
                coverageDutyPo.setCoverageDutyId(UUIDUtils.getUUIDShort());
                coverageDutyPo.setValidFlag(com.gclife.common.TerminologyConfigEnum.VALID_FLAG.effective.name());
            }
            coverageDutyPo.setCreatedUserId(userId);
            coverageDutyPo.setCreatedDate(System.currentTimeMillis());
            coverageDutyPo.setUpdatedUserId(userId);
            coverageDutyPo.setUpdatedDate(System.currentTimeMillis());
        });
        policyCoverageDutyDao.insert(policyCoverageDutyPos);
    }

    /**
     * 批量更新险种责任信息
     *
     * @param policyCoverageDutyPos 险种责任
     * @param userId                用户ID
     */
    @Override
    public void updatePolicyCoverageDuty(List<PolicyCoverageDutyPo> policyCoverageDutyPos, String userId) {
        policyCoverageDutyPos.forEach(coverageDutyPo -> {
            coverageDutyPo.setUpdatedUserId(userId);
            coverageDutyPo.setUpdatedDate(System.currentTimeMillis());
        });
        policyCoverageDutyDao.update(policyCoverageDutyPos);
    }

    @Override
    public List<PolicyCoveragePo> listPolicyCoverageOfInsured(String policyIdOrPolicyNo) {
        return this.listPolicyCoverageOfInsured(policyIdOrPolicyNo, null);
    }

    @Override
    public List<PolicyCoveragePo> listPolicyCoverageOfInsured(String policyIdOrPolicyNo, String insuredId) {
        return policyBaseDao.getPolicyCoverageList(policyIdOrPolicyNo, insuredId);
    }

    /**
     * 查询团险保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoveragePo> listGroupPolicyCoverage(String policyId) {
        return policyBaseDao.listGroupPolicyCoverage(policyId);
    }

    /**
     * 查询团险保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoverageLevelBo> listGroupPolicyPaymentCoverageLevel(String policyId) {
        return policyBaseDao.listGroupPolicyPaymentCoverage(policyId);
    }

    /**
     * 查询团险保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoveragePo> listAllGroupPolicyCoverage(String policyId) {
        return policyBaseDao.listAllGroupPolicyCoverage(policyId);
    }

    @Override
    public List<PolicyCoverageBo> listPolicyCoverage(String policyId) {
        List<PolicyCoverageBo> policyCoverageBos;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);

            policyCoverageBos = policyBaseDao.getPolicyCoverageList(policyId);

            PolicyPaymentBo newPolicyPayment = policyBaseDao.getNewPolicyPayment(policyId);

            List<PolicyCoveragePaymentBo> policyCoveragePaymentBos = policyBaseDao.queryPolicyCoveragePaymentByPaymentId(newPolicyPayment.getPolicyPaymentId());

            List<PolicyAddPremiumPo> policyAddPremiumPos = policyPremiumBaseService.listPolicyAddPremium(policyId);

            //设置责任信息
            List<PolicyCoverageDutyBo> policyCoverageDutyBos = this.getPolicyCoverageDutyList(policyId);
            policyCoverageBos.forEach(policyCoverageBo -> {
                policyCoveragePaymentBos.stream().filter(policyCoveragePaymentBo ->
                        policyCoveragePaymentBo.getCoverageId().equals(policyCoverageBo.getCoverageId())).findFirst().ifPresent(policyCoverageBo::setPolicyCoveragePayment
                );
                if (AssertUtils.isNotEmpty(policyCoverageBo.getInsuredId()) && AssertUtils.isNotNull(policyCoverageBo.getPolicyCoveragePremium()) && AssertUtils.isNotEmpty(policyCoverageBo.getPolicyCoveragePremium().getPolicyCoveragePremiumId())) {
                    policyCoveragePaymentBos.stream().filter(policyCoveragePaymentBo ->
                            policyCoveragePaymentBo.getPolicyCoveragePremiumId().equals(policyCoverageBo.getPolicyCoveragePremium().getPolicyCoveragePremiumId())).findFirst().ifPresent(policyCoveragePaymentBo -> policyCoverageBo.getPolicyCoveragePremium().setPolicyCoveragePayment(policyCoveragePaymentBo));
                }
                if (AssertUtils.isNotEmpty(policyAddPremiumPos)) {
                    List<PolicyAddPremiumPo> collect = policyAddPremiumPos.stream().filter(policyAddPremiumPo ->
                            policyAddPremiumPo.getCoverageId().equals(policyCoverageBo.getCoverageId())
                    ).collect(Collectors.toList());
                    if (AssertUtils.isNotEmpty(collect)) {
                        policyCoverageBo.setListAddPremium(collect);
                    }
                }
                //开始进行分组拆分
                if (AssertUtils.isNotEmpty(policyCoverageDutyBos)) {
                    List<PolicyCoverageDutyBo> coverageDutyBos = policyCoverageDutyBos.stream()
                            .filter(policyCoverageDutyBo -> policyCoverageBo.getCoverageId().equals(policyCoverageDutyBo.getCoverageId()))
                            .collect(Collectors.toList());
                    if (TerminologyConfigEnum.WHETHER.YES.name().equals(policyCoverageBo.getDutyChooseFlag()) && AssertUtils.isNotEmpty(coverageDutyBos)) {
                        policyCoverageBo.setListCoverageDuty(coverageDutyBos);
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_ERROR);
            }
        }
        return policyCoverageBos;
    }

    @Override
    public List<PolicyCoverageBo> listPolicyCoverageForPb(String policyId) {
        List<PolicyCoverageBo> policyCoverageBos;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(getLogger(), policyId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL);

            policyCoverageBos = policyBaseDao.getPolicyCoverageListForPb(policyId);

            PolicyPaymentBo newPolicyPayment = policyBaseDao.getNewPolicyPayment(policyId);

            List<PolicyCoveragePaymentBo> policyCoveragePaymentBos = policyBaseDao.queryPolicyCoveragePaymentByPaymentId(newPolicyPayment.getPolicyPaymentId());

            List<PolicyAddPremiumPo> policyAddPremiumPos = policyPremiumBaseService.listPolicyAddPremium(policyId);

            //设置责任信息
            List<PolicyCoverageDutyBo> policyCoverageDutyBos = this.getPolicyCoverageDutyList(policyId);
            policyCoverageBos.forEach(policyCoverageBo -> {
                policyCoveragePaymentBos.stream().filter(policyCoveragePaymentBo ->
                        policyCoveragePaymentBo.getCoverageId().equals(policyCoverageBo.getCoverageId())).findFirst().ifPresent(policyCoverageBo::setPolicyCoveragePayment
                );
                if (AssertUtils.isNotEmpty(policyCoverageBo.getInsuredId()) && AssertUtils.isNotNull(policyCoverageBo.getPolicyCoveragePremium()) && AssertUtils.isNotEmpty(policyCoverageBo.getPolicyCoveragePremium().getPolicyCoveragePremiumId())) {
                    policyCoveragePaymentBos.stream().filter(policyCoveragePaymentBo ->
                            policyCoveragePaymentBo.getPolicyCoveragePremiumId().equals(policyCoverageBo.getPolicyCoveragePremium().getPolicyCoveragePremiumId())).findFirst().ifPresent(policyCoveragePaymentBo -> policyCoverageBo.getPolicyCoveragePremium().setPolicyCoveragePayment(policyCoveragePaymentBo));
                }
                if (AssertUtils.isNotEmpty(policyAddPremiumPos)) {
                    List<PolicyAddPremiumPo> collect = policyAddPremiumPos.stream().filter(policyAddPremiumPo ->
                            policyAddPremiumPo.getCoverageId().equals(policyCoverageBo.getCoverageId())
                    ).collect(Collectors.toList());
                    if (AssertUtils.isNotEmpty(collect)) {
                        policyCoverageBo.setListAddPremium(collect);
                    }
                }
                //开始进行分组拆分
                if (AssertUtils.isNotEmpty(policyCoverageDutyBos)) {
                    List<PolicyCoverageDutyBo> coverageDutyBos = policyCoverageDutyBos.stream()
                            .filter(policyCoverageDutyBo -> policyCoverageBo.getCoverageId().equals(policyCoverageDutyBo.getCoverageId()))
                            .collect(Collectors.toList());
                    if (TerminologyConfigEnum.WHETHER.YES.name().equals(policyCoverageBo.getDutyChooseFlag()) && AssertUtils.isNotEmpty(coverageDutyBos)) {
                        policyCoverageBo.setListCoverageDuty(coverageDutyBos);
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_ERROR);
            }
        }
        return policyCoverageBos;
    }

    /**
     * 查询保单险种
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoveragePo> listPolicyCoveragePo(String policyId) {
        return policyCoverageBaseDao.listPolicyCoveragePo(policyId);
    }

    /**
     * 查询保单ID对应的险种责任
     *
     * @param policyId 保单ID
     * @return list
     */
    @Override
    public List<PolicyCoverageDutyBo> getPolicyCoverageDutyList(String policyId) {
        List<PolicyCoverageDutyBo> policyCoverageDutyBos = policyBaseDao.getPolicyCoverageDutyList(policyId);
        if (AssertUtils.isNotEmpty(policyCoverageDutyBos)) {
            List<PolicyCoverageLevelPo> applyCoverageLevelPos = this.listPolicyCoverageLevel(policyId, null);
            if (AssertUtils.isNotEmpty(applyCoverageLevelPos)) {
                Map<String, List<PolicyCoverageLevelPo>> coverageLevelPoMap =
                        applyCoverageLevelPos.parallelStream()
                                .filter(policyCoverageLevelPo -> AssertUtils.isNotEmpty(policyCoverageLevelPo.getCoverageDutyId()))
                                .collect(Collectors.groupingBy(PolicyCoverageLevelPo::getCoverageDutyId));
                policyCoverageDutyBos.forEach(policyCoverageDutyBo -> {
                    List<PolicyCoverageLevelPo> applyCoverageLevelPo = coverageLevelPoMap.get(policyCoverageDutyBo.getCoverageDutyId());
                    if (AssertUtils.isNotEmpty(applyCoverageLevelPo)) {
                        policyCoverageDutyBo.setListCoverageLevel(applyCoverageLevelPo);
                    }
                });
            }
        }
        return policyCoverageDutyBos;
    }

    /**
     * 保存保单险种信息
     *
     * @param policyCoveragePo 保单险种信息
     */
    @Override
    @Transactional
    public void savePolicyCoverage(PolicyCoveragePo policyCoveragePo) {
        try {
            if (!AssertUtils.isNotEmpty(policyCoveragePo.getCoverageId())) {
                //执行新增
                policyCoveragePo.setCoverageId(UUIDUtils.getUUIDShort());
                policyCoveragePo.setCreatedDate(DateUtils.getCurrentTime());
                policyCoveragePo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyCoverageDao.insert(policyCoveragePo);
            } else {
                //执行修改
                policyCoveragePo.setUpdatedDate(DateUtils.getCurrentTime());
                policyCoverageDao.update(policyCoveragePo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(getLogger(), e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_COVERAGE_ERROR);
        }
    }

    /**
     * 保单险种责任
     *
     * @param policyCoverageDutyPo 险种责任
     */
    @Override
    public void savePolicyCoverageDutyPo(PolicyCoverageDutyPo policyCoverageDutyPo) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(policyCoverageDutyPo.getCoverageDutyId())) {
            policyCoverageDutyPo.setUpdatedDate(currentTime);
            policyCoverageDutyDao.update(policyCoverageDutyPo);
        } else {
            policyCoverageDutyPo.setCoverageDutyId(UUIDUtils.getUUIDShort());
            policyCoverageDutyPo.setCreatedDate(currentTime);
            policyCoverageDutyPo.setValidFlag(com.gclife.common.model.config.TerminologyConfigEnum.VALID_FLAG.effective.name());
            policyCoverageDutyDao.insert(policyCoverageDutyPo);
        }
    }

    /**
     * 将险种扩展表数据写至险种表
     *
     * @param policyCoverageExtendPos 险种扩展信息
     * @return list
     */
    @Override
    public List<PolicyCoveragePo> copyExtendToCoverage(List<PolicyCoverageExtendPo> policyCoverageExtendPos) {
        policyCoverageExtendPos.forEach(policyCoverageExtendPo -> policyCoverageExtendPo.setCoverageStatus(PolicyTermEnum.COVERAGE_STATUS.EFFECTIVE.name()));
        // 更新险种扩展表
        policyCoverageExtendDao.update(policyCoverageExtendPos);

        //移除掉无需续保的扩展表数据
        policyCoverageExtendPos.removeIf(policyCoverageExtendPo -> AssertUtils.isNotEmpty(policyCoverageExtendPo.getRenewalPermitFlag())
                && com.gclife.common.model.config.TerminologyConfigEnum.WHETHER.NO.name().equals(policyCoverageExtendPo.getRenewalPermitFlag()));
        this.getLogger().info("移除掉无需续保的扩展表数据:{}", JackSonUtils.toJson(policyCoverageExtendPos));

        List<PolicyCoveragePo> policyCoveragePos = (List<PolicyCoveragePo>) this.converterList(
                policyCoverageExtendPos, new TypeToken<List<PolicyCoveragePo>>() {
                }.getType()
        );
        // 将险种信息保存至险种表
        policyCoverageDao.insert(policyCoveragePos);

        return policyCoveragePos;
    }

    /**
     * 保存保单险种缴费信息
     *
     * @param policyCoveragePaymentPo 保单险种缴费信息
     */
    @Override
    @Transactional
    public void savePolicyCoveragePayment(PolicyCoveragePaymentPo policyCoveragePaymentPo) {
        try {
            getLogger().info("policyCoveragePaymentPo:" + JSON.toJSONString(policyCoveragePaymentPo));
            if (!AssertUtils.isNotEmpty(policyCoveragePaymentPo.getPolicyCoveragePaymentId())) {
                //执行新增
                policyCoveragePaymentPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
                policyCoveragePaymentPo.setCreatedDate(DateUtils.getCurrentTime());
                policyCoveragePaymentPo.setPolicyCoveragePaymentId(UUIDUtils.getUUIDShort());
                policyCoveragePaymentDao.insert(policyCoveragePaymentPo);
            } else {
                //执行修改
                policyCoveragePaymentPo.setUpdatedDate(DateUtils.getCurrentTime());
                policyCoveragePaymentDao.update(policyCoveragePaymentPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(getLogger(), e, PolicyErrorConfigEnum.POLICY_BASE_SAVE_POLICY_COVERAGE_PAYMENT_ERROR);
        }
    }

    @Override
    public List<PolicyCoveragePaymentBo> queryPolicyCoveragePaymentBo(AppRequestHeads appRequestHeads, String
            policyPaymentId) {
        List<PolicyCoveragePaymentBo> policyCoveragePaymentBoList;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(getLogger(), policyPaymentId, PolicyErrorConfigEnum.POLICY_BASE_PARAMETER_POLICY_PAYMENT_ID_IS_NOT_NULL);

            policyCoveragePaymentBoList = policyBaseDao.queryPolicyCoveragePaymentBo(policyPaymentId);

            getLogger().info("policyCoveragePaymentBoList:" + JSON.toJSONString(policyCoveragePaymentBoList));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(PolicyErrorConfigEnum.POLICY_BASE_QUERY_POLICY_COVERAGE_PAYMENT_ERROR);
            }
        }
        return policyCoveragePaymentBoList;
    }

    /**
     * 查询保单险种列表
     *
     * @param policyPaymentIds 保单支付ID
     * @return List<PolicyCoveragePaymentBo>
     */
    @Override
    public List<PolicyCoveragePaymentBo> queryPolicyCoveragePaymentBos(List<String> policyPaymentIds) {
        return policyBaseDao.queryPolicyCoveragePaymentBos(policyPaymentIds);
    }

    @Override
    public PolicyCoveragePo queryPolicyCoveragePo(String policyId) {
        return policyBaseDao.queryPolicyCoveragePo(policyId);
    }

    /**
     * 批量新增保单险种扩展表数据
     *
     * @param policyCoverageExtendPos 保单险种扩展数据
     */
    @Override
    public void addPolicyCoverageExtend(List<PolicyCoverageExtendPo> policyCoverageExtendPos) {
        if (AssertUtils.isNotEmpty(policyCoverageExtendPos)) {
            policyCoverageExtendPos.forEach(policyCoverageExtendPo -> {
                policyCoverageExtendPo.setCoverageExtendId(UUIDUtils.getUUIDShort());
                policyCoverageExtendPo.setCreatedDate(DateUtils.getCurrentTime());
            });
            policyCoverageExtendDao.insert(policyCoverageExtendPos);
        }
    }

    /**
     * 查询待生效险种扩展信息
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoverageExtendPo> listPendingCoverageExtend(String policyId) {
        return policyBaseDao.listPendingCoverageExtend(policyId);
    }

    /**
     * 查询待生效险种扩展信息
     *
     * @param policyId 保单ID
     * @param dataType 数据类型
     * @return
     */
    @Override
    public List<PolicyCoverageExtendPo> listPendingCoverageExtend(String policyId, String dataType) {
        return policyBaseDao.listPendingCoverageExtend(policyId, dataType);
    }

    /**
     * 查询待生效险种扩展信息
     *
     * @param policyIds 保单ID集
     * @param dataType  数据类型
     * @return
     */
    @Override
    public List<PolicyCoverageExtendPo> listPendingCoverageExtend(List<String> policyIds, String dataType) {
        return policyBaseDao.listPendingCoverageExtend(policyIds, dataType);
    }

    /**
     * 查询保单险种信息
     *
     * @param policyId 保单ID
     * @return
     */
    @Override
    public List<PolicyCoveragePo> queryPolicyCoverage(String policyId) {
        return policyBaseDao.queryPolicyCoverage(policyId);
    }

    /**
     * 根据险种ID查询险种扩展信息
     *
     * @param coverageIds 险种IDS
     * @param dataType    数据类型
     * @return
     */
    @Override
    public List<PolicyCoverageExtendPo> listCoverageExtendByCoverageId(List<String> coverageIds, String dataType) {
        return policyBaseDao.listCoverageExtendByCoverageId(coverageIds, dataType);
    }

    /**
     * 删除险种信息
     *
     * @param policyCoveragePos 险种数据
     */
    @Override
    public void deletePolicyCoverage(List<PolicyCoveragePo> policyCoveragePos) {
        if (AssertUtils.isNotEmpty(policyCoveragePos)) {
            policyCoverageDao.delete(policyCoveragePos);
        }
    }

    /**
     * 删除险种缴费信息
     *
     * @param policyCoveragePaymentPo 险种缴费信息
     */
    @Override
    public void deletePolicyCoveragePayment(PolicyCoveragePaymentPo policyCoveragePaymentPo) {
        if (AssertUtils.isNotNull(policyCoveragePaymentPo)) {
            policyCoveragePaymentDao.delete(policyCoveragePaymentPo);
        }
    }

    /**
     * 删除险种扩展数据
     *
     * @param policyCoverageExtendPos 险种扩展数据
     */
    @Override
    public void deletePolicyCoverageExtend(List<PolicyCoverageExtendPo> policyCoverageExtendPos) {
        if (AssertUtils.isNotEmpty(policyCoverageExtendPos)) {
            policyCoverageExtendDao.delete(policyCoverageExtendPos);
        }
    }

    /**
     * 批量更新险种扩展信息
     *
     * @param policyCoverageExtendPos 险种扩展信息
     * @param userId                  用户ID
     */
    @Override
    public void updatePolicyCoverageExtend(List<PolicyCoverageExtendPo> policyCoverageExtendPos, String userId) {
        policyCoverageExtendPos.forEach(policyCoverageExtendPo -> {
            policyCoverageExtendPo.setUpdatedUserId(userId);
            policyCoverageExtendPo.setUpdatedDate(System.currentTimeMillis());
        });
        policyCoverageExtendDao.update(policyCoverageExtendPos);
    }

    /**
     * 保存险种扩展信息
     *
     * @param policyCoverageExtendPo 险种扩展信息
     * @param userId                 用户ID
     */
    @Override
    public void savePolicyCoverageExtend(PolicyCoverageExtendPo policyCoverageExtendPo, String userId) {
        if (!AssertUtils.isNotEmpty(policyCoverageExtendPo.getCoverageExtendId())) {
            //执行新增
            policyCoverageExtendPo.setValidFlag(PolicyTermEnum.VALID_FLAG.effective.name());
            policyCoverageExtendPo.setCreatedDate(DateUtils.getCurrentTime());
            policyCoverageExtendPo.setCoverageExtendId(UUIDUtils.getUUIDShort());
            policyCoverageExtendDao.insert(policyCoverageExtendPo);
        } else {
            //执行修改
            policyCoverageExtendPo.setUpdatedDate(DateUtils.getCurrentTime());
            policyCoverageExtendPo.setUpdatedUserId(userId);
            policyCoverageExtendDao.update(policyCoverageExtendPo);
        }
    }

    @Override
    public List<PolicyCoverageDutyBo> queryPolicyCoverageDutyBoByCoverageIds(List<String> coverageIds) {
        return policyBaseDao.queryPolicyCoverageDutyBoByCoverageIds(coverageIds);
    }

    @Override
    public List<PolicyCoverageLevelPo> queryListPolicyCoverageLevel(List<String> coverageIds) {
        return policyCoverageBaseDao.queryListPolicyCoverageLevel(coverageIds);
    }

    @Override
    public List<PolicyCoverageBo> queryListPolicyCoverageReportUnderwriting(BasePageRequest basePageRequest, String startDate) {
        return policyCoverageBaseDao.queryListPolicyCoverageReportUnderwriting(basePageRequest, startDate);
    }

    @Override
    public List<PolicyCoveragePo> queryReportPolicyCoverage(String policyId) {

        return policyCoverageBaseDao.queryReportPolicyCoverage(policyId);
    }

    @Override
    public List<PolicyCoverageDutyBo> queryReportPolicyCoverageDuty(String policyId) {
        return policyCoverageBaseDao.queryReportPolicyCoverageDuty(policyId);

    }

    @Override
    public List<PolicyCoverageLevelPo> queryReportPolicyCoverageLevel(String policyId) {
        return policyCoverageBaseDao.queryReportPolicyCoverageLevel(policyId);
    }

    @Override
    public List<PolicyCoveragePo> listPolicyInsuredCoverageByValidFlag(String policyId, String insuredId) {
        return policyCoverageBaseDao.listPolicyInsuredCoverageByValidFlag(policyId, insuredId);
    }

    @Override
    public List<PolicyCoverageLevelPo> listPolicyInsuredCoverageByValidFlag(List<String> coverageIds) {
        return policyCoverageBaseDao.listPolicyInsuredCoverageByValidFlag(coverageIds);
    }

    @Override
    public List<PolicyCoverageDutyBo> queryPolicyCoverageDutyValidFlagByCoverageIds(List<String> coverageIds) {
        return policyBaseDao.queryPolicyCoverageDutyValidFlagByCoverageIds(coverageIds);
    }

    /**
     * 批量删除险种档次信息
     *
     * @param coverageLevelPos 险种档次信息
     */
    @Override
    public void deletePolicyCoverageLevel(List<PolicyCoverageLevelPo> coverageLevelPos) {
        policyCoverageLevelDao.delete(coverageLevelPos);
    }

    /**
     * 批量删除险种责任
     *
     * @param coverageDutyPos 险种责任信息
     */
    @Override
    public void deletePolicyCoverageDuty(List<PolicyCoverageDutyPo> coverageDutyPos) {
        policyCoverageDutyDao.delete(coverageDutyPos);
    }

    @Override
    public List<PolicyCoverageBo> queryCoverageByInsuredNo(String idNo) {
        return policyCoverageBaseDao.queryCoverageByInsuredNo(idNo);
    }

    @Override
    public List<PolicyCoverageBo> queryPolicyCoverageByIdList(List<String> coverageIdList) {
        return policyCoverageBaseDao.queryPolicyCoverageByIdList(coverageIdList);
    }

    @Override
    public List<PolicyCoverageBo> queryPolicyCoverageByVersionNoAndCoverageIds(List<String> policyVersionNoAndCoverageIdList) {
        return policyCoverageBaseDao.queryPolicyCoverageByVersionNoAndCoverageIds(policyVersionNoAndCoverageIdList);
    }

    /**
     * 根据险种ID集查询险种信息
     *
     * @param coverageIds 险种ID集
     * @return
     */
    @Override
    public List<PolicyCoveragePo> listPolicyCoverageByIds(List<String> coverageIds) {
        return policyCoverageBaseDao.listPolicyCoverageByIds(coverageIds);
    }

    @Override
    public List<PolicyCoverageBo> queryAmountByCustomerId(List<String> customerAgentIds) {
        return policyCoverageBaseDao.queryAmountByCustomerId(customerAgentIds);
    }

    /**
     * 查询险种数据
     * @param policyIds 保单ID
     * @return
     */
    @Override
    public List<PolicyCoveragePo> listCoverage(List<String> policyIds) {
        return policyCoverageBaseDao.listCoverage(policyIds);
    }
}
