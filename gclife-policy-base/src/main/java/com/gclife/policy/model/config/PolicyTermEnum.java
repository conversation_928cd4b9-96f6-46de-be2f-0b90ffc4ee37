package com.gclife.policy.model.config;

/**
 * <AUTHOR>
 * 术语类型枚举
 */
public class PolicyTermEnum {

    private PolicyTermEnum() {
        throw new AssertionError();
    }

    public interface ConstantType {
    }


    /**
     * 缴费周期系数
     */
    public enum PREMIUM_FREQUENCY_CONVERSION_FACTOR implements ConstantType {
        YEAR(1.0),
        SEMIANNUAL(0.52),
        SEASON(0.27),
        MONTH(0.09),
        SINGLE(1.0),;

        private String code;
        private Double value;


        private PREMIUM_FREQUENCY_CONVERSION_FACTOR(Double value) {
            this.value = value;
        }

        public String code() {
            return code;
        }

        public Double value() {
            return value;
        }
    }

    public enum REPORT_TYPE implements ConstantType {
        /**
         * 报表类型
         */
        POLICY("业务报表-承保清单"),
        REGULATORY_POLICY("监管报表-承保清单"),
        PAYMENT("财务报表"),
        CUSTOMER("投保人资料报表"),
        RENEWAL("续期报表");
        private String code;
        private String desc;

        private REPORT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 变更标志
     */
    public enum CHANGE_FLAG implements ConstantType {

        UNCHANGE("未变更"),
        CHANGE("变更"),
        ADD("新增"),
        SUBTRACT("减少"),
        ;

        private String desc;

        CHANGE_FLAG(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return this.desc;
        }
    }

    /**
     * 操作编码
     */
    public enum OPERATION_CODE implements ConstantType {
        RENEWAL_PENDING_PAYMENT("续期待支付"),
        RENEWAL_FINISHED("续期完成"),
        RENEWAL_INSURANCE_APPLY("续保申请"),
        RENEWAL_INSURANCE_PENDING_REVIEW("续保待审核"),
        RENEWAL_INSURANCE_PENDING_PAYMENT("续保待支付"),
        RENEWAL_INSURANCE_FINISHED("续保完成"),
        RENEWAL_INSURANCE_RE_APPLY("续保重新申请"),
        RENEWAL_INSURANCE_EFFECTIVE("续保生效"),

        GROUP_CLAIM_UNFINISHED("团险理赔未完成"),
        GROUP_CLAIM_FINISHED("团险理赔已完成"),
        GROUP_ENDORSE_UNFINISHED("团险保全未完成"),
        GROUP_ENDORSE_FINISHED("团险保全已完成"),
        GROUP_RENEWAL_APPLIED("团险续保已申请"),
        // 团险续保生效时改为已完成
        GROUP_RENEWAL_FINISHED("团险续保已完成"),

        GROUP_INSTALLMENT_PENDING_PAYMENT("团险续期待支付"),
        GROUP_INSTALLMENT_FINISHED("团险续期完成"),

        ;

        private String desc;

        OPERATION_CODE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum RENEWAL_DATE_PROPERTY implements ConstantType {
        /**
         * 续期日期属性
         */
        GRACE_PERIOD("宽限期"),
        INVALID_PERIOD_YEAR("永久失效期(年)"),
        ;

        private String desc;

        RENEWAL_DATE_PROPERTY(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum PAY_NOTIFY_STATUS implements ConstantType {
        /**
         * 支付通知结果集
         */
        PAYMENT_FAILED("支付失败"),
        PAYMENT_FINISHED("支付完成"),
        PAYMENT_WAITTING("等待支付"),
        PAYMENT_INITIAL("初始化"),
        PAYMENT_TIMEOUT("支付超时"),
        PAYMENT_SUCCESS("支付成功"),
        PAYMENT_AUDIT("等待审核"),
        PAYMENT_AUDIT_NOPASS("审核不通过"),
        PAYMENT_FINISHED_ADJUST("支付完成调整"),
        PAYMENT_AUDIT_PASS("审核通过"),
        PAYMENT_PROCESSING("支付中"),
        PAYMENT_HOOK("支付挂起"),
        ;
        private String code;
        private String desc;

        private PAY_NOTIFY_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费方式
     */
    public enum PAYMENT_METHODS implements ConstantType {
        CASH("现金交易"),
        BANK_TRANSFER("银行转账"),
        BANK_DEDUCT("银行划扣"),
        WING_H5("WING在线支付"),
        WING_OFFLINE("wing线下支付"),
        OVER_PAYMENT_ACCOUNT_DEDUCTION("溢缴账户抵扣"),
        ABA_PAYMENTS("ABA支付")
        ;

        private String code;
        private String desc;

        private PAYMENT_METHODS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 团险续保状态
     */
    public enum GROUP_RENEWAL_STATUS implements ConstantType {
        APPLYING("待申请"),
        AUDITING("待审核"),
        AUDIT_NO_PASS("审核不通过"),
        UNDERWRITING("待核保"),
        UNDERWRITE_REFUND("拒保"),
        PAYMENT("待缴费"),
        RENEWALED("已续保"),
        EFFECTIVE("已生效"),
        INVALID("失效"),
        ;

        private String desc;

        GROUP_RENEWAL_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum RENEWAL_STATUS implements ConstantType {
        /**
         * 续期状态
         */
        INITIAL("初始化"),
        SEPERATE("分单"),
        INVALID("失效"),
        INVALID_THOROUGH("永久失效"),
        PAYMENT("待缴费"),
        ACTUAL_PAY("实收"),
        REFUND("退费"),
        ARCHIVE("归档"),
        CANCEL("取消"),
        APPLYING("待申请"),
        AUDITING("待审核"),
        RENEWALED("已续保"),
        EFFECTIVE("已生效"),
        ;

        private String code;
        private String desc;

        private RENEWAL_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum RENEWAL_TYPE implements ConstantType {
        /**
         * 续期类型
         */
        RENEWAL("续期"),
        RENEWAL_INSURANCE("续保"),
        GROUP_RENEWAL("团险续保"),
        GROUP_INSTALLMENT("团险续期"),
        //以下不用加入国际化，app保单列表特殊处理字段
        ALL("全部"),
        ;

        private String code;
        private String desc;

        RENEWAL_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 佣金业务类型
     */
    public enum COMMISSION_BUSINESS_TYPE implements ConstantType {

        BUSINESS_TYPE_NEW_CONTRACT("新契约"),
        BUSINESS_TYPE_RENEWAL("续期"),
        BUSINESS_TYPE_RENEWAL_INSURANCE("续保"),
        BUSINESS_TYPE_ENDORSE("保全"),
        BUSINESS_TYPE_GROUP_RENEWAL("团险续保"),
        BUSINESS_TYPE_GROUP_INSTALLMENT("团险续期"),
        BUSINESS_TYPE_ENDORSE_MODE_OF_PAYMENT_MODIFY("缴费周期变更"),
        BUSINESS_TYPE_ENDORSE_ADD_ADDITIONAL("增加附加险"),
        ;

        private String code;
        private String desc;

        COMMISSION_BUSINESS_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 佣金业务状态
     */
    public enum COMMISSION_BUSINESS_STATUS implements ConstantType {
        BUSINESS_STATUS_LOCK("锁定"),
        BUSINESS_STATUS_UNLOCK("解除锁定");

        private String code;
        private String desc;

        COMMISSION_BUSINESS_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum AGENT_STATUS implements ConstantType {
        /**
         * 业务员状态(INDUCTION：就职，DRAG:离职)
         */
        NON_ENTRY("NON_ENTRY", "未入职"),
        INDUCTION("INDUCTION", "就职"),
        DRAG("DRAG", "离职"),
        ;

        private String code;
        private String desc;

        private AGENT_STATUS(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum EXPIRY_PERIOD_UNIT implements ConstantType {
        /**
         * 永久失效期单位
         */
        YEAR("YEAR", "年"),
        MONTH("MONTH", "月"),
        DAY("DAY", "日"),
        ;

        private String code;
        private String desc;

        private EXPIRY_PERIOD_UNIT(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费终止期间单位
     */
    public enum PRODUCT_PREMIUM_PERIOD_UNIT implements ConstantType {
        PREMIUM_PERIOD_UNIT("缴费期限类型"),
        YEAR("年"),
        LIFELONG("终身"),
        MONTH("月"),
        DAY("日"),
        SINGLE("一次性缴费"),
        AGE("岁");

        private String code;
        private String desc;

        private PRODUCT_PREMIUM_PERIOD_UNIT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum WORKFLOW_ITEM_STATUS implements ConstantType {
        /**
         * 工作流状态
         */
        PROCESSING("处理中"),
        NEW_TASK("新任务"),
        GO_BACK("问题件"),
        ;

        private String desc;

        WORKFLOW_ITEM_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 影像件上传状态
     */
    public enum UPLOAD_STATUS implements ConstantType {

        UNDONE("未上传"),
        UPLOADING("上传中"),
        COMPLETE("已上传");

        private String code;
        private String desc;

        private UPLOAD_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 有效标识有效标识(effective:有效，invalid:失效)
     */
    public enum VALID_FLAG implements ConstantType {

        effective("有效"),
        invalid("失效");

        private String code;
        private String desc;

        private VALID_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    public enum POLICY_UPLOAD_BATCH_STATUS implements ConstantType {
        INITIAL("初始化"),
        PARAM_VALID("参数校验"),
        BUSINESS_VALID("业务校验"),
        COMPLETE("完成");

        private String code;
        private String desc;

        private POLICY_UPLOAD_BATCH_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    public enum RESOURCE implements ConstantType {
        /**
         * 资源
         */
        RESOURCE_APPLY_APPLYLIST("受理列表"),
        RESOURCE_APPLY_IMAGEPARTSLIST("影像上传列表"),
        RESOURCE_APPLY_PENDINGENTRYLIST("投保单录入"),
        RESOURCE_APPLY_UNDERWRITELIST("人工核保"),
        RESOURCE_APPLY_POLICYPRINT("保单打印"),
        RESOURCE_APPLY_REVIEWPOLICYRECEIPT("保单回执复核"),
        AGENT_CONFIRM_TASK("问题件工作流"),
        IMAGE_SCANNING_TASK("影像件上传"),
        RECEIVE_TASK("受理"),
        RETURN_RECEIPT_TASK("回执回销"),
        REVIEW_TASK("复核"),
        ARTIFICIAL_UNDERWRITING_TASK("人工核保"),
        POLICY_PRINT_TASK("保单打印"),
        NEW_ENTRY_TASK("新单录入"),
        CUSTOMER_SIGN_TASK("客户签收"),
        RECEIPT_REVIEW_TASK("回执复核"),
        ;

        private String code;
        private String desc;

        private RESOURCE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum MSG_BUSINESS_TYPE implements ConstantType {
        /**
         * 消息推送类型
         */
        POLICY_PRINT_CP("保单打印完成"),
        POLICY_RECEIPT_INPUT_CP("回执录入完成"),
        POLICY_RECEIPT_REVIEW_CP("回执复核完成"),
        GROUP_POLICY_RECEIPT_REVIEW_CP("团险回执复核完成"),
        GROUP_POLICY_RECEIPT_INPUT_CP("团险回执录入完成"),
        POLICY_AFTER_HESITATION("过犹豫期"),
        POLICY_ASSIGN_CP("生成分单数据时的消息提醒"),
        POLICY_ASSIGN_AUDIT("分单申请提交的消息提醒"),
        POLICY_ASSIGN_AUDIT_NO_PASS("分单审核不通过的消息提醒"),
        POLICY_RENEWAL_SEPERATE_POLICY_COMPLETE("分单审核完成"),
        POLICY_RETURN_VISIT_CHANGE("保单回访变更"),
        SERVICE_STAFF_ASSIGNMENT_APPLICATION_SUBMISSION("服务人员分配申请提交"),
        SERVICE_STAFF_ASSIGNMENT_REVIEW_FAILED("服务人员分配审核不通过"),
        SERVICE_STAFF_ASSIGNMENT_REVIEW_APPROVED("服务人员分配审核通过"),
        SERVICE_STAFF_ASSIGNMENT_GENERATE_THE_ASSIGNES_DATA("服务人员分配生成分配数据时"),
        POLICY_UNDERWRITTEN_TO_CUSTOMER("保单承保后发送客户"),

        RENEWAL_INSURANCE_NOTIFY_CUSTOMER("客户续保通知"),
        GROUP_RENEWAL_INSURANCE_NOTICE_TO_AGENT("团险续保通知业务员"),
        GROUP_RENEWAL_INSURANCE_NOTICE_TO_DELEGATE("团险续保通知客户"),
        APP_UPLOAD_RECEIPT_WECHAT_REMINDER("App上传回执微信提醒")
        ;
        private String code;
        private String desc;

        private MSG_BUSINESS_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 是否    YES   NO
     */
    public enum YES_NO implements ConstantType {

        YES("是"),
        NO("否");

        private String code;
        private String desc;

        private YES_NO(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 国际化类型
     */
    public enum INTERNATIONAL_TEXT implements ConstantType {
        GENDER("性别"),
        RELATIONSHIP_WITH_THE_INSURED("与被保人的关系"),
        NATIONALITY("国籍"),
        MARITAL_STATUS("婚姻状况"),
        ID_TYPE("证件类型"),
        SOCIAL_SECURITY("是否有社保"),
        CERTIFY_ATTACHMENT("单证附件"),
        PRODUCT_PAY_INTERVAL("缴费周期"),
        PRODUCT_BOUNS_GET_MODE("红利领取方式"),
        PRODUCT_LIVE_GET_MODE("生存金领取方式"),
        PRODUCT_INSURANCE_YEAR_UNIT("保险期间单位"),
        PRODUCT_COVERAGE_PERIOD_UNIT("保险期间单位"),
        PRODUCT_PERSION_GET_MODE("养老金领取方式"),
        PRODUCT_PERSION_GET_DATE("养老金领取时间"),
        PRODUCT_PERSION_PAY_MODE("养老金给付方式"),
        QUESTIONAIRE_ANSWER("健康告知答案"),
        WORKFLOW_TASK_END_STATUS("工作流任务处理结果"),
        BALANCE_STATUS("结算状态"),
        SIGN_TYPE("签单类型"),
        RECEIVABLE_STATUS("应收状态"),
        ACTUAL_STATUS("实收状态"),
        PREMIUM_CHECK_ATTACHMENT_TYPE("对账附件"),
        CHANNEL_TYPE("渠道类型"),


        ;
        private String code;
        private String desc;

        private INTERNATIONAL_TEXT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    public enum WORKFLOW_DICTIONARIES implements ConstantType {
        WORKFLOW_TASK_END_STATUS("工作流任务处理结果"),
        WORKFLOW_TASK_STATUS("工作流任务状态"),
        ;


        private String code;
        private String desc;

        private WORKFLOW_DICTIONARIES(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 有效标识有效标识(QUESTION:有效，invalid:失效)
     */
    public enum QUESTION_TYPE implements ConstantType {

        QUESTION("问题"),
        REMARK("备注");

        private String code;
        private String desc;

        private QUESTION_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 客户类型
     */
    public enum CUSTOMER_TYPE implements ConstantType {

        APPLICANT("投保人"),
        APPLICANT_DELEGATE("投保人代表"),
        INSURED("被保人"),
        AGENT("业务员"),
        ACCEPT("受理人"),
        BENEFICIARY("受益人"),
        INPUT("录入人");

        private String code;
        private String desc;

        private CUSTOMER_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 是否需要投保单影像件
     */
    public enum IS_IMAGE_FLAG implements ConstantType {

        NEED("需要"),
        NEEDLESS("不需要");

        private String code;
        private String desc;

        private IS_IMAGE_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 产品字段
     */
    public enum PRODUCT_FIELD implements ConstantType {

        PRODUCT_PAY_INTERVAL("缴费周期"),
        PRODUCT_INSURANCE_YEAR("保险期间"),
        PRODUCT_PREMIUM("保费"),
        PRODUCT_AMOUNT("保额"),
        PRODUCT_LIVE_GET_MODE("生存金领取方式"),
        PRODUCT_PAY_END_YEAR("缴费期限"),
        PRODUCT_BONUS_GET_MODE("红利领取方式"),
        PRODUCT_INTERESTDIF_TYPE("利差返还类型"),
        PRODUCT_MULT("份数"),
        PRODUCT_PERSION_GET_MODE("养老金领取方式"),
        PRODUCT_PAY_END_YEAR_UNIT("缴费期限单位"),
        PRODUCT_PERSION_GET_DATE("养老金领取时间"),
        PRODUCT_PERSION_PAY_MODE("养老金给付方式"),
        PRODUCT_COVERAGE_PERIOD_UNIT("保险期间单位"),
        PRODUCT_MAIN_PRODUCT_FLAG("主附险标识"),
        CURRENCY("币种"),

        ;

        private String code;
        private String desc;

        private PRODUCT_FIELD(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 强制核保标志
     */
    public enum FORCE_MANUAL_UNDERWRITE_FLAG implements ConstantType {

        FORCE("强制人工核保"),
        UNFORCE("不强制人工核保");

        private String code;
        private String desc;

        private FORCE_MANUAL_UNDERWRITE_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 保单类型
     */
    public enum POLICY_TYPE_FLAG implements ConstantType {

        LIFE_INSURANCE("寿险"),
        SHORT_TERM_INSURANCE("短期险");

        private String code;
        private String desc;

        private POLICY_TYPE_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum POLICY_TYPE implements ConstantType {
        /**
         * 保单类型
         */
        LIFE_INSURANCE_GROUP("寿险团险"),
        LIFE_INSURANCE_PERSONAL("寿险个险"),
        STATUTORY_TRAVEL_ACCIDENT_INSURANCE("法定旅游意外险");;

        private String code;
        private String desc;

        private POLICY_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 回执状态
     */
    public enum RECEIPT_STATUS implements ConstantType {
        WAIT_RECEIPT("待回执"),
        WAIT_REVIEW("待复核"),
        REVIEW_COMPLETE("复核完成"),
        SALE_APP_UPLOADED("銷售app已上傳"),
        ;
        private String desc;

        RECEIPT_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 附件类型   + 排序
     */
    public enum ATTACHMENT_TYPE_FLAG implements ConstantType {
        FIRST_ISSUE_BOOK("首刊"),
        FIRST_ISSUE_BOOK_ONLINE("网销产品首刊目录"),
        POLICY_BOOK("保单"),
        POLICY_ALL_BOOK("保单(完整)"),
        POLICY_TERMS_BOOK("保单条款"),
        PREMIUM_RATE_AND_CASH_VALUE("现金价值"),
        APPLY_BOOK("投保单"),
        APPLICANT_HEALTH_BOOK("投保人健康告知书"),
        INSURED_HEALTH_BOOK("被保人健康告知书"),
        PLAN_BOOK("计划书"),
        POLICY_CONFIRM("保险证确认书"),
        ACKNOWLEDGMENT_LETTER_BOOK("签收回执"),
        APPLY_RECEIPT_BOOK("收据"),
        POLICY_INSURED_LIST("保单被保人清单"),
        CUSTOMER_SERVICE_INSTRUCTION_BOOK("客户服务指南"),


        RECEIPT_IMAGE("回执影像"),
        COI_BOOK("COI"),

        ;

        private String desc;

        ATTACHMENT_TYPE_FLAG(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum BUSINESS_TYPE implements ConstantType {

        POLICY("保单"),
        APPLY("投保单"),
        PLAN("计划书"),
        RENEWAL_INSURANCE("续保"),
        ENDORSE_ADD_ADDITIONAL("增加附加险"),
        ENDORSE_MODIFY_PREMIUM_FREQUENCY("缴费周期变更"),
        REINSTATEMENT("复效"),
        GROUP_RENEWAL("团险续保"),
        ;

        private String code;
        private String desc;

        private BUSINESS_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum POLICY_BUSINESS_TYPE implements ConstantType {
        POLICY("保单"),
        RENEWAL("续期"),
        RENEWAL_INSURANCE("续保"),
        ;

        private String code;
        private String desc;

        private POLICY_BUSINESS_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum APPLICANT_TYPE implements ConstantType {
        /**
         * 投保人类型
         */
        PERSONAL("个人"),
        GROUP("团体"),
        ;

        private String code;
        private String desc;

        private APPLICANT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum CERTIFY_ATTACHMENT_TYPE implements ConstantType {
        //保单附件
        POLICY_BOOK("保单书"),
        RECEIPT_IMAGE("回执影像"),
        CERTIFY_ATTACHMENT_APPLY_BOOK("投保书"),
        CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE("投保人身份证"),
        CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE("被保人身份证件"),
        CERTIFY_ATTACHMENT_APPLY_OTHER("其它证件"),
        ;

        private String desc;

        CERTIFY_ATTACHMENT_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 产品字段是否需要录入标识
     */
    public enum PRODUCT_FIELD_INPUT implements ConstantType {

        INPUT("录入"),
        DEFAULT("默认(采用lable展示)"),
        SELECT("选择"),
        NOT("不录入");

        private String code;
        private String desc;

        private PRODUCT_FIELD_INPUT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 打印PDF类型
     */
    public enum PDF_TYPE implements ConstantType {

        PLAN("计划书pdf"),

        APPLY("投保单pdf"),

        POLICY("保单pdf"),
        ;

        private String desc;

        PDF_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum PRODUCT_PRIMARY_FLAG implements ConstantType {

        MAIN("主险"),
        ADDITIONAL("附加险");

        private String code;
        private String desc;

        private PRODUCT_PRIMARY_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum REMARK_RESULT implements ConstantType {
        /**
         * 审核结论
         */
        AUDIT_FAILED("审核未通过"),
        AUDIT_PASS("审核通过"),
        ;

        private String code;
        private String desc;

        private REMARK_RESULT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum REMARK_TYPE implements ConstantType {
        /**
         * 退费类型
         */
        REVIEW("审核"),
        APPLY("申请"),
        CONFIRM("确认"),
        PAYMENT_APPLY("支付申请"),
        PAYMENT_AUDIT("支付审核"),
        PAYMENT("缴费"),
        PAYMENT_CONFIRM("缴费确认"),
        ;

        private String desc;

        REMARK_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum ASSIGN_STATUS implements ConstantType {
        /**
         * 保单分单状态
         */
        INITIAL("初始化"),
        PENDING_REVIEW("待审核"),
        UNDER_AUDIT("审核中"),
        AUDIT_FAILED("审核未通过"),
        ASSIGNED("已指派"),
        ;

        private String code;
        private String desc;

        private ASSIGN_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 服务业务员分配状态
     */
    public enum SERVICE_STAFF_ASSIGN_STATUS implements ConstantType {
        PENDING_ASSIGNMENT("待分配"),
        PENDING_REVIEW("待审核"),
        UNDER_AUDIT("审核中"),
        AUDIT_FAILED("审核未通过"),
        ASSIGNED("已指派"),
        ;

        private String code;
        private String desc;

        private SERVICE_STAFF_ASSIGN_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 业务员异动类型
     */
    public enum AGENT_CHANGE_TYPE implements ConstantType {
        SERVICE_PERSON_MOVEMENT("服务业务员变动"),
        ORIGINAL_SALESPERSON_MOVEMENT("初始业务员变动"),
        ;

        private String code;
        private String desc;

        private AGENT_CHANGE_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum POLICY_STATUS_FLAG implements ConstantType {
        /**
         * code是客户APP保单的保单状态
         */
        POLICY_STATUS("POLICY_STATUS","保单状态"),
        NEW_POLICY_STATUS("POLICY_STATUS","保单状态"),
        //以下状态为终止
        POLICY_STATUS_INDEMNITY_TERMINATION("INVALID","赔付终止"),
        POLICY_STATUS_HESITATION_REVOKE("SURRENDER","犹豫期撤单"),
        POLICY_STATUS_INVALID_THOROUGH("INVALID","永久失效"),
        POLICY_STATUS_EFFECT_TERMINATION("INVALID","效力终止"),
        POLICY_STATUS_SURRENDER("SURRENDER","退保"),
        POLICY_STATUS_IEXPIRE("INVALID","保单满期"),

        //以下状态未终止
        POLICY_STATUS_WAIT_RENEWAL("EFFECTIVE","待续保"),
        POLICY_STATUS_PENDING_EFFECT("EFFECTIVE","待生效"),
        POLICY_STATUS_INVALID("INVALID","失效"),
        POLICY_STATUS_EFFECTIVE("EFFECTIVE","有效"),
        POLICY_STATUS_REINSTATEMENT("EFFECTIVE","保单复效"),
        POLICY_STATUS_EXTEND("EFFECTIVE","展期"),
        POLICY_STATUS_WAIVER_PREMIUM("EFFECTIVE","豁免保费"),
        POLICY_EFFECTIVE_HC("EFFECTIVE","保单暂予承保"),
        ;
        private String code;
        private String desc;

        private POLICY_STATUS_FLAG(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 被保人保障状态
     */
    public enum INSURED_STATUS implements ConstantType {

        PENDING_EFFECT("未开始"),
        EFFECTIVE("保障中"),
        SURRENDER("退保终止"),
        EXPIRED("满期终止"),
        INDEMNITY_TERMINATION("赔付终止"),
        REPLACED("被替换"),
        ;

        private String desc;

        INSURED_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 险种状态
     */
    public enum COVERAGE_STATUS implements ConstantType {
        COVERAGE_STATUS("险种状态"),

        PENDING_EFFECT("待生效"),
        EFFECTIVE("有效"),
        INVALID("失效"),
        EXPIRED("满期终止"),
        INDEMNITY_TERMINATION("赔付终止"),
        ;

        private String desc;

        COVERAGE_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 客户APP保单状态
     */
    public enum CLIENT_POLICY_STATUS implements ConstantType {

        EFFECTIVE("保障中"),
        INVALID("已失效"),
        SURRENDER("已退保"),
        ;

        private String desc;

        CLIENT_POLICY_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 加费状态
     */
    public enum ADD_PREMIUM_STATUS implements ConstantType {
        EFFECTIVE("有效"),
        EXPIRED("满期终止"),
        ;

        private String desc;

        ADD_PREMIUM_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 保单挂起状态
     */
    public enum HOOK_STATUS implements ConstantType {

        HOOK("挂起"),
        CANCEL("取消挂起"),
        ;

        private String desc;

        HOOK_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 保全费用备注
     */
    public enum POLICY_INSURED_HOOK_IS_EXIST implements ConstantType {
        ZH_CN("被保人(%s)已挂起，请勿操作"),
        EN_US("The insured (%s) is suspended, please do not operate");

        private String desc;

        POLICY_INSURED_HOOK_IS_EXIST(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum STATUS_CLASS implements ConstantType {

        STATUS_CLASS_EFFECTIVE("保障中"),
        STATUS_CLASS_RENEWAL_PAYMENT_WAITTING("待续期"),

        STATUS_CLASS_WAIT_RENEWAL("待续保"),
        STATUS_CLASS_POLICY_INVALID("已终止"),
        ;
        private String code;
        private String desc;

        private STATUS_CLASS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 强制发放标志：　ENFORCE_GRANT：强制发放，ENFORCE_NO_GRANT:强制不发放
     */
    public enum ENFORCE_GRANT_FLAG implements ConstantType {

        ENFORCE_GRANT("强制发放"),
        ENFORCE_NO_GRANT("强制不发放");

        private String code;
        private String desc;

        private ENFORCE_GRANT_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 支付状态
     * 结算状态(WAIT_PAYMENT:待支付，PAYMENT_COMPLETE:支付完成，COMMISSION_COMPLETE:发佣完成)
     */
    public enum PAYMENT_STATUS implements ConstantType {

        PAYMENT_FAILED("支付失败"),
        PAYMENT_INITIAL("初始化"),
        PAYMENT_WAITTING("待支付"),
        PAYMENT_FINISHED("支付完成"),
        PAYMENT_AUDIT("待审核"),
        PAYMENT_SUCCESS("支付成功"),
        PAYMENT_TIMEOUT("支付超时"),
        PAYMENT_INVALID("支付作废"),

        ;

        private String code;
        private String desc;

        private PAYMENT_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 发佣类型
     */
    public enum COMMISSION_TYPE implements ConstantType {
        FYC("首期新单佣金"),
        CYC("续期佣金");

        private String desc;

        COMMISSION_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 佣金生成标志
     */
    public enum COMMISSION_GENERATE_FLAG implements ConstantType {
        GENERATED("已生成"),
        UNGENERATED("未生成");

        private String desc;

        COMMISSION_GENERATE_FLAG(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * ROOT
     */
    public enum ROOT implements ConstantType {

        ROOT("根");
        private String code;
        private String desc;

        private ROOT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 保费结算状态
     */
    public enum PERSIST_STATUS implements ConstantType {

        RECEIVABLE("应收"),
        CHECK("已对帐"),
        ACTUAL("实收");

        private String desc;

        PERSIST_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 签单类型
     */
    public enum SIGN_TYPE implements ConstantType {

        ISSUE_PREMIUM("非见费"),
        PREMIUM_ISSUE("见费出单");

        private String desc;

        SIGN_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 成功失败
     */
    public enum SUCCESS_FAIL_TYPE implements ConstantType {

        INITIAL("初始化"),
        SUCCESS("成功"),
        FAILED("失败");

        private String desc;

        SUCCESS_FAIL_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * excel文件名
     */
    public enum EXCEL_FILE_NAME implements ConstantType {
        PREMIUM_CHECK_EXPORT("保费对账清单导出", ".xls");

        private String name;
        private String suffix;

        private EXCEL_FILE_NAME(String name, String suffix) {
            this.name = name;
            this.suffix = suffix;
        }

        public String getName() {
            return name;
        }

        public String getSuffix() {
            return suffix;
        }
    }

    /**
     * excel文件sheet名
     */
    public enum EXCEL_FILE_SHEET_NAME implements ConstantType {
        PREMIUM_CHECK_EXPORT("保费对账清单导出");

        private String name;

        private EXCEL_FILE_SHEET_NAME(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * excel文件sheet标题
     */
    public enum EXCEL_FILE_SHEET_TITLE implements ConstantType {
        PREMIUM_CHECK_EXPORT("保费对账清单导出");

        private String name;

        private EXCEL_FILE_SHEET_TITLE(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

    }

    /**
     * 应收状态
     */
    public enum RECEIVABLE_STATUS implements ConstantType {

        INITIATE("未确认"),
        ENTER("已确认");

        private String desc;

        RECEIVABLE_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 实收状态
     */
    public enum ACTUAL_STATUS implements ConstantType {

        INITIATE("待收款"),
        PAY_WAIT("已收款待确认"),
        ENTER("已实收");

        private String desc;

        ACTUAL_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 投保单来源
     */
    public enum APPLY_SOURCE implements ConstantType {

        APP("APP"),
        ACCEPT_INPUT("受理录入"),
        CIQ_API("海关API对接"),
        ;

        private String code;
        private String desc;

        private APPLY_SOURCE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 对账附件
     */
    public enum PREMIUM_CHECK_ATTACHMENT_TYPE implements ConstantType {

        ACTUAL_PAY_CERTIFY("实收支付凭证");

        private String desc;

        PREMIUM_CHECK_ATTACHMENT_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 算佣结算类型
     */
    public enum COMMISSION_SETTLEMENT_TYPE implements ConstantType {

        AGENT("业务员"),
        CHANNEL("渠道"),
        ;
        private String desc;

        COMMISSION_SETTLEMENT_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 团险项目类型
     */
    public enum GROUP_ENDORSE_PROJECT implements ConstantType {

        ADD_INSURED("增加被保人"),
        SUBTRACT_INSURED("减少被保人"),
        GROUP_ADD_ADDITIONAL("团险增加附加险"),
        ADD_SUBTRACT_INSURED("增减被保险人"),
        ;

        private String desc;

        GROUP_ENDORSE_PROJECT(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费周期
     */
    public enum PRODUCT_PREMIUM_FREQUENCY implements ConstantType {

        MONTH("月缴"),
        SEASON("季缴"),
        SEMIANNUAL("半年缴"),
        YEAR("年缴"),
        SINGLE("趸缴"),
        ;
        private String desc;

        PRODUCT_PREMIUM_FREQUENCY(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * Ratings Name
     */
    public enum RATINGS_NAME implements ConstantType {

        LIFE_RATINGS("Life Ratings"),
        TPD_RATINGS("TPD Ratings"),
        ADB_RATINGS("ADB Ratings"),
        EMPTY("empty"),
        ;
        private String desc;

        RATINGS_NAME(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费周期
     */
    public enum ADD_PREMIUM_OBJECT_CODE implements ConstantType {

        FER_5("FER(‰)"),
        EM_5("EM(%)"),
        ;
        private String desc;

        ADD_PREMIUM_OBJECT_CODE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 产品
     */
    public enum DUTY implements ConstantType {
        DUTY("责任"),

        PRO8800000000000G12_DUTY_1("门诊"),
        PRO8800000000000G12_DUTY_2("住院"),
        PRO8800000000000G12_DUTY_3("危重症转院"),
        ;
        private String desc;

        DUTY(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 不同周期对应数值
     */
    public enum FREQUENCY_VALUE {
        MONTH("月缴", 1),
        SEASON("季缴", 3),
        SEMIANNUAL("半年缴", 6),
        YEAR("年缴", 12),
        SINGLE("趸缴", 12);

        private String desc;
        private int value;

        FREQUENCY_VALUE(String desc, int value) {
            this.desc = desc;
            this.value = value;
        }

        public String desc() {
            return this.desc;
        }

        public int value() {
            return this.value;
        }
    }

    /**
     * 基础因子编码
     * 缴费周期
     */
    public enum POLICY_UPDATE_AUDIT_STATUS implements ConstantType {
        POLICY_UPDATE_AUDIT_STATUS("保单修改审核状态"),
        INITIAL("初始化"),
        PENDING_AUDIT("待审核"),
        AUDITING("审核中"),
        AUDIT_PASS("审核通过"),
        AUDIT_NO_PASS("审核不通过"),

        ;
        private String desc;

        POLICY_UPDATE_AUDIT_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费周期
     */
    public enum POLICY_UPDATE_PROJECT implements ConstantType {

        UPDATE_BASIC("基本信息"),
        UPDATE_AGENT("业务员信息"),
        UPDATE_APPLICANT("投保人信息"),
        UPDATE_INSURED("被保人信息"),
        UPDATE_COVERAGE("险种信息"),
        UPDATE_RECEIPT("保单回执信息"),
        ;
        private String desc;

        POLICY_UPDATE_PROJECT(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /*
     *  * 备注操作类型
     */
    public enum POLICY_UPDATE_REMARK_TYPE implements ConstantType {

        REVIEW("保单修改审核"),
        APPLY("保单修改申请");

        private String code;
        private String desc;


        POLICY_UPDATE_REMARK_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    /*
     *  * 备注操作类型
     */
    public enum OPERATIONAL_RESULT implements ConstantType {
        OPERATIONAL_RESULT("审核结论类型"),
        SUBMITTED("已提交"),
        NO_PASS("未通过"),
        ;

        private String code;
        private String desc;

        private OPERATIONAL_RESULT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /*
     *  基础因子编码
     */
    public enum BASE_FACTOR_CONFIG_CODE implements ConstantType {
        APP_POLICY_BUTTON_COLOR("按钮颜色"),
        ;
        private String code;
        private String desc;

        private BASE_FACTOR_CONFIG_CODE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /*
     * 回访国际化编码
     */
    public enum POLICY_RETURN_VISIT_ENUM implements ConstantType {
        RETURN_VISIT_CHANNEL("回访渠道"),
        RETURN_VISIT_RESULT("回访结果"),
        RETURN_VISIT_TYPE("回访类型"),
        ;
        private String code;
        private String desc;

        private POLICY_RETURN_VISIT_ENUM(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /*
     * 回访国际化编码
     */
    public enum RETURN_VISIT_TYPE implements ConstantType {
        NEW_POLICY("新单承保"),
        POLICY_EXPIRATION("保单满期"),
        PREMIUM_RENEWAL_POLICY_RENEWAL("续期/续保"),
        POLICY_INVALID("保单中止"),
        POLICY_TERMINATION("保单终止"),
        CUSTOMER_BIRTHDAY("客户生日"),
        OTHER("其他"),
        GROUP_RENEWAL("团险续保"),
        ;
        private String code;
        private String desc;

        private RETURN_VISIT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /*
     * 回访结果
     */
    public enum RETURN_VISIT_RESULT implements ConstantType {
        SUBMITTED("通过"),
        NO_PASS("不通过"),
        ;
        private String desc;

        RETURN_VISIT_RESULT(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /*
     * 回执复核结果
     */
    public enum RECEIPT_REVIEW_RESULT implements ConstantType {
        SUBMITTED("通过"),
        NO_PASS("不通过"),
        ;
        private String desc;

        RECEIPT_REVIEW_RESULT(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 事件业务类型
     */
    public enum BENEFICIARY_NO implements ConstantType {

        ORDER_ONE("ORDER_ONE", "第一"),
        ORDER_TWO("ORDER_TWO", "第二"),
        ORDER_THREE("ORDER_THREE", "第三"),
        ;
        private String code;
        private String desc;

        BENEFICIARY_NO(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum REFERRAL_SOURCES implements ConstantType {
        BANK("银行"),
        PERSONAL("个人"),
        CUSTOMER("客户"),
        ;
        private String code;
        private String desc;

        private REFERRAL_SOURCES(String desc) {
            this.code = code;
            this.desc = desc;
        }
        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 事件业务类型
     */
    public enum CHANNEL_TYPE implements ConstantType {

        BANK("BANK", "银保渠道"),
        BROKER("BROKER", "中介渠道"),
        PARTNER("PARTNER", "合作伙伴"),
        MANAGER("MANAGER", "行政管理机构"),
        CUSTOMS("CUSTOMS", "第三方合作机构"),
        AGENT("AGENT", "个险渠道"),
        ONLINE("ONLINE", "网销渠道"),
        GMSE("ONLINE", "销售执行"),
        ;
        private String code;
        private String desc;

        CHANNEL_TYPE(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum OCCUPATION_NATURE implements ConstantType {
        EMPLOYED_IN_GOVERNMENT_OR_PUBLIC_SECTOR("受雇于政府/公共部门"),
        EMPLOYED_IN_PRIVATE_SECTOR("受雇于私营部门"),
        SELF_EMPLOYED("自雇"),
        STUDENT("学生"),
        RETIRED("退休"),
        OTHERS("其它"),
        ;
        private String code;
        private String desc;

        private OCCUPATION_NATURE(String desc) {
            this.code = code;
            this.desc = desc;
        }
        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 与被保人关系
     */
    public enum RELATIONSHIP_WITH_THE_INSURED implements ConstantType {
        ONESELF("Self","本人"),
        SPOUSE("Spouse","配偶"),
        PARENTS("Parents","父母"),
        CHILD("Children","子女"),
        BROTHERS("Siblings","兄弟姐妹"),
        OTHER("Other","其他"),

        SISTERS("Sisters","姐妹"),
        BORROW("Borrow","借贷关系"),
        ;

        private String code;
        private String desc;

        private RELATIONSHIP_WITH_THE_INSURED(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

}
