package com.gclife.policy.model.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *         create 18-11-16
 *         description:
 */
@Data
public class PolicyReportUnderwritingBo {

    private String     reportPolicyId;
    private String     policyId;
    private String     policyNo;
    private String     applyId;
    private String     applyNo;
    private String     productId;
    private String     productName;
    private String     primaryFlag;
    private String     premiumFrequency;
    private String     coveragePeriodUnit;
    private String     coveragePeriod;
    private BigDecimal totalPremium;
    private BigDecimal actualPremium;
    private Long       approveDate;
    private Long       receivableDate;
    private String     paidTimes;
    private String     applicantName;
    private String     applicantIdType;
    private String     applicantIdNo;
    private String     applicantMobile;
    private String     applicantOccupationCode;
    private String     applicantCareerRiskLevel;
    private String     insuredName;
    private String     insuredIdType;
    private String     insuredIdNo;
    private String     insuredMobile;
    private String     insuredOccupationCode;
    private String     insuredCareerRiskLevel;
    private String     salesBranchId;
    private String     channelTypeCode;
    private String     agentName;
    private String     agentCode;
    private String     policyStatus;
    private String     renewalTimes;
    private String     agentId;
    private String     mainCoveragePeriodUnit;
    private String     mainCoveragePeriod;
    private  Long policyCreateDate;
    private String     coverageId;
    private String     agentTypeCode;
    private  Long surrenderDate;
    private Long policyCreatedDate;
    private Long policyUpdatedDate;
    private  Long applicantFirstPolicyDate;
    private  Long insuredFirstPolicyDate;
    private String applicantCustomerId;
    private String insuredCustomerId;
    private String selfInsuranceFlag;
    private String premiumPeriodUnit;
    private String premiumPeriod;
    private Long insuredBirthday;
    private String insuredSex;
    private Long   applyDate;
    private Long   effectiveDate;
    private Long   maturityDate;
    private Long invalidDate;
    private Long thoroughInvalidDate;
    private String homeAreaCode;
    private String homeAddress;
    /**
     * 风险承担开始日期
     */
    private Long riskCommencementDate;
    /**
     * 保单期数(起始为1)
     */
    private Long policyPeriod;
    /**
     * policy_premium表中的actual_premium
     */
    private BigDecimal premiumActualPremium;
    private String     referralName;
    private String     abaAccount;
    /**
     * 保单犹豫期结束日期
     */
    private Long hesitationEndDate;
}