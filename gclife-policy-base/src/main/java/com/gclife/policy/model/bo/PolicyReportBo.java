package com.gclife.policy.model.bo;

import com.gclife.policy.core.jooq.tables.pojos.PolicyPo;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *         create 18-11-12
 *         description:
 */
@Data
public class PolicyReportBo extends PolicyPo {
    //产品Id
    private String productId;
    //产品名称
    private String productName;
    //产品代码
    private String productCode;
    //档次
    private String productLevel;
    //投保人姓名
    private String applicantName;
    //被保人姓名
    private String insuredName;
    //被保人出生日期
    private Long insuredBirthday;
    //缴费周期
    //客户id
    private String customerId;
    //团险客户id
    private String delegateCustomerId;

    //投保单类型
    private String applicantType;
    //客户号
    private String applicantCustomerNo;
    private String premiumFrequency;
    private String     premiumPeriodUnit;
    private String     premiumPeriod;
    private String applicantMobile;
    private BigDecimal specialDiscount;
    private String     discountType;
    private String     promotionType;
    private String     discountModel;
    private BigDecimal originalPremium;
    private Long effectiveDate;
    private Long policyYear;
    private Long frequency;

   /* public BigDecimal getOriginalPremium() {
        return originalPremium;
    }

    public void setOriginalPremium(BigDecimal originalPremium) {
        this.originalPremium = originalPremium;
    }

    public Long getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Long effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public BigDecimal getSpecialDiscount() {
        return specialDiscount;
    }

    public void setSpecialDiscount(BigDecimal specialDiscount) {
        this.specialDiscount = specialDiscount;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getApplicantMobile() {
        return applicantMobile;
    }

    public void setApplicantMobile(String applicantMobile) {
        this.applicantMobile = applicantMobile;
    }

    public String getPremiumFrequency() {
        return premiumFrequency;
    }

    public void setPremiumFrequency(String premiumFrequency) {
        this.premiumFrequency = premiumFrequency;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductLevel() {
        return productLevel;
    }

    public void setProductLevel(String productLevel) {
        this.productLevel = productLevel;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public Long getInsuredBirthday() {
        return insuredBirthday;
    }

    public void setInsuredBirthday(Long insuredBirthday) {
        this.insuredBirthday = insuredBirthday;
    }

    public String getPremiumPeriodUnit() {
        return premiumPeriodUnit;
    }

    public void setPremiumPeriodUnit(String premiumPeriodUnit) {
        this.premiumPeriodUnit = premiumPeriodUnit;
    }

    public String getPremiumPeriod() {
        return premiumPeriod;
    }

    public void setPremiumPeriod(String premiumPeriod) {
        this.premiumPeriod = premiumPeriod;
    }*/
}