package com.gclife.policy.model.config;


import com.gclife.common.model.inter.IEnum;

/**
 * <AUTHOR>
 * Created by cqh on 17-9-5.
 */
public enum PolicyErrorConfigEnum implements IEnum {

    /**
     * 编码规则
     * 参数错误: 模块名称_PARAMETER_表名称_字段名称_验证结果{IS_NOT_NULL:不能为空,FORMAT_ERROR:格式错误,IS_NOT_FOUND_VALUE:未找到值}
     * 业务错误: 模块名称_BUSINESS_规则描述_验证结果{IS_NOT_FOUND_OBJECT:未找到对象}
     * 数据转换错误: 模块名称_CONVERSION_DTO对象名称_ERROR
     * 保存错误: 模块名称_SAVE_表名称_ERROR
     * 查询错误: 模块名称_QUERY_表名称_查询结果(ERROR:查询出错);
     * 通用错误: 模块名称_FAIL
     */
    POLICY_QUERY_POLICY_PRINT_ERROR("**********", "查询保单打印异常", "POLICY"),
    POLICY_QUERY_POLICY_SEE_ERROR("**********", "快速查询保单异常", "POLICY"),
    POLICY_QUERY_POLICY_ERROR("**********", "查询保单异常", "POLICY"),
    POLICY_POLICY_SAVE_ERROR("**********", "保单保存异常", "POLICY"),
    POLICY_POLICY_ACCOUNT_SAVE_ERROR("**********", "保单账户保存异常", "POLICY"),
    POLICY_POLICY_AGENT_SAVE_ERROR("**********", "保单业务员保存异常", "POLICY"),
    POLICY_POLICY_APPLICANT_SAVE_ERROR("**********", "保单投保人保存异常", "POLICY"),
    POLICY_POLICY_BENEFICIARY_SAVE_ERROR("**********", "保单受益人保存异常", "POLICY"),
    POLICY_POLICY_BENEFICIARY_INFO_SAVE_ERROR("**********", "保单受益人详情保存异常", "POLICY"),
    POLICY_POLICY_CONTACT_INFO_SAVE_ERROR("**********", "保单联系方式保存异常", "POLICY"),
    POLICY_POLICY_COVERAGE_SAVE_ERROR("**********", "保单险种保存异常", "POLICY"),
    POLICY_POLICY_COVERAGE_BONUS_SAVE_ERROR("**********", "保单险种红利保存异常", "POLICY"),
    POLICY_POLICY_COVERAGE_DUTY_SAVE_ERROR("**********", "保单险种责任保存异常", "POLICY"),
    POLICY_POLICY_COVERAGE_PREMIUM_SAVE_ERROR("**********", "保单险种缴费信息保存异常", "POLICY"),
    POLICY_POLICY_COVERAGE_SURVIVAL_SAVE_ERROR("2020000014", "保单险种生存给付保存异常", "POLICY"),
    POLICY_POLICY_INSURED_SAVE_ERROR("2020000015", "保单被保人保存异常", "POLICY"),
    POLICY_POLICY_PAYOR_INFO_SAVE_ERROR("2020000016", "保单付费人保存异常", "POLICY"),
    POLICY_POLICY_PREMIUM_SAVE_ERROR("2020000017", "保单缴费信息保存异常", "POLICY"),
    POLICY_POLICY_RECEIPT_INFO_SAVE_ERROR("2020000018", "保单回执保存异常", "POLICY"),
    POLICY_APPLY_TRANS_TO_POLICY_ERROR("2020000019", "投保单转保单异常", "POLICY"),
    POLICY_APPLY_TRANS_DATA_IS_NOT_NULL("2020000020", "投保单转保单的数据不为空", "POLICY"),
    POLICY_POLICY_PRINT_INFO_SAVE_ERROR("2020000021", "保单打印保存异常", "POLICY"),
    POLICY_POLICY_EXCLUDE_SAVE_ERROR("2020000022", "保单发佣排除保存异常", "POLICY"),
    POLICY_POLICY_PAYMENT_SAVE_ERROR("2020000023", "保存保单发佣支付数据异常", "POLICY"),
    POLICY_POLICY_INSURED_IS_NOT_NULL("2020000024", "保单被保人不为空", "POLICY"),
    POLICY_PRINT_FILE_ERROR("2020000025", "保单打印文件异常", "POLICY"),
    POLICY_QUERY_WORKFLOW_FAIL("2020000026", "请求工作流数据异常", "POLICY"),
    POLICY_QUERY_COMMISSION_FILTER_FAIL("2020000027", "保单发佣筛选查询异常", "POLICY"),
    POLICY_NEO4J_BRANCH_IS_NOT_NULL("2020000028", "保单发佣筛选机构列表不能为空", "POLICY"),
    POLICY_NEO4J_SAVE_FAIL("2020000029", "抽取保单数据到图异常", "POLICY"),
    POLICY_BUSINESS_POLICY_PAYMENT_IS_NOT_FOUND("2020000030", "未找到保单支付记录", "POLICY"),
    POLICY_FAIL("2020000031", "保单模块异常", "POLICY"),
    POLICY_QUERY_POLICY_FAIL("2020000032", "查询保单数据异常", "POLICY"),
    POLICY_QUERY_POLICY_GET_CODE_ERROR("2020000033", "查询数据字典异常", "POLICY"),
    POLICY_QUERY_POLICY_ID_IS_NOT_NULL("2020000034", "保单ID不为空", "POLICY"),
    POLICY_QUERY_ENDORSEAPPLYDATE_IS_NOT_NULL("2020000035", "保全客户申请日期", "POLICY"),
    POLICY_QUERY_CUSTOMER_POLICY_MEDAL_ERROR("2020000036", "查询保单勋章信息异常", "POLICY"),
    POLICY_QUERY_AGENT_ID_IS_NOT_NULL("2020000037", "业务员ID不为空", "POLICY"),
    POLICY_QUERY_POLICY_NO_IS_NOT_NULL("2020000038", "保单号不为空", "POLICY"),
    POLICY_QUERY_POLICY_PAYMENT_ID_IS_NOT_NULL("2020000039", "保单支付ID不为空", "POLICY"),
    POLICY_QUERY_SETTLEMENT_STATUS_ERROR("2020000040", "保单支付状态有误", "POLICY"),
    POLICY_BUSINESS_POLICY_PAYMENT_FAIL("2020000041", "查询保单支付数据异常", "POLICY"),
    POLICY_QUERY_COMMISSION_STATISTICS_ERROR("2020000042", "保单拥金统计异常", "POLICY"),
    POLICY_QUERY_COVERAGE_PAYMENTS_ERROR("2020000043", "查询保单险种缴费异常", "POLICY"),
    POLICY_BUSINESS_POLICY_APPLICANT_ERROR("2020000044", "未找到投保人", "POLICY"),
    POLICY_BUSINESS_POLICY_PRODUCT_ERROR("2020000045", "未找到产品", "POLICY"),
    POLICY_BUSINESS_POLICY_AGENT_ERROR("2020000046", "未找到业务员", "POLICY"),
    POLICY_BUSINESS_POLICY_INSURED_ERROR("2020000047", "未找到被保人", "POLICY"),
    POLICY_QUERY_POLICY_APPLICANT_ERROR("2020000048", "查询保单投保人信息异常", "POLICY"),
    POLICY_QUERY_POLICY_AGENT_ERROR("2020000049", "查询保单业务员信息异常", "POLICY"),
    POLICY_QUERY_POLICY_PREMIUM_ERROR("2020000050", "查询保单缴费信息异常", "POLICY"),
    POLICY_QUERY_POLICY_INSURED_ERROR("2020000051", "查询保单被保人信息异常", "POLICY"),
    POLICY_QUERY_POLICY_ATTACHMENT_ERROR("2020000052", "查询保单附件信息异常", "POLICY"),
    POLICY_QUERY_POLICY_COVERAGE_ERROR("2020000053", "查询保单险种信息异常", "POLICY"),
    POLICY_AGENT_IS_NOT_FOUND_OBJECT("2020000054", "未找到业务员信息", "POLICY"),
    POLICY_PARAMETER_PRINT_INFO_ID_IS_NOT_NULL("2020000055", "保单打印ID不能为空", "POLICY"),
    POLICY_BUSINESS_POLICY_PRINT_INFO_IS_NOT_FOUND_OBJECT("2020000056", "打印保单不存在", "POLICY"),
    POLICY_FEGIN_AGENT_FAIL("2020000057", "请求业务员微服务异常", "POLICY"),
    POLICY_QUERY_APPLY_ID_IS_NOT_NULL("**********", "投保单ID不为空", "POLICY"),
    POLICY_QUERY_POLICY_IS_EXIST("**********", "保单已存在，不可重复承保", "POLICY"),
    POLICY_QUERY_PREMIUM_CHECK_ERROR("**********", "查询保费对账清单异常", "POLICY"),
    POLICY_PARAMETER_POLICY_ID_IS_NOT_NULL("**********", "保单ID不能为空", "POLICY"),
    POLICY_PARAMETER_POLICY_PAYMENT_ID_IS_NOT_NULL("**********", "保单支付Id不能为空", "POLICY"),
    POLICY_PARAMETER_PROVIDER_ID_IS_NOT_NULL("**********", "保险公司ID不能为空", "POLICY"),
    POLICY_PARAMETER_BIZ_YEAR_MONTH_IS_NOT_NULL("**********", "业绩年月(时间轴ID)不能为空", "POLICY"),
    POLICY_PARAMETER_BIZ_DATE_IS_NOT_NULL("**********", "业务日期不能为空", "POLICY"),
    POLICY_PARAMETER_BIZ_BRANCH_ID_IS_NOT_NULL("**********", "业绩归属机构ID不能为空", "POLICY"),
    POLICY_PARAMETER_POLICY_YEAR_IS_NOT_NULL("**********", "保单年度不能为空", "POLICY"),
    POLICY_PARAMETER_FREQUENCY_IS_NOT_NULL("**********", "频次不能为空", "POLICY"),
    POLICY_PARAMETER_APPLY_DATE_IS_NOT_NULL("**********", "投保时间不能为空", "POLICY"),
    POLICY_PARAMETER_APPLICANT_NAME_IS_NOT_NULL("**********", "投保人姓名不能为空", "POLICY"),
    POLICY_PARAMETER_APPLICANT_IDNO_IS_NOT_NULL("**********", "投保人证件号码不能为空", "POLICY"),
    POLICY_PARAMETER_AGENT_ID_IS_NOT_NULL("**********", "业务员ID不能为空", "POLICY"),
    POLICY_PARAMETER_AGENT_CODE_IS_NOT_NULL("2020000073", "业务员工号不能为空", "POLICY"),
    POLICY_PARAMETER_AGENT_NAME_IS_NOT_NULL("2020000074", "业务员姓名不能为空", "POLICY"),
    POLICY_QUERY_PREMIUM_PERSIST_ERROR("2020000075", "查询保费应收应付清单异常", "POLICY"),
    POLICY_QUERY_PREMIUM_RECEIVABLE_ERROR("2020000076", "查询保费应收列表异常", "POLICY"),
    POLICY_PARAMETER_CHECK_ID_IS_NOT_NULL("2020000077", "对账批次ID不能为空", "POLICY"),
    POLICY_QUERY_PREMIUM_RECEIVABLE_DETAIL_ERROR("2020000078", "查询保费应收详情异常", "POLICY"),
    POLICY_BUSINESS_PREMIUM_CHECK_IS_NOT_FOUND_OBJECT("2020000079", "未找到保费对账清单对象", "POLICY"),
    POLICY_BUSINESS_PREMIUM_PERSIST_IS_NOT_FOUND_OBJECT("2020000080", "未找到保费实收实付清单对象", "POLICY"),
    POLICY_QUERY_PREMIUM_ACTUAL_ERROR("2020000081", "查询保费实收列表异常", "POLICY"),
    POLICY_QUERY_PREMIUM_ACTUAL_DETAIL_ERROR("2020000082", "查询保费实收详情异常", "POLICY"),
    POLICY_BUSINESS_PREMIUM_CHECK_ERROR("2020000083", "已对账数据不能重复确认", "POLICY"),
    POLICY_BUSINESS_PREMIUM_ACTUAL_ERROR("2020000084", "已确认收款不能重复确认", "POLICY"),
    POLICY_BUSINESS_PREMIUM_ACTUAL_INITIATE_ERROR("2020000085", "未收款不能确认", "POLICY"),
    POLICY_QUERY_PREMIUM_ACTUAL_ATTACH_ERROR("2020000086", "查询保费实收凭证异常", "POLICY"),
    POLICY_QUERY_RECEIPT_LIST_ERROR("2020000087", "查询保单回执列表异常", "POLICY"),
    POLICY_SAVE_RECEIPT_ERROR("2020000088", "提交回执信息异常", "POLICY"),
    POLICY_QUERY_POLICY_SIGN_DATE_IS_NOT_NULL("2020000089", "客户签收日期不能为空", "POLICY"),
    POLICY_QUERY_POLICY_RECEIPT_DATE_IS_NOT_NULL("**********", "回执日期不能为空", "POLICY"),
    POLICY_QUERY_POLICY_RECEIPT_SUBMIT_DATE_IS_NOT_NULL("**********", "回执提交日期不能为空", "POLICY"),
    POLICY_SIGN_DATE_FORMAT_ERROR("**********", "客户签收日期格式有误", "POLICY"),
    POLICY_RECEIPT_RETURN_DATE_FORMAT_ERROR("**********", "业务员回执交回日期格式有误", "POLICY"),
    POLICY_RECEIPT_INFO_IS_NOT_FOUND_OBJECT("**********", "未找到该保单回执信息", "POLICY"),
    POLICY_QUERY_POLICY_CONTACT_ERROR("**********", "查询保单联系信息异常", "POLICY"),
    POLICY_QUERY_POLICY_ACCOUNT_ERROR("**********", "查询保账户异常", "POLICY"),
    POLICY_UNDO_SAVE_ERROR("**********", "撤单保存出错", "POLICY"),
    POLICY_ATTACHMENT_ID_IS_NOT_NULL("**********", "请上传影像附件", "POLICY"),
    POLICY_ATTACHMENT_DELETE_ERROR("**********", "附件删除异常", "POLICY"),
    POLICY_BUSINESS_POLICY_ATTACHMENT_IS_NOT_FOUND_OBJECT("**********", "未查到保单附件", "POLICY"),
    POLICY_BUSINESS_POLICY_IS_NOT_FOUND("**********", "未找到保单信息", "POLICY"),
    POLICY_BUSINESS_POLICY_PRODUCT_TYPE_IS_NOT_RIGHT("**********", "请选择相关产品的保单信息", "POLICY"),
    CLAIM_BUSINESS_HAVE_EXIST("**********", "该保单已提交过申请,如需再次申请,请联系客服,谢谢", "POLICY"),
    POLICY_BUSINESS_POLICY_APPLICANT_INFO_IS_NOT_RIGHT("**********", "请输入正确的投保人信息", "POLICY"),
    POLICY_BUSINESS_POLICY_GROUP_APPLICANT_INFO_IS_NOT_RIGHT("**********", "请联系您的投保人以提交理赔。 谢谢", "POLICY"),
    POLICY_BUSINESS_POLICY_IS_INVALID("2020000101", "该保单已失效", "POLICY"),
    POLICY_BUSINESS_POLICY_IS_INVALID_THOROUGH("2020000101", "该保单已永久失效", "POLICY"),
    POLICY_PARAMETER_RECEIVER_EMAIL_IS_NOT_NULL("2020000102", "接收邮箱不能为空", "POLICY"),
    POLICY_PARAMETER_RECEIVER_EMAIL_ERROR("2020000103", "接收邮箱格式错误", "POLICY"),
    POLICY_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT("2020000104", "附件未找到", "POLICY"),
    POLICY_PARAMETER_LANGUAGE_IS_NOT_NULL("2020000105", "保单打印语言不能为空", "POLICY"),
    POLICY_PARAMETER_LANGUAGE_FORMAT_INVALID("2020000106", "保单打印语言格式错误", "POLICY"),
    POLICY_BUSINESS_CALCULATE_EFFTIVE_DATE_ERROR("2020000107", "计算有效期异常", "POLICY"),
    POLICY_BUSINESS_CALCULATE_COMMISSION_DATE_ERROR("2020000108", "保存佣金错误", "POLICY"),
    POLICY_BUSINESS_POLICY_NO_REPEAT_ERROR("2020000109", "保单号重复", "POLICY"),
    POLICY_QUERY_POLICY_RETURN_DATE_IS_NOT_NULL("2020000110", "回执交回日期不能为空", "POLICY"),
    POLICY_QUERY_POLICY_BIZ_DATE_IS_NOT_NULL("2020000111", "业务日期不能为空", "POLICY"),
    POLICY_QUERY_RECEIPT_REVIEW_LIST_ERROR("2020000112", "查询保单回执复核列表异常", "POLICY"),
    POLICY_QUERY_RECEIPT_DETAIL_ERROR("2020000113", "查询保单回执详情异常", "POLICY"),
    POLICY_QUERY_RECEIPT_REVIEW_DETAIL_ERROR("2020000114", "查询保单回执复核详情异常", "POLICY"),
    POLICY_RECEIPT_INFO_APPROVE_DATE_NOT_FOUND_OBJECT("2020000115", "回执日期不能小于承保日期", "POLICY"),
    POLICY_RECEIPT_INFO_BIZ_DATE_NOT_LESS_THAN_LAST_MONTH("2020000116", "业绩日期月份不能小于当前日期的上上月份", "POLICY"),
    POLICY_RECEIPT_INFO_RECEIPT_DATE_NOT_MORE_THAN_CURRENT_TIME("2020000117", "回执日期不能超过当前日期", "POLICY"),
    POLICY_RECEIPT_INFO_RECEIPT_RETURN_DATE_NOT_MORE_THAN_CURRENT_TIME("2020000118", "回执日期不能超过当前日期", "POLICY"),
    POLICY_RECEIPT_INFO_RECEIPT_DATE_NOT_MORE_THAN_PRINT_DATE("2020000119", "回执日期不能小于打印结束日期", "POLICY"),
    POLICY_RECEIPT_INFO_RECEIPT_RETURN_DATE_NOT_MORE_THAN_PRINT_DATE("2020000120", "回执交回日期不能小于打印结束日期", "POLICY"),
    POLICY_RECEIPT_SIGN_ERROR("2020000121", "保单回执签收异常", "APPLY"),
    POLICY_RECEIPT_REVIEW_SIGN_ERROR("2020000122", "保单回执复核签收异常", "APPLY"),
    GROUP_POLICY_QUERY_COVERAGE_IS_NOT_NULL("2020000123", "保单产品为空", "GROUP"),
    POLICY_BUSINESS_APPLY_TRANS_TO_POLICY_ROLLBACK_ERROR("2020000124", "投保单转保单回滚异常", "POLICY_BASE"),
    POLICY_BUSINESS_GENERATE_POLICY_RENEWAL_ERROR("2020000125", "生成保单续期记录异常", "POLICY"),
    POLICY_BUSINESS_GENERATE_RENEWAL_INSURANCE_ERROR("2020000126", "生成续保记录异常", "POLICY"),
    POLICY_BUSINESS_POLICY_RENEWAL_IS_NOT_NULL("2020000127", "保单续期记录不能为空", "POLICY"),
    POLICY_BUSINESS_RENEWAL_YEAR_MONTH_IS_NOT_NULL("2020000128", "应收年月不能为空", "POLICY"),
    POLICY_BUSINESS_RECEIVABLE_DATE_IS_NOT_NULL("2020000129", "应收日期不能为空", "POLICY"),
    POLICY_BUSINESS_RENEWAL_YEAR_MONTH_FORMAT_ERROR("2020000130", "请输入合法的应收月份", "POLICY"),
    POLICY_BUSINESS_POLICY_WAS_INVALID("2020000131", "此保单不允许生成续期应收数据", "POLICY"),
    POLICY_BUSINESS_QUERY_RENEWAL_IS_EXIST("2020000132", "该保单当月应收数据已存在,请先删除", "POLICY"),
    POLICY_BUSINESS_QUERY_RENEWAL_ALREADY_PAID("2020000133", "该保单当月续期保费已缴纳", "POLICY"),
    POLICY_BUSINESS_DELETE_RENEWAL_ERROR("2020000134", "续期应收记录删除错误", "POLICY"),
    POLICY_QUERY_POLICY_COVERAGE_IS_NOT_FOUND("2020000135", "未找到保单险种信息", "POLICY"),
    POLICY_QUERY_POLICY_IS_NOT_FOUND("2020000136", "未找到保单信息", "POLICY"),
    POLICY_QUERY_POLICY_HISTORY_IS_NOT_FOUND("2020000137", "未找到保单历史信息", "POLICY"),
    POLICY_QUERY_POLICY_COVERAGE_PREMIUM_IS_NOT_FOUND("2020000138", "未找到保单险种保费信息", "POLICY"),
    POLICY_QUERY_POLICY_PREMIUM_IS_NOT_FOUND("2020000139", "未找到保单保费信息", "POLICY"),
    POLICY_BUSINESS_POLICY_COVERAGE_IS_NOT_FOUND_OBJECT("2020000140", "未找到保单险种信息", "POLICY"),
    POLICY_QUERY_POLICY_NOT_PAY_IS_NOT_FOUND_OBJECT("2020000141", "未找到保单未缴费信息", "POLICY"),
    POLICY_QUERY_POLICY_PAY_IS_NOT_FOUND_OBJECT("2020000142", "未找到保单已缴费信息", "POLICY"),
    POLICY_QUERY_RENEWAL_INSURANCE_ERROR("2020000143", "查询续保缴费信息异常", "POLICY"),
    POLICY_QUERY_RENEWAL_INSURANCE_COVERAGE_EXTEND_ERROR("2020000144", "查询续保险种信息异常", "POLICY"),
    POLICY_PARAMETER_COVERAGE_IS_NOT_NULL("2020000145", "险种信息不能为空", "POLICY"),
    POLICY_SAVE_PAYMENT_ERROR("2020000146", "保存保单支付数据异常", "POLICY"),
    POLICY_SAVE_COVERAGE_EXTEND_ERROR("2020000147", "保存险种扩展信息出错", "POLICY"),
    POLICY_SAVE_PREMIUM_ERROR("2020000148", "保存保单保费信息出错", "POLICY"),
    POLICY_BUSINESS_RENEWAL_INTERTEMPORAL_ERROR("2020000149", "不允许跨期产生续期数据，请重新选择应收日期！", "POLICY"),
    POLICY_FEGIN_APPLY_ERROR("2020000150", "调用投保单微服务出错", "POLICY"),
    POLICY_FEGIN_MESSAGE_FAIL("2020000151", "请求消息微服务错误", "POLICY"),
    POLICY_FEGIN_PRODUCT_FAIL("2020000152", "请求产品微服务错误", "POLICY"),
    GROUP_POLICY_EXPORT_INSURED_ERROR("2020000153", "EXCEL导出异常", "POLICY"),
    POLICY_QUERY_POLICY_AGENT_HISTORY_ERROR("2020000154", "查询历史业务员异常", "GROUP"),
    POLICY_SAVE_CUSTOMER_ERROR("2020000155", "添加客户异常", "POLICY"),
    POLICY_GENERATE_RENEWAL_ERROR("2020000156", "该保单不能产生续期", "POLICY"),
    POLICY_UPDATE_POLICY_AGENT_ERROR("2020000157", "更新保单业务员异常", "POLICY"),
    POLICY_QUERY_POLICY_CUSTOMER_RELATION_IS_NOT_FOUND("2020000158", "未找到客户关联的保单信息", "POLICY"),
    POLICY_QUERY_POLICY_RELATION_IS_NOT_FOUND("2020000159", "未找到保单关联的保单信息", "POLICY"),
    POLICY_QUERY_POLICY_CUSTOMER_RELATION_ERROR("2020000160", "查询客户关联的保单信息异常", "POLICY"),
    POLICY_QUERY_POLICY_CUSTOMER_APPLICANT_IS_NOT_FOUND("2020000161", "未找到保单投保单的客户信息", "POLICY"),
    POLICY_QUERY_POLICY_RELATION_ERROR("2020000162", "查询保单关联的保单信息异常", "POLICY"),
    POLICY_QUERY_POLICY_CUSTOMER_ID_IS_NOT_NULL("2020000163", "查询保单的客户ID不为空", "POLICY"),
    POLICY_QUERY_POLICY_VERSION_NO_IS_NOT_NULL("2020000164", "保单的版本号不为空", "POLICY"),
    POLICY_QUERY_POLICY_HISTORY_ERROR("2020000165", "查询保单历史异常", "POLICY"),
    POLICY_SYNCHRONIZE_POLICY_CUSTOMER_ERROR("2020000166", "同步保单客户信息异常", "POLICY"),
    POLICY_QUERY_BASE_OPERATION_ERROR("2020000167", "查询基础操作异常", "POLICY"),
    POLICY_QUERY_POLICY_COVERAGE_EXTEND_IS_NOT_FOUND_OBJECT("2020000168", "未找到险种扩展信息", "POLICY"),
    POLICY_CUSTOMER_ID_IS_NOT_NULL("2020000169", "客户ID不能为空", "USER"),
    POLICY_CUSTOMER_HOME_PHONE_IS_NOT_NULL("2020000170", "固定电话不能为空", "USER"),
    POLICY_CUSTOMER_HOME_ADDRESS_IS_NOT_NULL("2020000171", "地址不能为空", "USER"),
    POLICY_CUSTOMER_MOBILE_IS_NOT_NULL("2020000172", "手机号码不能为空", "USER"),
    POLICY_CUSTOMER_EMAIL_IS_NOT_NULL("2020000173", "邮箱不能为空", "USER"),
    POLICY_CUSTOMER_HOME_ZIP_CODE_IS_NOT_NULL("2020000174", "邮政编码不能为空", "USER"),
    POLICY_CUSTOMER_HOME_AREA_CODE_IS_NOT_NULL("2020000175", "所属地区不能为空", "USER"),
    POLICY_CUSTOMER_HOME_PHONE_FORMAT_ERROR("2020000176", "固定电话格式不正确", "USER"),
    POLICY_CUSTOMER_MOBILE_FORMAT_ERROR("2020000177", "手机号格式不正确", "USER"),
    POLICY_CUSTOMER_HOME_AREA_CODE_IS_NOT_EXIST("2020000178", "地址区域编码不存在", "USER"),
    POLICY_CUSTOMER_EMAIL_FORMAT_ERROR("2020000179", "邮箱格式有误", "APPLY"),
    POLICY_DATA_TRANSFER_ERROR("2020000180", "数据转换异常", "APPLY"),
    POLICY_QUERY_POLICY_STATUS_IS_NOT_NULL("2020000181", "保单状态不为空", "POLICY"),
    POLICY_SYNCHRONIZE_POLICY_STATUS_ERROR("2020000182", "同步保单状态信息异常", "POLICY"),
    POLICY_SYNC_REINSTATEMENT_ERROR("2020000182", "同步复效数据出错", "POLICY"),
    POLICY_QUERY_POLICY_STATUS_IS_NOT_EXIST("2020000183", "保单状态不存在", "POLICY"),
    POLICY_QUERY_POLICY_BEYOND_HESITATION_ERROR("2020000184", "保单已过犹豫期，不能撤单", "POLICY"),
    POLICY_QUERY_POLICY_STATUS_REINSTATEMENT_ERROR("2020000185", "保单不是失效状态，不能复效", "POLICY"),
    POLICY_QUERY_POLICY_STATUS_SURRENDER_ERROR("2020000186", "保单不能退保", "POLICY"),
    POLICY_QUERY_POLICY_STATUS_CHANGE_ERROR("2020000187", "当前修改的保单状态没授权", "POLICY"),
    POLICY_QUERY_POLICY_STATUS_HESITATION_ERROR("2020000188", "保单不是有效状态，不能撤单", "POLICY"),
    POLICY_QUERY_POLICY_STATUS_EFFECTIVE_ERROR("2020000189", "保单不符合生效条件", "POLICY"),
    POLICY_QUERY_POLICY_STATUS_INVALID_ERROR("2020000190", "保单不符合失效条件", "POLICY"),
    POLICY_QUERY_POLICY_STATUS_INDEMNITY_TERMINATION_ERROR("2020000184", "保单已赔付终止，不能再次理赔", "POLICY"),
    POLICY_QUERY_POLICY_STATUS_WAIVER_PREMIUM_ERROR("2020000184", "当前保单不能豁免保费", "POLICY"),
    POLICY_QUERY_CUSTOMER_AGENT_IS_NOT_FOUND("2020000191", "未找到保单关联的客户", "POLICY"),
    POLICY_PARAMETER_OPERATION_CODE_IS_NOT_NULL("2020000192", "操作编码不能为空", "POLICY"),
    POLICY_BUSINESS_POLICY_OPERATION_IS_NOT_FOUND_OBJECT("2020000193", "未找到保单操作信息", "POLICY"),
    POLICY_HANDLE_RENEWAL_INSURANCE_EFFECT_ERROR("2020000194", "续保生效处理出错", "POLICY"),
    POLICY_BUSINESS_RENEWAL_INSURANCE_TO_POLICY_ERROR("2020000194", "续保转保单出错", "POLICY"),
    POLICY_QUERY_BRANCH_INFO_IS_ERROR("2020000195", "查询机构异常", "POLICY"),
    POLICY_ASSIGN_AGENT_REMARK_IS_NOT_NULL("2020000196", "分单备注不能为空", "POLICY"),
    POLICY_REMARK_INFO_IS_NOT_NULL("2020000197", "备注信息不能为空", "POLICY"),
    POLICY_REVIEW_STATUS_ERROR("2020000198", "请正确填写审核状态", "POLICY"),
    POLICY_QUERY_ASSIGN_RECORD_IS_NULL("2020000199", "未找到分单记录", "POLICY"),
    POLICY_QUERY_ASSIGN_AGENT_STATUS_DRAG("2020000200", "分配的业务员必须在职", "POLICY"),
    POLICY_QUERY_AGENT_IS_NOT_FOUND("2020000201", "未找到业务员信息", "POLICY"),
    POLICY_SYNC_POLICY_PAYMENT_ERROR("2020000202", "同步保单缴费信息出错", "POLICY"),
    POLICY_SAVE_ENDORSE_INSURED_ERROR("2020000202", "团险新增被保人数据同步失败", "POLICY"),
    POLICY_SAVE_ENDORSE_INSURED_EXTEND_ERROR("2020000202", "团险新增被保人扩展表数据同步失败", "POLICY"),
    POLICY_SAVE_ENDORSE_INSURED_COLLECT_ERROR("2020000202", "团险新增被保人统计数据同步失败", "POLICY"),
    POLICY_SAVE_ENDORSE_PREMIUM_ERROR("2020000202", "团险新增保费信息同步失败", "POLICY"),
    POLICY_SAVE_ENDORSE_COVERAGE_ERROR("2020000202", "团险新增被保人险种数据同步失败", "POLICY"),
    POLICY_SAVE_ENDORSE_COVERAGE_LEVEL_ERROR("2020000202", "团险新增被保人险种档次数据同步失败", "POLICY"),
    POLICY_BUSINESS_COVERAGE_EXTEND_IS_NOT_FOUND_OBJECT("2020000202", "未找到保单待生效险种信息", "POLICY"),
    POLICY_SAVE_ENDORSE_PAYMENT_ERROR("2020000202", "团险新增保单缴费信息同步失败", "POLICY"),
    POLICY_BUSINESS_VERIFY_NOT_MATCH_ERROR("2020000202", "验证码校验失败", "POLICY"),
    POLICY_PARAMETER_COUNTRY_CODE_IS_NOT_NULL("2020000202", "国家区号不能为空", "USER"),
    POLICY_PARAMETER_MOBILE_IS_NOT_NULL("2020000202", "手机号码不能为空", "USER"),
    POLICY_PARAMETER_SMS_TYPE_CODE_IS_NOT_NULL("2020000202", "短信类型不能为空", "USER"),
    POLICY_PARAMETER_CHANNEL_TYPE_IS_NOT_NULL("2020000202", "渠道类型不能为空", "USER"),
    POLICY_PARAMETER_VERIFY_CODE_IS_NOT_NULL("2020000202", "验证码不能为空", "USER"),
    POLICY_PARAMETER_BUSINESS_ID_IS_NOT_NULL("2020000202", "业务ID不能为空", "USER"),
    POLICY_BUSINESS_MOBILE_ERROR("2020000202", "该手机号码无效", "POLICY"),
    POLICY_SAVE_ENDORSE_APPLICANT_ERROR("2020000202", "团险变更投保人代表信息同步失败", "POLICY"),
    POLICY_RECEIPT_REVIEW_REMARK_IS_NOT_NULL("***********","保单回执复效结论不能为空","POLICY"),
    POLICY_BUSINESS_AFTER_RENEWAL_DATE_ERROR("***********", "请将当前时间之前应收的续期都选中", "POLICY"),
    POLICY_QUERY_RENEWAL_RECEIVABLE_ERROR("2020000202", "查询保单剩余应收期数异常", "POLICY"),
    POLICY_BUSINESS_RENEWAL_GENERATE_BATCH_ERROR("2020000202", "批量生成续期应收数据出错", "POLICY"),
    POLICY_QUERY_CERTIFY_INFO_IS_ERROR("2021001020", "查询单证信息异常", "POLICY"),
    POLICY_POLICY_RELATION_SAVE_ERROR("**********", "保单关系表保存异常", "POLICY"),
    APPLY_APP_TRANS_BIRTHDAY_TO_AGE_ERROR("**********", "出生日期转年龄异常", "POLICY"),
    POLICY_QUERY_RENEWAL_ERROR("**********", "查询续期数据异常", "POLICY"),

    POLICY_IMPORT_PARAM_VALID_POLICYNO_IS_NOT_NULL("**********", "保单号不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLYNO_IS_NOT_NULL("**********", "投保单号不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PROVIDERID_IS_NOT_NULL("**********", "保险公司id不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_POLICYSTATUS_IS_NOT_NULL("**********", "保单状态不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLYDATE_IS_NOT_NULL("**********", "投保日期不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_EFFECTIVEDATE_IS_NOT_NULL("**********", "生效日期不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPROVEDATE_IS_NOT_NULL("**********", "承保日期不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_HESITATION_IS_NOT_NULL("**********", "犹豫期不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_HESITATIONENDDATE_IS_NOT_NULL("**********", "犹豫期截止日期不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_CANCELDATE_IS_NOT_NULL("**********", "撤单日期不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_DATASOURCE_IS_NOT_NULL("**********", "保单来源不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_RECEIPTDATE_IS_NOT_NULL("2020000214", "回执日期不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_AGENTCODE_IS_NOT_NULL("2020000215", "业务员编号不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_AGENTNAME_IS_NOT_NULL("2020000216", "业务员姓名不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLICANTNAME_IS_NOT_NULL("2020000217", "投保人姓名不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLYCANTIDTYPE_IS_NOT_NULL("2020000218", "投保人证件类型不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLICANTIDNO_IS_NOT_NULL("2020000219", "投保人证件号码不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLICANTSEX_IS_NOT_NULL("2020000220", "投保人性别不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLICANTBIRTHDAY_IS_NOT_NULL("2020000221", "投保人出生年月不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLICANTMOBILE_IS_NOT_NULL("2020000222", "投保人手机号不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_INSUREDNAME_IS_NOT_NULL("2020000223", "被保人姓名不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_INSUREDIDTYPE_IS_NOT_NULL("2020000224", "被保人证件类型不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_INSUREDIDNO_IS_NOT_NULL("2020000225", "被保人证件号码不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_INSUREDSEX_IS_NOT_NULL("2020000226", "被保人性别不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_INSUREDBIRTHDAY_IS_NOT_NULL("2020000227", "被保人出生年月不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_INSUREDMOBILE_IS_NOT_NULL("2020000228", "被保人手机号不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_INSURERELATIONSHIP_IS_NOT_NULL("2020000229", "投被保人关系不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PRODUCTCODE_IS_NOT_NULL("2020000230", "险种编码不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PRODUCTNAME_IS_NOT_NULL("2020000231", "险种名称不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_MAINFLAG_IS_NOT_NULL("2020000232", "主附险标识不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_BASEPREMIUM_IS_NOT_NULL("2020000233", "其础保费不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_BASEAMOUNT_IS_NOT_NULL("2020000234", "其础保额不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_TOTALPREMIUM_IS_NOT_NULL("2020000235", "总保费不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_TOTALAMOUNT_IS_NOT_NULL("2020000236", "总保额不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_COVERAGE_IS_NOT_NULL("2020000237", "险种不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_MULT_IS_NOT_NULL("2020000238", "险种份数不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PRODUCTLEVELCODE_IS_NOT_NULL("2020000239", "险种级别不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PREMIUMFREQUENCY_IS_NOT_NULL("2020000240", "缴费周期不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PREMIUMPERIODUNIT_IS_NOT_NULL("2020000241", "缴费期限单位不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PREMIUMPERIOD_IS_NOT_NULL("2020000242", "缴费期限不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_COVERAGEPERIODUNIT_IS_NOT_NULL("2020000243", "保障期限单位不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_COVERAGEPERIOD_IS_NOT_NULL("2020000244", "保障期限不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_COVERAGEEFFECTIVEDATE_IS_NOT_NULL("2020000245", "生效日期不可为空", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLYDATE_ERROR("2020000246", "投保日期格式错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPROVEDATE_ERROR("2020000247", "承保日期格式错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_EFFECTIVEDATE_ERROR("2020000248", "生效日期格式错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_HESITATIONENDDATE_ERROR("2020000249", "犹豫期日期格式错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_CANCELDATE_ERROR("2020000250", "撤单日期格式错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_RECEIPTDATE_ERROR("2020000251", "回执日期格式错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLICANTBIRTHDAY_ERROR("2020000252", "投保人生日日期格式错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_INSUREDBIRTHDAY_ERROR("2020000253", "被保人生日日期格式错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_COVERAGEEFFECTIVEDATE_ERROR("2020000254", "险种生效日期格式错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_HESITATION_ERROR("2020000255", "犹豫期非数字", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_BASEPREMIUM_ERROR("2020000256", "基础保费非数字", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_BASEAMOUNT_ERROR("2020000257", "基础保额非数字", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_TOTALPREMIUM_ERROR("2020000258", "总保费非数字", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_TOTALAMOUNT_ERROR("2020000259", "总保额非数字", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_MULT_ERROR("2020000260", "份数非数字", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PREMIUMPERIOD_ERROR("2020000261", "缴费期限非数字", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_COVERAGEPERIOD_ERROR("2020000262", "保险期限非数字", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_POLICYSTATUS_ERROR("2020000263", "保单状态错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_POLICYSOURCE_ERROR("2020000264", "保单资源错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLICANTIDTYPE_ERROR("2020000265", "投保人证件类型错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_INSUREDIDTYPE_ERROR("2020000266", "被保人证件类型错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_APPLICANTSEX_ERROR("2020000267", "投保人性别错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_INSUREDSEX_ERROR("2020000268", "被保人性别错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_RELATIONSHIP_ERROR("2020000269", "与投保人关系错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PRODUCTMAINFLAG_ERROR("2020000270", "主附险标识错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PREMIUMFAEQUENCY_ERROR("2020000271", "缴费周期错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PREMIUMPERIODUNIT_ERROR("2020000272", "缴费期限单位错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_COVERAGEPERIODUNIT_ERROR("2020000273", "保险期限单位错误", "POLICY"),
    POLICY_IMPORT_POLICY_IS_EXITS_ERROR("2020000274", "保单已存在", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_AGENTCODE_ERROR("2020000275", "代理人编码错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_PRODUCTCODE_ERROR("2020000276", "产品编码错误", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_MAIN_PRODUCT_IS_NOT_NULL("2020000277", "未找到主险", "POLICY"),
    POLICY_IMPORT_PARAM_VALID_MAIN_PRODUCT_ERROR("2020000278", "存在多个主险", "POLICY"),
    POLICY_IMPORT_TRANSFER_DATA_PRODUCT_CAL_ERROR("2020000279", "产品计算费率异常", "POLICY"),
    POLICY_IMPORT_READ_ATTRACHMENT_DATA_ERROR("2020000280", "保单导入读取附件数据错误", "POLICY"),
    POLICY_IMPORT_READ_ATTRACHMENT_DATA_IS_NULL("2020000281", "未读取到文件数据", "POLICY"),
    POLICY_IMPORT_READ_ATTRACHMENT_DATA_IS_IMPORT("2020000282", "该文件数据已导入", "POLICY"),
    POLICY_IMPORT_BATCH_DATA_IS_NULL("2020000283", "未找到保单导入批次", "POLICY"),
    POLICY_IMPORT_POLICY_SAVE_DATA_ERROR("2020000284", "保单导入保存保单信息出错", "POLICY"),
    POLICY_IMPORT_POLICY_BUSINESS_OPTION_ERROR("2020000285", "保单导入保存保单信息出错", "POLICY"),
    POLICY_SAVE_POLICY_RETURN_VISIT_CHANNEL_IS_NOT_NULL_ERROR("2020000286", "渠道不能为空", "POLICY"),
    POLICY_SAVE_POLICY_RETURN_VISIT_RESULT_IS_NOT_NULL_ERROR("2020000287", "结果不能为空", "POLICY"),
    POLICY_SAVE_POLICY_RETURN_VISIT_AUDIT_RESULT_IS_NOT_NULL_ERROR("2020000288", "审核结果不能为空", "POLICY"),
    POLICY_SAVE_POLICY_RETURN_VISIT_DATE_IS_NOT_NULL_ERROR("2020000289", "回访日期不能为空", "POLICY"),
    POLICY_SAVE_POLICY_RETURN_VISIT_REMARK_IS_NOT_NULL_ERROR("2020000290", "回访备注不能为空", "POLICY"),
    POLICY_SAVE_POLICY_RETURN_VISIT_AUDIT_REMARK_IS_NOT_NULL_ERROR("2020000291", "回访审核备注不能为空", "POLICY"),
    POLICY_PARAMETER_HOOK_CLASS_CODE_IS_NOT_NULL("2020000292", "挂起类别编码不能为空", "POLICY"),
    POLICY_PARAMETER_HOOK_SOURCE_CODE_IS_NOT_NULL("2020000293", "挂起源编码不能为空", "POLICY"),
    POLICY_PARAMETER_HOOK_OBJECT_ID_IS_NOT_NULL("2020000294", "挂起对象ID不能为空", "POLICY"),
    POLICY_HOOK_IS_EXIST("2020000295", "该保单已挂起，请勿操作", "POLICY"),
    POLICY_RETURN_VISIT_DATE_CANNOT_BE_GREATER_THAN_THE_CURRENT_DATE("2020000296", "回访日期不能大于当前日期", "POLICY"),
    POLICY_RETURN_VISIT_IS_EXIST("2020000297", "该保单已经回访", "POLICY"),
    POLICY_RETURN_VISIT_ID_NOT_NULL("2020000298", "保单回访ID不能为空", "POLICY"),
    POLICY_RETURN_VISIT_CHANGE_ID_NOT_NULL("2020000299", "保单回访申请ID不能为空", "POLICY"),
    POLICY_RETURN_VISIT_ID_NOT_EXIST("2020000300", "保单回访不存在", "POLICY"),
    POLICY_RETURN_VISIT_CHANGE_IS_EXIST("2020000301", "保单回访修改申请已存在", "POLICY"),
    POLICY_RETURN_VISIT_CHANGE_NOT_EXIST("2020000302", "保单回访修改申请不存在", "POLICY"),
    POLICY_RETURN_VISIT_OBJECT_NOT_NULL("2020000422", "回访数据不能为空", "POLICY"),
    POLICY_RETURN_VISIT_OBJECT_BUSINESS_ID_NOT_NULL("2020000423", "回访业务id不能为空", "POLICY"),
    POLICY_RETURN_VISIT_IS_NULL("2020000424", "未找到回访记录", "POLICY"),
    POLICY_COMMISSION_POLICY_UNLOCK_IS_NOT_NULL("2020000425", "解锁数据不能为空", "POLICY"),
    POLICY_COMMISSION_UNLOCK_BUSINESS_ID_IS_NOT_NULL("2020000426", "解锁业务ID不能为空", "POLICY"),
    POLICY_RETURN_VISIT_BUSINESS_TYPE_IS_UNEQUALU("2020000427", "业务类型不相等", "POLICY"),
    // 保单基础服务
    POLICY_BASE_FAIL("2020000303", "保单基础服务报错", "POLICY_BASE"),
    POLICY_BASE_PARAMETER_POLICY_ID_IS_NOT_NULL("2020000304", "保单ID不能为空", "POLICY_BASE"),
    POLICY_BASE_PARAMETER_APPLY_ID_IS_NOT_NULL("2020000305", "投保单ID不能为空", "POLICY_BASE"),
    POLICY_BASE_PARAMETER_POLICY_NO_IS_NOT_NULL("2020000306", "保单号不能为空", "POLICY_BASE"),
    POLICY_BASE_PARAMETER_POLICY_IS_NOT_NULL("2020000307", "保单信息不能为空", "POLICY_BASE"),
    POLICY_BASE_PARAMETER_POLICY_STATUS_IS_NOT_NULL("2020000308", "保单状态不能为空", "POLICY_BASE"),
    POLICY_BASE_PARAMETER_POLICY_INSURED_IS_NOT_NULL("2020000309", "保单被保人信息不能为空", "POLICY_BASE"),
    POLICY_BASE_PARAMETER_INSURED_ID_IS_NOT_NULL("2020000310", "保单被保人ID不能为空", "POLICY_BASE"),
    POLICY_BASE_PARAMETER_AGENT_ID_IS_NOT_NULL("2020000311", "保单业务员ID不能为空", "POLICY_BASE"),
    POLICY_BASE_PARAMETER_RECEIVABLE_DATE_IS_NOT_NULL("2020000312", "保单应缴日期不能为空", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_ERROR("2020000313", "查询保单信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_APPLICANT_ERROR("**********", "查询投保人信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_ATTACHMENT_ERROR("**********", "查询保单附件列表出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_COVERAGE_ERROR("**********", "查询保单险种列表出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_INSURED_ERROR("**********", "查询保单被保人列表出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_ACCOUNT_ERROR("**********", "查询保单账户信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_AGENT_ERROR("**********", "查询保单业务员信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_INSURED_COLLECT_ERROR("**********", "查询被保人统计信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_CONTACT_INFO_ERROR("**********", "查询保单联系信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_PREMIUM_ERROR("**********", "查询保单缴费信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_QUESTION_FLOW_ERROR("**********", "查询问题件信息异常", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_SPECIAL_CONTRACT_ERROR("**********", "查询特别约定信息异常", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_REMARK_ERROR("**********", "查询备注信息异常", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_INSURED_UPLOAD_ERROR("**********", "查询被保人清单上传表异常", "POLICY_BASE"),
    POLICY_BASE_QUERY_WORKFLOW_FAIL("2020000327", "请求工作流数据异常", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_CERTIFY_ERROR("2020000328", "单证信息查询异常", "POLICY"),
    POLICY_BASE_QUERY_POLICY_ADD_PREMIUM_ERROR("2020000329", "查询保单加费信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_RECEIPT_INFO_ERROR("2020000330", "查询保单回执信息异常", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_PAYMENT_ERROR("2020000331", "查询保单缴费信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_IMPORT_BATCH_ERROR("2020000332", "查询保单导入批次信息出错", "POLICY_BASE"),
    POLICY_BASE_DELETE_POLICY_IMPORT_UPLOAD_ERROR("2020000333", "删除导入数据失败", "POLICY_BASE"),
    POLICY_BASE_DELETE_POLICY_IMPORT_POLICY_ERROR("2020000334", "删除导入保单数据失败", "POLICY_BASE"),
    POLICY_BASE_DELETE_POLICY_IMPORT_COVERAGE_ERROR("2020000335", "删除导入险种数据失败", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_IMPORT_UPLOAD_ERROR("2020000336", "查询导入数据失败", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_IMPORT_POLICY_ERROR("2020000337", "查询导入保单数据失败", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_IMPORT_COVERAGE_ERROR("2020000338", "查询导入保单险种数据失败", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_APPLY_TRANS_TO_POLICY_ERROR("2020000339", "投保单转保单异常", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_POLICY_IS_EXIST("2020000340", "保单已存在,请勿重复承保", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_POLICY_NO_REPEAT_ERROR("2020000341", "保单号重复", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_CALCULATE_EFFTIVE_DATE_ERROR("2020000342", "计算有效期异常", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_CALCULATE_COMMISSION_ERROR("2020000343", "计算直佣错误", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_APPLY_TRANS_TO_POLICY_ROLLBACK_ERROR("2020000344", "投保单转保单回滚异常", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_POLICY_IS_NOT_FOUND_OBJECT("2020000345", "未找到保单信息", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_POLICY_PREMIUM_IS_NOT_FOUND_OBJECT("2020000346", "未找到保单保费信息", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_POLICY_APPLICANT_IS_NOT_FOUND_OBJECT("2020000347", "未找到保单投保人信息", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_ROOLBACK_PAYMENT_ERROR("2020000348", "保单缴费信息回滚出错", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_POLICY_INSURED_IS_NOT_FOUND_OBJECT("**********", "未找到保单被保人信息", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_RECEIPT_INFO_ERROR("**********", "保存保单回执信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_ATTACHMENT_ERROR("**********", "保存保单附件信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_ERROR("**********", "保存保单信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_ACCOUNT_ERROR("**********", "保存保单账户信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_AGENT_ERROR("**********", "保存保单业务员信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_APPLICANT_ERROR("**********", "保存保单投保人信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_CONTACT_INFO_ERROR("**********", "保存保单联系人信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_PREMIUM_ERROR("**********", "保存保单保费信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_PAYMENT_ERROR("**********", "保存保单缴费信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_PRINT_INFO_ERROR("**********", "保存保单打印信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_INSURED_ERROR("**********", "保存保单被保人信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_INSURED_EXTEND_ERROR("**********", "保存保单被保人拓展信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_COVERAGE_ERROR("2020000362", "保存保单险种信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_COVERAGE_PREMIUM_ERROR("2020000363", "保存保单险种保费信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_COVERAGE_PAYMENT_ERROR("2020000364", "保存保单险种缴费信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_INSURED_COLLECT_ERROR("2020000365", "保存保单被保人统计信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_BENEFICIARY_ERROR("2020000366", "保存保单受益人信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_ADD_PREMIUM_ERROR("2020000367", "保存保单加费信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_SPECIAL_CONTRACT_ERROR("2020000368", "保存保单特约信息异常", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_AGENT_HISTORY_ERROR("2020000369", "查询保单历史业务员信息出错", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_AGENT_HISTORY_ERROR("2020000370", "保存保单历史业务员信息出错", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_REVIEW_DECISION_ERROR("2020000370", "保存保单复核结论信息出错", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_POLICY_AGENT_IS_NOT_FOUND_OBJECT("2020000371", "未找到保单业务员", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_POLICY_PAYMENT_ID_LIST_IS_NOT_EMPTY("2020000372", "保单缴费ID不为空", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_POLICY_PAYMENT_STATUS_CODE_IS_NOT_NULL("2020000373", "保单缴费状态不为空", "POLICY_BASE"),
    POLICY_BASE_BUSINESS_POLICY_PAYMENT_IS_NOT_FOUND("2020000374", "未找到保单缴费信息", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_COVERAGE_PAYMENT_ERROR("2020000375", "查询保单险种缴费信息异常", "POLICY_BASE"),
    POLICY_BASE_PARAMETER_POLICY_PAYMENT_ID_IS_NOT_NULL("2020000376", "保单缴费信息ID不能为空", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_CUSTOMER_RELATION_ERROR("2020000377", "查询客户关联保单信息异常", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_CUSTOMER_APPLICANT_ERROR("2020000378", "查询客户名下保单信息异常", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_HISTORY_ERROR("2020000379", "查询保单历史信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_APPLICANT_HISTORY_ERROR("2020000380", "查询保单投保人历史信息出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_COVERAGE_HISTORY_ERROR("2020000381", "查询保单险种历史列表出错", "POLICY_BASE"),
    POLICY_BASE_QUERY_POLICY_HISTORY_IS_NOT_FOUND("**********", "未找到保单历史信息", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_RECEIPT_INFO_HISTORY_ERROR("**********", "保存保单回执历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_ATTACHMENT_HISTORY_ERROR("**********", "保存保单附件历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_HISTORY_ERROR("**********", "保存保单历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_ACCOUNT_HISTORY_ERROR("**********", "保存保单账户历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_APPLICANT_HISTORY_ERROR("**********", "保存保单投保人历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_CONTACT_INFO_HISTORY_ERROR("**********", "保存保单联系人历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_PREMIUM_HISTORY_ERROR("**********", "保存保单保费历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_PAYMENT_HISTORY_ERROR("**********", "保存保单缴费历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_PRINT_INFO_HISTORY_ERROR("**********", "保存保单打印历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_INSURED_HISTORY_ERROR("**********", "保存保单被保人历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_INSURED_EXTEND_HISTORY_ERROR("**********", "保存保单被保人拓展历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_COVERAGE_HISTORY_ERROR("2020000394", "保存保单险种历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_COVERAGE_PREMIUM_HISTORY_ERROR("2020000395", "保存保单险种保费历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_COVERAGE_PAYMENT_HISTORY_ERROR("2020000396", "保存保单险种缴费历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_COVERAGE_BOUNS_HISTORY_ERROR("2020000397", "保存保单险种红利历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_COVERAGE_DUTY_HISTORY_ERROR("2020000398", "保存保单险种责任历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_INSURED_COLLECT_HISTORY_ERROR("2020000399", "保存保单被保人统计历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_SPECIAL_CONTRACT_HISTORY_ERROR("2020000400", "保存保单特约历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_ADD_PREMIUM_HISTORY_ERROR("2020000401", "保存保单加费历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_BENEFICIAL_HISTORY_ERROR("2020000402", "保存保单受益人历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_BENEFICIAL_INFO_HISTORY_ERROR("2020000403", "保存保单受益人关系历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_PAYOR_INFO_HISTORY_ERROR("2020000404", "保存保单付费人历史信息异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_BASE_HISTORY_DATA_ERROR("2020000405", "保存保单基础历史数据异常", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_RENEWAL_GRAB_ERROR("2020000406", "保存保单续期抢单表出错", "POLICY_BASE"),
    POLICY_BASE_ROLLBACK_POLICY_ERROR("2020000407", "回滚保单数据出错", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_PROJECT_CODE_IS_NOT_NULL("2020000408", "保单修改项目编码不能为空", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_PROJECT_CODE_NOT_EXIST("2020000409", "保单修改项目编码信息不存在", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_REMARK_IS_NOT_NULL("2020000410", "保单修改备注不能为空", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_EXISTS_AUDIT("2020000411", "保单修改已存在正在审核中", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_NOT_EXISTS("2020000412", "保单修改不存在", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_ID_IS_NOT_NULL("2020000413", "保单修改Id不能为空", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_DATA_IS_NOT_NULL("2020000414", "保单修改数据不能为空", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_REMARK_RESULT_IS_NOT_NULL("2020000415", "审核结论不能为空", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_REMARK_CONTENT_IS_NOT_NULL("2020000416", "审核备注不能为空", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_DATA_NOT_CHANGED("2020000417", "你尚未修改任何信息，不能提交", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_DATA_NOT_NULL("2020000418", "保存数据不能为空", "POLICY_BASE"),
    POLICY_BASE_SAVE_POLICY_UPDATE_SUBMIT_EXISTS("2020000419", "保单修改提交已申请", "POLICY_BASE"),
    POLICY_PRODUCT_MAIN_ERROR("2020000420", "主险险种信息错误", "POLICY"),
    POLICY_BASE_QUERY_COVERAGE_EXTEND_IS_NOT_FOUND("2020000421", "未找到险种扩展信息", "POLICY_BASE"),
    POLICY_BASE_DELETE_POLICY_COVERAGE_ERROR("2020000422", "删除保单险种信息异常", "POLICY_BASE"),
    POLICY_BASE_ROLL_BACK_POLICY_ERROR("2020000423", "回滚保单信息异常", "POLICY_BASE"),
    /**
     * 未找到产品附件
     */
    POLICY_QUERY_PRODUCT_ATTACHMENT_IS_NOT_FOUND_OBJECT("2020000424", "未找到产品附件", "POLICY_BASE"),

    POLICY_SAVE_POLICY_REVIEW_ERROR("2020000088", "保存保费复核信息异常", "POLICY"),

    POLICY_BUSINESS_GROUP_RENEWAL_APPLY_ENDORSE_UNFINISHED_ERROR("2020000500", "当前保单还有未完成的保全，不允许申请续保", "POLICY"),
    POLICY_BUSINESS_GROUP_RENEWAL_APPLY_CLAIM_UNFINISHED_ERROR("2020000501", "当前保单还有未完成的理赔，不允许申请续保", "POLICY"),

    POLICY_INSURED_HOOK_IS_EXIST("2020000295", "被保人(%s)已挂起，请勿操作", "POLICY"),

    POLICY_REVIEW_RESULT_IS_NOT_NULL("**********", "复核结论不能为空", "POLICY"),
    POLICY_PLEASE_COMPLETE_THE_MERGER_SUSPECTED_CUSTOMERS_ERROR("2020000501", "请先完成疑似客户合并", "POLICY"),
    POLICY_THE_MERGER_SUSPECTED_CUSTOMERS_ERROR("2020000501", "所选客户合并操作已更改算费因子，不允许操作。", "POLICY"),
    POLICY_AGENT_CODE_IS_NOT_NULL("2020000502", "业务员代码不能为空", "POLICY"),
    POLICY_REMARK_IS_NOT_NULL("2020000503", "备注不能为空", "POLICY"),
    POLICY_EFFECTIVE_DATE_IS_NOT_NULL("2020000503", "生效日期不能为空", "POLICY"),
    POLICY_SERVICE_AGENT_ALREADY_ALLOCATED_ERROR("2020000504", "审核阶段的保单不能再次分配", "POLICY"),
    POLICY_SERVICE_AGENT_DELETE_ASSIGN_ERROR("2020000505", "只能删除暂存的分配", "POLICY"),
    POLICY_SERVICE_AGENT_SERVICE_AGENT_REPEAT_ERROR("2020000506", "业务员不能和服务业务员是同一人", "POLICY"),
    POLICY_POLICY_SERVICE_AGENT_ID_IS_NOT_FOUND("2020000507", "请选择保单", "POLICY"),
    POLICY_SERVICE_AGENT_SERVICE_AGENT_ID_REPEAT_ERROR("2020000508", "请重新选择服务业务人员", "POLICY"),
    POLICY_SERVICE_AGENT_SALES_AGENT_IS_NOT_NULL("2020000509", "找不到保单销售业务员", "POLICY"),
    POLICY_SERVICE_AGENT_ID_REPEAT_ERROR("2020000508", "请重新选择业务员", "POLICY"),

    ;

    private String code;

    private String value;

    private String group;

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String getGroup() {
        return group;
    }

    @Override
    public String getCode(String value, String group) {
        return null;
    }

    @Override
    public String getValue(String code, String group) {
        return null;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    PolicyErrorConfigEnum(String code, String value, String group) {
        this.code = code;
        this.value = value;
        this.group = group;
    }

}